image: 
  name: registry-intl.ap-southeast-1.aliyuncs.com/madex-public/bitbucket-images:4
  username: $KTX_DOCKERHUB_USERNAME
  password: $KTX_DOCKERHUB_PASSWORD

pipelines:
     branches:
       deploy-pre:
        - step:
            name: Build nodejs
            runs-on:
              - self.hosted
              - linux
            size: 2x 
            caches:
              - node
            script:
              - eval "curl -X POST "https://open.larksuite.com/open-apis/bot/v2/hook/$LARK_API_KEY" -H \"Content-Type:application/json\" -d '{\"msg_type\":\"text\",\"content\":{\"text\":\"$BITBUCKET_REPO_SLUG -分支($BITBUCKET_BRANCH) PIPELINE开始构建\"}}'" 
              - npm install
              - npm run build
              - npm run static
            artifacts:
              - node_modules/**
              - .output/**
        - step:
            name: Build Docker Images & deploy to dockerhub
            runs-on:
              - self.hosted
              - linux
            script:
              - IMAGE_NAME=$BITBUCKET_REPO_SLUG
              - docker build . --file Dockerfile --tag ${IMAGE_NAME}
              - VERSION="pre-${BITBUCKET_BUILD_NUMBER}"
              - IMAGE=${MADEX_DOCKERHUB_URL}/${MADEX_DOCKERHUB_NAMESPACE_PROD}/${IMAGE_NAME}
              - docker tag "${IMAGE_NAME}" "${IMAGE}:${VERSION}"
              - docker tag "${IMAGE_NAME}" "${IMAGE}:pre-latest"
              - echo ${MADEX_DOCKERHUB_PASSWORD} | docker login --username ${MADEX_DOCKERHUB_USERNAME} --password-stdin ${MADEX_DOCKERHUB_URL}
              - docker push "${IMAGE}:pre-latest"
              - docker push "${IMAGE}:${VERSION}"
            after-script:
              - ALERT_TYPE="成功"
              - if [[ $BITBUCKET_EXIT_CODE -ne 0 ]]; then ALERT_TYPE="失败" ; fi
              - eval "curl -X POST "https://open.larksuite.com/open-apis/bot/v2/hook/$LARK_API_KEY" -H \"Content-Type:application/json\" -d '{\"msg_type\":\"text\",\"content\":{\"text\":\"$BITBUCKET_REPO_SLUG -分支($BITBUCKET_BRANCH) PIPELINE $ALERT_TYPE\"}}'" 
            services:
              - docker
            caches:
              - docker
       deploy-prod:
        - step:
            name: Build nodejs
            runs-on:
              - self.hosted
              - linux
            size: 2x 
            caches:
              - node
            script:
              - eval "curl -X POST "https://open.larksuite.com/open-apis/bot/v2/hook/$LARK_API_KEY" -H \"Content-Type:application/json\" -d '{\"msg_type\":\"text\",\"content\":{\"text\":\"$BITBUCKET_REPO_SLUG -分支($BITBUCKET_BRANCH) PIPELINE开始构建\"}}'" 
              - npm install
              - npm run build
              - npm run static
            artifacts:
              - node_modules/**
              - .output/**
        - step:
            name: Build Docker Images & deploy to dockerhub
            runs-on:
              - self.hosted
              - linux
            script:
              - IMAGE_NAME=$BITBUCKET_REPO_SLUG
              - docker build . --file Dockerfile --tag ${IMAGE_NAME}
              - VERSION="${BITBUCKET_BUILD_NUMBER}"
              - IMAGE=${KTX_DOCKERHUB_URL_HK}/${KTX_DOCKERHUB_NAMESPACE}/${IMAGE_NAME}
              - docker tag "${IMAGE_NAME}" "${IMAGE}:${VERSION}"
              - docker tag "${IMAGE_NAME}" "${IMAGE}:latest"
              - echo ${KTX_DOCKERHUB_PASSWORD_HK} | docker login --username ${KTX_DOCKERHUB_USERNAME_HK} --password-stdin ${KTX_DOCKERHUB_URL_HK}
              - docker push "${IMAGE}:latest"
              - docker push "${IMAGE}:${VERSION}"
            after-script:
              - ALERT_TYPE="成功"
              - if [[ $BITBUCKET_EXIT_CODE -ne 0 ]]; then ALERT_TYPE="失败" ; fi
              - eval "curl -X POST "https://open.larksuite.com/open-apis/bot/v2/hook/$LARK_API_KEY" -H \"Content-Type:application/json\" -d '{\"msg_type\":\"text\",\"content\":{\"text\":\"$BITBUCKET_REPO_SLUG -分支($BITBUCKET_BRANCH) PIPELINE$ALERT_TYPE\"}}'" 
            services:
              - docker
            caches:
              - docker
