#!/bin/bash

function CheckResults(){
  STATUS=$?
  if [ $STATUS -eq 0 ];then
    echo "..... success `date`"
  else
    echo "..... fail, exit `date`"
    exit -1
  fi
}

echo "Create docker image target: madex-web-ssr"

echo 'git 更新代码...'
git pull
CheckResults

echo 'npm 更新依赖...'
npm install && npm update
npm run build
CheckResults
tag=v`date +%Y%m%d-%H%M`
docker build -t registry-intl.ap-southeast-1.aliyuncs.com/madex/madex-web-ssr:latest .
docker tag registry-intl.ap-southeast-1.aliyuncs.com/madex/madex-web-ssr:latest registry-intl.ap-southeast-1.aliyuncs.com/madex/madex-web-ssr:${tag}

CheckResults
docker push registry-intl.ap-southeast-1.aliyuncs.com/madex/madex-web-ssr:latest
docker push registry-intl.ap-southeast-1.aliyuncs.com/madex/madex-web-ssr:${tag}

