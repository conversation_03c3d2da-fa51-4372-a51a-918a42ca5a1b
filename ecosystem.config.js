const path = require('path')
require('ts-node').register({
  "compilerOptions": {
    "verbatimModuleSyntax": false,
    "module": "CommonJS"
  }
})
const config = require(path.join(process.cwd(), 'src/config/index.ts'))

module.exports = {
  apps: [
    {
      name: config.PROJECT_KEY,
      max_memory_restart: '512M',
      autorestart: true,
      port: config.port,
      exec_mode: 'cluster',
      instances: '4', // Or a number of instances
      script: './.output/server/index.mjs',
      env_qa: { // 环境变量
        NODE_ENV: 'production',
        PATH_TYPE: 'production'
      },
      env_production: { // 环境变量
        NODE_ENV: 'production',
        PATH_TYPE: 'production'
      },
      env_pre: { // 环境变量
        NODE_ENV: 'production',
        PATH_TYPE: 'pre'
      },
      env_dev: { // 环境变量
        NODE_ENV: 'production',
        PATH_TYPE: 'production'
      }
    }
  ]
}
