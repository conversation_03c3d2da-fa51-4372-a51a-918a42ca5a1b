{"name": "madex-ssr", "private": true, "scripts": {"build": "NODE_OPTIONS=--max-old-space-size=4096 nuxt build --dotenv .env.production", "build:test": "NODE_OPTIONS=--max-old-space-size=4096 nuxt build --dotenv .env.development", "build:local": "NODE_OPTIONS=--max-old-space-size=4096 nuxt build --dotenv .env.local", "static": "node ./script/upload-to-oss.js", "dev": "nuxt dev --dotenv .env.local", "pro": "nuxt dev --dotenv .env.production", "test": "nuxt dev --dotenv .env.development", "start": "node .output/server/index.mjs", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "devDependencies": {"@nuxt/devtools": "latest", "@types/cookie": "^0.6.0", "@types/crypto-js": "^4.2.1", "@types/jsrsasign": "^10.4.1", "@types/node": "^20.14.2", "camelcase": "^8.0.0", "cross-env": "^7.0.3", "fast-glob": "^3.3.2", "path": "^0.12.7", "prettier": "^3.3.2"}, "dependencies": {"@element-plus/nuxt": "^1.0.9", "@nuxtjs/color-mode": "^3.3.3", "@nuxtjs/i18n": "^8.0.0", "@pinia-plugin-persistedstate/nuxt": "^1.2.0", "@pinia/nuxt": "^0.5.1", "@vant/nuxt": "^1.0.4", "ali-oss": "^6.22.0", "alioss-upload": "^0.0.1", "axios": "^1.6.3", "bignumber.js": "^9.1.2", "blueimp-md5": "^2.19.0", "cookie": "^0.6.0", "crypto-js": "^4.2.0", "date-format": "^4.0.14", "dayjs": "^1.11.13", "echarts": "^5.5.1", "element-plus": "2.8.0", "install": "^0.13.0", "isemail": "^3.2.0", "jsrsasign": "^10.4.1", "jstz": "^2.1.1", "klinecharts": "^10.0.0-alpha5", "lodash-unified": "^1.0.3", "mlly": "^1.7.1", "npm": "^10.8.2", "nuxt": "^3.11.2", "nuxt-swiper": "^1.2.2", "pinia": "^2.1.7", "pm2": "^5.4.2", "qr-code-styling": "^1.9.2", "qrcode": "^1.5.4", "qs": "^6.12.3", "sass": "1.77.6", "screenfull": "^6.0.2", "ts-node": "^10.9.2", "ua-parser-js": "^1.0.38", "unhead": "1.0.0", "unplugin-auto-import": "^0.17.6", "unplugin-vue-components": "^0.27.0", "vant": "^4.9.1", "vue": "^3.3.13", "vue-i18n": "^9.13.1", "vue-lazyload-next": "^0.0.2", "vue-router": "^4.2.5", "vue3-colorpicker": "^2.3.0"}}