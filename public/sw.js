const CACHE_NAME = 'ktx-static-cache-v1'
const STATIC_CACHE_URLS = []

const EXTERNAL_CACHE_URLS = [
  'https://code.highcharts.com/highcharts.js'
]

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        if (EXTERNAL_CACHE_URLS.length > 0) {
          return cache.addAll(EXTERNAL_CACHE_URLS)
        }
        return Promise.resolve()
      })
      .catch(() => {
        return Promise.resolve()
      })
  )
  self.skipWaiting()
})

self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            return caches.delete(cacheName)
          }
        })
      )
    })
  )
  self.clients.claim()
})

self.addEventListener('fetch', (event) => {
  const { request } = event

  if (request.method !== 'GET') {
    return
  }

  if (request.url.includes('highcharts.com')) {
    event.respondWith(
      caches.match(request)
        .then((response) => {
          if (response) {
            return response
          }

          return fetch(request.clone())
            .then((response) => {
              if (response && response.status === 200) {
                const responseToCache = response.clone()
                caches.open(CACHE_NAME)
                  .then((cache) => {
                    cache.put(request, responseToCache)
                  })
                  .catch(() => {})
              }
              return response
            })
            .catch(() => {
              return fetch(request)
            })
        })
    )
  }
})
