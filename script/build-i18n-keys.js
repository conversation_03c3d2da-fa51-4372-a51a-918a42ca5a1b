const fs = require('fs')
const path = require('path')

const src = 'src/pages/launchpool'
const sonDir = ''
const outputDir = path.resolve('src', 'i18n', 'keys')
const regexp = /\$t\((.+?)(\'\,|\"\,|\'\))/g

try {
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir)
  }
} catch (err) {
  console.log(err)
}
const fileSuffix = ['.vue']

const targetPath = path.resolve(src, sonDir)

const getAllTargetFile = readTargetPath => {
  const result = []
  const readResult = fs.readdirSync(readTargetPath)
  if (readResult) {
    readResult.forEach(fileName => {
      const filePath = path.resolve(readTargetPath, fileName)
      if (fs.statSync(filePath).isFile()) {
        fileSuffix.some(v => fileName.endsWith(v)) && result.push(filePath)
      } else {
        const innerReadResult = getAllTargetFile(filePath)
        innerReadResult.forEach(itemFileName => {
          result.push(path.resolve(readTargetPath, itemFileName))
        })
      }
    })
  }

  return result
}

const targetFileArray = getAllTargetFile(targetPath)

const resultI18nKey = []

targetFileArray.forEach(filePath => {
  const fileContent = fs.readFileSync(filePath)
  if (fileContent) {
    const ary = fileContent.toString().match(regexp)
    if (ary) {
      ary.forEach(item => {
        if (!resultI18nKey.includes(item)) {
          resultI18nKey.push(item)
        }
      })
    }
  }
})

const formatedI18nKey = []
const formatedRegexp = /(\'|\")(\s*|\S*|.*)(\'|\")\)/g

resultI18nKey.forEach(item => {
  let str = item.match(formatedRegexp)
  if (str) {
    str = str && str[0] || ''
    str = str.replace(/^('|")/g, '').replace(/('|")\)?$/g, '')
    formatedI18nKey.push(str)
  } else {
    formatedI18nKey.push(item.replace(/^\$t\((\'|\")/g, '').replace(/(\'|")?,?/g, ''))
  }
})
let outputName = sonDir.split(/(\/|\\)/).filter(v => v !== '/' && v !== '\\').join('-')

if (!outputName) {
  outputName = 'all'
}

fs.writeFileSync(path.resolve(outputDir, outputName) + '.txt', formatedI18nKey.filter(v => v).join('\n'))