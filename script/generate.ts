import path from 'node:path'
import { dirname, resolve } from 'node:path'
import { fileURLToPath } from 'node:url'
import { type BuiltInParserName, format } from 'prettier'
import camelcase from 'camelcase'
// import { findWorkspacePackages } from '@pnpm/find-workspace-packages'
// import { findWorkspaceDir } from '@pnpm/find-workspace-dir'
import glob from 'fast-glob'
import { readFile, writeFile } from 'node:fs/promises'

const dir = dirname(fileURLToPath(import.meta.url))

const pathRoot = resolve(dir, '..')
const pathSrc = resolve(pathRoot, 'src')
const pathIcons = resolve(pathRoot, 'icons')
const pathComponents = resolve(pathSrc, 'icons')


const files = await getSvgFiles()
function getName(file: string) {
  const filename = path.basename(file).replace('.svg', '')
  const folders = file.replace(`/${filename}.svg`, '').split('/')
  const folder = folders[folders.length - 1]
  const componentName = camelcase(`${folder}-${filename}`, { pascalCase: true })
  return {
    folder,
    filename,
    componentName,
  }
}

async function getSvgFiles() {
  const files = await glob('*/*.svg', {
    cwd: pathIcons,
    absolute: true
  })
  return files
}


const vueComponents: any[] = []
await Promise.all(files.map((file) => transformToVueComponent(file)))
await generateTs()

async function generateTs() {
  let ip: string = ''
  let ep: string = ''
  vueComponents.forEach((v: any) => {
    ip += `import ${v.componentName} from "${v.path}"\n`
    ep += `${v.componentName},\n`
  })
  const ts = await formatCode(`
    ${ip}
    export default {
      ${ep}
    }
  `)
  writeFile(path.resolve(pathComponents, `index.ts`), ts, 'utf-8')
}

async function transformToVueComponent(file: string) {
  let content = await readFile(file, 'utf-8')
  const { filename, componentName, folder } = getName(file)
  if (filename.startsWith('c2') || filename.startsWith('c3')) {
    content = content
      .replace('<svg ', '<svg class="bge-color-svg" :style="`width: 1em; height: 1em;font-size: ${size}px;`"')
      .replace(/fill=\"#FFFF38\"/g, 'class="bge-fill-icon-theme"')
      .replace(/fill=\"#708397\"/g, 'class="bge-fill-icon-gray"')
      .replace(/fill=\"#32404D\"/g, 'class="bge-fill-gay-dark"')
      .replace(/stroke=\"#FFFF38\"/g, 'class="bge-stroke-icon-theme"')
      .replace(/stroke=\"#708397\"/g, 'class="bge-stroke-icon-gray"')
      .replace(/stroke=\"#32404D\"/g, 'class="bge-stroke-gay-dark"')
  } else if (folder === 'Mono' || folder === 'Media') {
    content = content.replace('<svg ', '<svg :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? \'color:\' + color + \';\' : \'\'};`"').replace(/#808080/g, 'currentColor')
  } else {
    content = content.replace('<svg ', '<svg :style="`width: 1em; height: 1em; font-size: ${size}px;`"')
  }
  const vue = await formatCode(
    `
<template>
${content}
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  }
})
defineOptions({
  name: ${JSON.stringify(componentName)}
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #FFF833;
  --icon-gray: #9BAEC2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #FFFF38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404D;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>`,
    'vue',
  )
  writeFile(path.resolve(pathComponents, `${componentName}.vue`), vue, 'utf-8')
  vueComponents.push({
    name: filename,
    componentName: componentName,
    path: `./${componentName}.vue`
  })
}

function formatCode(code: string, parser: BuiltInParserName = 'typescript') {
  return format(code, {
    parser,
    semi: false,
    singleQuote: true,
  })
}