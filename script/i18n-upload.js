const axios = require('axios');

// 获取 access_token
const getAccessToken = async () => {
  const response = await fetch('https://open.larksuite.com/open-apis/auth/v3/app_access_token/internal', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      app_id: 'cli_a74100f7b038d02e', // 替换为你的 App ID
      app_secret: 'hBwQD2eXwJXdJjO3NQ2TEdTShI3B5NUn', // 替换为你的 App Secret
    }),
  });

  const data = await response.json();
  if (data.code !== 0) {
    throw new Error(`获取 access_token 失败: ${data.msg}`);
  }
  return data.app_access_token;
};

// 获取文档内容
const getDocumentContent = async (docToken, accessToken) => {
  const response = await fetch(`https://open.larksuite.com//open-apis/sheets/v2/spreadsheets/${docToken}/values_batch_get?ranges=0lDjLx`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json; charset=utf-8'
    },
  });

  const data = await response.json();
  console.log('获取文档内容:', data)
  if (data.code !== 0) {
    throw new Error(`获取文档内容失败: ${data.msg}`);
  }
  return data;
};

// 处理数据并上传
const processData = async () => {
  try {
    // 1. 获取 access_token
    const accessToken = await getAccessToken();
    console.log('Access Token:', accessToken);

    // 2. 获取文档内容
    const docToken = 'VHEzs12gih8ImYt5YiUjtRU9pEh'; // 替换为实际的 doc_token
    const documentData = await getDocumentContent(docToken, accessToken);
    console.log('文档内容:', documentData.data.valueRanges);

    // 3. 处理文档数据
    const headers = documentData.data.valueRanges[0].values[0];
    const rows = documentData.data.valueRanges[0].values.slice(1)
    const formattedJson = rows.map(row => {
      const rowData = {};
      headers.forEach((header, index) => {
        rowData[header] = row[index] || null; // 如果值为空，设置为 null
      });
      return {
        platform: 2, // 假设平台值为 2，根据实际情况修改
        module: 1,   // 假设模块值为 1，根据实际情况修改
        code: rowData.code, // 将 `key` 映射为 `code`
        sub_code: rowData.sub_code || null,  // 这里假设 `sub_code` 为空，如果有实际值可以修改
        zh_cn: rowData.zh_cn || null, // `中文` 列映射为 `zh_cn`
        en_us: rowData.en_us || null, // `英语` 列映射为 `en_us`
        ja_jp: rowData.ja_jp || null, // 如果没有日语值，则使用空字符串
        ko_kr: rowData.ko_kr || null, // 如果没有韩语值，则使用空字符串
        zh_hant: rowData.zh_hant || null, // 如果没有繁体中文值，则使用空字符串
        ar_ae: rowData.ar_ae || null, // 如果没有阿拉伯语值，则使用空字符串
        vi_vn: rowData.vi_vn || null // 如果没有越南语值，则使用空字符串
      }
    });
    console.log('处理后的数据:', JSON.stringify(formattedJson));

    // 4. 调用接口上传数据
    const uploadResponse = await axios.post('http://47.236.167.35:8081/backend/v1/i18n/info/add', {
      info_json: JSON.stringify(formattedJson),
      site: 1
    }, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 检查上传结果
    if (uploadResponse.status === 200) {
      console.log('数据上传成功:', uploadResponse.data);
    } else {
      console.error('数据上传失败:', uploadResponse.statusText);
    }
  } catch (error) {
    console.error('处理数据或上传数据时出错:', error);
  }
};

// 调用函数
processData();