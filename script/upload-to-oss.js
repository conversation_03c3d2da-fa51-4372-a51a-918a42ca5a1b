const OSS = require('ali-oss');
const fs = require('fs');
const path = require('path');
const ossConfig = require('../oss.config.js');

// 初始化 OSS 客户端
const client = new OSS({
  region: ossConfig.region,
  accessKeyId: ossConfig.accessKeyId,
  accessKeySecret: ossConfig.accessKeySecret,
  bucket: ossConfig.bucket,
});

// 上传目录下的所有文件
async function uploadDirectory(directoryPath, bucketPath) {
  const files = fs.readdirSync(directoryPath);
  let successCount = 0; // 成功上传的文件数
  let failureCount = 0; // 上传失败的文件数

  for (const file of files) {
    const filePath = path.join(directoryPath, file);
    const stats = fs.statSync(filePath);

    if (stats.isDirectory()) {
      // 如果是目录，递归上传
      const result = await uploadDirectory(filePath, `${bucketPath}/${file}`);
      successCount += result.successCount;
      failureCount += result.failureCount;
    } else if (stats.isFile()) {
      // 如果是文件，上传到 OSS
      const ossFilePath = `${bucketPath}/${file}`;
      try {
        const result = await client.put(ossFilePath, filePath);
        console.log(`Uploaded: ${filePath} -> ${ossFilePath}`);
        successCount += 1; // 成功上传，计数器加 1
      } catch (err) {
        console.error(`Failed to upload ${filePath}:`, err);
        failureCount += 1; // 上传失败，计数器加 1
      }
    }
  }

  return { successCount, failureCount }; // 返回成功和失败的文件数
}

// 上传静态资源
async function uploadStaticAssets() {
  const staticDir = path.resolve(__dirname, '../.output/public/ktx-web-ssr'); // Nuxt 3 静态资源目录
  const bucketPath = ossConfig.bucketPath; // OSS 存储路径

  if (!fs.existsSync(staticDir)) {
    console.error(`Static directory not found: ${staticDir}`);
    return;
  }

  console.log('Uploading static assets to OSS...');
  const { successCount, failureCount } = await uploadDirectory(staticDir, bucketPath);
  console.log('Upload completed.');
  console.log(`Successfully uploaded files: ${successCount}`);
  console.log(`Failed to upload files: ${failureCount}`);
}

// 执行上传
uploadStaticAssets();