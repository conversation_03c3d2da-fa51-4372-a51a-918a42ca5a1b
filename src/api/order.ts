import { get, post, orderPost, encrypt, Delete, orderDelete } from '~/utils/http'
export const openOrder = (params) => { // 下单
  return post('/api/v1/order', params)
}
export const orderDetail = (params) => { // 查询订单详情
  return get('/api/v1/order', params)
}
export const getCurrenOrderList = (params) => { // 当前委托列表
  return get('/api/v1/orders', params)
}
export const deleteDetail = (params) => { // 撤销订单
  return post('/api/v1/order/delete', params)
}
export const deleteOrders = (params) => { // 撤销全部订单
  return post('/api/v1/orders/delete', params)
}
export const getTradesList = (params) => { // 获取最新交易列表
  return get('/api/v1/trades', params)
}
export const getMyDealList = (params) => { // 获取个人成交记录
  return get('/api/v1/fills', params)
}
export const setCollateral = (params) => {
  return post('/api/v1/usersetting/collateral/set', params)
}
export const getFillsDetail = (params) => { // 获取指定成交记录详情
  return get('/api/v1/fills/detail', params)
}
export const getTicker = (params) => {
  return get('/api/v1/ticker', params)
}
export const getDepthList = (params) => {
  return get('/api/v1/order_book', params)
}
export const getApiCBUPositions = (params) => {
  return get('/api/v1/positions', params)
}
export const changePositionMarginApi = (params) => { // 调整保证金
  return post('/api/v1/positions/transfer/margin', params)
}
export const fastOrderOpen = (params) => { // 闪电下单
  return post ('/api/v1/cs/baseUQuickClose/order', params)
}
export const getKlinesApi = (params) => { // 获取K线数据
  return get('/api/v1/candles', params)
}
export const changePositionLevalageApi = (params) => { // 切换仓位杠杆
  return post('/api/v1/positions/change/leverage', params)
}