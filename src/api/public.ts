import { get, post, encrypt } from '~/utils/http'
export const getErrors = (params) => { // 公共 错误提示国际化
  return post('/api/v1/pu/defines/errors', params)
}
export const getPairAreas = (params) => { // 返回全部分区
  return post('/api/v1/pu/pairArea/getPairArea', params)
}
export const getPairsByArea = (params) => { // 返回交易区下的交易对信息
  return post('/api/v1/pu/pairArea/getPairsByArea', params)
}
export const getAllPairs = (params) => { // 返回全部交易对
  return post('/api/v1/pu/pairArea/getAllPairs', params)
}
export const getFavoriteList = (params) => { // 收藏列表
  return post('/api/v1/pu/favorite/listAll', params)
}
export const favoriteAddOrDel = (params) => { // 添加或者删除交易对
  return post('/api/v1/pu/favorite/addOrDel', params)
}
export const hotPairList = (params) => { // 热门搜索列表
  return post('/api/v1/pu/hotPair/list', params)
}
export const getPairSortList = (params) => { // 返回榜单列表
  return post('/api/v1/pu/top/pairs', params)
}
export const getUsefulLink = (params) => { // 活动 轮播图广告配置接口
  return post('/api/v1/pu/adv/list', params)
}
export const getPairHistory = (params) => { // 获取搜索历史记录
  return post('/api/v1/pu/search/pairHistory', params)
}
export const listMainCoins = (params) => { // 获取所有币种列表
  return post('/api/v1/pu/coin/listMainCoins', params)
}
export const getListByGeneralNameApi = (params) => { // 获取指定币种的转账网络或者memo的信息
  return post('/api/v1/pu/coin/listByGeneralName', params)
}
export const getPairList = (params) => { // 获取所有交易对列表
  return post('/api/v1/pu/pair/listAll', params)
}
export const getCoinParams = (params) => { // 获取币种信息
  return post('/api/v1/pu/coin/describe', params)
}
export const getPairSetting = (params) => { // 获取交易对详情
  return post('/api/v1/pu/pair/setting', params)
}
export const getCurrencyRateData = (params) => { // 获取所有汇率
  return post('/api/v1/pu/currency/rate', params)
}
export const getCurrencyDesc = (params) => { // 获取币种详情信息
  return post('/api/v1/pu/coin/pageDesc', params)
}
export const getNoteListApi = (params) => { // 获取消息列表
  return post('/api/v1/pu/notice/list', params)
}
export const getLandingPairsApi = (params) => { // 获取即将上的新币
  return post('/api/v1/pu/landingPairs', params)
}
export const getI18nInfoApi = (params) => { // 获取国际化列表
  return post('/api/v1/pu/i18n/getInfo', params)
}
export const getCollaterals = (params) => { // 获取资产抵扣列表
  return post('/api/v1/pu/coin/collaterals', params)
}
export const getAddDownLink = (params) => { // 获取app下载链接
  return post('/api/v1/pu/app/lastestVersion', params)
}