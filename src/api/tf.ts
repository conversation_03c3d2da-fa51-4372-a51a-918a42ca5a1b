import { get, post, encrypt } from '~/utils/http'
export const transfer = (params) => { // 资金划转
  return post('/api/v1/tf/transfer', params)
}
export const multiSideTransfer = (params) => {
  return post('/api/v1/tf/multiSide/transfer', params)
}
export const assetsByCoin = (params) => { // 资产查询
  return post('/api/v1/tf/accounts', params)
}
export const MainBillsChange = (params) => { // 钱包账户变动
  return post('/api/v1/tf/main/bills', params)
}
export const tarderBillsChange = (params) => { // 交易账户变动
  return get('/api/v1/ledger', params)
}
export const assetByCoinList  = (params) => { // 钱包交易账户币种列表
  return post('/api/v1/tf/assets', params)
}
export const getWithdrawAddrApi = (params) => { // 获取提现列表
  return post('/api/v1/tf/tx/getWithdrawAddr', params)
}
export const delWhiteItemApi = (params) => { // 删除提现地址
  return post('/api/v1/tf/tx/delWithdrawAddr', params)
}
export const transferWhiteItemApi = (params) => { // 白名单和普通地址转换
  return post('/api/v1/tf/tx/transferWithdrawAddr', params)
}
export const getDepositAddressApi = (params) => { // 获取充值地址
  return post('/api/v1/tf/tx/getAddr', params)
}
export const editAddressApi = (params) => { // 编辑地址备注
  return post('/api/v1/tf/tx/editAddr', params)
}
export const getDepositListApi = (params) => { // 获取充值地址列表
  return post('/api/v1/tf/tx/getAddrs', params)
}
export const transferTransferInList = (params) => { // 充值记录列表
  return post('/api/v1/tf/tx/depositList', params)
}
export const transferTransferOutList = (params) => { // 提现记录列表
  return post('/api/v1/tf/tx/withdrawList', params)
}
export const getWithdrawAclStatus = (params) => { // 获取白名单开启还是关闭
  return post('/api/v1/tf/tx/withdrawAclStatus', params)
}
export const changeWithdrawAclStatus = (params) => { // 切换白名单开启还是关闭状态
  return post('/api/v1/tf/tx/withdrawAclChangeStatus', params)
}
export const getwithdrawLimitApi = (params) => { // 获取指定币种的限额
  return post('/api/v1/tf/tx/withdrawLimit', params)
}
export const addWithdrawAddrApi = (params) => { // 新增提现白名单
  return post('/api/v1/tf/tx/addWithdrawAddr', params)
}
export const transferTransferOutApi = (params) => { // 提现操作
  return post('/api/v1/tf/tx/withdraw', params)
}
export const transferSonIn = (params) => { // 子账户 从主账户转入子账户资产
  return post('/api/v1/tf/son/in', params)
}
export const transferSonOut = (params) => { // 子账户 从子账户转入主账户资产
  return post('/api/v1/tf/son/out', params)
}
export const getSonAssetsAll = (params) => { // 子账户 通过UID查询子账户资产
  return post('/api/v1/tf/son/getAccounts', params)
}
export const lastDepositNotify = (params) => { // 返回半小时内的充值简单信息List
  return post('/api/v1/tf/tx/lastDepositNotify', params)
}
export const addrAclChangeStatusApi = (params) => { // 提现地址修改免验证
  return post('/api/v1/tf/tx/addrAclChangeStatus', params)
}
export const getAddrAclInfoApi = (params) => { // 获取地址是否免验证
  return post('/api/v1/tf/tx/addrAclStatus', params)
}
export const getTreeNewApi = (params) => { // 获取账单树状列表
  return post('/api/v1/tf/bills/getTree', params)
}
export const getBillsDetailsApi = (params) => { // 获取账单列表
  return post('/api/v1/tf/bills/getDetails', params)
}
export  const getNftListApi = (params) => { // 获取nft币种list
  return post('/api/v1/tf/tx/nftList ', params)
}
export  const getNftAddrApi = (params) => { // 获取nft充值地址
  return post('/api/v1/tf/tx/getNftAddr', params)
}
export const getNftAssets = (params) => { // 获取nft资产
  return post('/api/v1/tf/nft/assets', params)
}
export const getNftDepositList = (params) => { // 获取nft充值列表
  return post('/api/v1/tf/nft/depositList', params)
}
export const getNftWithdrawList = (params) => { // 获取nft提现列表
  return post('/api/v1/tf/nft/withdrawList', params)
}