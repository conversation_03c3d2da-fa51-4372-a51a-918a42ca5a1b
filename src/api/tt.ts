import { get, post, encrypt } from '~/utils/http'
export const getVipDetail = (params) => {
  return post('/api/v1/tt/vip/info', params)
}
export const getMyInvAPI = (params) => { // 邀请返佣 个人邀请总揽
  return post('/api/v1/tt/inv/total', params)
}
export const getInvitattionLinkListAPI = (params) => { // 邀请返佣 获取邀请链接列表
  return post('/api/v1/tt/inv/list', params)
}
export const sendLinkAPI = (params) => { // 邀请返佣 发送用户专属链接邀请
  return post('/api/v1/tt/inv/send', params)
}
export const editInvitationLinkAPI = (params) => { // 邀请返佣 编辑
  return post('/api/v1/tt/inv/editUrl', params)
}
export const getRateAPI = (params) => { // 邀请返佣 获取用户返佣比例
  return post('/api/v1/tt/inv/rate', params)
}
export const getinvitationLinkHis = (params) => { // 邀请返佣 返佣列表
  return post('/api/v1/tt/inv/his', params)
}
export const genInvitationLink = (params) => { // 邀请返佣 新增邀请链接
  return post('/api/v1/tt/inv/genUrl', params)
}
export const applyBackTwo = (params) => { // 邀请返佣 申请二级返佣
  return post('/api/v1/tt/inv/apply/back/two', params)
}
export const packetOutQueryAPI = (params) => { // 红包转账通过key获取
  return post('/api/v1/tt/redPacket/packetOutQuery', params)
}
export const joinPacketAPI = (params) => { // 抢红包
  return post('/api/v1/tt/redPacket/joinPacket', params)
}
export const getPacketDetailAPI = (params) => { // 红包详情
  return post('/api/v1/tt/redPacket/detail', params)
}
export const cashPacketAPI = (parmas) => { // 注册后发红包到账户
  return post('/api/v1/tt/redPacket/joinByNew', parmas)
}
export const getLpSummaryAPI = (params) => { // 挖矿总揽
  return post('/api/v1/tt/lp/summary', params)
}
export const getLpSubInfoAPI = (params) => { // 挖矿订阅通知
  return post('/api/v1/tt/lp/sub', params)
}
export const getLpListAPI = (params) => { // 挖矿列表
  return post('/api/v1/tt/lp/list', params)
}
export const getLpDetailAPI = (params) => { // 挖矿详情
  return post('/api/v1/tt/lp/detail', params)
}
export const getLpPrizeListAPI = (params) => { // 挖矿-用户奖励列表
  return post('/api/v1/tt/lp/prizeList', params)
}
export const getLpLockListAPI = (params) => { // 挖矿-用户锁仓列表
  return post('/api/v1/tt/lp/lockList', params)
}
export const getLpNftListAPI = (params) => { // 挖矿-用户可用的锁仓的nft列表
  return post('/api/v1/tt/lp/nftList', params)
}
export const getLpPrizeGeAPI = (params) => { // 挖矿-主动领取奖励
  return post('/api/v1/tt/lp/prizeGet', params)
}
export const getLpTokenLockAPI = (params) => { // 挖矿-普通币种锁仓
  return post('/api/v1/tt/lp/tokenLock', params)
}
export const getLpNftLockAPI = (params) => { // 挖矿-nft锁仓
  return post('/api/v1/tt/lp/nftLock', params)
}
export const officialVerification = (params) => { // 域名验证
  return post('/api/v1/tt/official/check', params)
}
export const officialReport = (params) => { // 举报功能
  return post('/api/v1/tt/official/report', params)
}
export const businessCoinApply = (params) => { // 申请上币
  return post('/api/v1/tt/business/coin/apply', params)
}
export const businessNodeApply = (params) => { // 节点申请
  return post('/api/v1/tt/business/node/apply', params)
}
export const businessCoinCheck = (params) => { // 校验币种是否存在
  return post('/api/v1/tt/business/coin/check', params)
}
export const getVoucherList = (params) => { // 获取卡劵列表
  return post('/api/v1/tt/voucher/list', params)
}
export const getVoucherPrize  = (params) => {
  return post('/api/v1/tt/voucher/prize', params)
}
export const useLossOrderApi  = (params) => {
  return post('/api/v1/tt/voucher/lossOrder/use', params)
}

