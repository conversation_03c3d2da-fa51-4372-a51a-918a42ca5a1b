.flex-box {
  display: flex;
  align-items: center;

  &.inline {
    display: inline-flex;
  }
  
  &.space-center {
    justify-content: center;
  }

  &.space-start {
    justify-content: flex-start;
  }

  &.space-end {
    justify-content: flex-end;
  }

  &.space-between {
    justify-content: space-between;
  }

  &.space-around {
    justify-content: space-around;
  }

  &.align-start {
    align-items: flex-start;
  }

  &.align-end {
    align-items: flex-end;
  }

  &.align-stretch {
    align-items: stretch;
  }

  &.align-baseline {
    align-items: baseline;
  }

  &.align-center {
    align-items: center;
  }

  &.flex-column {
    flex-direction: column;
  }

  &.flex-wrap {
    flex-wrap: wrap;
  }

  &.row-reverse {
    flex-direction: row-reverse;
  }

  @for $i from 1 through 9 {
    .flex-#{$i} {
      flex: #{$i};
    }
  }
}
.text-right{
  text-align:right;
}
.text-left{
  text-align:left;
}
.text-center{
  text-align:center;
}
.cursor-pointor {
  cursor: pointer;
}


.font-size-12 {
  font-size: 12px;
  line-height: 20px;
}

.font-size-14 {
  font-size: 14px;
  line-height: 24px;
}

.font-size-16 {
  font-size: 16px;
  line-height: 28px;
}

.font-size-18 {
  font-size: 18px;
  line-height: 32px;
}

.font-size-20 {
  font-size: 20px;
  line-height: 32px;
}

.font-size-24 {
  font-size: 24px;
  line-height: 36px;
}

.font-size-32 {
  font-size: 32px;
  line-height: 48px;
  @include md {
    font-size: 24px;
    line-height: 36px;
  }
}
.font-size-40 {
  font-size: 40px;
  line-height: 56px;
}

.font-size-48 {
  font-size: 48px;
  line-height: 64px;
}

.font-weight-400 {
  font-weight: 400;
}

.font-weight-500 {
  font-weight: 500;
}

.font-weight-600 {
  font-weight: 600;
}

.full {
  width: 100%;
}


@for $i from 0 through 30 {
  .font-#{$i} {
    font-size: #{$i}px !important;
  }

  .mg-t#{$i * 4} {
    margin-top: #{$i * 4}px !important;
  }

  .gap-#{$i * 4} {
    gap: #{$i * 4}px !important;
  }

  .mg-b#{$i * 4} {
    margin-bottom: #{$i * 4}px !important;
  }

  .mg-l#{$i * 4} {
    margin-left: #{$i * 4}px !important;
  }

  .mg-r#{$i * 4} {
    margin-right: #{$i * 4}px !important;
  }

  .pd-t#{$i * 4} {
    padding-top: #{$i * 4}px !important;
  }

  .pd-b#{$i * 4} {
    padding-bottom: #{$i * 4}px !important;
  }

  .pd-l#{$i * 4} {
    padding-left: #{$i * 4}px !important;
  }

  .pd-r#{$i * 4} {
    padding-right: #{$i * 4}px !important;
  }

  .mg-tb#{$i * 4} {
    margin-top: #{$i * 4}px !important;
    margin-bottom: #{$i * 4}px !important;
  }

  .mg-lr#{$i * 4} {
    margin-left: #{$i * 4}px !important;
    margin-right: #{$i * 4}px !important;
  }

  .pd-tb#{$i * 4} {
    padding-top: #{$i * 4}px !important;
    padding-bottom: #{$i * 4}px !important;
  }

  .pd-lr#{$i * 4} {
    padding-left: #{$i * 4}px !important;
    padding-right: #{$i * 4}px !important;
  }
}

.tc-btn-violet {
  cursor: pointer;
  color: var(--btn-violet);
  @include pc-hover {
    &:hover {
      color: var(--btn-violet-hover);
    }
  }

  &.disabled {
    cursor: default;
    color: var(--tc-quaternary);
  }
}

.bge-alert {
  background: var(--bg-red-08);
  color: var(--tc-red);
  gap: 8px;
  width: 100%;
  padding: 16px;
  font-size: 14px;
  line-height: 24px;
  font-weight: 400;
  display: flex;
  justify-content: flex-start;
  align-items: start;

  .bge-alert-icon {
    width: 24px;
    height: 24px;
    display: inline-block;
  }

  svg {
    font-size: 24px;
  }
}
.cursor-pointer{
  cursor: pointer;
}
.cursor-default{
  cursor: default;
}
.black-text-hover{
  cursor: pointer;
  &.active{
    color:#f0b90b!important;
  }
  &:hover{
    @include color(theme);
    i{
      @include color(theme);
    }
  }
}
