.exchange-container{
  padding:15px;
  .pc-item{
    display: flex;
    align-items: center;
  }
  .mobile-item{
    display:none;
  }
  .exchange-wrapper{
    overflow:hidden;
    box-sizing: border-box;
    margin: 0;
    min-width: 0;
    display: grid;
    grid-gap: 1px;
    grid-template-columns: 0fr minmax(700px,2fr) minmax(223px,350px) minmax(223px,350px) 0fr;
    grid-template-rows: minmax(0px,auto) 72px minmax(200px,640px) 386px;
    grid-template-areas:
      "header header header header header"
      "left subHeader subHeader subHeader right"
      "left chart trades orderform right"
      "left orders orders orders right";
    /* sub-header */
    @include bg-color(border);
    &.future{
      grid-template-rows: minmax(0px,auto) 72px minmax(200px,738px) 386px;
    }
    .cont-wrap-bg{
      overflow:hidden;
      &.left{
        grid-area: left;
      }
      &.right{
        grid-area: right;
      }
      &.subHeader{
        grid-area: subHeader;
      }
      &.trades{
        grid-area: trades;
        overflow:visible;
      }
      &.chart{
        grid-area: chart;
      }
      &.orderform{
        grid-area: orderform;
      }
      &.orders{
        grid-area: orders;
      }
      @include bg-color(bg-primary);
    }
    .sub-header{
      height:72px;
      margin:0 20px;
      overflow:hidden;
      .header-left{
        position: relative;
        &:after{
          content:'';
          position: absolute;
          right:0;
          top:50%;
          margin-top:-8px;
          width:1px;
          height:16px;
          @include bg-color(border);
        }
        .icon-box{
          width:32px;
          height:auto;
          font-size:32px;
        }
        .symbol-text{
          margin-left:12px;
          .symbol-title{
            h2{
              font-size:20px;
              font-weight:700;
              margin-right:12px;
              @include color(tc-primary);
            }
            .arrow-down-box, .collect-box{
              width:20px;
              height:20px;
              border-radius:50%;
              cursor: pointer;
              margin-right:12px;
              @include bg-color(bg-quaternary);
              @include pc-hover {
                &:hover{
                  svg{
                    @include color(theme);
                  }
                }
              }
              svg{
                font-size:12px !important;
              }
            }
          }
          p{
            font-size:14px;
            @include color(tc-secondary);
          }
        }
      }
      .arrow-down-box, .collect-box{
        width:20px;
        height:20px;
        line-height:20px;
        border-radius:50%;
        cursor: pointer;
        margin-right:12px;
        text-align:center;
        @include bg-color(bg-quaternary);
        @include pc-hover {
          &:hover{
            svg{
              @include color(theme);
            }
          }
        }
        svg{
          font-size:12px !important;
        }
      }
      .def-li{
        margin-right:32px;
        padding-left:12px;
        span, em{
          display:block;
          font-style: inherit;
          white-space: nowrap;
        }
        span{
          font-weight:700;
          font-size:18px;
          @include color(tc-primary);
          &.fit-rise{
            @include color(rise);
          }
          &.fit-fall{
            @include color(fall);
          }
        }
        em{
          font-size:14px;
          @include color(tc-primary);
          &.fit-rise{
            @include color(rise);
          }
          &.fit-fall{
            @include color(fall);
          }
        }
      }
      .scroll-item{
        padding-right:32px;
        &:last-child{
          padding-right:0;
        }
        span,em{
          font-size:14px;
          font-style: inherit;
          display:block;
        }
        span{
          @include color(tc-secondary);
        }
        em{
          @include color(tc-primary);
          &.fit-rise{
            @include color(rise);
          }
          &.fit-fall{
            @include color(fall);
          }
        }
      }
    }
    /* sub-header */
    // trades
    .trades{
      .trade-nav{
        height:46px;
        padding:0 16px;
        border-bottom:1px solid;
        @include border-color(border);
        white-space:nowrap;
        li{
          font-size:14px;
          margin-right:20px;
          height:46px;
          line-height:46px;
          cursor: pointer;
          @include color(tc-secondary);
          &:last-child{
            margin-right:0;
          }
          &.active{
            position: relative;
            @include color(tc-primary);
            &:after{
              content: '';
              position: absolute;
              bottom:0;
              width:16px;
              height:2px;
              left:50%;
              margin-left:-8px;
              @include bg-color(theme);
            }
          }
        }
      }
      .tarde-cont{
        height: calc(100% - 46px);
      }
    }
    // trades
  }
}
@include md {
  .exchange-container{
    padding:10px;
    .pc-item{
      display: flex;
      align-items: center;
    }
    .mobile-item{
      display:none;
    }
    .exchange-wrapper{
      grid-template-columns: 0fr 2fr 1fr 0fr;
      grid-template-rows: minmax(0px,auto) 72px minmax(250px,600px) minmax(200px,480px) 320px;
      grid-template-areas:
        "header header header header"
        "left subHeader subHeader right"
        "left chart orderform right"
        "left trades trades right"
        "left orders orders right";
      // trades
      &.future{
        grid-template-rows: minmax(0px,auto) 72px minmax(200px,738px) 420px;
      }
    .trades{
      .trade-nav{
        padding:0;
        border-bottom:1px solid;
        display: flex;
        flex: 1 1;
        @include border-color(border);
        li{
          margin-right:0;
          cursor: default;
          padding:0 16px;
          flex:1;
          border-left:1px solid;
          @include border-color(border);
          @include color(tc-primary);
          &.active{
            position: relative;
            @include color(tc-primary);
            &:after{
              display:none;
            }
          }
          &:last-child{
            // border-left:0;
          }
        }
      }
      .tarde-cont{
        display:flex;
        display:-webkit-flex;
        display:-moz-flex;
      }
      .exchnage-depth-wrap, .exchnage-deals-wrap{
        flex:1;
        border-left:1px solid;
        @include border-color(border);
      }
    }
    // trades
    }
  }
}
@include mb {
  .exchange-container{
    padding:0;
    .pc-item{
      display:none;
    }
    .mobile-item{
      display:flex;
    }
    .exchange-wrapper{
      box-sizing: border-box;
      margin: 0px;
      min-width: 0px;
      display: grid;
      width: 100vw;
      height: auto;
      min-height: 930px;
      grid-gap: 0px;
      grid-template-columns: 100vw;
      grid-template-rows: 0px minmax(150px, auto) minmax(366px, auto) minmax(450px, auto) 64px 0px;
      grid-template-areas:
        "header"
        "subHeader"
        "chart"
        "orders"
        "orderform";
      /* sub-header */
      &.future{
        grid-template-rows: 0px minmax(150px, auto) minmax(366px, auto) minmax(450px, auto) 64px 0px;
      }
      .cont-wrap-bg{
        &.orderform {
          box-sizing: border-box;
          margin: 0px;
          min-width: 0px;
          width: 100%;
          overflow-y: initial;
          overflow-x: visible;
          position: fixed;
          z-index: 2000;
          bottom: 0;
          height: 64px;
          border-top:1px solid;
          grid-area: orderform / orderform / orderform / orderform;
          @include border-color(border);
          @include bg-color(bg-primary);
        }
        &.trades{
          display:none;
        }
      }
      .sub-header{
        height:auto;
        margin:0 16px;
        &.flex-box{
          flex-direction: column;
        }
        .header-left{
          width: 100%;
          height:44px;
          border-bottom:1px solid;
          @include border-color(border);
          &.flex-box{
            justify-content: space-between;
          }
          &:after{
            display:none;
          }
          .icon-box{
            width:24px;
            height:auto;
            font-size:24px;
          }
          .symbol-text{
            margin-left:12px;
            .symbol-title{
              h2{
                font-size:18px;
              }
              .collect-box{
                display:none;
              }
              .arrow-down-box, .collect-box{
                width:auto;
                height:auto;
                border-radius:0;
                cursor: pointer;
                margin-right:0;
                @include bg-color(bg-primary);
                svg{
                  font-size:12px !important;
                }
              }
            }
            p{
              display:none;
            }
          }
        }
        .collect-box{
          display:none;
          &.mobile-item{
            display:block;
            @include bg-color(bg-primary);
            margin-right:0;
            svg{
              font-size:18px !important;
            }
          }
        }
        .def-li{
          margin-right:0;
          text-align:right;
          padding-left:0;
        }
        .header-right{
          padding-left:0;
          padding-top:12px;
          width:100%;
          &.flex-box{
            &.moblie-item{
              display:block;
            }
          }
          .scroll-item{
            width:50%;
            float:left;
            margin-bottom:8px;
            padding-right:0;
            &.mg-r24{
              margin-right:0 !important;
            }
            span,em{
              font-size:12px;
              font-style: inherit;
              display:block;
            }
            span{
              @include color(tc-secondary);
            }
            em{
              @include color(tc-primary);
            }
          }
        }
      }
    }
  }
}