.dialog-container-m{
  box-sizing: border-box;
  margin: 0px;
  min-width: 0px;
  position: fixed;
  inset: 0px;
  z-index: 9999;
  display:flex;
  align-items: flex-end;
  background-color: rgba(0, 0, 0, 0.5) !important;
  .dialog-wrapper-m{
    box-sizing: border-box;
    margin: 0px;
    min-width: 0px;
    overflow: auto;
    width: 100%;
    transition: all 0.5s ease 0s;
    max-height: 80%;
    border-radius:10px 10px 0px 0px;
    &.heiFixed{
      height:80%;
    }
    [class="light"] & {
      background:#fff;
    }
    [class="dark"] & {
      background:#181A1F;
    }
    .wrapper-pd-box{
      display:flex;
      flex-direction: column;
      padding:12px 20px;
      height:100%;
      .dialog-title-m{
        height:46px;
        font-size:18px;
        @include color(tc-primary);
        svg{
          @include color(tc-primary);
          cursor:pointer;
        }
      }
      .dialog-body-m{
        height:calc(100% - 40px);
        overflow-y: auto;
        .list-box{
          margin-top:12px;
          height: calc(100% - 44px);
          overflow-y: auto;
          ul{
            li{
              height:34px;
              line-height:34px;
              font-size:14px;
              margin-bottom:4px;
              @include color(tc-primary);
              position: relative;
              svg{
                display:none;
              }
              &.active{
                @include color(theme);
                svg{
                  display:block;
                  position: absolute;
                  right:0;
                  top:50%;
                  margin-top:-10px;
                }
              }
            }
          }
        }
        .label-item-cont{
          padding-bottom:16px;
          .label-title{
            font-size:14px;
            padding-bottom:4px;
            @include color(tc-primary);
          }
          .label-cont-box{
            .label-select-box{
              flex-wrap:wrap;
              li{
                font-size:14px;
                width:calc(33.33% - 8px);
                height:34px;
                line-height:34px;
                border:1px solid;
                text-align: center;
                margin-right:10px;
                border-radius:4px;
                margin-top:10px;
                cursor:pointer;
                @include border-color(border);
                @include color(tc-primary);
                &:nth-child(3n){
                  margin-right:0;
                }
                &.active{
                  @include border-color(theme);
                }
              }
            }
            .el-input{
              height:36px;
            }
          }
        }
        .date-tab-box{
          height:44px;
          font-size:14px;
          .date-tab-left{
            @include color(tc-primary);
          }
          .date-tab-right{
            @include color(tc-secondary);
            svg{
              &.selectshow{
                transform: rotate(90deg);
              }
            }
          }
        }
      }
      .dialog-footer-m{
        height:64px;
        .el-button{
          width:100%;
          height:40px;
          &.reset-btn{
            border:1px solid;
            @include border-color(theme);
            @include color(theme);
            @include pc-hover {
              &:hover{
                @include bg-color(bg-primary);
              }
            }
          }
        }
      }
    }
  }
}