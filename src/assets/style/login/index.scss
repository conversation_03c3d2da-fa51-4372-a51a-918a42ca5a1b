.login-register-container{
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  &.dialog-cont{
    min-height:auto;
  }
  .login-register-wrapper{
    padding-top:20px;
    .login-register-box{
      border:1px solid;
      border-radius:20px;
      width:460px;
      margin:0 auto;
      @include border-color(border);
      .login-register-box-wrap{
        padding:40px;
        .login-logo{
          width:80px;
          height:24px;
          margin-bottom:32px;
          img{
              width:100%;
              height:auto;
          }
        }
        .login-mobile-header{
          display:none;
        }
        .login-register-title{
          font-size:28px;
          padding-bottom:32px;
          @include color(tc-primary);
        }
        .login-nav-type{
          padding-bottom:20px;
          ul{
            li{
              line-height:18px;
              font-size:14px;
              font-weight: 500;
              cursor: pointer;
              margin-right:16px;
              position: relative;
              @include color(tc-secondary);
              &.active{
                @include color(tc-primary);
                &:after{
                  position: absolute;
                  left:50%;
                  margin-left: -7px;
                  bottom:-8px;
                  content: '';
                  display:block;
                  width:14px;
                  height:2px;
                  @include bg-color(theme);
                }
              }
            }
          }
        }
        .ewm-cont-box{
          height:278px;
          .ewm-cont{
            border-radius:12px;
            padding:0;
            border:1px solid;
            @include border-color(border);
            position: relative;
            height:178px;
            width:178px;
            overflow: hidden;
            .qrcode-expired{
              position: absolute;
              top:0;
              left:0;
              right:0;
              bottom:0;
              background-color: rgba(0,0,0,0.5);
              .qrcode-expired-text{
                font-size:14px;
                color:#fff;
              }
            }
          }
        }
        .el-form {
          &.login-register-form{
            .el-form-item{
              &.first-label-box{
                .el-form-item__label{
                  display:none;
                }
              }
            }
            .normal-btn{
              width:100%;
              height:40px;
              margin-top:32px;
            }
            .checkBox-xy{
              // display:block;
              em{
                font-style: normal;
              }
              a{
                @include color(theme);
              }
            }
            .forget-pwd{
              font-size:14px;
              a{
                @include color(theme);
              }
            }
            .yq-cont{
              padding-bottom:20px;
              .yq-label {
                cursor: pointer;
                font-size:14px;
                padding-bottom:8px;
                @include color(tc-primary);
                em{
                  font-style: inherit;
                  margin-right:8px;
                }
                svg{
                  transform: rotate(0deg);
                  -webkit-transform: rotate(0deg);
                  -moz-transform: rotate(0deg);
                  transition: 0.2s transform ease-in-out;
                  -webkit-transition: 0.2s transform ease-in-out;
                  -moz-transition: 0.2s transform ease-in-out;
                  &.up {
                    transform: rotate(180deg);
                    -webkit-transform: rotate(180deg);
                    -moz-transform: rotate(180deg);
                  }
                }
              }
            }
          }
        }
        .or-line-box{
          margin:16px 0;
          .line-box{
            width:100%;
            height:1px;
            @include bg-color(border);
          }
        }
        .login-more-btn{
          cursor: pointer;
          padding:0 16px;
          height:44px;
          border:1px solid;
          font-size:14px;
          border-radius:6px;
          @include color(tc-primary);
          @include border-color(border);
          &:hover{
            @include color(theme);
          }
          .google-more-icon{
            width:20px;
            height:20px;
            background:url('@/assets/images/login/google-more-icon.png') no-repeat center;
            background-size:100% auto;
          }
          .apple-more-icon{
            width:20px;
            height:20px;
            @include get-img('~/assets/images/download/ios-black-icon.png', '~/assets/images/download/ios-white-icon.png');
            background-size:100% auto;
          }
        }
      }
    }
    .login-register-text{
      margin:0 auto;
      width:460px;
      text-align: center;
      padding-top:20px;
      font-size:14px;
      padding-bottom:24px;
      a{
        @include color(theme);
      }
    }
  }
  }
  @include mb {
    .login-register-container{
      display:block;
      .login-register-wrapper{
        padding-top:0px;
        .login-register-box{
          border:0;
          border-radius:0;
          width:100%;
          margin:0 auto;
          .login-register-box-wrap{
            padding:0 20px;
            .login-logo{
              width:80px;
              height:24px;
              margin-top:20px;
              margin-bottom:38px;
            }
            .login-mobile-header{
              display:block;
            }
            .login-register-title{
              font-size:28px;
              padding-bottom:40px;
            }
            .login-nav-type{
              display:none;
            }
            .el-form {
              &.login-register-form{
                .el-form-item{
                  &.first-label-box{
                    .el-form-item__label{
                      display:block;
                    }
                  }
                }
              }
            }
          }
        }
        .login-register-text{
          width:100%;
        }
      }
    }
  }