
@import './_var.scss';
// 公用变量
@mixin pc {
  @media only screen and (min-width: 1064px)
  {
    @content;
  }
}
@mixin pc-hover {
  @media (any-hover: hover) {
    @content;
  }
}
@mixin md1280 {
  @media only screen and (min-width: 1164px) and (max-width: 1280px)
  {
    @content;
  }
}
@mixin md1164 {
  @media only screen and (min-width: 1064px) and (max-width: 1164px)
  {
    @content;
  }
}
@mixin md1423 {
  @media only screen and (min-width: 1280px) and (max-width: 1423px)
  {
    @content;
  }
}
@mixin md1920 {
  @media only screen and (min-width: 1423px) and (max-width: 1920px)
  {
    @content;
  }
}
@mixin md {
  @media only screen and (min-width: 768px) and (max-width: 1064px)
  {
    @content;
  }
}

@mixin mb {
  @media only screen and (max-width: 768px)
  {
    @content;
  }
}
@mixin color($key: theme, $o: 1, $theme: "") {
  @if $theme== "" {
    @each $themeKey in $theme-key {
      [class^="#{$themeKey}"] & {
        $list: getList($color-obj, $themeKey);
        color: getColor($key, $list, $o);
      }
    }
  } @else {
    $list: getList($color-obj, $theme);
    color: getColor($key, $list, $o) !important;
  }
}
// 背景颜色
@mixin bg-color($key: theme, $o: 1, $theme: "") {
  @if $theme== "" {
    @each $themeKey in $theme-key {
      [class^="#{$themeKey}"] & {
        $list: getList($color-obj, $themeKey);
        background-color: getColor($key, $list, $o) !important;
      }
    }
  } @else {
    $list: getList($color-obj, $theme);
    background-color: getColor($key, $list, $o) !important;
  }
}
// 边框颜色
@mixin border-color($key: theme, $o: 1, $theme: "") {
  @if $theme== "" {
    @each $themeKey in $theme-key {
      [class^="#{$themeKey}"] & {
        $list: getList($color-obj, $themeKey);
        border-color: getColor($key, $list, $o) !important;
      }
    }
  } @else {
    $list: getList($color-obj, $theme);
    border-color: getColor($key, $list, $o) !important;
  }
}
// 背景图
@mixin get-img($light-url, $dark-url) {
  [class^="light"] & {
    background-image: url(#{$light-url});
  }
  [class^="dark"] & {
    background-image: url(#{$dark-url});
  }
}
// 全局主题函数
@function getColor($key: theme, $list: $color-list, $o: 1) {
  @return rgba($color: map-get($list, $key), $alpha: $o);
}

@function getList($obj, $key) {
  @return map-get($map: $obj, $key: $key);
}

@function getSingleColor($key: primary, $o: 1, $theme: dark) {
  $list: getList($color-obj, $theme);
  @return rgba($color: map-get($list, $key), $alpha: $o);
}

@function getThemeColor($key: primary, $o: 1) {
  @if class==light {
    $list: getList($color-obj, light);
    @return rgba($color: map-get($list, $key), $alpha: $o);
  }

  @if class==light-cvd {
    $list: getList($color-obj, light);
    @return rgba($color: map-get($list, $key), $alpha: $o);
  }

  @if class==dark {
    $list: getList($color-obj, dark);
    @return rgba($color: map-get($list, $key), $alpha: $o);
  }

  @if class==dark-cvd {
    $list: getList($color-obj, dark);
    @return rgba($color: map-get($list, $key), $alpha: $o);
  }
};
$list: getList($color-obj, light);
@each $key, $value in $list {
  .fit-#{"" + $key}_bg {
    @include bg-color($key);
  }
  .fit-#{"" + $key} {
    @include color($key);
  }
  .fit-#{"" + $key}__dark {
    @include color($key, 1, dark);
  }
  .fit-#{"" + $key}_border {
    @include border-color($key);
  }
};
