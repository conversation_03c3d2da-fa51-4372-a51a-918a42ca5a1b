.account-container{
  width:100%;
  .account-wrapper{
    padding:0 20px 20px;
    .account-nav-box{
      padding-bottom:20px;
    }
    .account-title{
      padding-bottom:20px;
      h2{
        font-size:20px;
        @include color(tc-primary);
      }
      p{
        font-size:14px;
        padding-top:8px;
        @include color(tc-secondary);
      }
      .el-button{
        padding:10px 65px;
      }
    }
    .account-body-container{
      .account-body-wrapper{
        .security-list{
          border:1px solid;
          border-radius:12px;
          @include border-color(border);
          li{
            padding:20px 24px;
            .item-left{
              h3{
                font-size:18px;
                @include color(tc-primary);
              }
              p{
                padding-top:4px;
                font-size:14px;
                @include color(tc-secondary);
                &.warn{
                  display:none;
                }
              }
            }
            .item-right{
              p{
                font-size:14px;
                padding-right:20px;
                @include color(tc-primary);
                &.warn{
                  @include color(warn);
                }
              }
              .el-button{

              }
            }
          }
        }
        .google-install-cont{
          .install-box{
            border-radius:12px;
            border:1px solid;
            width:383px;
            height:324px;
            padding:0 20px;
            float: left;
            margin-right:40px;
            text-align:center;
            display:flex;
            flex-direction: column;
            @include border-color(border);
            &:last-child{
              margin-right:0;
            }
            h3{
              font-size:18px;
              padding:20px 0;
              @include color(tc-primary);
            }
            .ewm-box{
              width:200px;
              width:200px;
              margin:0 auto;
              padding:20px;
              border-radius:12px;
              border:1px solid;
              @include border-color(border);
            }
            .install-btn-box{
              a{
                margin-bottom:16px;
              }
              dl{
                padding:16px;
                border-radius:12px;
                cursor: pointer;
                @include bg-color(bg-tertiary);
                &:last-child{
                  margin-bottom:0;
                }
                dt{
                  width:60px;
                  height:60px;
                  img{
                    display: block;
                    width:100%;
                    height:auto;
                  }
                }
                dd{
                  margin-left:16px;
                  font-size:18px;
                  @include color(tc-primary);
                  .icon-bg{
                    width:32px;
                    height:32px;
                    padding:8px;
                    border-radius:6px;
                    background-color: rgba(79, 99, 121, 0.04);
                    svg{
                      display:block;
                      font-size:16px !important;
                      width:16px;
                      height:16px;
                    }
                  }
                }
              }
            }
            p{
              font-size:12px;
              padding-top:18px;
              @include color(tc-secondary);
            }
          }
        }
        .google-install-btn{
          width:380px;
          padding-top:20px;
          clear:both;
          .el-button{
            width:100%;
            height:40px;
          }
        }
        .google-setting-cont{
          width:100%;
          border:1px solid;
          border-radius:12px;
          @include border-color(border);
          .google-setting-wrap{
            padding:20px;
            .setting-title{
              font-size:14px;
              @include color(tc-secondary);
            }
            .setting-cont-box{
              padding-bottom:24px;
              dl{
                dt,dd{
                  h3{
                    padding:20px 0;
                    font-size:18px;
                    @include color(tc-primary);
                  }
                  .ewm-cont{
                    width:200px;
                    height:200px;
                    padding:20px;
                    border:1px solid;
                    border-radius:12px;
                    @include border-color(border);
                  }
                  .code-text{
                    padding:20px 16px;
                    border-radius:12px;
                    @include color(tc-primary);
                    @include bg-color(bg-quaternary);
                    svg{
                      font-size:16px !important;
                      margin-left:8px;
                      cursor: pointer;
                    }
                  }
                }
              }
            }
            .google-code-box{
              padding-top:20px;
              width:426px;
              .goInstall-text{
                display:none !important;
              }
            }
          }
        }
        .subAccount-cont{
          border:1px solid;
          border-radius:12px;
          @include border-color(border);
          .subAccount-wrap{
            .subAccount-title{
              padding:24px;
              font-size:14px;
              border-bottom:1px solid;
              @include color(tc-secondary);
              @include border-color(border);
            }
          }
        }
      }
    }
  }
}
@include mb {
  .account-container{
    width:100%;
    .account-wrapper{
      padding:0 20px 20px;
      .account-nav-box{
        position: absolute;
        top:106px;
        left:20px;
        right:20px;
        padding-bottom:0;
        height:48px;
        line-height:48px;
        display:flex;
        border-bottom:1px solid;
        @include bg-color(bg-primary);
        @include border-color(border);
        .el-breadcrumb{
          display:flex;
        }
      }
      .account-title{
        padding-bottom:20px;
        .flex-box{
          width:100%;
          .el-button{
            padding:12px;
            flex:1;
          }
        }
        &.flex-box{
          display:flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: flex-start;
        }
        h2{
          display:none;
        }
        p{
          font-size:14px;
          padding-top:20px;
          @include color(tc-secondary);
        }
        .el-button{
          margin-top:16px;
        }
      }
      .account-body-container{
        .account-body-wrapper{
          .google-setting-cont{
            border:0;
            .google-setting-wrap{
              padding:0;
              .google-code-box{
                width:100%;
              }
              .setting-cont-box{
                dl{
                  &.flex-box{
                    flex-direction: column;
                  }
                }
              }
            }
          }
          .google-install-cont{
            width:100%;
            height:auto;
            .install-box{
              width:auto;
              margin:0;
              float:none;
              &.install-right{
                margin:0;
                margin-top:20px;
              }
              &.install-left{
                margin:0;
              }
              .install-btn-box{
                dl{
                  dt{
                    width:40px;
                    height:40px;
                  }
                  dd{
                    font-size:16px;
                  }
                }
              }
            }
          }
          .google-install-btn{
            width:100%;
          }
          .security-list{
            border:0px solid;
            border-radius:0;
            li{
              padding:20px 0;
              .item-left{
                p{
                  &.warn{
                    display:block;
                    @include color(warn);
                  }
                }
              }
              .item-right{
                p{
                  &.warn{
                    display:none;
                  }
                }
              }
            }
          }
          .subAccount-cont{
            border:0;
            .subAccount-wrap{
              .subAccount-title{
                padding:0 0 10px;
                font-size:14px;
              }
            }
          }
        }
      }
    }
  }
}