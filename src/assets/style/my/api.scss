.sub-account-api-container{
  padding:0 20px 20px;
  .account-nav-box, .account-nav{
    p{
      font-size:14px;
      padding-top:20px;
      @include color(tc-secondary);
    }
    .el-button{
      height:40px;
      padding:0 65px;
    }
  }
  .sub-account-api-wrapper{
    min-height:500px;
    margin-top:20px;
    border:1px solid;
    border-radius:12px;
    @include border-color(border);
    .api-item-box{
      border-bottom:1px solid;
      @include border-color(border);
      &:last-child{
        border-bottom:0;
      }
      .api-item-pd{
        padding:20px;
        position:relative;
        .api-title{
          font-size:18px;
          padding-bottom:20px;
          @include color(tc-primary);
        }
        dl{
          @include color(tc-primary);
          dt{
            width:150px;
            height:150px;
            border:1px solid;
            border-radius:10px;
            padding:20px;
            margin-right:20px;
            @include border-color(border);
          }
          dd{
            flex: 1;
            .li-item{
              display:flex;
              width:100%;
              padding:12px 0;
              border-bottom:1px solid;
              @include border-color(border);
              &:last-child{
                border-bottom:0;
                padding-bottom:12px;
              }
              .item-left{
                width:22%;
              }
              .item-right{
                flex:1;
                p{
                  font-size:14px;
                  padding-top:8px;
                  @include color(tc-secondary);
                }
              }
            }
          }
        }
        .btn-box{
          position:absolute;
          top:20px;
          right:20px;
          .el-button{
            flex:1;
          }
        }
      }
    }
  }
}
@include mb{
  .sub-account-api-container{
    padding:0 20px 20px;
    .sub-account-title{
      display:none;
    }
    .account-nav{
      h2{
        display:none;
      }
      .el-button{
        margin-top: 20px;
      }
      &.flex-box{
        flex-direction: column;
        align-items: start;
      }
    }
    .account-nav-box{
      position: absolute;
      top:64px;
      left:20px;
      right:20px;
      padding-bottom:0;
      height:48px;
      line-height:48px;
      display:flex;
      border-bottom:1px solid;
      flex-direction: column;
      align-items:flex-start;
      @include bg-color(bg-primary);
      @include border-color(border);
      &.hasad{
        top:102px;
      }
      &.headNo{
        top:0;
        &.hasadHeadNo{
          top:40px;
        }
      }
      .el-breadcrumb{
        display:flex;
        height:48px;
      }
      p{
        margin-top:-8px;
        line-height:26px;
        a{
          display:block;
          padding:0;
        }
      }
      .el-button{
        line-height:40px;
        margin-top:14px;
      }
    }
    .sub-account-api-wrapper{
      &.noTop{
        margin-top:0;
      }
      margin-top:120px;
      border:0px;
      border-radius:0;
      .api-item-box{
        .api-item-pd{
          padding:20px 0;
          .api-title{
            font-size:18px;
            text-align:left;
            padding-bottom:0;
            @include color(tc-primary);
          }
          dl{
            &.flex-box{
              flex-direction:column;
              align-items:center;
            }
            dt{
              display: none;
            }
            dd{
              flex: 1;
              @include color(tc-primary);
              .li-item{
                display:block;
                width:100%;
                padding:12px 0;
                border-bottom:1px solid;
                @include border-color(border);
                &:last-child{
                  border-bottom:0;
                  padding-bottom:12px;
                }
                .item-left{
                  width:100%;
                }
                .item-right{
                  flex:1;
                  p{
                    font-size:14px;
                    padding-top:8px;
                    @include color(tc-secondary);
                  }
                }
              }
            }
          }
          .btn-box{
            position:static;
            .el-button{
              flex:1;
            }
          }
        }
      }
    }
  }
}