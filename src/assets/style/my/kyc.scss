
.limit-line-wrap{
  padding:24px;
  height:auto;
  overflow:hidden;
  @include bg-color(bg-tertiary);
  .limit-line-cont{
    .limit-left{
      h2{
        line-height:24px;
      }
      p{
        line-height:16px;
      }
    }
    .limit-right{
      .line-text{
        h3{
          line-height:20px;
          i{
            display: block;
            width:20px;
            height:20px;
            line-height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            &.not-reach{
              &:after{
                background-color: #7D92A8;
                display: inline-block;
                content: "";
                width: 6px;
                height: 6px;
                border-radius: 3px;
              }
            }
          }
          span{
            line-height:20px;
          }
        }
        p{
          line-height:16px;
        }
      }
      .line-box{
        width:64px;
        height:2px;
        margin:9px 16px;
        @include bg-color(border);
      }
    }
  }
}
.kyc-wrap-cont{
  padding:24px 24px 40px;
  @include bg-color(bg-tertiary);
}
.uc-title{
  padding:24px 0;
  h2{
    line-height:32px;
    font-size:24px;
    font-weight: 600;
    @include color(tc-primary);
  }
  .tag-tips{
    margin-top:16px;
    padding:8px 16px;
    border-radius: 4px;
    @include bg-color(bg-secondary);
    span{
      font-size:14px;
      @include color(tc-primary);
    }
  }
}
.uc-auth-container{
  padding:0 20px;
  .el-form{
    &.kyc-form{
      width:480px;
      &.kyc-step2{
        width:100%;
        .el-form-item{
          display:flex;
          margin-bottom:20px;
        }
        .el-form-item__label{
          padding:0;
          display:block;
          width:20%;
          height:36px;
          font-size:14px;
          text-align:left;
          padding-bottom:48px;
          @include color(tc-secondary);
        }
        .el-form-item__content{
          display:block;
          width:100%;
        }
        .img-cont-box{
          width:100%;
          position: relative;
          margin-bottom:6px;
          // display:flex;
          // justify-content: center;
          // flex-direction: column;
          // align-items: center;
          .add-btn{
            width:84px;
            height:84px;
            position: absolute;
            top:30%;
            left:38%;
            background-size: 84px auto;
            background-position: center;
            background-repeat: no-repeat;
            @include get-img(
              '@/assets/images/my/kyc/add-btn-light.png',
              '@/assets/images/my/kyc/add-btn-dark.png'
            );
          }
          .card-img{
            width:362px;
            height:216px;
            @include bg-color(bg-secondary);
          }
          .card-before{
            &.id-card{
              background-repeat: no-repeat;
              background-position: center;
              background-size:360px auto;
              @include get-img(
                '@/assets/images/my/kyc/cn-front-light.png',
                '@/assets/images/my/kyc/cn-front-dark.png'
              );
              &.img-cont{
                background-position: center;
                background-size:140px 80px;
              }
            }
            &.id-card-en{
              background-repeat: no-repeat;
              background-position: center;
              background-size:360px auto;
              @include get-img(
                '@/assets/images/my/kyc/en-front-light.png',
                '@/assets/images/my/kyc/en-front-dark.png'
              );
              &.img-cont{
                background-position: 0 0;
                background-size:140px 80px;
              }
            }
            &.id-card-passport{
              background-repeat: no-repeat;
              background-position: center;
              background-size:360px auto;
              @include get-img(
                '@/assets/images/my/kyc/en-passport-template-light.png',
                '@/assets/images/my/kyc/en-passport-template-dark.png'
              );
              &.img-cont{
                background-position: 0 0;
                background-size:140px 80px;
              }
            }
            &.id-card-driver{
              background-repeat: no-repeat;
              background-position: center;
              background-size:360px auto;
              @include get-img(
                '@/assets/images/my/kyc/driver-front-light.png',
                '@/assets/images/my/kyc/driver-front-dark.png'
              );
              &.img-cont{
                background-position: 0 0;
                background-size:140px 80px;
              }
            }
          }
          .card-back{
            &.id-card{
              background-repeat: no-repeat;
              background-position: center;
              background-size:360px auto;
              @include get-img(
                '@/assets/images/my/kyc/cn-back-light.png',
                '@/assets/images/my/kyc/cn-back-dark.png'
              );
              &.img-cont{
                background-position: 0 0;
                background-size:140px 80px;
              }
            }
            &.id-card-en{
              background-repeat: no-repeat;
              background-position: center;
              background-size:360px auto;
              @include get-img(
                '@/assets/images/my/kyc/en-back-light.png',
                '@/assets/images/my/kyc/en-back-dark.png'
              );
              &.img-cont{
                background-position: 0 0;
                background-size:140px 80px;
              }
            }
            &.id-card-passport{
              background-repeat: no-repeat;
              background-position: center;
              background-size:360px auto;
              @include get-img(
                '@/assets/images/my/kyc/en-passport-front-light.png',
                '@/assets/images/my/kyc/en-passport-front-dark.png'
              );
              &.img-cont{
                background-position: 0 0;
                background-size:140px 80px;
              }
            }
            &.id-card-driver{
              background-repeat: no-repeat;
              background-position: center;
              background-size:360px auto;
              @include get-img(
                '@/assets/images/my/kyc/driver-back-light.png',
                '@/assets/images/my/kyc/driver-back-dark.png'
              );
              &.img-cont{
                background-position: 0 0;
                background-size:140px 80px;
              }
            }
          }
          .card-selfie{
            &.id-card{
              background-repeat: no-repeat;
              background-position: center;
              background-size:360px auto;
              @include get-img(
                '@/assets/images/my/kyc/cn-selfie-light.png',
                '@/assets/images/my/kyc/cn-selfie-dark.png'
              );
              &.img-cont{
                background-position: 0 0;
                background-size:140px 80px;
              }
            }
            &.id-card-en{
              background-repeat: no-repeat;
              background-position: center;
              background-size:360px auto;
              @include get-img(
                '@/assets/images/my/kyc/en-selfie-light.png',
                '@/assets/images/my/kyc/en-selfie-dark.png'
              );
              &.img-cont{
                background-position: 0 0;
                background-size:140px 80px;
              }
            }
            &.id-card-passport{
              background-repeat: no-repeat;
              background-position: center;
              background-size:360px auto;
              @include get-img(
                '@/assets/images/my/kyc/en-passport-selfie-light.png',
                '@/assets/images/my/kyc/en-passport-selfie-dark.png'
              );
              &.img-cont{
                background-position: 0 0;
                background-size:140px 80px;
              }
            }
            &.id-card-driver{
              background-repeat: no-repeat;
              background-position: center;
              background-size:360px auto;
              @include get-img(
                '@/assets/images/my/kyc/driver-selfie-light.png',
                '@/assets/images/my/kyc/driver-selfie-dark.png'
              );
              &.img-cont{
                background-position: 0 0;
                background-size:140px 80px;
              }
            }
          }
          .el-upload{
            width:362px;
            height:216px;
            border-radius:4px;
            padding:0;
            border:0;
            position: relative;
            .loading-box{
              position: absolute;
              top:0;
              left:0;
              right:0;
              bottom:0;
              background-color: rgba(0,0,0,0.5);
              display:flex;
              align-items: center;
              justify-content: center;
              border-radius:4px;
              font-size:16px;
              color:#ffffff;
              font-weight:600;
              z-index:9;
            }
            .error-box{
              position: absolute;
              bottom:-24px;
              left:0;
              font-size:12px;
              line-height:16px;
              z-index:9;
              width:100%;
              @include color(error);
              @include bg-color(bg-primary);
            }
            .img-box{
              width:362px;
              height:216px;
              display: flex;
              align-items: center;
              justify-content: center;
              overflow:hidden;
              @include bg-color(bg-secondary);
              img{
                display:block;
                width:100%;
                height:auto;
              }
            }
          }
          .sl-box{
            width: 140px;
            height:80px;
            border:1px solid;
            border-radius:4px;
            padding:4px;
            margin:36px auto;
            display:flex;
            align-items:center;
            justify-content: center;
            overflow:hidden;
            position: relative;
            @include border-color(border);
            .fixed-dialog{
              position: absolute;
              top:4px;
              left:4px;
              right:4px;
              bottom:4px;
              border-radius:4px;
              background-color: rgba(0,0,0,0.4);
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              i{
                font-size:16px;
                color:#fff;
                font-style: inherit;
              }
            }
            .img-cont{
              width: 140px;
              height:80px;
              border-radius:4px;
              overflow:hidden;
              img{
                display: block;
                width:100%;
                height:auto;
              }
            }
          }
        }
      }
      .el-form-item{
        margin-bottom:16px;
      }
      .el-form-item__label{
        height:36px;
        line-height:36px;
      }
      .el-select, .el-input{
        width:480px;
      }
      .el-select{
        .el-input__suffix{
          .el-icon-arrow-up{
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top:6px;
            &.is-reverse{
              margin-top:-6px;
            }
            &:before{
              content: '';
              display:block;
              width:0;
              height:0px;
              border:5px solid transparent;
              [data-theme^="light"] & {
                border-bottom:5px solid #10171f;
              }
              [data-theme^="dark"] & {
                border-bottom:5px solid #fff;
              }
            }
          }
        }
        &.china-icon{
          .el-input__inner{
            padding-left:42px;
          }
          .pre-icon{
            width:20px;
            height:20px;
            margin-top:12px;
          }
        }
      }
    }
  }
  .kyc-btn{
    &.first{
      .el-button{
        width:480px;
      }
    }
    .el-button{
      width:235px;
      height:44px;
    }
  }
  .line-box{
    width:100%;
    height:1px;
    margin-bottom:40px;
    margin-top:40px;
    @include bg-color(border);
  }
}
.kyc-form-info-container{
  border-bottom:1px solid;
  padding:0px 0 24px;
  @include border-color(border);
  h2{
    font-size:20px;
    font-weight:700;
    padding-bottom:16px;
    @include color(tc-primary);
  }
  ul{
    width:754px;
    font-size:14px;
  }
}
.step2-info-box{
  font-size:12px;
  padding: 34px 0;
  dl{
    width:100%;
    dt{
      width:230px;
      &.enTxt{
        width:300px;
      }
    }
  }
  .n-font-p-sty{
    margin-top:16px;
    li{
      padding-right:36px;
      i{
        display:block;
        width:4px;
        height:4px;
        border-radius:50%;
        margin-right:8px;
        @include bg-color(tc-secondary);
      }
    }
  }
}
.result-container{
  display:flex;
  justify-content: center;
  .result-wrapper{
    padding:40px 0;
    text-align:center;
    .result-title{
      line-height:24px;
      margin-top:16px;
      span{
        font-weight:700;
      }
    }
    .result-info{
      line-height:20px;
      margin-top:16px;
    }
    .btn-box{
      margin-top:16px;
    }
  }
}
@include mb {
  .uc-auth-container{
    padding-bottom:40px;
    .el-form{
      &.kyc-form{
        width:100%;
        &.kyc-step2{
          width:100%;
          .el-form-item{
            display:block;
          }
          .el-form-item__label{
            width:100%;
            height:28px;
            padding-bottom:0;
          }
          .img-cont-box{
            width:100%;
            position: relative;
            margin-bottom:6px;
            .add-btn{
              width:64px;
              height:64px;
              top:30%;
              left:38%;
              background-size: 64px auto;
            }
            .card-img{
              width:100%;
              height:216px;
              max-width: 362px;
              overflow:hidden;
              @include bg-color(bg-secondary);
            }
            .card-before{
              &.id-card{
                background-repeat: no-repeat;
                background-position: center;
                background-size:360px auto;
                @include get-img(
                  '@/assets/images/my/kyc/cn-front-light.png',
                  '@/assets/images/my/kyc/cn-front-dark.png'
                );
                &.img-cont{
                  background-position: center;
                  background-size:140px 80px;
                }
              }
              &.id-card-en{
                background-repeat: no-repeat;
                background-position: center;
                background-size:360px auto;
                @include get-img(
                  '@/assets/images/my/kyc/en-front-light.png',
                  '@/assets/images/my/kyc/en-front-dark.png'
                );
                &.img-cont{
                  background-position: 0 0;
                  background-size:140px 80px;
                }
              }
              &.id-card-passport{
                background-repeat: no-repeat;
                background-position: center;
                background-size:360px auto;
                @include get-img(
                  '@/assets/images/my/kyc/en-passport-template-light.png',
                  '@/assets/images/my/kyc/en-passport-template-dark.png'
                );
                &.img-cont{
                  background-position: 0 0;
                  background-size:140px 80px;
                }
              }
              &.id-card-driver{
                background-repeat: no-repeat;
                background-position: center;
                background-size:360px auto;
                @include get-img(
                  '@/assets/images/my/kyc/driver-front-light.png',
                  '@/assets/images/my/kyc/driver-front-dark.png'
                );
                &.img-cont{
                  background-position: 0 0;
                  background-size:140px 80px;
                }
              }
            }
            .card-back{
              &.id-card{
                background-repeat: no-repeat;
                background-position: center;
                background-size:360px auto;
                @include get-img(
                  '@/assets/images/my/kyc/cn-back-light.png',
                  '@/assets/images/my/kyc/cn-back-dark.png'
                );
                &.img-cont{
                  background-position: 0 0;
                  background-size:140px 80px;
                }
              }
              &.id-card-en{
                background-repeat: no-repeat;
                background-position: center;
                background-size:360px auto;
                @include get-img(
                  '@/assets/images/my/kyc/en-back-light.png',
                  '@/assets/images/my/kyc/en-back-dark.png'
                );
                &.img-cont{
                  background-position: 0 0;
                  background-size:140px 80px;
                }
              }
              &.id-card-passport{
                background-repeat: no-repeat;
                background-position: center;
                background-size:360px auto;
                @include get-img(
                  '@/assets/images/my/kyc/en-passport-front-light.png',
                  '@/assets/images/my/kyc/en-passport-front-dark.png'
                );
                &.img-cont{
                  background-position: 0 0;
                  background-size:140px 80px;
                }
              }
              &.id-card-driver{
                background-repeat: no-repeat;
                background-position: center;
                background-size:360px auto;
                @include get-img(
                  '@/assets/images/my/kyc/driver-back-light.png',
                  '@/assets/images/my/kyc/driver-back-dark.png'
                );
                &.img-cont{
                  background-position: 0 0;
                  background-size:140px 80px;
                }
              }
            }
            .card-selfie{
              &.id-card{
                background-repeat: no-repeat;
                background-position: center;
                background-size:360px auto;
                @include get-img(
                  '@/assets/images/my/kyc/cn-selfie-light.png',
                  '@/assets/images/my/kyc/cn-selfie-dark.png'
                );
                &.img-cont{
                  background-position: 0 0;
                  background-size:140px 80px;
                }
              }
              &.id-card-en{
                background-repeat: no-repeat;
                background-position: center;
                background-size:360px auto;
                @include get-img(
                  '@/assets/images/my/kyc/en-selfie-light.png',
                  '@/assets/images/my/kyc/en-selfie-dark.png'
                );
                &.img-cont{
                  background-position: 0 0;
                  background-size:140px 80px;
                }
              }
              &.id-card-passport{
                background-repeat: no-repeat;
                background-position: center;
                background-size:360px auto;
                @include get-img(
                  '@/assets/images/my/kyc/en-passport-selfie-light.png',
                  '@/assets/images/my/kyc/en-passport-selfie-dark.png'
                );
                &.img-cont{
                  background-position: 0 0;
                  background-size:140px 80px;
                }
              }
              &.id-card-driver{
                background-repeat: no-repeat;
                background-position: center;
                background-size:360px auto;
                @include get-img(
                  '@/assets/images/my/kyc/driver-selfie-light.png',
                  '@/assets/images/my/kyc/driver-selfie-dark.png'
                );
                &.img-cont{
                  background-position: 0 0;
                  background-size:140px 80px;
                }
              }
            }
            .el-upload{
              width:362px;
              height:216px;
              border-radius:4px;
              padding:0;
              border:0;
              position: relative;
              .loading-box{
                position: absolute;
                top:0;
                left:0;
                right:0;
                bottom:0;
                background-color: rgba(0,0,0,0.5);
                display:flex;
                align-items: center;
                justify-content: center;
                border-radius:4px;
                font-size:16px;
                color:#ffffff;
                font-weight:600;
              }
              .error-box{
                position: absolute;
                bottom:-22px;
                left:0;
                font-size:12px;
                line-height:16px;
                z-index:9;
                @include color(error);
                @include bg-color(bg-primary);
              }
              .img-box{
                width:362px;
                height:216px;
                display: flex;
                align-items: center;
                justify-content: center;
                overflow:hidden;
                @include bg-color(bg-secondary);
                img{
                  display:block;
                  width:100%;
                  height:auto;
                }
              }
            }
            .sl-box{
              width: 140px;
              height:80px;
              border:1px solid;
              border-radius:4px;
              padding:4px;
              margin:36px auto;
              display:flex;
              align-items:center;
              justify-content: center;
              overflow:hidden;
              position: relative;
              @include border-color(border);
              .fixed-dialog{
                position: absolute;
                top:4px;
                left:4px;
                right:4px;
                bottom:4px;
                border-radius:4px;
                background-color: rgba(0,0,0,0.4);
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                i{
                  font-size:16px;
                  color:#fff;
                  font-style: inherit;
                }
              }
              .img-cont{
                width: 140px;
                height:80px;
                border-radius:4px;
                overflow:hidden;
                img{
                  display: block;
                  width:100%;
                  height:auto;
                }
              }
            }
          }
        }
        .el-form-item{
          margin-bottom:16px;
        }
        .el-form-item__label{
          height:36px;
          line-height:36px;
        }
        .el-select, .el-input{
          width:480px;
        }
        .el-select{
          .el-input__suffix{
            .el-icon-arrow-up{
              display: flex;
              align-items: center;
              justify-content: center;
              margin-top:6px;
              &.is-reverse{
                margin-top:-6px;
              }
              &:before{
                content: '';
                display:block;
                width:0;
                height:0px;
                border:5px solid transparent;
                [data-theme^="light"] & {
                  border-bottom:5px solid #10171f;
                }
                [data-theme^="dark"] & {
                  border-bottom:5px solid #fff;
                }
              }
            }
          }
          &.china-icon{
            .el-input__inner{
              padding-left:42px;
            }
            .pre-icon{
              width:20px;
              height:20px;
              margin-top:12px;
            }
          }
        }
      }
    }
    .kyc-btn{
      &.first{
        width:100%;
      }
      .el-button{
        width:100% !important;
        height:44px;
      }
    }
    .line-box{
      width:100%;
      height:1px;
      margin-bottom:40px;
      margin-top:40px;
      @include bg-color(border);
    }
  }
  .kyc-form-info-container{
    border-bottom:1px solid;
    padding:0px 0 24px;
    @include border-color(border);
    h2{
      font-size:16px;
      padding-bottom:14px;
      padding-top:14px;
    }
    ul{
      width:100%;
      font-size:14px;
    }
  }
  .step2-info-box{
    font-size:12px;
    padding: 18px 0 14px;
    dl{
      width:100%;
      &.flex-box{
        display:block;
      }
      dt{
        width:100%;
        &.enTxt{
          width:100%;
        }
      }
    }
    .n-font-p-sty{
      margin-top:16px;
      flex-wrap:wrap;
      li{
        padding-right:0;
        width:50%;
        padding-bottom:8px;
        i{
          display:block;
          width:4px;
          height:4px;
          border-radius:50%;
          margin-right:8px;
          @include bg-color(tc-secondary);
        }
      }
    }
  }
}
