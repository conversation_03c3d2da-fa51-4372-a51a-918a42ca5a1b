.nft-assets-container{
  max-width:1920px;
  margin:0 auto;
  .nft-h5-cont{
    display:none;
  }
  .nft-h5-btn{
    display:none;
  }
  .nft-assets-wrapper{
    padding:0 20px;
    height:auto;
    overflow:hidden;
    .nft-assets-title{
      padding:32px 0;
      h1{
        font-size:40px;
        font-weight:600;
        @include color(tc-primary);
      }
      .nft-title-btn{
        .el-button{
          width:180px;
        }
      }
    }
    .nft-nav-list{
      ul{
        margin-bottom:16px;
        li{
          cursor:pointer;
          font-size:16px;
          margin-right:20px;
          position: relative;
          padding-bottom:8px;
          @include color(tc-secondary);
          &.active{
            @include color(tc-primary);
            &:after{
              content: '';
              position: absolute;
              display: block;
              width:14px;
              height:2px;
              left:50%;
              margin-left:-7px;
              bottom:0;
              @include bg-color(theme);
            }
          }
        }
      }
    }
  }
}
.nft-check-box-container{
  height:800px;
  overflow:auto;
  &.heiAuto{
    height:auto;
    .el-checkbox-group{
      .check-item{
        margin-right:12px;
        margin-top:12px;
      }
    }
  }
  .el-checkbox-group{
    font-size:14px;
    line-height:18px;
    flex-wrap:wrap;
    margin-right:-16px;
    .check-item{
      cursor:pointer;
      border-radius:16px;
      border:1px solid;
      margin-right:15px;
      margin-top:15px;
      @include border-color(border);
      .check-item-pd{
        padding:16px;
        .check-item-title{
          padding-bottom:16px;
          @include color(tc-primary);
        }
        .check-item-img{
          width:160px;
          height:160px;
          border-radius:8px;
          @include bg-color(bg-quaternary);
          img{
            display:block;
            width:100%;
            border-radius:8px;
          }
        }
      }
    }
  }
}
@include mb{
  .nft-assets-container{
    max-width:100%;
    margin:0 auto;
    .nft-h5-cont{
      font-size:14px;
      display:flex;
      height:40px;
      align-items: center;
      padding:0 16px;
      border-bottom:1px solid;
      @include border-color(border);
      @include color(tc-secondary);
      span{
        &.active{
          @include color(tc-primary);
        }
      }
      svg{
        &.rotate{
          transform: rotate(90deg);
        }
      }
    }
    .nft-h5-btn{
      display:block;
      position: fixed;
      z-index:99;
      bottom:0;
      left:0;
      right:0;
      @include bg-color(bg-primary);
      .el-button{
        height:40px;
      }
    }
    .nft-assets-wrapper{
      padding:0 16px;
      .nft-assets-title{
        display:none;
      }
      .nft-nav-list{
        ul{
          margin-bottom:12px;
          li{
            height:44px;
            line-height:44px;
            font-size:14px;
            margin-right:16px;
            padding-bottom:0;
            &.active{
              &:after{
                bottom:6px;
              }
            }
          }
        }
      }
    }
  }
  .nft-check-box-container{
    height:auto;
    overflow:hidden;
    padding-top:8px;
    margin-bottom:74px;
    &.heiAuto{
      height:auto;
      margin-left:-30px;
      .el-checkbox-group{
        .check-item{
          margin-right:8px;
          margin-top:8px;
        }
      }
    }
    .el-checkbox-group{
      font-size:14px;
      line-height:18px;
      flex-wrap:wrap;
      margin-right:-8px;
      .check-item{
        cursor:pointer;
        border-radius:16px;
        border:1px solid;
        margin-right:8px;
        margin-top:8px;
        @include border-color(border);
        &:nth-child(3n){
          margin-right: 0;
        }
        .check-item-pd{
          padding:8px;
          .check-item-title{
            padding-bottom:16px;
            @include color(tc-primary);
          }
          .check-item-img{
            width:82px;
            height:82px;
          }
        }
      }
    }
  }
}