.deposit-container{
  padding: 0 20px 20px;
  .deposit-nav-box{
    padding-bottom:20px;
  }
  .deposit-body-cont{
    width:100%;
    .deposit-body-cont-detail{
      max-width:1024px;
    }
    .deposit-info-title{
      padding-bottom:32px;
      .deposit-value{
        font-size:40px;
        @include color(tc-primary);
      }
      .deposit-status{
        display: inline-block;
        font-size:14px;
        padding:8px 12px;
        border-radius:20px;
        vertical-align: middle;
        text-align:center;
        @include color(tc-secondary);
        @include bg-color(bg-quaternary);
        svg, span{
          display: inline-block;
          vertical-align: middle;
        }
        &.fall{
          background-color: rgba(255, 98, 98, 0.1) !important;
        }
        &.rise{
          background-color: rgba(59, 193, 137, 0.1) !important;
        }
      }
    }
    .deposit-detail-info{
      dl{
        dd{
          display: flex;
          justify-content: flex-end;
          a{
            display: block;
          }
        }
      }
    }
    .address-cont{
      width:460px;
      .address-wrap{
        border:1px solid;
        border-radius:12px;
        padding:20px;
        margin-bottom:12px;
        @include border-color(border);
        &.flex-box{
          flex-direction: column;
          justify-content: center;
          align-items: center;
        }
        dt{
          padding:4px;
          border-radius:12px;
          border:1px solid;
          margin-right:8px;
          @include border-color(border);
          canvas{
            width:150px;
            height:150px;
          }
        }
        dd{
          width:100%;
          h3{
            padding-top:20px;
            text-align:center;
            font-size:16px;
            @include color(tc-primary);
          }
          p{
            display: flex;
            justify-content: center;
            width:100%;
            padding-top:12px;
            font-size:14px;
            @include color(tc-secondary);
            svg{
              margin-left:8px;
              @include color(theme);
            }
          }
        }
      }
      .info-p{
        font-size:14px;
        padding-bottom:8px;
        span{
          @include color(tc-secondary);
        }
        em{
          font-style:inherit;
          @include color(tc-primary);
        }
      }
    }
    .record-list-cont{
      .record-title{
        font-size:20px;
        padding:24px;
        border-bottom:1px solid;
        @include border-color(border);
        @include color(tc-primary);
      }
    }
    .input-text-box{
      width:460px;
      position: relative;
      .list-wrap-dialog{
        position:fixed;
        top:0;
        right:0;
        left:0;
        bottom:0;
        cursor: pointer;
        z-index:1998;
      }
      .list-select-body{
        position: absolute;
        top:50px;
        left:0;
        right:0;
        z-index:1999;
        border-radius:8px;
        box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.1);
        @include bg-color(bg-dialog);
        .select-body-wrap{
          padding:8px 0;
          .select-title{
            padding:12px 24px;
            @include color(tc-secondary);
            svg{
              font-size:16px !important;
              cursor:pointer;
              @include pc-hover {
                &:hover{
                  @include color(theme);
                }
              }
            }
          }
          .select-special-box{
            ul{
              padding:0 4px 12px 24px;
              display:flex;
              flex-wrap:wrap;
              li{
                cursor:pointer;
                padding:8px;
                border-radius:6px;
                font-size:14px;
                font-weight:500;
                margin-right:20px;
                @include bg-color(bg-quaternary);
                @include pc-hover {
                  &:hover{
                    @include color(theme);
                  }
                }
                .icon-box{
                  width:16px;
                  height:auto;
                  font-size:16px;
                }
              }
            }
          }
          .select-coin-list-box{
            max-height:400px;
            overflow:auto;
            .item-title-box{
              padding:8px 16px;
              .add-item, .manger-item{
                cursor: pointer;
                font-size:14px;
                display:flex;
                align-items: center;
                @include color(tc-primary);
                svg{
                  display: block;
                  font-size:14px !important;
                  margin-left:4px;
                }
              }
              .manger-item{
                @include color(theme);
              }
            }
            ul{
              li{
                display:flex;
                padding:12px 16px;
                align-items:center;
                cursor:pointer;
                .verify-icon-text{
                  font-size:12px;
                  padding:2px 8px;
                  border-radius:2px;
                  background-color:rgba(59, 193, 137, 0.1);
                  @include color(rise);
                }
                .white-address-tag{
                  margin-top:8px;
                  font-size:12px;
                  padding:4px 8px;
                  border-radius:4px;
                  white-space: nowrap;
                  @include bg-color(bg-secondary);
                  @include color(tc-primary);
                }
                &.active{
                  @include color(theme);
                }
                &.noflex{
                  display:block;
                }
                &.disabled{
                  cursor:not-allowed;
                  span{
                    @include color(tc-secondary);
                  }
                  @include pc-hover {
                    &:hover{
                      @include bg-color(bg-primary);
                    }
                  }
                }
                @include pc-hover {
                  &:hover{
                    @include bg-color(bg-quaternary);
                  }
                }
                .list-p{
                  width:100%;
                  span, em{
                    display:inline-block;
                  }
                }
                p{
                  padding-top:8px;
                  @include color(tc-primary);
                }
                .icon-box{
                  width:24px;
                  height:auto;
                  font-size:24px;
                  margin-right:8px;
                }
                span{
                  font-size:16px;
                  margin-right:8px;
                  display:block;
                  @include color(tc-primary);
                }
                em{
                  font-size:14px;
                  margin-right:8px;
                  display:block;
                  font-style:inherit;
                  @include color(tc-secondary);
                }
                i{
                  font-style: inherit;
                  font-size:14px;
                  @include color(tc-primary);
                }
              }
            }
          }
          .bottom-cont-box{
            padding:12px 0 4px;
            .add-item{
              width:160px;
              height:38px;
              line-height:38px;
              cursor: pointer;
              text-align:center;
              border:1px solid;
              border-radius:25px;
              @include border-color(border);
              @include color(tc-primary);
            }
          }
        }
      }
      .el-input {
        &.tp-input{
          .el-input__inner{
            padding-right:72px;
          }
        }
      }
    }
    .tb-btn{
      width: 100%;
      height:40px;
      margin-top:8px;
    }
    .white-info-cont{
      width:460px;
      .white-info-title{
        padding:12px 0; 
        font-size:14px;
        .info-left{
          font-weight:500;
          @include color(tc-primary);
        }
        .info-right{
          a{
            @include color(theme);
          }
        }
      }
      p{
        font-size:14px;
        padding-bottom:8px;
        @include color(tc-secondary);
      }
    }
    .white-list-body{
      .address-add-cont{
        margin-bottom:16px;
        .white-mode-box{
          padding:16px;
          border-radius:12px;
          border:1px solid;
          @include border-color(border);
          .white-mode-switch{
            margin-bottom:16px;
            h2{
              font-size:16px;
              font-weight:500;
              @include color(tc-primary);
            }
          }
          p{
            padding-top:2px;
            font-size:14px;
            font-weight:400;
            @include color(tc-secondary);
          }
        }
      }
      .white-search{
        padding-bottom:8px;
        border-bottom:1px solid;
        @include border-color(border);
        .search-left{
          .el-input{
            width:240px;
            margin-right:16px;
            &.white-search-input{
              .el-input__inner{
                height:34px !important;
                padding-left:20px !important;
              }
            }
          }
          .orders-select{
            margin-top:0;
          }
        }
        .search-right{
          .switch-box{
            margin-right:12px;
            span{
              margin-left:8px;
              font-size:12px;
            }
          }
        }
      }
      .pl-btn-cont{
        margin-top:20px;
        .pl-btn{
          cursor: pointer;
          height:40px;
          line-height:40px;
          border-radius:6px;
          padding:0 16px;
          font-size:14px;
          margin-left:20px;
          @include bg-color(bg-secondary);
          @include color(tc-primary);
        }
      }
    }
  }
}
@include mb {
  .deposit-container{
    .deposit-nav-box{
      position: absolute;
      top:66px;
      left:20px;
      right:20px;
      padding-bottom:0;
      height:48px;
      line-height:48px;
      display:flex;
      border-bottom:1px solid;
      &.hasad{
        top:102px;
      }
      &.headNo{
        top:0;
        &.hasadHeadNo{
          top:40px;
        }
      }
      @include bg-color(bg-primary);
      @include border-color(border);
      .el-breadcrumb{
        display:flex;
      }
    }
    .deposit-body-cont{
      padding-top:20px;
      .deposit-body-cont-detail{
        max-width:100%;
      }
      .deposit-info-title{
        padding-bottom:24px;
        text-align:center;
        .deposit-value{
          font-size:32px;
        }
        .deposit-status{
          display: inline-block;
          font-size:14px;
          padding:8px 12px;
          border-radius:20px;
          vertical-align: middle;
          text-align:center;
          @include color(tc-secondary);
          @include bg-color(bg-quaternary);
          svg, span{
            display: inline-block;
            vertical-align: middle;
          }
          &.fall{
            background-color: rgba(255, 98, 98, 0.1) !important;
          }
          &.rise{
            background-color: rgba(59, 193, 137, 0.1) !important;
          }
        }
      }
      .deposit-detail-info{
        dl{
          dd{
            display: flex;
            justify-content: flex-end;
            a{
              display: block;
            }
          }
        }
      }
      .address-cont{
        width:100%;
        .address-wrap{
          border:1px solid;
          border-radius:12px;
          padding:20px;
          margin-bottom:12px;
          @include border-color(border);
          &.flex-box{
            flex-direction: column;
            justify-content: center;
            align-items: center;
          }
          dt{
            padding:4px;
            border-radius:12px;
            border:1px solid;
            margin-right:8px;
            @include border-color(border);
            canvas{
              width:150px;
              height:150px;
            }
          }
          dd{
            padding-top:20px;
            h3{
              font-size:16px;
              @include color(tc-primary);
              text-align:center;
            }
            p{
              width:100%;
              padding-top:12px;
              font-size:14px;
              word-break:break-all;
              @include color(tc-secondary);
              svg{
                font-size:16px;
                margin-left:8px;
                @include color(theme);
              }
              span{
                width:100% !important;
              }
            }
          }
        }
        .info-p{
          font-size:14px;
          padding-bottom:8px;
          span{
            @include color(tc-secondary);
          }
          em{
            font-style:inherit;
            @include color(tc-primary);
          }
        }
      }
      .record-list-cont{
        .record-title{
          font-size:20px;
          padding:16px 0;
          border-bottom:1px solid;
          @include border-color(border);
          @include color(tc-primary);
        }
      }
      .input-text-box{
        width:100%;
      }
      .white-info-cont{
        width:100%;
        .white-info-title{
          padding:12px 0; 
          font-size:14px;
          .info-left{
            font-weight:500;
            @include color(tc-primary);
          }
          .info-right{
            a{
              @include color(theme);
            }
          }
        }
        p{
          font-size:14px;
          padding-bottom:8px;
          @include color(tc-secondary);
        }
      }
      .white-list-body{
        position: relative;
        .address-add-cont{
          margin-bottom:0;
          &.flex-box{
            flex-direction: column;
          }
          .white-mode-box{
            margin-bottom:16px;
          }
          .el-button{
            width:100%;
          }
        }
        .white-search{
          padding-bottom:20px;
          border-bottom:1px solid;
          @include border-color(border);
          .search-left{
            display:none;
          }
          .search-icon-box{
            position: absolute;
            right:-10px;
            top:-62px;
            padding: 10px;
            svg{
              @include color(tc-secondary);
            }
          }
          .search-right{
            width:100%;
            justify-content: space-between;
          }
        }
      }
    }
  }
}