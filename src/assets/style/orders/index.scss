.orders-tab-content{
    .orders-nav{
      ul{
        li{
          cursor: pointer;
          margin-left:20px;
          height:46px;
          line-height:46px;
          @include color(tc-secondary);
          &.active{
            position:relative;
            @include color(tc-primary);
            a{
              @include color(tc-primary);
            }
            &:after{
              content:'';
              display:block;
              width:16px;
              height:2px;
              position:absolute;
              bottom:0;
              left:50%;
              margin-left:-8px;
              @include bg-color(theme);
            }
          }
          a{
            display:block;
            height:46px;
            line-height:46px;
            @include color(tc-secondary);
          }
        }
      }
    }
  }
  .orders-search-container{
    .orders-search-wrapper-pc{
      padding:0 20px 12px;
      .el-button{
        margin-top:14px;
        margin-right:16px;
      }
      .checkbox-value{
        display:block;
        padding-top:12px;
      }
    }
    .orders-search-wrapper-m{
      display:none;
    }
  }
  .orders-search-box-input{
    padding:10px;
    .el-input{
      .el-input__wrapper{
        border-radius:4px;
        .el-input__inner{
          height:36px !important;
        }
      }
    }
  }
  @include mb {
    .orders-tab-content{
      margin:0 16px;
      .orders-nav{
        ul{
          li{
            margin-left:0px;
            margin-right:16px;
            height:44px;
            line-height:44px;
            font-size:14px;
            &.active{
              &:after{
                width:10px;
                margin-left:-5px;
                bottom:6px;
              }
            }
            a{
              display:block;
              height:44px;
              line-height:44px;
            }
          }
        }
      }
    }
    .orders-search-container{
      display:flex;
      align-items:center;
      justify-content: flex-end;
      .orders-search-wrapper-pc{
        display:none;
      }
      .orders-search-wrapper-m{
        display:flex;
        flex:1;
        margin:0 16px;
        height:32px;
        &.isBill{
          margin:0;
        }
        .select-wrap{
          font-size:14px;
          cursor: pointer;
          @include color(tc-secondary);
          &.fit-tc-primary{
            @include color(tc-primary);
          }
        }
        .tag-icon{
          width:16px;
          height:16px;
          margin-left:16px;
          cursor:pointer;
          &.date-icon-m{
            background-size:100% auto;
            @include get-img('@/assets/images/common/date-icon-m-light.png', '@/assets/images/common/date-icon-m-dark.png');
          }
          &.select-icon-m{
            background-size:100% auto;
            @include get-img('@/assets/images/common/select-icon-m-light.png', '@/assets/images/common/select-icon-m-dark.png');
          }
        }
      }
    }
    .orders-container-content{
      height: calc(100% - 44px);
      overflow: inherit;
    }
  }