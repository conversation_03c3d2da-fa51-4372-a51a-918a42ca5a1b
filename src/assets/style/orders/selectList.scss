.list-select-container{
  .list-select-wrapper{
    .select-input{
      width:460px;
      .selected-input-box{
        height:44px;
        border:1px solid;
        border-radius:4px;
        padding:0 12px;
        @include border-color(border);
        @include pc-hover {
          &:hover{
            @include border-color(tc-tertiary);
          }
        }
        &.active{
          @include border-color(theme);
        }
        &.withdrawal{
          height:62px;
        }
        .selected-left{
          .icon-box{
            width:24px;
            height:auto;
            font-size:24px;
            margin-right:8px;
          }
          span{
            font-size:14px;
            margin-right:8px;
            font-weight:500;
            @include color(tc-primary);
          }
          em{
            font-style:inherit;
            @include color(tc-secondary);
          }
          .placeholder-txt{
            font-size:14px;
            @include color(tc-secondary);
          }
        }
      }
    }
    .select-hot-list{
      width:460px;
      display:flex;
      flex-wrap:wrap;
      margin-right:-20px;
      li{
        margin-top:12px;
        cursor:pointer;
        padding:8px;
        border-radius:6px;
        font-size:14px;
        font-weight:500;
        margin-right:20px;
        @include bg-color(bg-quaternary);
        @include pc-hover {
          &:hover{
            @include color(theme);
          }
        }
        .icon-box{
          width:16px;
          height:auto;
          font-size:16px;
        }
      }
    }
  }
}
.el-popper{
  &.select-list-popover{
    padding:0 !important;
    .list-select-body{
      // max-height: 480px;
      overflow-y: auto;
      &.small{
        // max-height:320px;
      }
      .select-body-wrap{
        padding:8px 0;
        .select-title{
          padding:12px 24px;
          @include color(tc-secondary);
          svg{
            font-size:16px !important;
            cursor:pointer;
            @include pc-hover {
              &:hover{
                @include color(theme);
              }
            }
          }
        }
        .select-special-box{
          ul{
            padding:0 4px 12px 24px;
            display:flex;
            flex-wrap:wrap;
            li{
              cursor:pointer;
              padding:8px;
              border-radius:6px;
              font-size:14px;
              font-weight:500;
              margin-right:20px;
              @include bg-color(bg-quaternary);
              @include pc-hover {
                &:hover{
                  @include color(theme);
                }
              }
              .icon-box{
                width:16px;
                height:auto;
                font-size:16px;
              }
            }
          }
        }
        .select-coin-list-box{
          max-height:400px;
          overflow:auto;
          .item-title-box{
            padding:8px 16px;
            .add-item, .manger-item{
              cursor: pointer;
              font-size:14px;
              display:flex;
              align-items: center;
              @include color(tc-primary);
              svg{
                display: block;
                font-size:14px !important;
                margin-left:4px;
              }
            }
            .manger-item{
              @include color(theme);
            }
          }
          ul{
            li{
              display:flex;
              padding:12px 16px;
              align-items:center;
              cursor:pointer;
              .verify-icon-text{
                font-size:12px;
                padding:2px 8px;
                border-radius:2px;
                background-color:rgba(59, 193, 137, 0.1);
                @include color(rise);
              }
              .white-address-tag{
                margin-top:8px;
                font-size:12px;
                padding:4px 8px;
                border-radius:4px;
                white-space: nowrap;
                @include bg-color(bg-secondary);
                @include color(tc-primary);
              }
              &.active{
                @include color(theme);
              }
              &.noflex{
                display:block;
              }
              &.disabled{
                cursor:not-allowed;
                span, p{
                  @include color(tc-secondary);
                }
                @include pc-hover {
                  &:hover{
                    @include bg-color(bg-primary);
                  }
                }
              }
              @include pc-hover {
                &:hover{
                  @include bg-color(bg-quaternary);
                }
              }
              .list-p{
                width:100%;
                span, em{
                  display:inline-block;
                }
              }
              p{
                padding-top:8px;
                @include color(tc-primary);
              }
              .icon-box{
                width:24px;
                height:auto;
                font-size:24px;
                margin-right:8px;
              }
              span{
                font-size:16px;
                margin-right:8px;
                display:block;
                @include color(tc-primary);
              }
              em{
                font-size:14px;
                margin-right:8px;
                display:block;
                font-style:inherit;
                @include color(tc-secondary);
              }
              i{
                font-style: inherit;
                font-size:14px;
                @include color(tc-primary);
              }
            }
          }
        }
        .bottom-cont-box{
          padding:12px 0 4px;
          .add-item{
            width:160px;
            height:38px;
            line-height:38px;
            cursor: pointer;
            text-align:center;
            border:1px solid;
            border-radius:25px;
            @include border-color(border);
            @include color(tc-primary);
          }
        }
      }
    }
  }
}
.orders-search-box-input{
  padding:10px;
  .el-input{
    .el-input__wrapper{
      border-radius:4px;
      .el-input__inner{
        height:36px !important;
      }
    }
  }
}
@include mb {
  .list-select-container{
    .list-select-wrapper{
      .select-input{
        width:100%;
        .selected-input-box{
          height:44px;
          border:1px solid;
          border-radius:4px;
          padding:0 12px;
          @include border-color(border);
          .selected-left{
            .icon-box{
              width:24px;
              height:auto;
              font-size:24px;
              margin-right:8px;
            }
            span{
              font-size:14px;
              margin-right:8px;
              font-weight:500;
              @include color(tc-primary);
            }
            em{
              font-style:inherit;
              @include color(tc-secondary);
            }
            .placeholder-txt{
              font-size:14px;
              @include color(tc-secondary);
            }
          }
        }
      }
      .select-hot-list{
        width:100%;
        display:flex;
        flex-wrap:wrap;
        margin-right:-20px;
        li{
          margin-top:12px;
          cursor:pointer;
          padding:8px;
          border-radius:6px;
          font-size:14px;
          font-weight:500;
          margin-right:20px;
          @include bg-color(bg-quaternary);
          @include pc-hover {
            &:hover{
              @include color(theme);
            }
          }
          .icon-box{
            width:16px;
            height:auto;
            font-size:16px;
          }
        }
      }
    }
  }
  .el-popper{
    &.select-list-popover{
      padding:0 !important;
      .list-select-body{
        max-height: 480px;
        overflow-y: auto;
        &.small{
          max-height:320px;
        }
        .select-body-wrap{
          padding:8px 0;
          .select-title{
            padding:12px 24px;
            @include color(tc-secondary);
            svg{
              font-size:16px !important;
              cursor:pointer;
              @include pc-hover {
                &:hover{
                  @include color(theme);
                }
              }
            }
          }
          .select-special-box{
            ul{
              padding:0 4px 12px 24px;
              display:flex;
              flex-wrap:wrap;
              li{
                cursor:pointer;
                padding:8px;
                border-radius:6px;
                font-size:14px;
                font-weight:500;
                margin-right:20px;
                @include bg-color(bg-quaternary);
                @include pc-hover {
                  &:hover{
                    @include color(theme);
                  }
                }
                .icon-box{
                  width:16px;
                  height:auto;
                  font-size:16px;
                }
              }
            }
          }
          .select-coin-list-box{
            ul{
              li{
                display:flex;
                padding:12px 24px;
                align-items:center;
                cursor:pointer;
                @include pc-hover {
                  &:hover{
                    @include bg-color(bg-quaternary);
                  }
                }
                .icon-box{
                  width:24px;
                  height:auto;
                  font-size:24px;
                  margin-right:8px;
                }
                span{
                  font-size:16px;
                  margin-right:8px;
                  display:block;
                  @include color(tc-primary);
                }
                em{
                  font-size:14px;
                  margin-right:8px;
                  display:block;
                  font-style:inherit;
                  @include color(tc-secondary);
                }
                i{
                  font-style: inherit;
                  font-size:14px;
                  @include color(tc-primary);
                }
              }
            }
          }
        }
      }
    }
  }
}