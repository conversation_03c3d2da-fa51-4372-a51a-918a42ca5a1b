/*
* 重置浏览器默认样式
*/

/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */

/* Document
========================================================================== */

*,
*::before,
*::after {
  outline: none;
  box-sizing: border-box;
  margin: 0;
}

a {
  text-decoration: none;
  background-color: transparent;
}

a,
a:link,
a:visited,
a:hover,
a:active {
  text-decoration: none;
}

/**
  * 1. Correct the line height in all browsers.
  * 2. Prevent adjustments of font size after orientation changes in iOS.
  */

html,
body {
  width: 100%;
  height: 100%;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  font-weight: 400;
}

/* Sections
  ========================================================================== */

/**
  * Remove the margin in all browsers.
  */

body {
  margin: 0;
  -webkit-font-smoothing: antialiased;
  font-weight: 400;
}

iframe {
  border: none;
}

/**
  * Render the `main` element consistently in IE.
  */

main {
  display: block;
}

/* Grouping content
  ========================================================================== */

/**
  * 1. Add the correct box sizing in Firefox.
  * 2. Show the overflow in Edge and IE.
  */

hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}

/**
  * 1. Correct the inheritance and scaling of font size in all browsers.
  * 2. Correct the odd `em` font sizing in all browsers.
  */

pre {
  font-family: monospace;
  font-size: 1em;
}

/* Text-level semantics
  ========================================================================== */

/**
  * 1. Remove the bottom border in Chrome 57-
  * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
  */

abbr[title] {
  border-bottom: none;
  text-decoration: underline;
  text-decoration: underline dotted;
}

/**
  * Add the correct font weight in Chrome, Edge, and Safari.
  */

b,
strong {
  font-weight: bolder;
}
.el-button:focus-visible{
  outline:0 !important;
}
/**
  * 1. Correct the inheritance and scaling of font size in all browsers.
  * 2. Correct the odd `em` font sizing in all browsers.
  */

code,
kbd,
samp {
  font-family: monospace;
  font-size: 1em;
}

/**
  * Add the correct font size in all browsers.
  */

small {
  font-size: 80%;
}

/**
  * Prevent `sub` and `sup` elements from affecting the line height in
  * all browsers.
  */

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/* Embedded content
  ========================================================================== */

/**
  * Remove the border on images inside links in IE 10.
  */

img {
  border-style: none;
}

/* Forms
  ========================================================================== */

/**
  * 1. Change the font styles in all browsers.
  * 2. Remove the margin in Firefox and Safari.
  */

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
}

/**
  * Show the overflow in IE.
  * 1. Show the overflow in Edge.
  */

button,
input {
  overflow: visible;
}

/**
  * Remove the inheritance of text transform in Edge, Firefox, and IE.
  * 1. Remove the inheritance of text transform in Firefox.
  */

button,
select {
  text-transform: none;
}

/**
  * Correct the inability to style clickable types in iOS and Safari.
  */

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}

/**
  * Remove the inner border and padding in Firefox.
  */

button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

/**
  * Restore the focus styles unset by the previous rule.
  */

button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
  outline: 1px dotted ButtonText;
}

/**
  * Correct the padding in Firefox.
  */

fieldset {
  padding: 0.35em 0.75em 0.625em;
}

/**
  * 1. Correct the text wrapping in Edge and IE.
  * 2. Correct the color inheritance from `fieldset` elements in IE.
  * 3. Remove the padding so developers are not caught out when they zero out
  *  `fieldset` elements in all browsers.
  */

legend {
  box-sizing: border-box;
  color: inherit;
  display: table;
  max-width: 100%;
  padding: 0;
  white-space: normal;
}

.nuxt-progress {
  display: none;
}

/**
  * Add the correct vertical alignment in Chrome, Firefox, and Opera.
  */

progress {
  vertical-align: baseline;
}

/**
  * Remove the default vertical scrollbar in IE 10+.
  */

textarea {
  overflow: auto;
}

/**
  * 1. Add the correct box sizing in IE 10.
  * 2. Remove the padding in IE 10.
  */

[type="checkbox"],
[type="radio"] {
  box-sizing: border-box;
  padding: 0;
}

/**
  * Correct the cursor style of increment and decrement buttons in Chrome.
  */

[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

/**
  * 1. Correct the odd appearance in Chrome and Safari.
  * 2. Correct the outline style in Safari.
  */

[type="search"] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

/**
  * Remove the inner padding in Chrome and Safari on macOS.
  */

[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
  * 1. Correct the inability to style clickable types in iOS and Safari.
  * 2. Change font properties to `inherit` in Safari.
  */

::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

/* Interactive
  ========================================================================== */

/*
  * Add the correct display in Edge, IE 10+, and Firefox.
  */

details {
  display: block;
}

/*
  * Add the correct display in all browsers.
  */

summary {
  display: list-item;
}

/* Misc
  ========================================================================== */

/**
  * Add the correct display in IE 10+.
  */

template {
  display: none;
}

/**
  * Add the correct display in IE 10.
  */

[hidden] {
  display: none;
}

ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

li {
  list-style: none;
  padding: 0;
  margin: 0;
}

*::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.xc-show-scrollbar {
  &::-webkit-scrollbar {
    width: 8px;
  }
}

*::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background: #c0c4cc;
}

*::-webkit-scrollbar-track {
  border: 1px solid #ebeef5;
  background: #fff;
  border-radius: 4px;
}

th {
  font-weight: 400;
}

svg {
  width: 1em;
  height: 1em;
}

.pdf-app .pdfViewer .page {
  border-image: none !important;
  border: none !important;
  margin: 0 auto !important;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type="number"] {
  -moz-appearance: textfield;
}