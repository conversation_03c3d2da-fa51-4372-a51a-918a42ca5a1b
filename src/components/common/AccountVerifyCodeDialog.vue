<template>
  <el-dialog :title="$t(dialogTitle)" v-model="dialogTableVisible" class="account-verify-dialog" append-to-body :close-on-click-modal="false" @close="handleClose" width="480">
    <div class="codeInput-text">{{ $t('谷歌验证码') }}</div>
    <GoogleCodeInputMin v-model="code" :focus="codeFocus" :input-error="inputError" @blur="codeFocus = false;" @focus="emit('focus', code)" />
    <slot />
    <el-button :disabled="isDisabled" class="google-btn" type="primary" @click="success">{{ $t('确认') }}</el-button>
  </el-dialog>
</template>
<script lang="ts" setup>
import { ElDialog, ElButton } from 'element-plus'
import GoogleCodeInputMin from './GoogleCodeInput.vue'
const props = defineProps({
  dialogTitle: {
    type: String,
    default: '安全验证'
  },
  dialogVisible: {
    type: Boolean,
    default: false
  },
  params: { // 接口所需的参数
    type: Object,
    default () {
      return {}
    }
  },
  isBindGoogle: {
    type: Boolean,
    default: true
  }
})
const code = ref('')
const isDisabled = computed(() => {
  return code.value.length < 6
})
const dialogTableVisible = ref(false)
const emit = defineEmits(['handleClose', 'request'])
const handleClose = () => {
  emit('handleClose')
}
const success = () => {
  emit('request', code.value)
}
onMounted (() => {
  dialogTableVisible.value = props.dialogVisible
})
</script>
<style lang="scss">
.el-dialog{
  &.account-verify-dialog{
    .codeInput-text{
      font-size:14px;
      padding-bottom:8px;
      line-height:20px;
      @include color(tc-primary);
    }
    .goInstall-text{
      font-size:14px;
      padding-top:8px;
      a{
        @include color(theme);
      }
    }
    .google-btn{
      margin-top:32px;
      width:100%;
      height:40px;
    }
  }
}
@include mb {
}
</style>
