<template>
  <img v-if="isUseDefault" key="svg" :src="defaultIcon" :class="size ? `font-size-${size}` : ''" class="box-svg" />
  <img v-else-if="isUseLocalImg" ref="localSvg" key="svg-local" v-lazy="localSvg" :class="size ? `font-size-${size}` : ''" class="box-svg local-img" />
  <img v-else-if="isUseImg" :key="imgSrc ?'img' : 'svg-default'" :src="imgSrc ? imgSrc : defaultIcon" :class="size ? `font-size-${size}` : ''" class="box-svg" alt="icon" />
  <img v-else key="svg-default" :src="defaultIcon" :class="size ? `font-size-${size}` : ''" class="box-svg" />
</template>

<script>
import { imageUrl } from '~/utils'
import defaultIcon from '~/assets/svg-icon/icon-default-icon.svg'
export default {
  name: 'BoxSvgIcon',
  props: {
    icon: {
      type: String,
      required: true
    },
    size: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      defaultIcon,
      isUseDefault: false,
      isUseImg: false,
      isUseLocalImg: false,
      localSvg: ''
    }
  },
  computed: {
    // 通过icon 获取svg文件名
    iconName () {
      return `#icon-icon-${this.icon}`
    },
    iconUrl () {
      if (this.icon) {
        return `${this.icon}${this.icon.includes('.png') ? '?x-oss-process=image/format,webp' : ''}`
      }
      return ''
    },
    imgSrc () {
      return imageUrl(this.iconUrl)
    }
  },
  watch: {
    icon (v, ov) {
      if (v && v === ov) {
        return
      }
      this.isUseImg = false
      this.isUseDefault = false
      this.verifyIconExist()
    }
  },
  async mounted () {
    this.verifyIconExist()
  },
  methods: {
    async verifyIconExist () {
      this.tryLoadImg()
    },
    tryLoadImg () {
      this.isUseLocalImg = false
      this.isUseImg = true
      if (this.$refs.img) {
        this.$refs.img.onerror = () => {
          this.isUseDefault = true
          this.isUseImg = false
        }
      }
    }
  }
}
</script>

<style scoped>
.box-svg {
  width: 1em;
  min-width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
</style>
