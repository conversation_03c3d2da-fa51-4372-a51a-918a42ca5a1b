<template>
  <div class="loading-bk" style="position:absolute;top:0;left:0;bottom:0;right:0;z-index:99;">
    <div style="margin-top:-24px;position:absolute;text-align:center;top:50%;width:100%;">
      <svg viewBox="0 0 50 50" style="animation:loading-rotate 2s linear infinite;display:inline;height:48px;width:48px;">
        <circle cx="25" cy="25" r="20" fill="none" style="animation:loading-dash 1.5s ease-in-out infinite;stroke-dasharray:90,150;stroke-dashoffset: 0;stroke-width:2;stroke:#f0b90b;stroke-linecap:round;">
        </circle>
      </svg>
    </div>
  </div>
</template>
<style lang="scss" scoped>
  .loading-bk{
    @include bg-color(bg-primary);
  }
</style>

