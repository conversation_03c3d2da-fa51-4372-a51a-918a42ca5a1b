<template>
  <div class="box-no-data-wrap flex-box space-center">
    <div class="no-data-icon">
      <div class="null-icon no-order-icon"></div>
      <slot />
      <p>{{ text }}</p>
    </div>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps({
  text: {
    type: String,
    default: ''
  }
})
</script>
<style lang="scss">
.box-no-data-wrap{
  height: calc(100%);
  .no-data-icon{
    .null-icon{
      width:120px;
      height:120px;
      margin:0 auto;
      &.no-order-icon{
        background-size:100% auto;
        @include get-img('@/assets/images/common/no-order-data-light.png', '@/assets/images/common/no-order-data-dark.png');
      }
    }
    p{
      text-align:center;
      font-size:14px;
      padding-top:16px;
      @include color(tc-secondary);
    }
  }
}
</style>