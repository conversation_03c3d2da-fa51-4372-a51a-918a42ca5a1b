<template>
  <div ref="qrcodeWrap" :style="qrcodeStyle" class="qrcode-normal"></div>
</template>

<script lang="ts" setup>
import QRCode from 'qrcode' // 确保这是正确的导入方式
const props = defineProps({
  value: {
    default: '',
    type: String
  },
  size: {
    default: 128,
    type: Number
  },
  logo: {
    default: '',
    type: String
  },
  padding: {
    default: 0,
    type: [Number, String]
  },
  level: {
    type: String,
    default: 'L' // L M Q H
  }
})

const qrcodeWrap = ref<HTMLDivElement | null>(null)
const qrcodeStyle = computed(() => ({
  width: `${props.size}px`,
  height: `${props.size}px`,
  minWidth: `${props.size}px`,
  minHeight: `${props.size}px`,
  padding: `${props.padding}px`
}))
const generateQRCode = async () => {
  if (!qrcodeWrap.value || !process.client) return
  try {
    const options = {
      errorCorrectionLevel: props.level,
      width: props.size,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#ffffff'
      }
    }

    // 总是创建一个新的canvas元素
    const canvas = document.createElement('canvas')
    
    // 生成二维码到canvas
    await QRCode.toCanvas(canvas, props.value, options)

    // 如果有logo，添加logo
    if (props.logo) {
      const ctx = canvas.getContext('2d')
      if (ctx) {
        const img = new Image()
        img.crossOrigin = 'Anonymous'
        img.src = props.logo
        
        await new Promise((resolve, reject) => {
          img.onload = resolve
          img.onerror = reject
        })
        
        const logoSize = props.size * 0.2
        const x = (props.size - logoSize) / 2
        const y = (props.size - logoSize) / 2
        
        // 绘制白色背景
        ctx.beginPath()
        ctx.roundRect(x, y, logoSize, logoSize, 8)
        ctx.fillStyle = '#ffffff'
        ctx.fill()
        
        // 绘制logo
        ctx.drawImage(img, x, y, logoSize, logoSize)
      }
    }

    // 清空容器并添加新canvas
    qrcodeWrap.value.innerHTML = ''
    qrcodeWrap.value.appendChild(canvas)
  } catch (err) {
    console.error('生成二维码失败:', err)
  }
}
onMounted(() => {
  generateQRCode()
})
watch(() => [props.value, props.logo, props.size, props.level], () => {
  generateQRCode()
})
</script>

<style lang="scss">
.qrcode-normal {
  display: flex;
  background-color: #fff;
  box-sizing: border-box;
  align-items: center;
  justify-content: center;
  
  canvas {
    width: 100% !important;
    height: 100% !important;
  }
}
</style>