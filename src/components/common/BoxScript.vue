<template>
</template>
<script lang="ts" setup>
  const props = defineProps({
    src: { type: String, required: true },
    id: { type: String, required: true },
    appendToBody: { type: Boolean, default: false },
  });

  const emit = defineEmits(['onload', 'onerror'])

  const isLoaded = ref(false);

  onBeforeMount(async () => {
    try {
      const script = document.createElement('script');
      script.src = props.src
      script.id = props.id
      script.async = true;
      document.body.appendChild(script);

      // 如果需要等待脚本加载完成后再执行某些操作，你可能需要监听 script 的 load 事件
      script.onload = () => {
        emit('onload');
      };
    } catch (error) {
      emit('onerror')
    }
  })
</script>
