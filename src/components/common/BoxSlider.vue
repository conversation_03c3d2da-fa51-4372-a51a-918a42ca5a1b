<template>
  <div class="slider-demo-block-levelage">
    <div class="slider-track-clickable" ref="track" @mousedown="handleTrackMouseDown">
      <el-slider v-model="multiple" :min="0" :max="max" :marks="marks" />
    </div>
    <ul class="slider-dot-list">
      <li 
        v-for="(item, index) in multipleList" 
        :key="index"
        v-show="multiple >= item"
        :style="{ left: `${calculatePosition(item)}%` }"
        @click="changeValue(item)"
      ></li>
    </ul>
  </div>
</template>
<script lang="ts" setup>
  import { ElSlider } from 'element-plus'
  const multiple = ref('')
  const props = defineProps({
    max: {
      type: [String, Number],
      default: ''
    },
    modelValue: {
      type: [String, Number],
      default: ''
    },
    typeStr: {
      type: String,
      default: ''
    },
    defaultValue: {
      type: [String, Number],
      default: ''
    },
    marksValue: {
      type: Object,
      default () {
        return []
      }
    }
  })
  const emit = defineEmits(['update:modelValue'])
  const multipleList = ref([])
  const marks = computed(() => {
    if (props.typeStr === '%') {
      return multipleList.value.reduce((obj, item) => {
        if(item * 1 > 1 && item * 1 < 100) {
          obj[item] = ''
        } else {
          obj[item] = `${item}%`;
        }
        return obj
      }, {})
    }
    return multipleList.value.reduce((obj, item) => {
      obj[item] = `${item}x`;
      return obj;
    }, {})
  })
  const generateScaledArray = (maxValue) => {
    const ratios = [0.01, 0.2, 0.4, 0.6, 0.8, 1]
    return ratios.map(ratio => {
      const value = Math.round(maxValue * ratio)
      return value === 0 && maxValue > 0 ? 1 : value
    })
  }
  watch(() => props.modelValue, (val) => {
    console.log(val, 'djdjeidjeijeijie')
    changeValue(val)
  })
  const changeValue = (val) => {
    multiple.value = val
  }
  const calculatePosition = (value) => {
    if (value * 1 > 40) {
      return `${(((value - 1) / (props.max - 1)) * 100) - 0.999}`;
    } else {
      return `${(((value - 1) / (props.max - 1)) * 100)}`;
    }
  }
  const slider = ref() // el-slider 实例
  const track = ref()  // 轨道容器实例
  const handleTrackMouseDown = (e: MouseEvent) => {
    e.preventDefault()
    // 1. 计算点击位置对应的值
    const trackRect = track.value.getBoundingClientRect()
    const percent = (e.clientX - trackRect.left) / trackRect.width
    const newValue = Math.round(
      1 + percent * (props.max - 1)
    )
    // 2. 更新滑块值
    multiple.value = Math.max(1, Math.min(props.max, newValue))
    // 3. 绑定全局鼠标事件实现拖动
    const handleMouseMove = (moveE: MouseEvent) => {
      const movePercent = (moveE.clientX - trackRect.left) / trackRect.width
      const moveValue = Math.round(
        1 + movePercent * (props.max - 1)
      )
      multiple.value = Math.max(1, Math.min(props.max, moveValue))
    }
    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }
  watch(() => multiple.value, (val) => {
    emit('update:modelValue', val)
  })
  onMounted(() => {
    multiple.value = props.defaultValue || props.modelValue
    multipleList.value = props.marksValue.length > 0 ? props.marksValue : generateScaledArray(props.max)
  })
</script>
<style lang="scss">
  .slider-demo-block-levelage{
    padding:0 10px;
    position:relative;
    .slider-track-clickable {
      position: relative;
      cursor: pointer;
    }
    .slider-dot-list{
      position:absolute;
      top:12px;
      left:10px;
      right:10px;
      li{
        position:absolute;
        top:0;
        border-radius:50%;
        width:8px;
        height:8px;
        border: 1px solid;
        cursor:pointer;
        @include bg-color(tc-primary);
        @include border-color(theme);
        // &:nth-child(1) {
        //   left:-1%;
        // }
        // &:nth-child(2) {
        //   left:18.5%;
        // }
        // &:nth-child(3) {
        //   left:38.5%;
        // }
        // &:nth-child(4) {
        //   left:58.5%;
        // }
        // &:nth-child(5) {
        //   left:78.9%;
        // }
        &:hover{
          transform:scale(1.2);
        }
      }
    }
    .el-tooltip__popper {
        background-color: #f56c6c; /* 修改气泡的背景色 */
        color: #fff; /* 修改气泡的文本颜色 */
      }
    .el-slider{
      .el-slider__runway{
        height:2px;
        @include bg-color(bg-secondary);
      }
      .el-slider__button-wrapper{
        top:-17px;
      }
      .el-slider__button{
        width:9px;
        height:9px;
        border:1px solid;
        @include bg-color(tc-primary);
        @include border-color(theme);
      }
      .el-slider__bar{
        height:2px;
      }
      .el-slider__stop{
        width:6px;
        height:6px;
        border:1px solid;
        top:-2px;
        @include bg-color(bg-secondary);
        @include border-color(border);
        &.active{
          @include bg-color(tc-primary);
          @include border-color(theme);
        }
      }
      .el-slider__marks-text{
        margin-top:8px;
      }
    }
  }
</style>