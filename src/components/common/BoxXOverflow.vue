<template>
  <div ref="swiperScrollRef" class="left-right-swiper-scroll-box">
    <div v-show="showLeftIcon" class="scroll-icon left-icon" @click="handleLeftClick">
      <MonoRightArrowShort size="14" />
    </div>
    <div ref="swiperScrollContentRef" class="swiper-scroll-content">
      <slot></slot>
    </div>
    <div v-show="showRightIcon" class="scroll-icon right-icon" @click="handleRightClick">
      <MonoRightArrowShort size="14" />
    </div>
  </div>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'LeftRightSwiperScroll'
})

const props = defineProps({
  swiperList: {
    type: Object,
    default: () => ({})
  }
})

const showRightIcon = ref(false)
const showLeftIcon = ref(false)
const swiperScrollRef = ref<HTMLElement | null>(null)
const swiperScrollContentRef = ref<HTMLElement | null>(null)
const scrollPosition = ref(0)
const maxScrollPosition = ref(0)

const updateScrollState = () => {
  if (!swiperScrollRef.value || !swiperScrollContentRef.value) return
  
  const containerWidth = swiperScrollRef.value.getBoundingClientRect().width
  const contentWidth = swiperScrollContentRef.value.getBoundingClientRect().width
  
  maxScrollPosition.value = Math.max(0, contentWidth - containerWidth)
  showRightIcon.value = scrollPosition.value < maxScrollPosition.value
  showLeftIcon.value = scrollPosition.value > 0
}

const handleRightClick = () => {
  if (!swiperScrollRef.value || !swiperScrollContentRef.value) return
  
  const containerWidth = swiperScrollRef.value.getBoundingClientRect().width
  const remainingSpace = maxScrollPosition.value - scrollPosition.value
  
  // 计算需要滚动的距离，确保不会滚动过头
  const scrollDistance = Math.min(remainingSpace, containerWidth * 0.8)
  
  scrollPosition.value += scrollDistance
  swiperScrollContentRef.value.style.transform = `translateX(-${scrollPosition.value}px)`
  
  // 更新按钮状态
  updateScrollState()
}

const handleLeftClick = () => {
  if (!swiperScrollRef.value || !swiperScrollContentRef.value) return
  
  const containerWidth = swiperScrollRef.value.getBoundingClientRect().width
  const scrollDistance = Math.min(scrollPosition.value, containerWidth * 0.8)
  
  scrollPosition.value -= scrollDistance
  swiperScrollContentRef.value.style.transform = `translateX(-${scrollPosition.value}px)`
  
  // 更新按钮状态
  updateScrollState()
}

const resetScroll = () => {
  scrollPosition.value = 0
  if (swiperScrollContentRef.value) {
    swiperScrollContentRef.value.style.transform = 'translateX(0)'
  }
  updateScrollState()
}

// 使用ResizeObserver监听尺寸变化
let resizeObserver: ResizeObserver | null = null

onMounted(() => {
  updateScrollState()
  
  resizeObserver = new ResizeObserver(() => {
    updateScrollState()
  })
  
  if (swiperScrollRef.value && swiperScrollContentRef.value) {
    resizeObserver.observe(swiperScrollRef.value)
    resizeObserver.observe(swiperScrollContentRef.value)
  }
})

onBeforeUnmount(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
})

defineExpose({
  resetScroll
})
</script>

<style lang='scss' scoped>
.left-right-swiper-scroll-box {
  position: relative;
  width: 100%;
  overflow: hidden;
  
  .scroll-icon {
    position: absolute;
    top: 0;
    width: 24px;
    height: 100%;
    z-index: 9;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    @include bg-color(bg-primary);
    
    i {
      font-size: 16px;
      @include color(tc-secondary);
    }
    
    &.left-icon {
      left: 0;
      transform: rotate(180deg);
      @include color(tc-secondary);
    }
    
    &.right-icon {
      right: 0;
      @include color(tc-secondary);
    }
  }
  
  .swiper-scroll-content {
    display: inline-flex;
    white-space: nowrap;
    transition: transform 0.3s ease;
    will-change: transform;
    align-items: center;
  }
}
</style>