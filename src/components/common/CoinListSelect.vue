<template>
  <div class="list-select-container" :style="{'width': width}">
    <div class="list-select-wrapper" :style="{'width': width}">
      <el-popover :visible="visible" popper-class="select-list-popover" width="460">
        <template #reference>
          <div class="select-input" :style="{'width': width}">
            <el-input ref="searchInputRef" :placeholder="$t('搜索币种')" v-show="!isShowSelected" type="text" v-model="searchText" clearable @focus="focusFun" @blur="blurFun">
              <template #prepend>
                <MonoSearch size="16" />
              </template>
            </el-input>
            <div v-if="isShowSelected" class="selected-input-box flex-box space-between" @click="focusSelect">
              <div v-if="!isNFT" class="selected-left flex-box">
                <BoxCoinIcon :icon="curItem.icon_url" class="icon-box"/>
                <span>{{ curItem.general_name }}</span>
                <em>{{ curItem.name }}</em>
              </div>
              <div v-else class="selected-left flex-box">
                <span>{{ curItem.name }}</span>
              </div>
              <div class="selected-right">
                <MonoDownArrowMin :size="12" />
              </div>
            </div>
          </div>
        </template>
        <div class="list-select-body">
          <div class="select-body-wrap">
            <div v-if="historyList.length > 0 && !isWithdrawal" class="select-special-box">
              <div class="select-title flex-box space-between">
                {{ $t('历史搜索') }}
                <MonoDelete />
              </div>
              <ul>
                <li v-for="(item, index) in historyList" :key="index" @pointerdown="changeSymbol(item)">
                  <BoxCoinIcon :icon="item.general_name" class="icon-box"/>
                  {{ item.general_name }}
                </li>
              </ul>
            </div>
            <div v-if="hotEndList.length > 0 && !isNFT && !isWithdrawal" class="select-special-box">
              <div class="select-title flex-box space-between">
                {{ $t('热门') }}
              </div>
              <ul>
                <li v-for="(item, index) in hotEndList" :key="index" :class="{'disabled': isDisabled[item.general_name]}" @pointerdown="changeSymbol(item)">
                  <BoxCoinIcon :icon="item.icon_url" class="icon-box"/>
                  {{ item.general_name }}
                </li>
              </ul>
            </div>
            <div class="select-coin-list-box">
              <div class="select-title flex-box space-between">
                {{ $t('币种列表') }}
              </div>
              <ul v-if="filterCoinList.length > 0">
                <li v-for="(item, index) in filterCoinList" :key="index" class="flex-box space-between" :class="{'disabled': isDisabled[item.general_name]}" @pointerdown="changeSymbol(item)">
                  <div v-if="!isNFT" class="flex-box">
                    <BoxCoinIcon :icon="item.icon_url" class="icon-box"/>
                      <span>{{ item.general_name }}</span>
                      <em>{{ item.name }}</em>
                  </div>
                  <div v-else class="flex-box">
                      <span>{{ item.name }}</span>
                  </div>
                  <i v-if="item.enable_deposit * 1 === 0 && isDeposit" class="font-size-12 fit-tc-secondary">{{ $t('暂停充值') }}</i>
                  <i v-if="item.enable_withdraw * 1 === 0 && isWithdrawal" class="font-size-12 fit-tc-secondary">{{ $t('暂停提币') }}</i>
                </li>
              </ul>
              <div v-if="filterCoinList.length === 0" class="pd-b24">
                <BoxNoData :text="$t('暂无数据')" />
              </div>
            </div>
          </div>
        </div>
      </el-popover>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ElPopover, ElInput } from 'element-plus'
  import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
  import { hotPairList, getPairHistory } from '~/api/public'
  import { commonStore } from '~/stores/commonStore'
  import { getNftListApi } from '~/api/tf'
  const store = commonStore()
  const { getCoinList, getAssetsByCoin } = store
  const { coinList, mainAssetObj } = storeToRefs(store)
  const props = defineProps({
    width: {
      type: String,
      default: ''
    },
    modelValue: {
      type: String,
      default: ''
    },
    isDeposit: {
      type: Boolean,
      default: false
    },
    isWithdrawal: {
      type: Boolean,
      default: false
    },
    isNFT: {
      type: Boolean,
      default: false
    }
  })
  const emit = defineEmits(['update:modelValue'])
  const isShowSelected = ref<boolean>(false)
  const curItem = ref({})
  const visible = ref<boolean>(false)
  const searchText = ref<string|number>('')
  const searchInputRef = ref(null)
  const hotMap = ref({})
  const historyMap = ref({})
  const historyList = computed(() => {
    return !searchText.value ? coinList.value.filter((item) => {
      return historyMap.value[item.general_name]
    }) : []
  })
  const getHotList = async() => {
    const { data } = await hotPairList()
    if (data) {
      hotMap.value = {}
      data.forEach((item) => {
        hotMap.value[item.pair.split('_')[0]] = true
      })
    }
  }
  const getHistoryList = async() => {
    const { data } = await getPairHistory()
    if (data) {
      historyMap.value = {}
      data.forEach((item) => {
        historyMap.value[item.pair.split('_')[0]] = true
      })
    }
  }
  const hotEndList = computed(() => {
    return !searchText.value ? coinList.value.filter((item) => {
      return hotMap.value[item.general_name]
    }) : []
  })
  const nftList = ref([])
  const filterCoinList = computed(() => {
    // 1. 处理 NFT 列表
    let arr = []
    nftList.value.forEach((item) => {
      arr.push({
        general_name: item.id,
        name: `${item.name}(${item.symbol})`,
        enable_deposit: 1,
        enable_withdraw: 1
      })
    })
    // 1. NFT 列表逻辑：仅按 id 筛选
    if (props.isNFT) {
      return arr
    }
    // 2. 处理提币列表
    const withdrawalList = coinList.value.filter(
      (item) => item.enable_withdraw * 1 === 1 && mainAssetObj.value[item.general_name]
    );

    // 3. 根据 isNFT 和 isWithdrawal 选择数据源
    const sourceList = props.isWithdrawal ? withdrawalList : coinList.value;
    // 4. 如果没有 searchText，直接返回完整数据
    if (!searchText.value) {
      return sourceList;
    }
    // 5. 有 searchText 时，检查是否有匹配项
    const searchTerm = searchText.value.toUpperCase();
    const hasMatch = sourceList.some(item => 
      item.general_name.toUpperCase().includes(searchTerm)
    );
    // 6. 如果没有匹配项，返回空数组
    if (!hasMatch) {
      return [];
    }
    // 7. 有匹配项时，返回所有数据，但匹配项排在前面
    return [...sourceList].sort((a, b) => {
      const nameA = a.general_name.toUpperCase();
      const nameB = b.general_name.toUpperCase();
      const aMatches = nameA.includes(searchTerm);
      const bMatches = nameB.includes(searchTerm);

      // 匹配的项排在前面
      if (aMatches && !bMatches) return -1;
      if (!aMatches && bMatches) return 1;

      // 如果都匹配或都不匹配，按原顺序或字母排序（可选）
      return nameA.localeCompare(nameB);
    });
  });
  const focusFun = () => {
    visible.value = true
    searchText.value = props.isNFT ? curItem.value.name : curItem.value.general_name
  }
  const blurFun = () => {
    visible.value = false
    if (searchText.value === '' || searchText.value === undefined) {
      curItem.value = {}
      isShowSelected.value = false
      emit('update:modelValue', searchText.value)
    } else {
      filterCoinList.value.forEach((item) => {
        if (searchText.value === item.general_name && !props.isNFT) {
          emit('update:modelValue', searchText.value || '')
        } else if (searchText.value === item.name && props.isNFT) {
          emit('update:modelValue', item.general_name || '')
        } else {
          isShowSelected.value = true
        }
      })
    }
  }
  const isDisabled = computed(() => {
    const isDisabled = {}
    coinList.value.forEach((item) => {
      isDisabled[item.general_name] = (item.enable_deposit * 1 === 0 && props.isDeposit) || (item.enable_withdraw * 1 === 0 && props.isWithdrawal)
    })
    return isDisabled
  })
  const changeSymbol = (item) => {
    if (item.enable_deposit * 1 === 0 && props.isDeposit) {
      return false
    }
    if (item.enable_withdraw * 1 === 0 && props.isWithdrawal) {
      return false
    }
    console.info(item, 'item.dhdddggdgd')
    visible.value = false
    isShowSelected.value = true
    searchText.value = item.general_name
    curItem.value = item
    emit('update:modelValue', item.general_name)
  }
  watch(() => props.modelValue, (val) => {
    if (val !== '') {
      isShowSelected.value = true
    } else {
      isShowSelected.value = false
    }
  }, {
    immediate: true
  })
  watch(() => filterCoinList.value, (val) => {
    if (val.length > 0 && props.modelValue !== '') {
      curItem.value = val.filter((item) => {
        return item.general_name === props.modelValue
      })[0]
    }
  }, {
    immediate: true
  })
  const focusSelect = () => {
    visible.value = true
    if (searchInputRef.value) {
      nextTick(() => {
        searchInputRef.value.focus()
      })
    }
    isShowSelected.value = false
    searchText.value = props.modelValue
  }
  const getNftList = async() => {
    const { data } = await getNftListApi()
    if (data) {
      nftList.value = data
    }
  }
  onBeforeMount(() => {
    if (props.isNFT) {
      getNftList()
    }
    getHotList()
    getHistoryList()
    getCoinList()
    getAssetsByCoin()
  })
</script>
<style lang="scss">
@import url('@/assets/style/orders/selectList.scss');
</style>