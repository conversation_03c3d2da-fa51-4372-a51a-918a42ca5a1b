<template>
  <el-dialog v-model="visible" @close="emit('close')" width="320">
    <div class="flex-box space-center flex-column">
      <MonoWarn size="42" class="fit-warn" />
      <div class="fit-tc-primary font-size-16 pd-tb24">{{ $t('您还没有绑定谷歌验证码') }}</div>
      <el-button type="primary" @click="goSet()">{{ $t('去绑定') }}</el-button>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
  import MonoWarn from '~/components/common/icon-svg/MonoWarn.vue'
  const router = useRouter()
  const { locale, t } = useI18n()
  const props = defineProps({
    isShow: {
      type: Boolean,
      default: false
    }
  })
  const emit = defineEmits(['close'])
  const visible = ref(false)
  const goSet = () => {
    router.push(`/${locale.value}/my/account/security`)
    emit('close')
  }
  onMounted(() => {
    visible.value = props.isShow
  })
</script>
<style lang="scss" scoped>
</style>