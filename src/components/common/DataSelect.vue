<template>
  <el-dropdown class="orders-select" trigger="click" placement="bottom-end" @command="handleCommand">
    <div class="select-box flex-box w300">
      <div class="flex-box space-between select-text">
        <div class="select-name">
          <span>{{ search.startDate }}</span>
          <span class="heng">-</span>
          <span>{{ search.endDate }}</span>
        </div>
        <div class="select-content">{{  $t(activeName) }}</div>
      </div>
      <MonoDownArrowMin size="12" />
    </div>
    <template #dropdown>
      <el-dropdown-menu :style="`width:${wid}px`">
        <slot></slot>
        <el-dropdown-item v-for="(item, index) in list" :key="index" :class="{ active: activeSelect === item.value }" :command="index">{{ $t(item.name) }}</el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
  <el-dialog v-model="showDialog" :title="$t('自定义时间')" width="480" class="dialog-date">
    <p class="fit-tc-secondary ts-14 pd-b16">*{{ $t('查询时间范围最大支持24个月') }}</p>
    <div class="flex-box date-range fit-tc-primary">
      <div>
        <div class="title ts-12 pd-b8">{{ $t('开始时间') }}</div>
        <el-date-picker size="large" v-model="dialog.startDate" class="ts-14" :picker-options="pickerOptions" type="date" format="YYYY-MM-DD" @change="handleDateChange"/>
      </div>
      <div class="xian">-</div>
      <div>
        <div class="title ts-12 pd-b8">{{ $t('结束时间') }}</div>
        <el-date-picker size="large" v-model="dialog.endDate" class="ts-14" :picker-options="pickerOptions" type="date" format="YYYY-MM-DD" @change="handleDateChange"/>
      </div>
    </div>
     <p v-if="showErrorMsg" class="fit-fall ts-12 error pd-t12">*{{ errorMsg }}</p>
    <div v-if="isChao" class="fit-fall ts-12 error pd-t12">
      <p>*{{ $t('您当前输入的导出时间范围已超过24个月。') }}</p>
      <p>*{{ $t('系统已为您调整为最大可支持范围。') }}</p>
    </div>
    <template #footer>
      <div class="text-right pd-lr20 flex-box">
        <el-button @click="showDialog = false" class="flex-1">{{ $t('取消') }}</el-button>
        <el-button type="primary" class="flex-1" @click="handleSure">{{ $t('确认') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElDropdown, ElDropdownMenu, ElDropdownItem, ElDialog, ElDatePicker } from 'element-plus'
  import { timeFormat } from '~/utils'
  import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
  const { locale, t } = useI18n()
  const props = defineProps({
    wid: {
      type: [String, Number],
      default: 180
    },
    isReset: {
      type: Boolean,
      default: false
    }
  })
  const emit = defineEmits(['change'])
  const list = ref([
    { name: t('近7天'), key: 'days', value: 7 },
    { name: t('近1个月'), key: 'months', value: 1 },
    { name: t('近3个月'), key: 'months', value: 3 },
    { name: t('近6个月'), key: 'months', value: 6 },
    { name: t('自定义时间'), value: 0 }
  ])
  const pickerOptions = ref({
    disabledDate: (val) => {
      return val.getTime() > new Date().getTime()
    }
  })
  const search = ref({
    startDate: '',
    endDate: ''
  })
  const dialog = ref({
    startDate: '',
    endDate: ''
  })
  const activeName = ref('近7天')
  const activeSelect = ref(7)
  const showDialog = ref(false)
  const showErrorMsg = ref(false)
  const errorMsg = ref('')
  const isChao = ref(false)
  watch(() => search.value, (val) => {
    emit('change', {
      start: val.startDate,
      end: val.endDate,
      type: activeSelect.value
    })
  })
  watch(() => props.isReset, (val) => {
    if (val) {
      initDate()
      emit('resetFun')
    }
  })
  const subtractFromCurrentDate = (type, amount) => {
    const currentDate = new Date()
    let newDate = new Date(currentDate)
    if (type === 'months') {
        // 设置月份时，需要注意JavaScript中的月份是从0开始的
        newDate.setMonth(newDate.getMonth() - amount)
    } else if (type === 'days') {
        newDate.setDate(newDate.getDate() - amount)
    } else if(type === 'years') {
      newDate.setFullYear(newDate.getFullYear() - amount)
    } else {
        throw new Error('Invalid type. Expected "months" or "days".')
    }
    return newDate
  }
  const addFormCurrentDate = (type, amount) => {
    const currentDate = new Date()
    let newDate = new Date(currentDate)
    if (type === 'years') {
      newDate.setFullYear(newDate.getYear() + amount)
    }
    return newDate
  }
  const handleCommand = (index) => {
    if (list.value[index].value === 0) { // 自定义时间
      dialog.value.startDate = search.value.startDate
      dialog.value.endDate = search.value.endDate
      console.info(dialog.value, 'pdpdpdpdpdpd')
      errorMsg.value = ''
      showErrorMsg.value = false
      isChao.value = false
      showDialog.value = true
    } else {
      activeName.value = list.value[index].name
      activeSelect.value = list.value[index].value
      const key = list.value[index].key
      const value = list.value[index].value
      search.value = {
        startDate: timeFormat(subtractFromCurrentDate(key, value), 'yyyy-MM-dd'),
        endDate: timeFormat(new Date(), 'yyyy-MM-dd')
      }
    }
  }
  const handleSure = () => {
    if (validate()) {
      activeName.value = '自定义时间'
      activeSelect.value = 0
      search.value = {
        startDate: timeFormat(new Date(dialog.value.startDate), 'yyyy-MM-dd'),
        endDate: timeFormat(new Date(dialog.value.endDate), 'yyyy-MM-dd')
      }
      showDialog.value = false
    }
  }
  const handleDateChange = () => {
    validate()
  }
  const validate = () => {
    // Check if startDate is missing
    if (!dialog.value.startDate) {
      errorMsg.value = t('请选择开始时间');
      showErrorMsg.value = true;
      return false;
    }

    // Check if endDate is missing
    if (!dialog.value.endDate) {
      errorMsg.value = t('请选择结束时间');
      showErrorMsg.value = true;
      return false;
    }

    // Convert strings to Date objects for comparison
    const startDateObj = new Date(dialog.value.startDate);
    const endDateObj = new Date(dialog.value.endDate);

    // Check if startDate is after endDate
    if (startDateObj > endDateObj) {
      errorMsg.value = t('开始时间不能大于结束时间');
      showErrorMsg.value = true;
      return false;
    }

    // Calculate the minimum allowed date (2 years ago)
    const minDate = new Date();
    minDate.setFullYear(minDate.getFullYear() - 2);

    // Check if startDate is earlier than the minDate
    if (startDateObj < minDate) {
      dialog.value.startDate = timeFormat(minDate, 'yyyy-MM-dd');
      isChao.value = true;
    } else {
      isChao.value = false;
    }

    // Clear error messages if validation passed
    errorMsg.value = '';
    showErrorMsg.value = false;

    return true;
  };

  const initDate = () => {
    activeName.value = '近7天'
    activeSelect.value = 7
    const start = timeFormat(subtractFromCurrentDate('days', 7), 'yyyy-MM-dd')
    const end = timeFormat(new Date(), 'yyyy-MM-dd')
    search.value = {
      startDate: start,
      endDate: end
    }
  }
  onMounted(() => {
    initDate()
  })
</script>
<style lang="scss" scoped>
.orders-select{
  margin-right:16px;
  margin-top:12px;
    .select-box{
      border:1px solid;
      border-radius:6px;
      height:36px;
      padding:0 12px;
      cursor:pointer;
      @include border-color(border);
      &.w200{
        width:200px;
      }
      &.w280{
        width:280px;
      }
      &.w300{
        width:300px;
      }
      .select-text{
        width:100%;
        .select-name{
          @include color(tc-secondary);
        }
        .select-content{
          padding-right:8px;
          @include color(tc-primary);
        }
      }
    }
  }
</style>
<style lang="scss">
.dialog-date{
  .el-dialog__header{
    padding: 24px 40px;
    .el-dialog__close{
      top:10px;
    }
  }
  .el-dialog__body{
    padding: 16px 30px 24px !important;
    border-top: 1px solid;
    border-bottom: 1px solid;
    min-height:240px;
    @include border-color(border);
    .xian{
      padding: 28px 8px 0;
    }
    .el-input{
      &.el-date-editor{
        width:100%;
        flex:1;
        .el-input__wrapper{
          .el-input__inner{
            padding-left:0;
          }
          .el-input__suffix{
            width:16px;
          }
        }
      }
    }
  }
  .el-dialog__footer{
    padding: 16px 20px !important;
  }
}
</style>