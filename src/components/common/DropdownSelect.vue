<template>
  <el-dropdown class="orders-select" :popper-class="popperClass" trigger="click" placement="bottom-start" @command="handleCommand" :style="`width:${wid}${ typeof wid !== 'number' ? '' :'px'}`">
    <div class="select-box flex-box w200" :style="`width:${wid}${ typeof wid !== 'number' ? '' :'px'}`">
      <div class="flex-box space-between select-text">
        <div class="select-name" :class="{'hideClass': activeName !== '' && isFormInput}">{{ labelName }}</div>
        <div class="select-content">{{ activeName }}</div>
      </div>
      <MonoDownArrowMin size="12" />
    </div>
    <template #dropdown>
      <el-dropdown-menu :style="`width:${wid}${ typeof wid !== 'number' ? '' :'px'};${isSearch ? 'height:300px' : 'max-height: 300px'};position:relative;`">
        <slot></slot>
        <div :style="isSearch ? 'height:calc(100% - 58px);overflow:auto;' : ''">
          <el-dropdown-item v-for="(item, index) in list" :key="index" :class="{ active: modelValue === item[val] }" :command="index">
            <BoxCoinIcon v-if="isShowIcon" style="font-size:24px;margin-right:12px;" :icon="item.icon_url" class="icon-box" />
            {{ item[label] }}
          </el-dropdown-item>
        </div>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>
<script lang="ts" setup>
  import { ElDropdown, ElDropdownMenu, ElDropdownItem, ElInput } from 'element-plus'
  import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
  const props = defineProps({
    list: {
      type: Array,
      default() {
        return []
      }
    },
    isShowIcon: {
      type: Boolean,
      default: false
    },
    popperClass: {
      type: String,
      default: 'popperClass'
    },
    isFormInput: {
      type: Boolean,
      default: false
    },
    isSearch: {
      type: Boolean,
      default: false
    },
    wid: {
      type: [Number, String],
      default: 160
    },
    labelName: {
      type: String,
      default: ''
    },
    modelValue: {
      type: String,
      default: ''
    },
    label: {
      type: String,
      default: 'label'
    },
    val: {
      type: String,
      default: 'value'
    }
  })
  const emit = defineEmits(['update:modelValue', 'change'])
  const activeName = computed(() => {
    const list = props.list.filter(item => {
      return item[props.val] === props.modelValue
    })
    return list[0] ? list[0][props.label] : ''
  })
  const handleCommand = (index) => {
    if (props.modelValue !== props.list[index][props.val]) {
      emit('update:modelValue', props.list[index][props.val])
      emit('change', props.list[index][props.val])
    }
  }
</script>
<style lang="scss" scoped>
  .orders-select{
    margin-right:16px;
    margin-top:12px;
    .select-box{
      border:1px solid;
      border-radius:6px;
      height:36px;
      padding:0 12px;
      cursor:pointer;
      @include border-color(border);
      &.w200{
        width:200px;
      }
      &.w280{
        width:280px;
      }
      .select-text{
        width:100%;
        .select-name{
          @include color(tc-secondary);
          &.hideClass{
            display:none;
          }
        }
        .select-content{
          padding-right:8px;
          @include color(tc-primary);
        }
      }
    }
  }
</style>
<style lang="scss">
.w100{
  width:calc(100% - 38px);
}
.dialogw100{
  width: calc(480px - 48px);
}
.w50{
  width:calc(50% - 32px);
}
</style>