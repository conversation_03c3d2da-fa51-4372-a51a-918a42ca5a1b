<template>
  <div class="relative" style="width:100%;">
    <ul class="relative flex-box" :class="{'google-disabled': disabled}" @click="focusInput">
      <li :class="{ 'code-focus': isFocus && code.length < 3, 'error': inputError, [`fit-${bgColor}_bg`]: bgColor }" class="input-wrap flex-1 flex-box">
        <span v-for="index in 3" :key="index - 1" :class="{ 'custom-cursor': isFocus && ((code[index - 2] && !code[index - 1]) || (index === 1 && !code[0])) }">{{ code[index - 1] }}</span>
      </li>
      <li class="pd-lr8"></li>
      <li :class="{'code-focus': isFocus && code.length >= 3, 'error': inputError, [`fit-${bgColor}_bg`]: bgColor}" class="input-wrap flex-1 flex-box">
        <span v-for="index in 3" :key="index + 2" :class="{ 'custom-cursor': isFocus && code[index + 1] && !code[index + 2] }">{{ code[index + 2] }}</span>
      </li>
    </ul>
    <div v-if="inputError" class="common-input-error">{{ inputError }}</div>
    <input
      ref="realInputRef"
      :value="code"
      :max-length="6"
      class="real-input"
      :disabled="disabled"
      @input="updateCode"
      @focus="focusInput(false)"
      @blur="handleBlur"
      @keyup="checkSuccess"
    />
  </div>
  <!-- <p v-if="isShowInstall" class="goInstall-text"><NuxtLink :to="`/${locale}/my/account/bindGoogle?step=1`">{{ $t('去安装') }}</NuxtLink></p> -->
</template>
<script lang="ts" setup>
import { useUserStore } from '~/stores/useUserStore'
const store = useUserStore()
const { userInfo } = storeToRefs(store)
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  focus: Boolean,
  isShowInstall: {
    type: Boolean,
    default: false
  },
  inputError: {
    type: String,
    default: ''
  },
  bgColor: {
    type: String,
    default: ''
  },
  isShowBtn: {
    type: Boolean,
    default: false
  },
  defaultFocus: {
    type: Boolean,
    default: true
  },
  disabled: {
    type: Boolean,
    default: false
  }
})
const { locale } = useI18n()
const code = ref(props.modelValue)
watch(() => code.value, (val) => {
  emit('update:modelValue', val)
})
if (props.modelValue !== code.value) {
  code.value = props.modelValue
}
watch(() => props.modelValue, (val) => {
  code.value = val
})
watch(() => props.focus, (val) => {
  if (val) {
    focusInput()
  }
})
const emit = defineEmits(['update:modelValue', 'focus', 'success', 'blur'])
const isFocus = ref(false)
const realInputRef = ref(null)
const updateCode = (el) => {
  let value = el.target.value
  code.value = value && typeof value === 'string' ? value.replace(/[^\d]/g, '') : value
  console.info(code.value, 'code.value')
}
const focusInput = () => {
  if (props.disabled) {
    return false
  }
  isFocus.value = true
  emit('focus')
  nextTick(() => {
    realInputRef.value.focus()
  })
}
const handleBlur = () => {
  isFocus.value = false
  emit('blur')
}
const checkSuccess = () => {
  if (code.value.length >= 6) {
    isFocus.value = false
    realInputRef.value.blur()
    emit('success', code.value.substring(0, 6))
  }
}
onMounted(() => {
  if (props.defaultFocus) {
    focusInput()
  }
})
</script>
<style scoped lang="scss">
.goInstall-text{
  font-size:14px;
  padding-top:8px;
  a{
    @include color(theme);
  }
}
@keyframes blink-light {
  0% {
    background-color: transparent;
  }
  30% {
    background: getSingleColor(tc-primary, 1, light);
  }
  60% {
    background: getSingleColor(tc-primary, 1, light);
  }
  100% {
    background-color: transparent;
  }
}
@keyframes blink-dark {
  0% {
    background-color: transparent;
  }
  30% {
    background: getSingleColor(tc-primary, 1, dark);
  }
  60% {
    background: getSingleColor(tc-primary, 1, dark);
  }
  100% {
    background-color: transparent;
  }
}
.google-disabled{
  cursor:not-allowed;
  .input-wrap{
    span{
      @include bg-color(bg-tertiary);
      @include border-color(border);
    }
  }
}
.input-wrap {
  overflow: hidden;
  &.error {
    @include border-color(error);
  }

  &.code-focus {
    @include border-color(theme);

    &.error {
      @include border-color(theme);
    }
  }

  span {
    flex: 1;
    height: 60px;
    line-height: 42px;
    display: block;
    text-align: center;
    padding: 10px 0;
    font-size: 32px;
    font-weight: 500;
    border:1px solid;
    border-radius:8px;
    margin:0 4px;
    @include color(tc-primary);
    @include border-color(border-input);
  }

  .custom-cursor {
    position: relative;
    @include border-color(theme);
    &::after {
      content: "";
      position: absolute;
      width: 1px;
      height: 30px;
      left: calc(50% - 0.5px);
      top: calc(50% - 15px);
      [class^="light"] & {
        animation: blink-light 1s infinite;
      }
      [class^="dark"] & {
        animation: blink-dark 1s infinite;
      }
    }
  }
}

.common-input-error {
  margin-top:20px;
  text-align: center;
  font-size: 12px;
  width: 100%;
  @include color(error);
}

.real-input {
  position: absolute;
  z-index: -999;
  left: 0;
  top: 0;
  opacity: 0;
}

.code-split-line {
  width: 8px;
  height: 2px;
  @include bg-color(border);
}
</style>
