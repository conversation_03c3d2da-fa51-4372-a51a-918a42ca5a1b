<template>
  <el-autocomplete 
    v-model="inputValue"
    :fetch-suggestions="querySearch"
    name="username"
    autocomplete="username"
    :placeholder="placeholder"
    clearable
    @select="handleSelect"
    @keyup.enter.native="handleEnter"
    >
    <template #default="{ item }">
      <div class="custom-suggestion-item">{{ inputValue.includes('@') ? inputValue.split('@')[0] : inputValue }}{{ item.value }}</div>
    </template>
  </el-autocomplete>
</template>
<script lang="ts" setup>
  import { ElAutocomplete } from 'element-plus'
  const props = defineProps({
    isTypeLogin: {
      type: Boolean,
      default: false
    },
    modelValue: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: '请输入邮箱',
    },
    locale: {
      type: String,
      default: 'zh', // 默认语言为中文
    }
  })
  const emit = defineEmits(['update:modelValue', 'enter'])
  // 输入框的值
  const inputValue = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })
  const emailSuggestions = computed(() => {
    if (props.locale === 'zh') {
      return [
        { value: '@126.com' },
        { value: '@qq.com' },
        { value: '@sina.com' },
        { value: '@foxmail.com' },
        { value: '@sohu.com' }
      ]
    } else {
      return [
        { value: '@yahoo.com' },
        { value: '@skype.com' },
        { value: '@gmail.com' },
        { value: '@hotmail.com' },
        { value: '@syvip.com' }
      ]
    }
  })
  const curText = ref('')
  const querySearch = (queryString, cb) => {
    curText.value = queryString
    let results = [];
    // 只有当输入内容包含 @ 时才进行过滤
    if ((queryString.includes('@') && props.isTypeLogin) || (!props.isTypeLogin && queryString !== '')) {
      const domainPart = queryString.split('@')[1] // 获取 @ 后面的部分
      if (queryString.includes('@')) {
        // 过滤建议列表，匹配 @ 后面的部分
        results = emailSuggestions.value.filter((item) =>
          item.value.toLowerCase().includes(domainPart.toLowerCase())
        )
      } else {
        // 如果 @ 后面没有内容，返回所有建议
        results = emailSuggestions.value;
      }
    }
    cb(results); // 返回过滤后的结果
  };
  const handleSelect= (item) => {
    const usernamePart = curText.value.split('@')[0];
    const newValue = `${usernamePart}${item.value}`;
    inputValue.value = newValue;
    console.log('选中:', newValue);
  }
  const handleEnter = () => {
    emit('enter', inputValue.value)
  }
</script>
<style lang="scss">
.custom-suggestion-item:hover {
  margin:0 -20px;
  padding:0 20px;
  @include bg-color(bg-quaternary);
}
</style>