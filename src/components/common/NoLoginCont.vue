<template>
  <div class="no-login-info-box">
    <a class="cursor-pointer" @click="useCommon.openLogin()">{{ $t('登录') }}</a>
    <span>{{ $t('或') }}</span>
    <a class="cursor-pointer" @click="useCommon.openRegister()">{{ $t('注册') }}</a>
    <span>{{ $t('进行交易') }}</span>
  </div>
</template>
<script lang="ts" setup>
  import { useCommonData } from '~/composables/index'
  const useCommon = useCommonData()
</script>
<style lang="scss">
  .no-login-btn{
    margin-top:38px;
    width:100%;
    height:40px;
    line-height:40px;
    border-radius:4px;
    @include bg-color(bg-secondary);
    text-align:center;
    cursor:pointer;
  }
  .no-login-info-box{
    font-size:14px;
    text-align:center;
    @include color(tc-secondary);
    a{
      @include color(theme);
      margin:0 4px;
    }
  }
</style>