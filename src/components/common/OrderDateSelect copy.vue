<template>
  <div class="dialog-container-m">
    <div class="dialog-wrapper-m animate__animated" :class="{'animate__fadeOutDown': !isShowSlectDateCont, 'animate__fadeInUp': isShowSlectDateCont}">
      <div class="wrapper-pd-box">
        <div class="dialog-title-m flex-box space-between">
          {{ $t('筛选') }}
          <MonoClose @click="emit('closeDialog')" />
        </div>
        <div class="dialog-body-m">
          <div class="label-item-cont">
            <div class="label-title">{{ $t('时间') }}</div>
            <div class="label-cont-box">
              <ul class="label-select-box flex-box">
                <li v-for="(item, index) in list" :key="index" :class="{active: search.type === item.value}" @click="handleCommand(item)">{{ $t(item.name) }}</li>
              </ul>
            </div>
          </div>
          <div class="date-tab-box flex-box space-between" @click="startEndDateFun('start')">
            <div class="date-tab-left">
              {{ $t('开始日期') }}
            </div>
            <div class="date-tab-right flex-box">
              <span class="mg-r8">{{ search.start }}</span>
              <MonoRightArrowShort :class="{'selectshow': isShowStart}" size="16" />
            </div>
          </div>
          <div v-if="isShowStart" class="date-picker-box">
            <van-date-picker
              :show-toolbar="false"
              v-model="startDate"
              @change="changeStartDate"
            />
          </div>
          <div class="date-tab-box flex-box space-between" @click="startEndDateFun('end')">
            <div class="date-tab-left">
              {{ $t('结束时间') }}
            </div>
            <div class="date-tab-right flex-box">
              <span class="mg-r8">{{ search.end }}</span>
              <MonoRightArrowShort :class="{'selectshow': isShowEnd}" size="16" />
            </div>
          </div>
          <div v-if="isShowEnd" class="date-picker-box">
            <van-date-picker
              :show-toolbar="false"
              v-model="endDate"
              @change="changeEndDate"
            />
          </div>
        </div>
        <div class="dialog-footer-m flex-box">
          <div class="flex-1 mg-r12">
            <el-button class="reset-btn" @click="initDate">{{ $t('重置') }}</el-button>
          </div>
          <div class="flex-1">
            <el-button type="primary" @click="confirmFun">{{ $t('确认') }}</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { timeFormat } from '~/utils'
  import MonoRightArrowShort from '~/components/common/icon-svg/MonoRightArrowShort.vue'
  import 'vant/lib/index.css'
  const { locale, t } = useI18n()
  const props = defineProps({
    isShowSlectDateCont: {
      type: Boolean,
      default: false
    },
    isShowDateSelect: {
      type: Boolean,
      default: false
    }
  })
  const emit = defineEmits(['confirmChange', 'closeDialog'])
  const list = ref([
    { name: t('近7天'), key: 'days', value: 7 },
    { name: t('近1个月'), key: 'months', value: 1 },
    { name: t('近3个月'), key: 'months', value: 3 },
    { name: t('近6个月'), key: 'months', value: 6 }
  ])
  const startDate = ref([])
  const endDate = ref([])
  const search = ref({
    type: 0,
    start: '',
    end: ''
  })
  const isShowStart = ref(false)
  const isShowEnd = ref(false)
  const subtractFromCurrentDate = (type, amount) => {
    const currentDate = new Date()
    let newDate = new Date(currentDate)
    if (type === 'months') {
        // 设置月份时，需要注意JavaScript中的月份是从0开始的
        newDate.setMonth(newDate.getMonth() - amount)
    } else if (type === 'days') {
        newDate.setDate(newDate.getDate() - amount)
    } else if(type === 'years') {
      newDate.setDate(newDate.getYear() - amount)
    } else {
        throw new Error('Invalid type. Expected "months" or "days".')
    }
    return newDate
  }
  const addFormCurrentDate = (type, amount) => {
    const currentDate = new Date()
    let newDate = new Date(currentDate)
    if (type === 'years') {
      newDate.setDate(newDate.getYear() + amount)
    }
    return newDate
  }
  const initDate = () => {
    const type = 3
    const start = timeFormat(subtractFromCurrentDate('months', 3), 'yyyy-MM-dd')
    const end = timeFormat(new Date(), 'yyyy-MM-dd')
    startDate.value = start.split('-')
    endDate.value = end.split('-')
    console.info(startDate.value, endDate.value, 'sshshuwhsuw')
    search.value = {
      type,
      start,
      end
    }
  }
  const startEndDateFun = (type) => {
    if (type === 'start') {
      isShowStart.value = !isShowStart.value
      isShowEnd.value = false
    } else if (type === 'end') {
      isShowEnd.value = !isShowEnd.value
      isShowStart.value = false
    }
  }
  const changeStartDate = (item) => {
    search.value.type = 0
    search.value.start = startDate.value.join('-')
  }
  const changeEndDate = (item) => {
    search.value.type = 0
    search.value.end = endDate.value.join('-')
  }
  const handleCommand = (item) => {
    const start = timeFormat(subtractFromCurrentDate(item.key, item.value), 'yyyy-MM-dd')
    const end = timeFormat(new Date(), 'yyyy-MM-dd')
    search.value = {
      type: item.value,
      start,
      end
    }
  }
  const confirmFun = () => {
    emit('confirmChange', search.value)
    emit('closeDialog')
  }
  onMounted(() => {
    initDate()
  })
</script>
<style lang="scss" scoped>
@import '@/assets/style/global-dialog-m.scss';
</style>