<template>
  <div class="dialog-container-m">
    <div class="dialog-wrapper-m animate__animated" :class="{'animate__fadeOutDown': !isShowSlectParamsCont, 'animate__fadeInUp': isShowSlectParamsCont}">
      <div class="wrapper-pd-box">
        <div class="dialog-title-m flex-box space-between">
          {{ $t('筛选') }}
          <MonoClose @click="emit('closeDialog')" />
        </div>
        <div class="dialog-body-m">
          <div v-if="isBill" class="label-item-cont">
            <div class="label-title">{{ $t('账户') }}</div>
            <div class="label-cont-box">
              <ul class="label-select-box flex-box">
                <li v-for="(item, index) in accountTypeList" :key="index" :class="{active: search.accountType === item.id}" @click="handleCommand(item, 'accountType', 'id')">{{ $t(item.bill_info_alias) }}</li>
              </ul>
            </div>
          </div>
          <div v-if="isStatus" class="label-item-cont">
            <div class="label-title">{{ $t('订单状态') }}</div>
            <div class="label-cont-box">
              <ul class="label-select-box flex-box">
                <li v-for="(item, index) in statusList" :key="index" :class="{active: search.status === item.value}" @click="handleCommand(item, 'status', 'value')">{{ $t(item.label) }}</li>
              </ul>
            </div>
          </div>
          <div v-if="isType" class="label-item-cont">
            <div class="label-title">{{ $t('委托类型') }}</div>
            <div class="label-cont-box">
              <ul class="label-select-box flex-box">
                <li v-for="(item, index) in typeList" :key="index" :class="{active: search.order_type === item.value}" @click="handleCommand(item, 'order_type', 'value')">{{ $t(item.label) }}</li>
              </ul>
            </div>
          </div>
          <div v-if="isSide" class="label-item-cont">
            <div class="label-title">{{ $t('委托方向') }}</div>
            <div class="label-cont-box">
              <ul class="label-select-box flex-box">
                <li v-for="(item, index) in sideList" :key="index" :class="{active: search.side === item.value}" @click="handleCommand(item, 'side', 'value')">{{ $t(item.label) }}</li>
              </ul>
            </div>
          </div>
          <div v-if="isMode" class="label-item-cont">
            <div class="label-title">{{ $t('仓位模式') }}</div>
            <div class="label-cont-box">
              <ul class="label-select-box flex-box">
                <li v-for="(item, index) in modeList" :key="index" :class="{active: search.margin_method === item.value}" @click="handleCommand(item, 'margin_method', 'value')">{{ $t(item.label) }}</li>
              </ul>
            </div>
          </div>
          <OrderDateSelect v-model="timeObj" v-if="isDate" @confirmChange="changeDateFun" />
        </div>
        <div class="dialog-footer-m flex-box">
          <div class="flex-1 mg-r12">
            <el-button class="reset-btn" @click="initDate">{{ $t('重置') }}</el-button>
          </div>
          <div class="flex-1">
            <el-button type="primary" @click="confirmFun">{{ $t('确认') }}</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import DropdownSelect from '~/components/common/DropdownSelect'
  import OrderDateSelect from '~/components/common/OrderDateSelect'
  import { timeFormat } from '~/utils'
  const props = defineProps({
    isType: {
      type: Boolean,
      default: false
    },
    isDate: {
      type: Boolean,
      default: false
    },
    isSide: {
      type: Boolean,
      default: false
    },
    isMode: {
      type: Boolean,
      default: false
    },
    isFuture: {
      type: Boolean,
      default: false
    },
    isStatus: {
      type: Boolean,
      default: false
    },
    isBill: {
      type: Boolean,
      default: false
    },
    isShowSlectParamsCont: {
      type: Boolean,
      default: false
    },
    defaultSearch: {
      type: Object,
      default() {
        return {}
      }
    },
    modeList: {
      type: Boolean,
      default () {
        return []
      }
    },
    statusList: {
      type: Boolean,
      default () {
        return []
      }
    },
    accountTypeList: {
      type: Array,
      default () {
        return []
      }
    },
    sideList: {
      type: Boolean,
      default () {
        return []
      }
    },
    typeList: {
      type: Boolean,
      default () {
        return []
      }
    },
    accountList: {
      type: Boolean,
      default () {
        return []
      }
    },
    pairList: {
      type: Boolean,
      default () {
        return []
      }
    }
  })
  const emit = defineEmits(['confirmParams', 'cloaseDialog', 'change'])
  const search = ref({
    accountType: '',
    status: '',
    order_type: '',
    side: '',
    margin_method: ''
  })
  const timeObj = ref({
    type: 0,
    start: '',
    end: ''
  })
  const handleCommand = (item, name, type) => {
    search.value[name] = item[type]
  }
  const subtractFromCurrentDate = (type, amount) => {
    const currentDate = new Date()
    let newDate = new Date(currentDate)
    if (type === 'months') {
        // 设置月份时，需要注意JavaScript中的月份是从0开始的
        newDate.setMonth(newDate.getMonth() - amount)
    } else if (type === 'days') {
        newDate.setDate(newDate.getDate() - amount)
    } else if(type === 'years') {
      newDate.setDate(newDate.getYear() - amount)
    } else {
        throw new Error('Invalid type. Expected "months" or "days".')
    }
    return newDate
  }
  const initDate = () => {
    if (props.isBill) {
      search.value = {
        accountType: props.accountTypeList[0].id,
        order_type: '',
        order_side: ''
      }
    } else {
      search.value = {
        accountType: '',
        status: '',
        order_type: '',
        side: '',
        margin_method: ''
      }
    }
    const type = 7
    const start = timeFormat(subtractFromCurrentDate('days', 7), 'yyyy-MM-dd')
    const end = timeFormat(new Date(), 'yyyy-MM-dd')
    timeObj.value = {
      type,
      start,
      end
    }
  }
  const confirmFun = () => {
    const obj = Object.assign({}, search.value, timeObj.value)
    console.log(obj, 'djdjeijuiejieij')
    emit('confirmParams', obj)
    emit('cloaseDialog')
  }
  const changeDateFun = (item) => {
    timeObj.value = item
  }
  onMounted(() => {
    console.log(props.defaultSearch, 'djdejiejiejiijdijeij')
    if (props.isBill) {
      search.value.accountType = props.defaultSearch.accountType
    } else {
      search.value.status = props.defaultSearch.order_status
      search.value.order_type = props.defaultSearch.type
      search.value.side = props.defaultSearch.side
      search.value.margin_method = props.defaultSearch.margin_method
    }
    timeObj.value.type = props.isBill ? props.defaultSearch.type : props.defaultSearch.timeType
    timeObj.value.start = props.defaultSearch.start || props.defaultSearch.startDate || ''
    timeObj.value.end = props.defaultSearch.end || props.defaultSearch.endDate || ''
  })
</script>
<style lang="scss" scoped>
@import '@/assets/style/global-dialog-m.scss';
</style>