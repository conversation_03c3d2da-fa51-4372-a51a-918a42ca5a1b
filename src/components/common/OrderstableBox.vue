<template>
  <div v-loading="isLoading" class="div-table">
    <div class="div-table-header-wrapper" ref="headerWrapper">
      <div class="div-table-header" :style="{ width: tableWidth }">
        <template v-for="(header, index) in headers">
          <div
            :class="`div-table-cell header-cell fit-tc-secondary ${header.align} ${header.wid} ${header.fixed ? 'fixed-column' : ''} ${header.fixed === 'left' ? 'fixed-left' : header.fixed === 'right' ? 'fixed-right' : ''}`"
            :style="{left: header.fixedLeftOffset}"
            v-if="!header.hide"
            :key="index"
          >
            <template v-if="header.headerAuto">
              <slot :name="`header-${header.key}`" :data="header"></slot>
            </template>
            <template v-else>
              {{ header.text }}
            </template>
            <MonoDownArrowMin v-if="header.isSort" :size="12" class="fit-tc-secondary mg-l4" :class="{ 'arrowTop': sortDirection === 'asc' }" @click="sortTable(index)"/>
          </div>
        </template>
      </div>
    </div>
    <div v-if="list.length > 0 && !isLoading" class="div-table-body-wrapper" ref="bodyWrapper" @scroll="syncScroll">
      <div class="div-table-body" :style="{ width: tableWidth }">
        <div class="div-table-row" v-for="(row, rowIndex) in list" :key="rowIndex" @click="emit('rowChange', row)">
          <template v-for="(col, colIndex) in headers" :key="colIndex">
            <div v-if="!col.hide" :class="`div-table-cell ${col.align} ${col.wid} ${col.fixed ? 'fixed-column' : ''} ${col.fixed === 'left' ? 'fixed-left' : col.fixed === 'right' ? 'fixed-right' : ''}`"
              :style="{left: col.fixedLeftOffset}">
              <template v-if="col.style === 'auto'">
                <slot :name="col.key" :data="row"></slot>
              </template>
              <span v-else :class="`${col.style}`">{{ row[col.key] }}</span>
            </div>
          </template>
        </div>
      </div>
    </div>
    <div v-if="list.length === 0 && !isLoading" style="height:calc(100% - 46px);">
      <BoxNoData :text="$t('暂无数据')" />
    </div>
  </div>
  <div class="div-table-m">
    <template v-if="list.length > 0 && !isLoading">
      <ul v-for="(mrow, mrowIndex) in list" :key="mrowIndex" class="div-table-row-m" @click="emit('rowChange', row)">
        <template v-for="(mcol, mcolIndex) in mSortList" :key="mcolIndex">
          <li>
            <dl>
              <dt>
                <template v-if="mcol.headerAuto">
                  <slot :name="`header-${mcol.key}`" :data="mcol"></slot>
                </template>
                <template v-else>
                  {{ mcol.text }}
                </template>
              </dt>
              <dd class="flex-box">
                <template v-if="mcol.style === 'auto'">
                  <slot :name="mcol.key" :data="mrow"></slot>
                </template>
                <span v-else :class="`${mcol.style}`">{{ mrow[mcol.key] }}</span>
              </dd>
            </dl>
          </li>
        </template>
      </ul>
    </template>
    <div v-if="list.length === 0 && !isLoading" style="height:400px;">
      <BoxNoData :text="$t('暂无数据')" />
    </div>
    <div v-if="isLoading" style="height:400px;">
    </div>
  </div>
  <div v-if="list.length > 0" class="font-size-14 fit-tc-secondary text-center">{{ moreTxt }}</div>
</template>

<script lang="ts" setup>
  import BoxNoData from '~/components/common/BoxNoData.vue'
  import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
  const props = defineProps({
    isLoading: {
      type: Boolean,
      default: false
    },
    list: {
      type: Object,
      default () {
        return []
      }
    },
    headers: {
      type: Object,
      default () {
        return []
      }
    },
    mSortList: {
      type: Object,
      default () {
        return []
      }
    },
    moreTxt: {
      type: String,
      default: ''
    }
  })
  const emit = defineEmits(['rowChange'])
  const sortIndex = ref(null)
  const sortDirection = ref('asc')
  const sortedList = computed(() => {
    const sorted = [...props.list]
    if (sortIndex.value !== null) {
      sorted.sort((a, b) => {
        const modifier = sortDirection.value === 'asc' ? 1 : -1
        if (a[props.headers[sortIndex.value].key] < b[props.headers[sortIndex.value].key]) {
          return -1 * modifier
        }
        if (a[props.headers[sortIndex.value].key] > b[props.headers[sortIndex.value].key]) {
          return 1 * modifier
        }
        return 0
      })
    }
    return sorted
  })
  const sortTable = (index) => {
    if (sortIndex.value === index) {
      sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
    } else {
      sortIndex.value = index
      sortDirection.value = 'asc'
    }
  }
  // 新增的代码
  const headerWrapper = ref<HTMLElement | null>(null)
  const bodyWrapper = ref<HTMLElement | null>(null)
  const tableWidth = ref('auto')

  const syncScroll = (e: Event) => {
    if (headerWrapper.value && bodyWrapper.value) {
      headerWrapper.value.scrollLeft = bodyWrapper.value.scrollLeft
    }
  }
  onMounted(() => {
    nextTick(() => {
      // 计算表格总宽度
      if (bodyWrapper.value) {
        const firstRow = bodyWrapper.value.querySelector('.div-table-row')
        if (firstRow) {
          tableWidth.value = `${firstRow.scrollWidth}px`
        }
      }
    })
  })
</script>

<style scoped lang="scss">
/* 添加你的样式 */
.div-table {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: calc(100% - 46px);
  flex: 1 1;
  overflow: hidden; /* 防止容器滚动 */

  .div-table-header-wrapper {
    position: sticky;
    top: 0;
    z-index: 3; /* 确保表头在最上层 */
    background: inherit; /* 继承背景色 */
    @include bg-color(bg-primary); /* 使用你的背景色混合器 */
  }
  
  .div-table-header {
    display: flex;
    white-space: nowrap;
    min-width: 100px;
  }
  .div-table-body-wrapper {
    height: calc(100% - 34px);
    overflow-y: scroll;
  }
}

.div-table-header-wrapper {
  position: sticky;
  top: 0;
  z-index: 3;
  overflow-x: hidden; /* 隐藏水平滚动条 */
  @include bg-color(bg-primary);
}

.div-table-body-wrapper {
  flex: 1;
  overflow: auto;
  position: relative;
}

.div-table-body {
  display: flex;
  flex-direction: column;
  min-width: 100%;
}

/* 固定列样式调整 */
.fixed-column {
  position: sticky;
  z-index: 2;
  @include bg-color(bg-primary);
  
  /* 为固定列添加阴影效果，增强视觉区分 */
  &.fixed-left {
    left: 0;
  }
  
  &.fixed-right {
    right: 0;
  }
}
.div-table-header, .div-table-body {
  position: relative;
}
.fixed-column {
  position: sticky;
  z-index: 2;
  @include bg-color(bg-primary); /* 使用你的混合器 */
}

.fixed-left {
  left: 0;
}

.fixed-right {
  right: 0;
}
.div-table-row {
  display: flex;
  cursor:default;
  @include pc-hover {
    &:hover{
      @include bg-color(bg-quaternary);
      .fixed-left, .fixed-right{
        @include bg-color(bg-quaternary);
      }
    }
  }
  /* 添加边框或其他样式 */
}
.div-table-cell {
  flex: 1;
  padding: 14px;
  text-align: center;
  align-items:center;
  justify-content:center;
  display:flex;
  min-width:100px;
  font-size:14px;
  @include color(tc-primary);
  span{
    word-break: break-all;
    text-align: left;
  }
  &.w80{
    width:80px;
    flex:inherit;
  }
  &.flex-2{
    flex: 2;
  }
  &:first-child{
    padding-left:20px;
  }
  &:last-child{
    padding-right:20px;
  }
  &.left{
    justify-content:flex-start;
  }
  &.right{
    justify-content:flex-end;
  }
}

.header-cell {
  cursor: pointer;
  font-size:12px;
  @include color(tc-secondary);
}

.sort-indicator {
  margin-left: 5px; /* 可选 */
}
.cursor{
  cursor:pointer;
}
.div-table-m{
  display:none;
}
@include mb {
  .div-table{
    display:none;
  }
  .div-table-m{
    display:block;
    .div-table-row-m{
      border-bottom:1px solid;
      padding-top:12px;
      @include border-color(border);
      &:last-child{
        border-bottom:0;
      }
      li{
        padding-bottom:8px;
        width:100%;
        clear:both;
        &:first-child, &:nth-child(2){
          width:50%;
          float:left;
          clear:none;
          padding-bottom:12px;
          dl{
            dt{
              display:none;
            }
          }
        }
        &:nth-child(2){
          dl{
            justify-content: flex-end;
          }
        }
        dl{
          display:flex;
          justify-content: space-between;
          align-items:center;
          dt{
            min-width:90px;
            font-size:14px;
            @include color(tc-secondary);
          }
          dd{
            text-align:right;
            font-size:14px;
            @include color(tc-primary);
          }
        }
      }
    }
  }
}
</style>