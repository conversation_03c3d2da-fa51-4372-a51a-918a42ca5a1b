<template>
  <div class="dialog-container-m">
    <div class="dialog-wrapper-m animate__animated" :class="{'animate__fadeOutDown': !isShowDialog, 'animate__fadeInUp': isShowDialog, 'heiFixed': isShowInput }">
      <div class="wrapper-pd-box">
        <div class="dialog-title-m flex-box space-between">
          {{ title || $t('筛选') }}
          <MonoClose @click="emit('close')" />
        </div>
        <div class="dialog-body-m">
          <slot></slot>
          <div class="list-box">
            <ul>
              <li v-for="(item, index) in list" :key="index" class="flex-box" :class="{'active': defaultValue === item[val] }" @click="changeLi(item)">
                <BoxCoinIcon v-if="isShowIcon" style="font-size:24px;margin-right:12px;" :icon="item.icon_url" class="icon-box" />
                {{ item[label].includes('_SWAP') ? item[label].replace('_SWAP', '').replace('_', '') : item[label] }}
                <MonoRigthChecked size="20" />
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import MonoRigthChecked from '~/components/common/icon-svg/MonoRigthChecked.vue'
  const props = defineProps({
    isShowDialog: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    isShowIcon: {
      type: Boolean,
      default: false
    },
    list: {
      type: Object,
      default () {
        return []
      }
    },
    defaultValue: {
      type: [String, Number],
      default: ''
    },
    isShowInput: {
      type: Boolean,
      default: false
    },
    label: {
      type: String,
      default: 'label'
    },
    val: {
      type: String,
      default: 'value'
    }
  })
  const emit = defineEmits(['close', 'changeItem'])
  const changeLi = (item) => {
    emit('changeItem', item)
    emit('close')
  }
</script>
<style lang="scss" scoped>
@import '@/assets/style/global-dialog-m.scss';
</style>