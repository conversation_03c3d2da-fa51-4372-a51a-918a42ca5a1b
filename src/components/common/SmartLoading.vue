<template>
  <div class="smart-loading-container">
    <div v-if="showSkeleton" class="skeleton-wrapper">
      <div v-if="type === 'chart'" class="chart-skeleton">
        <div class="skeleton-header">
          <div class="skeleton-line skeleton-title"></div>
          <div class="skeleton-line skeleton-subtitle"></div>
        </div>
        <div class="skeleton-chart-area">
          <div class="skeleton-chart-bars">
            <div v-for="i in 20" :key="i" class="skeleton-bar" :style="{ height: Math.random() * 80 + 20 + '%' }"></div>
          </div>
        </div>
        <div class="skeleton-footer">
          <div class="skeleton-line skeleton-small"></div>
          <div class="skeleton-line skeleton-small"></div>
        </div>
      </div>
      
      <div v-else-if="type === 'depth'" class="depth-skeleton">
        <div class="skeleton-header">
          <div class="skeleton-line skeleton-title"></div>
        </div>
        <div class="skeleton-depth-list">
          <div v-for="i in 10" :key="i" class="skeleton-depth-item">
            <div class="skeleton-line skeleton-price"></div>
            <div class="skeleton-line skeleton-amount"></div>
          </div>
        </div>
      </div>
      
      <div v-else-if="type === 'orderbook'" class="orderbook-skeleton">
        <div class="skeleton-tabs">
          <div class="skeleton-tab"></div>
          <div class="skeleton-tab"></div>
          <div class="skeleton-tab"></div>
        </div>
        <div class="skeleton-table">
          <div v-for="i in 8" :key="i" class="skeleton-row">
            <div class="skeleton-cell"></div>
            <div class="skeleton-cell"></div>
            <div class="skeleton-cell"></div>
          </div>
        </div>
      </div>
      
      <div v-else class="default-skeleton">
        <div class="skeleton-line skeleton-title"></div>
        <div class="skeleton-line skeleton-content"></div>
        <div class="skeleton-line skeleton-content"></div>
      </div>
    </div>
    
    <div v-else-if="showSpinner" class="spinner-wrapper">
      <div class="smart-spinner">
        <div class="spinner-ring"></div>
        <div class="spinner-text">{{ loadingText }}</div>
      </div>
    </div>
    
    <slot v-else />
  </div>
</template>

<script setup lang="ts">
interface Props {
  loading?: boolean
  type?: 'chart' | 'depth' | 'orderbook' | 'default'
  skeleton?: boolean
  loadingText?: string
  minLoadingTime?: number
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  type: 'default',
  skeleton: true,
  loadingText: 'Loading...',
  minLoadingTime: 300
})

const showSkeleton = computed(() => props.loading && props.skeleton)
const showSpinner = computed(() => props.loading && !props.skeleton)

const startTime = ref(0)
const actualLoading = ref(props.loading)

watch(() => props.loading, (newVal) => {
  if (newVal) {
    startTime.value = Date.now()
    actualLoading.value = true
  } else {
    const elapsed = Date.now() - startTime.value
    if (elapsed < props.minLoadingTime) {
      setTimeout(() => {
        actualLoading.value = false
      }, props.minLoadingTime - elapsed)
    } else {
      actualLoading.value = false
    }
  }
})
</script>

<style scoped lang="scss">
.smart-loading-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.skeleton-wrapper {
  width: 100%;
  height: 100%;
  padding: 16px;
  background: var(--el-bg-color);
}

.chart-skeleton {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .skeleton-header {
    margin-bottom: 16px;
  }
  
  .skeleton-chart-area {
    flex: 1;
    display: flex;
    align-items: end;
    padding: 20px 0;
    
    .skeleton-chart-bars {
      display: flex;
      align-items: end;
      width: 100%;
      height: 200px;
      gap: 2px;
      
      .skeleton-bar {
        flex: 1;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        border-radius: 2px;
      }
    }
  }
  
  .skeleton-footer {
    display: flex;
    gap: 16px;
    margin-top: 16px;
  }
}

.depth-skeleton {
  .skeleton-depth-list {
    .skeleton-depth-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      
      .skeleton-price {
        width: 80px;
      }
      
      .skeleton-amount {
        width: 100px;
      }
    }
  }
}

.orderbook-skeleton {
  .skeleton-tabs {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    
    .skeleton-tab {
      width: 60px;
      height: 32px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: skeleton-loading 1.5s infinite;
      border-radius: 4px;
    }
  }
  
  .skeleton-table {
    .skeleton-row {
      display: flex;
      gap: 8px;
      margin-bottom: 8px;
      
      .skeleton-cell {
        flex: 1;
        height: 20px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        border-radius: 2px;
      }
    }
  }
}

.skeleton-line {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 8px;
  
  &.skeleton-title {
    height: 24px;
    width: 200px;
  }
  
  &.skeleton-subtitle {
    height: 16px;
    width: 150px;
  }
  
  &.skeleton-content {
    height: 16px;
    width: 100%;
  }
  
  &.skeleton-small {
    height: 12px;
    width: 80px;
  }
}

.spinner-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 200px;
}

.smart-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  
  .spinner-ring {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--el-color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  .spinner-text {
    color: var(--el-text-color-secondary);
    font-size: 14px;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.dark {
  .skeleton-line,
  .skeleton-bar,
  .skeleton-tab,
  .skeleton-cell {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
    background-size: 200% 100%;
  }
}
</style>
