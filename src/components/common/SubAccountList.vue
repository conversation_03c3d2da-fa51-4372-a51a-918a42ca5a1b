<template>
  <el-dialog v-model="visible" :title="$t('切换账户')" width="520px" @close="emit('close')" class="check-son-container-dialog">
    <div v-loading="isLoadingCheck" class="sub-account-list">
      <h2>{{ $t('主账户') }}</h2>
      <ul>
        <li class="flex-box space-between" :class="{'active': userInfo.user_id === mainUserInfo.user_id}" @click="checkMainAccount(mainUserInfo.user_id)">
          <span class="active-tag">{{ $t('当前使用') }}</span>
          <div class="flex-box">
            <span class="icon-box mg-r12"></span>
            <span  class="fit-tc-primary font-size-16">{{ mainUserInfo.name }}</span>
          </div>
          <span class="fit-tc-primary">{{ format(mainUserInfo.total_assets_u, 4, true) }} USDT</span>
        </li>
      </ul>
      <h2 v-if="sqSonList.length > 0" class="pd-t20">{{ $t('授权子账户') }}</h2>
      <ul v-if="sqSonList.length > 0">
        <li v-for="(item, index) in sqSonList" :key="index" :class="{'active': userInfo.user_id === item.user_id}" class="flex-box space-between mg-b20" @click="checkMainAccount(item.user_id)">
          <span class="active-tag">{{ $t('当前使用') }}</span>
          <div class="flex-box">
              <span class="icon-box mg-r12"></span>
              <div>
                <span class="fit-tc-primary font-size-16">{{ item.name }}</span>
                <p class="fit-tc-secondary font-size-14">{{ item.desc }}</p>
              </div>
            </div>
          <span class="fit-tc-primary">{{ format(item.total_assets_u, 4, true) }} USDT</span>
        </li>
      </ul>
      <h2 v-if="kjSonList.length > 0" class="pd-t20">{{ $t('快捷子账户') }}</h2>
      <ul v-if="kjSonList.length > 0">
        <li v-for="(item, index) in kjSonList" :key="index" :class="{'active': userInfo.user_id === item.user_id}" class="flex-box space-between mg-b20" @click="checkMainAccount(item.user_id)">
          <span class="active-tag">{{ $t('当前使用') }}</span>
          <div class="flex-box">
              <span class="icon-box mg-r12"></span>
              <div>
                <span class="fit-tc-primary font-size-16">{{ item.desc }}</span>
                <p class="fit-tc-secondary font-size-14">{{ $t('快捷子账户') }}</p>
              </div>
            </div>
          <span class="fit-tc-primary">{{ format(item.total_assets_u, 4, true) }} USDT</span>
        </li>
      </ul>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElDialog } from 'element-plus'
  import { format } from '~/utils'
  import { useCommonData } from '~/composables/index'
  import { switchAccount } from '~/api/user'
  import { useUserStore } from '~/stores/useUserStore'
  const store = useUserStore()
  const { getUserInfoAction, getsubAccountList } = store
  const { userInfo, mainUserInfo, sonInfoList } = storeToRefs(store)
  const useCommon = useCommonData()
  const { locale, t } = useI18n()
  const router = useRouter()
  const isLoadingCheck = ref(false)
  const props = defineProps({
    isShow: {
      type: Boolean,
      default: false
    }
  })
  const sqSonList = computed(() => {
    return sonInfoList.value.filter((item) => {
      return item.type * 1 === 1
    })
  })
  const kjSonList = computed(() => {
    return sonInfoList.value.filter((item) => {
      return item.type * 1 === 2
    })
  })
  const emit = defineEmits(['close'])
  const visible = ref(false)
  const checkMainAccount = async(userId) => {
    const { data, error } = await switchAccount({
      user_id: userId
    })
    if (data) {
      emit('close')
      await getUserInfoAction()
      useCommon.showMsg('success', t('切换账户成功！'))
      router.push(`/${locale.value}/my/dashboard`)
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  onMounted(() => {
    getsubAccountList()
    visible.value = props.isShow
  })
</script>
<style lang="scss">
  @include mb{
    .el-dialog{
      &.check-son-container-dialog{
        width: 95% !important;
        .el-dialog__header{
          padding:12px 0;
        }
        .sub-account-list{
          margin:0 -16px;
        }
      }
    }
  }
  .sub-account-list{
    h2{
      font-size:16px;
      padding-bottom:20px;
      @include color(tc-primary);
    }
    ul{
      li{
        cursor:pointer;
        padding:12px;
        border-radius:12px;
        border:1px solid;
        position:relative;
        @include border-color(bg-quaternary);
        @include bg-color(bg-quaternary);
        .active-tag{
          display:none;
          font-size:12px;
          top:0;
          right:0;
          position:absolute;
          padding:2px 12px;
          border-top-right-radius:10px;
          border-bottom-left-radius:12px;
          @include color(tc-primary);
          @include bg-color(theme);
        }
        .icon-box{
          display:block;
          width:40px;
          height:40px;
          background-size:100% auto;
          @include get-img('@/assets/images/my/sub-head-icon.png', '@/assets/images/my/sub-head-icon-dark.png');
        }
        &.active{
          @include border-color(theme);
          .active-tag{
            display:block;
          }
          .icon-box{
            background-size:100% auto;
            @include get-img('@/assets/images/my/sub-head-icon-active.png', '@/assets/images/my/sub-head-icon-active-dark.png');
          }
        }
      }
    }
  }
</style>
