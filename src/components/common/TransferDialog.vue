<template>
  <el-dialog v-model="visible" width="480" :title="$t('资金划转')" @close="handleClose">
    <ul v-if="sonInfoList.length > 0 && toList.length > 0" class="transfer-tab flex-box">
      <li :class="{'active': type === 1}" @click="type = 1">{{ $t('资金和交易账户') }}</li>
      <li :class="{'active': type === 2}" @click="type = 2">{{ $t('主/子账户') }}</li>
    </ul>
    <template v-if="type === 1">
      <div class="flex-box transfer-items">
        <div class="pd-tb8 pd-lr12 item flex-1">
          <p class="mg-tb0 font-size-12 fit-tc-tertiary">{{ $t('从') }}</p>
          <p class="mg-tb0 font-size-16 tw-5">{{ transferType ? $t('钱包账户') : $t('交易账户') }}</p>
        </div>
        <div class="pd-lr12">
          <MonoTransfer :size="16" class="icon-contract_convert cursor-pointer fit-theme" @click="transferType = !transferType; errMsg = '';params.amount=''" />
        </div>
        <div class="pd-tb8 pd-lr12 item flex-1">
          <p class="mg-tb0 font-size-12 fit-tc-tertiary">{{ $t('到') }}</p>
          <p class="mg-tb0 font-size-16 tw-5">{{ !transferType ? $t('钱包账户') : $t('交易账户') }}</p>
        </div>
      </div>
      <DropdownSelect v-model="params.symbol" isSearch :isShowIcon="true" label="asset" val="asset" :isFormInput="true" class="select-symbol-box" popperClass="dialogw100" :list="filterCoinList" :labelName="$t('选择币种')" wid="100%" @change="searchInput = ''">
        <div class="orders-search-box-input">
          <el-input v-model="searchInput" clearable>
            <template #prepend>
              <MonoSearch size="16" />
            </template>
          </el-input>
        </div>
      </DropdownSelect>
      <el-input type ="text" v-model="params.amount" class="transfer-input">
        <template #suffix>
          <div class="flex-box transfer-input-tag" @click="params.amount = balance.replace(/,/g, '') || 0">
            <span>{{ params.symbol }}</span>
            <em>{{ $t('全部') }}</em>
          </div>
        </template>
      </el-input>
      <div class="transfer-assets">
        {{ $t('可划转') }} {{ balance }} {{ params.symbol }}
      </div>
      <div class="flex-box">
        <el-button :disabled="isDisabled" type="primary" @click="transferFun">{{ $t('确认') }}</el-button>
      </div>
    </template>
    <template v-if="type === 2">
      <div v-loading="sonInfoList.length === 0" class="flex-box transfer-items">
        <div class="item-cont-box flex-1 pd-tb8 pd-lr8">
          <el-select v-model="accontTransferParmas.from_user_id" class="mg-b8">
            <el-option v-for="(item, index) in fromList" :key="`${index}-from`" :value="item.user_id" :label="item.name">
              {{ item.name }}
            </el-option>
          </el-select>
          <el-select v-model="from_type">
            <el-option v-for="(item, index) in accountTypeList" :key="`${index}-account`" :value="item.type" :label="item.name">
              {{ item.name }}
            </el-option>
          </el-select>
        </div>
        <div class="pd-lr12">
          <span v-if="userInfo.is_switch_login * 1 === 0" class="font-size-14 fit-tc-primary">{{ $t('到') }}</span>
          <MonoTransfer v-if="userInfo.is_switch_login * 1 === 1" :size="16" class="icon-contract_convert cursor-pointer fit-theme" @click="transferClickFun()" />
        </div>
        <div class="item-cont-box flex-1 pd-tb8 pd-lr8">
          <el-select v-model="accontTransferParmas.to_user_id" class="mg-b8">
            <el-option v-for="(item, index) in toList" :key="`${index}-to`" :value="item.user_id" :label="item.name">
              {{ item.name }}
            </el-option>
          </el-select>
          <el-select v-model="to_type">
            <el-option v-for="(item, index) in accountTypeList" :key="`${index}-account`" :value="item.type" :label="item.name">
              {{ item.name }}
            </el-option>
          </el-select>
        </div>
      </div>
      <DropdownSelect v-model="accontTransferParmas.coin_symbol" isSearch :isShowIcon="true" label="asset" val="asset" :isFormInput="true" class="select-symbol-box" popperClass="dialogw100" :list="filterCoinList" :labelName="$t('选择币种')" wid="100%" @change="searchInput = ''">
        <div class="orders-search-box-input">
          <el-input v-model="searchInput" clearable>
            <template #prepend>
              <MonoSearch size="16" />
            </template>
          </el-input>
        </div>
      </DropdownSelect>
      <el-input type ="text" v-model="accontTransferParmas.amount" class="transfer-input">
        <template #suffix>
          <div class="flex-box transfer-input-tag" @click="accontTransferParmas.amount = accountBalance.replace(/,/g, '') || 0">
            <span>{{ accontTransferParmas.coin_symbol }}</span>
            <em>{{ $t('全部') }}</em>
          </div>
        </template>
      </el-input>
      <div class="transfer-assets">
        {{ $t('可划转') }} {{ accountBalance }} {{ accontTransferParmas.coin_symbol }}
      </div>
      <div class="flex-box">
        <el-button :disabled="isDisabledSubAccount" type="primary" @click="transferAccountFun">{{ $t('确认') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { format } from '~/utils'
  import { ElDialog, ElSelect, ElInput, ElOption } from 'element-plus'
  import DropdownSelect from '~/components/common/DropdownSelect'
  import MonoTransfer from '~/components/common/icon-svg/MonoTransfer.vue'
  import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
  import { commonStore } from '~/stores/commonStore'
  import { transfer, getSonAssetsAll, multiSideTransfer } from '~/api/tf'
  import { getSwitchAccountList } from '~/api/user'
  import { useCommonData } from '~/composables/index'
  import { useUserStore } from '~/stores/useUserStore'
  const userStore = useUserStore()
  const { userInfo } = storeToRefs(userStore)
  const { locale, t } = useI18n()
  const useCommon = useCommonData()
  const store = commonStore()
  const { getAssetsByCoin } = store
  const { mainAssetObj, tradeAssetObj, coinAssetListObj } = storeToRefs(store)
  const props = defineProps({
    transferType: { // 1 普通划转 2 子主账户划转
      type: Number,
      default: 1
    },
    toUserId: {
      type: [Number, String],
      default: ''
    },
    visibleDialog: {
      type: Boolean,
      default: false
    },
    defaultAccount: {
      type: String,
      default: 'main'
    },
    defaultSymbol: {
      type: String,
      default: 'USDT'
    }
  })
  const emit = defineEmits(['close'])
  const handleClose = () => {
    emit('close')
  }
  const type = ref(1)
  const visible = ref(false)
  const searchInput = ref('')
  const removeTrailingZeros = (num) => {
    let str = num.toString()
    if (str.includes('.')) {
      // 移除小数部分末尾的零
      str = str.replace(/0+$/, '')
      // 处理移除零后可能残留的小数点
      if (str.endsWith('.')) {
        str = str.slice(0, -1)
      }
    }
    return str;
  }
  const transferType = ref(true)
  const params = ref({
    symbol: 'USDT',
    amount: ''
  })
  const isDisabled = computed(() => {
    if (params.value.symbol !== '' && params.value.amount !== '' && params.value.amount * 1 > 0) {
      return false
    } else {
      return true
    }
  })
  const balance = computed(() => {
    if (transferType.value) {
      return JSON.stringify(mainAssetObj.value) !== '{}' ? (mainAssetObj.value[params.value.symbol] ? format(mainAssetObj.value[params.value.symbol].balance, 10, true, true) : 0) : 0
    } else {
      return JSON.stringify(tradeAssetObj.value) !== '{}' ? (tradeAssetObj.value[params.value.symbol] ? format(tradeAssetObj.value[params.value.symbol].withdrawable || 0, 10, true, true) : 0) : 0
    }
  })
  const transferFun = async() => {
    if (isDisabled.value) {
      return false
    }
    const { data, error } = await transfer({
      symbol: params.value.symbol,
      amount: transferType.value ? params.value.amount : -params.value.amount
    })
    if (data) {
      getAssetsByCoin()
      emit('close')
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const subAccountInput = ref('')
  const accontTransferParmas = ref({
    from_user_id: '',
    to_user_id: '',
    coin_symbol: 'USDT',
    amount: ''
  })
  const from_type = ref(1)
  const to_type = ref(1)
  const allList = ref([])
  const fromList = computed(() => {
    return allList.value.filter((item) => {
      if (userInfo.value.is_switch_login * 1 === 0) {
        return userInfo.value.user_id === item.user_id
      } else {
        return (from_type.value * 1 === to_type.value * 1) || (from_type.value * 1 !== to_type.value * 1 && item.user_type * 1 !== 100) ? accontTransferParmas.value.to_user_id !== item.user_id : item
      }
    })
  })
  const toList = computed(() => {
    return allList.value.filter((item) => {
      if (userInfo.value.is_switch_login * 1 === 0) {
        return userInfo.value.father_user_id === item.user_id
      } else {
        return (from_type.value * 1 === to_type.value * 1) || (from_type.value * 1 !== to_type.value * 1 && item.user_type * 1 !== 100) ? accontTransferParmas.value.from_user_id !== item.user_id : item
      }
    })
  })
  const accountTypeList = ref([
    {
      type: 1,
      name: t('钱包账户')
    },
    {
      type: 2,
      name: t('交易账户')
    }
  ])
  const transferClickFun = () => {
    const fromUserId = accontTransferParmas.value.from_user_id
    const fromType = from_type.value
    accontTransferParmas.value.from_user_id = accontTransferParmas.value.to_user_id
    accontTransferParmas.value.to_user_id = fromUserId
    from_type.value = to_type.value
    to_type.value = fromType
  }
  const transferTypeObj = ref({
    '1_1': 1,
    '1_2': 2,
    '2_1': 3,
    '2_2': 4
  })
  const curTransfterType = ref('')
  watch(() => [from_type.value, to_type.value], ([from, to]) => {
    curTransfterType.value = `${from}_${to}`
  })
  const isDisabledSubAccount = computed(() => {
    if (accontTransferParmas.value.from_user_id !== '' && accontTransferParmas.value.to_user_id !== ''&& accontTransferParmas.value.coin_symbol !== '' && accontTransferParmas.value.amount !== '' && accontTransferParmas.value.amount * 1 > 0 && from_type.value !== '' && to_type.value !== '') {
      return false
    } else {
      return true
    }
  })
  const curFromItem = computed(() => {
    return allList.value.filter((item) => {
      return accontTransferParmas.value.from_user_id === item.user_id
    })[0]
  })
  const sonMainAssetObj = ref({})
  const sonTradeAssetObj = ref({})
  const sonAssetsListObj = ref({})
  const accountBalance = computed(() => {
    if ((curFromItem.value || {}).isMain && (curFromItem.value || {}).user_id === userInfo.value.user_id) {
      if (from_type.value * 1 === 1) {
        return JSON.stringify(mainAssetObj.value) !== '{}' ? (mainAssetObj.value[accontTransferParmas.value.coin_symbol] ? format(mainAssetObj.value[accontTransferParmas.value.coin_symbol].balance, 10, true, true) : 0) : 0
      } else {
        return JSON.stringify(tradeAssetObj.value) !== '{}' ? (tradeAssetObj.value[accontTransferParmas.value.coin_symbol] ? format(tradeAssetObj.value[accontTransferParmas.value.coin_symbol].withdrawable || 0, 10, true, true) : 0) : 0
      }
    } else {
      if (from_type.value * 1 === 1) {
        return JSON.stringify(sonMainAssetObj.value) !== '{}' ? (sonMainAssetObj.value[accontTransferParmas.value.coin_symbol] ? removeTrailingZeros(sonMainAssetObj.value[accontTransferParmas.value.coin_symbol].balance) : 0) : 0
      } else {
        return JSON.stringify(sonTradeAssetObj.value) !== '{}' ? (sonTradeAssetObj.value[accontTransferParmas.value.coin_symbol] ? removeTrailingZeros(sonTradeAssetObj.value[accontTransferParmas.value.coin_symbol].withdrawable || 0) : 0) : 0
      }
    }
  })
  const filterNormalCoinList = computed(() => {
    if (type.value * 1 === 1) {
      if (transferType.value) {
        return coinAssetListObj.value.main
      } else {
        return coinAssetListObj.value.trade
      }
    } else {
      if ((curFromItem.value || {}).isMain && (curFromItem.value || {}).user_id === userInfo.value.user_id) {
        if (from_type.value * 1 === 1) {
          return coinAssetListObj.value.main
        } else {
          return coinAssetListObj.value.trade
        }
      } else {
        if (from_type.value * 1 === 1) {
          return sonAssetsListObj.value.main
        } else {
          return sonAssetsListObj.value.trade
        }
      }
    }
  })
  const filterCoinList = computed(() => {
    if (searchInput.value !== '') {
      return filterNormalCoinList.value.filter(v => {
        return v.asset.includes(searchInput.value.toUpperCase()) && v.asset !== 'KtxQ1' && v.asset !== 'KtxQ2'
      }).sort((a, b) => {
        const searchTerm = searchInput.value.toUpperCase()
        const nameA = a.asset.toUpperCase()
        const nameB = b.asset.toUpperCase()
        // 检查是否以搜索词开头
        const aStartsWith = nameA.startsWith(searchTerm)
        const bStartsWith = nameB.startsWith(searchTerm)
        
        // 优先显示以搜索词开头的结果
        if (aStartsWith && !bStartsWith) return -1
        if (!aStartsWith && bStartsWith) return 1
        
        // 其他情况按A-Z排序
        return nameA.localeCompare(nameB)
      })
    } else {
      return filterNormalCoinList.value.filter((item) => {
        return item.asset !== 'KtxQ1' && item.asset !== 'KtxQ2'
      })
    }
  })
  watch(() => curFromItem.value, (val) => {
    if (!(val.isMain && val.user_id === userInfo.value.user_id)) {
      getSonAssets(val.user_id)
    } else {
      getAssetsByCoin()
    }
  })
  const getSonAssets = async(sonUserId) => {
    let mainAssetObj = {}
    let tradeAssetObj = {}
    const { data } = await getSonAssetsAll({
      son_user_id: sonUserId
    })
    if (data) {
      data.main.forEach((item) => {
        mainAssetObj[item.asset] = item
      })
      data.trade.forEach((item) => {
        tradeAssetObj[item.asset] = item
      })
      sonMainAssetObj.value = mainAssetObj
      sonTradeAssetObj.value = tradeAssetObj
      sonAssetsListObj.value = {
        'main': data.main.length > 0 ? data.main.map((item) => {
          item.icon_url =  data.assetmap[item.asset].icon_url
          return item
        }) : [{ 'asset': 'USDT', 'icon_url': data.assetmap['USDT'].icon_url }],
        'trade': data.trade.length > 0 ? data.trade.map((item) => {
          item.icon_url =  data.assetmap[item.asset].icon_url
          return item
        }) : [{ 'asset': 'USDT', 'icon_url': data.assetmap['USDT'].icon_url }]
      }
    }
  }

  const sonInfoList = ref([])
  const getSonListFun = async() => {
    const { data } = await getSwitchAccountList()
    if (data) {
      sonInfoList.value = data.son_info_list
      allList.value = data.son_info_list
      allList.value.unshift({
        user_id: data.main_user_info.user_id,
        name: data.main_user_info.name,
        isMain: true
      })
      accontTransferParmas.value.from_user_id = userInfo.value.user_id || ''
      accontTransferParmas.value.to_user_id = props.toUserId ? props.toUserId : (toList.value.length > 0 ? toList.value[0].user_id : '')
    }
  }
  const transferAccountFun = async() => {
    if (isDisabledSubAccount.value) {
      return false
    }
    const params = {
      transfer_type: transferTypeObj.value[curTransfterType.value],
      from_user_id: accontTransferParmas.value.from_user_id,
      to_user_id: accontTransferParmas.value.to_user_id,
      coin_symbol: accontTransferParmas.value.coin_symbol,
      amount: accontTransferParmas.value.amount,
      lang: locale.value
    }
    const { data, error } = await multiSideTransfer(params)
    if (data) {
      getAssetsByCoin()
      emit('close')
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  onMounted(async() => {
    curTransfterType.value = '1_1'
    type.value = props.transferType
    visible.value = props.visibleDialog
    transferType.value = props.defaultAccount === 'main'
    params.value.symbol = props.defaultSymbol
    await getSonListFun()
    await store.getCoinList()
    await getAssetsByCoin()
  })
</script>
<style lang="scss" scoped>
  .transfer-tab{
    margin-bottom:24px;
    margin-top:0px;
    li{
      font-size:16px;
      position:relative;
      margin-right:20px;
      padding-bottom:12px;
      cursor:pointer;
      @include color(tc-secondary);
      &.active{
        @include color(tc-primary);
        &:after{
          content: '';
          display:block;
          position:absolute;
          bottom:0;
          left:50%;
          margin-left:-8px;
          width:16px;
          height:2px;
          @include bg-color(theme);
        }
      }
    }
  }
  .transfer-items{
    margin-bottom:20px;
    .item{
      border:1px solid;
      border-radius:4px;
      @include border-color(border);
    }
    .item-cont-box{
      border-radius:12px;
      @include bg-color(bg-quaternary);
    }
  }
  .el-button{
    margin-top:30px;
  }
  .transfer-input{
    .transfer-input-tag{
      cursor:pointer;
      font-size:14px;
      span{
        @include color(tc-primary);
      }
      em{
        font-style:inherit;
        cursor:pointer;
        padding-left:12px;
        @include color(theme);
      }
    }
  }
  .transfer-assets{
    padding-top:8px;
    font-size:14px;
    text-align:right;
    @include color(tc-secondary);
  }
</style>
<style lang="scss">
  .select-symbol-box{
    margin-top:0 !important;
    margin-bottom:20px;
    .select-box{
      height:44px !important;
    }
  }
  .orders-search-box-input{
    padding:10px;
    .el-input{
      .el-input__wrapper{
        border-radius:4px;
        .el-input__inner{
          height:36px !important;
        }
      }
    }
  }
</style>