<template>
  <div class="login-register-container dialog-cont">
    <div class="login-register-wrapper flex-column space-between">
      <div class="login-register-box">
        <div class="login-register-box-wrap">
          <div class="login-logo">
            <img src="~/assets/images/common/logo.png" />
          </div>
          <div v-if="isEmail" class="login-register-title">{{ $t('输入您接收到的邮箱验证码') }}</div>
          <div v-if="isEmail" class="login-register-form">
            <el-input v-model="code" type="text" maxlength="6" :placeholder="$t('请在此输入您接收到的邮箱验证码')" class="codeInput" :class="{'input-error': inputError}" @keyup.enter.native="success" @input="inputFun">
              <template #append>
                <div class="yz-container">
                  <span class="small-btn" :class="{'disabled':isPending}" @click="sendCodeFun">{{ isPending ? `${left}S ${$t('重新发送')}` : $t('发送验证码') }}</span>
                </div>
              </template>
            </el-input>
            <span v-if="inputError" class="ts-12 fit-new-error">{{ inputError }}</span>
            <div class="form-info-text">
              <p>{{ $t('*邮箱验证码已发送至{email}请前往获取', { email: params.email }) }}</p>
              <p>{{ $t('*如果长时间未收到验证码，请检查您注册邮箱的垃圾收件箱') }}</p>
            </div>
            <el-button :disabled="code === ''" type="primary" :isLoading="isLoading" class="normal-btn" @click="success">{{ $t('确定') }}</el-button>
          </div>
          <div v-if="isGoogle" class="login-register-title">{{ $t('请输入谷歌验证码') }}</div>
          <div v-if="isGoogle" class="login-register-form">
            <GoogleCodeInput v-model="code" :focus="codeFocus" :input-error="inputError" @blur="codeFocus = false;" @focus="emit('focus', code)" @input="emit('input', code)" />
          </div>
          <div v-if="isLogin && isGoogle && !isEmail" class="verify-code-text mg-t24">
            <el-checkbox v-model="isVerify">
              <div class="left-wrap-cont pd-t16">
                <h2 class="font-size-14 fit-tc-primary" style="font-weight:500;">{{ $t('信任设备, 登录免二次验证') }}</h2>
                <p class="font-size-12 fit-tc-secondary tw-4" style="white-space: break-spaces;">{{ $t('调整请前往账号活动') }}</p>
              </div>
            </el-checkbox>
            <el-button :disabled="code === ''" type="primary" :isLoading="isLoading" class="normal-btn" @click="successGoogle(code)">{{ $t('确定') }}</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <BoxScript id="gt" ref="gt" append-to-body src="https://res.ktx.com/common/js/gt4.js" />
</template>
<script lang="ts" setup>
import { ElInput, ElButton, ElCheckbox } from 'element-plus'
import GoogleCodeInput from './GoogleCodeInput.vue'
import { useCommonData } from '~/composables/index'
import Verify from '~/utils/verify'
import { addUserByEmailAsk, resetLoginByEmailAsk, emailConfirmAsk } from '~/api/user'
const useCommon = useCommonData()
const { locale, t } = useI18n()
const props = defineProps({
  account: {
    default () {
      return null
    },
    type: Object
  },
  isRegister: {
    type: Boolean,
    default: false
  },
  isEmail: {
    type: Boolean,
    default: false
  },
  isGoogle: {
    type: Boolean,
    default: false
  },
  isReset: {
    type: Boolean,
    default: false
  },
  isLogin: {
    type: Boolean,
    default: false
  },
  autoCommit: { // 填完验证码是否自动提交
    type: Boolean,
    default: true
  },
  isLoading: {
    type: Boolean,
    default:false
  },
  params: { // 接口所需的参数
    type: Object,
    default () {
      return {}
    }
  }
})
const emit = defineEmits(['request', 'input', 'focus'])
const code = ref('')
const isVerify = ref(false)
const isPending = ref(false)
const left = ref(60)
const inputError = ref('')
const verifyResigterCode = ref(null)
watch(() => code, (val) => {
  console.info(val, 'ppppppppppp')
})
const sendCodeFun = () => {
  console.info(isPending.value, props.isRegister, 'ddeeee')
  if (isPending.value) {
    return false
  }
  console.info(props.isRegister, 'ddeeee')
  if (props.isRegister || props.isReset || (props.isLogin && props.isEmail)) {
    verifyResigterCode.value = new Verify(getCodeVerify) // 机器验证码的获取
    verifyResigterCode.value.verify()
  }
}
const setIntervalFun = () => { // 验证码倒计时
  let timer = setInterval(() => {
    if (left.value > 0) {
      left.value--
    } else {
      left.value = 60
      isPending.value = false
      clearInterval(timer)
      timer = null
    }
  }, 1000)
}
const success = (() => {
  let key
  let type
  switch (props.isEmail) {
    case true:
      key = 'email_code'
      type = 1
      break
    default:
      key = 'code'
      type = 3
  }
  emit('request', {
    [key]: code.value,
    type,
    realCode: code.value
  })
})
const inputFun = (() => {
  emit('input', code.value)
})
const getCodeVerify = async(err, res) => {
  if (err) {
    return
  }
  if (res.type === 'success' || res.type === 'resend') {
    const params = {
      ...props.params,
      ...res.param
    }
    const { data, error } = props.isRegister ? await addUserByEmailAsk(params) : (props.isLogin && props.isEmail ? await emailConfirmAsk({
      ...res.param,
      lang: locale.value
    }) : await resetLoginByEmailAsk(params))
    if (data) {
      isPending.value = true
      setIntervalFun()
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
}
const codeFocus = ref(true)
const autoSuccess = (code) => {
  if (props.autoCommit) {
    successGoogle(code)
  }
}
const successGoogle = (code) => {
  let key
  let type
  switch (props.isEmail) {
    case true:
      key = 'email_code'
      type = 1
      break
    default:
      key = 'code'
      type = 3
  }
  emit('request',{
    [key]: code,
    type,
    realCode: code,
    trusted: isVerify.value ? 1 : 0
  })
}
onMounted(() => {
  isPending.value = true
  setIntervalFun()
})
</script>
<style lang="scss">
@import '@/assets/style/login/index.scss';
.el-input{
  &.codeInput{
    .el-input-group__append{
      .yz-container{
        height:42px;
        line-height:42px;
        font-size:12px;
        .small-btn{
          display:block;
          height:42px;
          line-height:42px;
          cursor:pointer;
          padding-right:30px;
          @include color(theme);
        }
      }
    }
  }
}
.login-register-container{
  .el-button{
    &.normal-btn{
      width:100%;
      height:40px;
      margin-top:32px;
    }
  }
}
.form-info-text{
  p{
    padding-top:12px;
    font-size:12px;
    @include color(tc-primary);
    a{
      @include color(theme);
    }
  }
}
</style>
