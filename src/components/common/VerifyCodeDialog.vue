<template>
  <el-dialog v-model="dialogTableVisible" class="verify-dialog" append-to-body :close-on-click-modal="false" @close="handleClose">
    <VerifyCode
      :isEmail="isEmail"
      :isGoogle="isGoogle"
      :isRegister="isRegister"
      :isReset="isReset"
      :isLogin="isLogin"
      :isLoading="isLoading"
      :autoCommit="autoCommit"
      :params="params"
      @request="success"
    />
  </el-dialog>
</template>
<script lang="ts" setup>
import { ElDialog  } from 'element-plus'
import VerifyCode from './VerifyCode.vue'
const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  isRegister: {
    type: Boolean,
    default: false
  },
  isEmail: {
    type: Boolean,
    default: false
  },
  autoCommit: { // 填完验证码是否自动提交
    type: Boolean,
    default: true
  },
  isGoogle: {
    type: Boolean,
    default: false
  },
  isReset: {
    type: Boolean,
    default: false
  },
  isLogin: {
    type: Boolean,
    default: false
  },
  isLoading: {
    type: Boolean,
    default:false
  },
  params: { // 接口所需的参数
    type: Object,
    default () {
      return {}
    }
  }
})
const dialogTableVisible = ref(false)
const emit = defineEmits(['handleClose', 'request'])
const handleClose = () => {
  emit('handleClose')
}
const success = (params) => {
  emit('request', params)
}
onMounted (() => {
  dialogTableVisible.value = props.dialogVisible
})
</script>
<style lang="scss">
.verify-dialog{
  .el-dialog{
    width:580px;
    @include bg-color(bg-primary);
    .el-dialog__header{
      padding:0;
    }
    .el-dialog__body{
      padding:20px 60px 60px;
    }
  }
}
@include mb {
  .el-dialog{
    &.verify-dialog{
      margin-top:0 !important;
      box-shadow:none;
      border-radius:0;
      margin:0 !important;
      width:100% !important;
      height:100vh;
      position:fixed;
      top:0;
      left:0;
      right:0;
      bottom:0;
      margin:0 !important;
      @include bg-color(bg-primary);
      .el-dialog__body{
        padding:0 !important;
      }
      .el-dialog__headerbtn{
        top:0;
        .el-dialog__close{
          width:28px;
          height:28px;
        }
      }
    }
  }
}
</style>
