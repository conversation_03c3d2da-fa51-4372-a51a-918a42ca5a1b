<template>
	<div v-if="isShow" class="warn-tips-cont flex-box space-between" :class="{'red': type === 'warn'}">
		<div class="warn-tips-left flex-box">
			<MonoWarn v-if="type === 'warn'" />
			{{ tipsText }}
		</div>
		<div class="warn-tips-right">
			<MonoCloseBg @click="closeTips" />
		</div>
	</div>
</template>
<script lang="ts" setup>
	import MonoWarn from '~/components/common/icon-svg/MonoWarn.vue'
	import MonoCloseBg from '~/components/common/icon-svg/MonoCloseBg.vue'
	const props = defineProps({
		tipsText: {
			type: String,
			default: ''
		},
		isShow: {
			type: Boolean,
			default: false
		},
		type: {
			type: String,
			default: ''
		}
	})
	const emit = defineEmits(['close'])
	const closeTips = () => {
		emit('close')
	}
</script>
<style lang="scss" scoped>
.warn-tips-cont{
	padding:12px 20px;
	border-radius:6px;
	border:1px solid;
	font-size:14px;
	@include color(tc-primary);
	@include border-color(border);
	&.red{
		background-color: rgba(255, 98, 98, 0.04);
		@include color(warn);
		@include border-color(warn);
	}
	.warn-tips-left{
		svg{
			display:block;
			font-size:16px !important;
			margin-right:4px;
		}
	}
	.warn-tips-right{
		width:16px;
		height:16px;
		cursor:pointer;
		margin-left:8px;
		svg{
			font-size:16px !important;
			@include color(tc-tertiary);
		}
	}
}
@include mb {
	.warn-tips-cont{
    padding:8px 12px;
    border-radius:6px;
    border:1px solid;
    font-size:14px;
    @include color(tc-primary);
    @include border-color(border);
    &.red{
      background-color: rgba(255, 98, 98, 0.04);
      @include color(warn);
      @include border-color(warn);
      svg{
        width:16px !important;
        height:16px !important;
      }
    }
    .warn-tips-left{
      svg{
        display:block;
        font-size:16px !important;
        margin-right:4px;
      }
    }
    .warn-tips-right{
      width:16px;
      height:16px;
      cursor:pointer;
      margin-left:8px;
      svg{
        font-size:16px !important;
        @include color(tc-tertiary);
      }
    }
  }
}
</style>