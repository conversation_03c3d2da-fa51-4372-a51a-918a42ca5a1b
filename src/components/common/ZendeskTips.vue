<template>
  <el-tooltip v-if="isShowFun" :content="$t('在线客服')" placement="left">
    <div
      class="draggable-box"
      draggable="true"
      @dragstart="handleDragStart"
      @dragend="handleDragEnd"
      @dragover.prevent
      @drop="handleDrop"
      :style="{
        left: `${position.x}px`,
        top: `${position.y}px`,
      }">
      <div class="robote-icon flex-box space-center" @click.stop="showChat">
        <MonoCustom size="24" style="color:#414655;" />
      </div>
    </div>
  </el-tooltip>
</template>

<script lang="ts" setup>
import { ElTooltip } from 'element-plus'
import MonoCustom from '~/components/common/icon-svg/MonoCustom.vue'
import { useZendesk } from '~/composables/useZendesk'
import { onMounted, onBeforeUnmount } from 'vue'

const { showChat, isChatOpen } = useZendesk();

const isShowFun = computed(() => {
  if (process.client) {
    return true
  } else {
    return false
  }
})

// 初始化位置为右下角
const position = ref({ x: 0, y: 0 })

// 计算右下角位置
const updatePosition = () => {
  if (process.client) {
    const boxWidth = 40 // 与CSS中的宽度一致
    const boxHeight = 40 // 与CSS中的高度一致
    position.value = {
      x: window.innerWidth - boxWidth - 20, // 20px是右边距
      y: window.innerHeight - boxHeight - (window.innerHeight * 0.1) // 10%的底部间距
    }
  }
}

// 组件挂载时计算初始位置并监听resize事件
onMounted(() => {
  updatePosition()
  window.addEventListener('resize', updatePosition)
})

// 组件卸载时移除resize事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', updatePosition)
})

let offset = { x: 0, y: 0 }

const handleDragStart = (e) => {
  // 记录鼠标相对元素左上角的偏移
  offset.x = e.clientX - position.value.x;
  offset.y = e.clientY - position.value.y;
  e.dataTransfer.setData('text/plain', 'dragging');
}

const handleDragEnd = (e) => {
  // 更新元素位置
  position.value = {
    x: e.clientX - offset.x,
    y: e.clientY - offset.y,
  };
}

const handleDrop = (e) => {
  e.preventDefault();
}
</script>

<style lang="scss" scoped>
.draggable-box {
  cursor: pointer;
  position: fixed;
  z-index: 1000;
  width: 40px;
  height: 40px;
  cursor: move;
  user-select: none;
}

.robote-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  @include bg-color(theme);
}
</style>