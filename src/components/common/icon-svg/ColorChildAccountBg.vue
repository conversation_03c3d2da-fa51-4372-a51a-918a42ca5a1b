<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px;`"
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 1024 1024"
    version="1.1"
  >
    <path
      d="M512 256c0 78.222222-64 142.222222-142.222222 142.222222s-142.222222-64-142.222222-142.222222S291.555556 113.777778 369.777778 113.777778s142.222222 64 142.222222 142.222222zM56.888889 777.955556c0-35.555556 0-52.622222 1.422222-68.266667 12.8-102.4 93.866667-182.044444 196.266667-196.266667 14.222222-1.422222 32.711111-1.422222 68.266666-1.422222h95.288889c35.555556 0 52.622222 0 68.266667 1.422222 24.177778 2.844444 48.355556 9.955556 69.688889 21.333334-64 31.288889-110.933333 91.022222-123.733333 163.555555-5.688889 19.911111-5.688889 45.511111-5.688889 93.866667 0 29.866667 0 45.511111 2.844444 58.311111 4.266667 22.755556 12.8 42.666667 25.6 59.733333H189.155556c-17.066667 0-27.022222 0-34.133334-1.422222-51.2-7.111111-91.022222-46.933333-98.133333-98.133333v-32.711111z"
      fill="#C1CEDB"
      p-id="19220"
    ></path>
    <path
      d="M725.333333 455.111111c62.577778 0 113.777778-51.2 113.777778-113.777778s-51.2-113.777778-113.777778-113.777777-113.777778 51.2-113.777777 113.777777 51.2 113.777778 113.777777 113.777778zM489.244444 695.466667c-5.688889 21.333333-5.688889 48.355556-5.688888 100.977777 0 27.022222 0 39.822222 2.844444 51.2 8.533333 29.866667 31.288889 52.622222 59.733333 59.733334 11.377778 2.844444 24.177778 2.844444 51.2 2.844444h256c27.022222 0 39.822222 0 51.2-2.844444 29.866667-8.533333 52.622222-31.288889 59.733334-59.733334 2.844444-11.377778 2.844444-24.177778 2.844444-51.2 0-52.622222 0-79.644444-5.688889-100.977777-15.644444-58.311111-61.155556-105.244444-120.888889-120.888889-21.333333-5.688889-48.355556-5.688889-100.977777-5.688889h-28.444445c-52.622222 0-79.644444 0-100.977778 5.688889-58.311111 15.644444-105.244444 62.577778-120.888889 120.888889z"
      fill="#F0B90B"
      p-id="19221"
    ></path>
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'ColorChildAccountBg',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
