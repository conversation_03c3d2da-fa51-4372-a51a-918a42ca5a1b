<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="2.3"
      y="1.8"
      width="15.4"
      height="16.4"
      rx="2.2"
      stroke="currentColor"
      stroke-width="1.6"
    />
    <circle cx="10" cy="7" r="2" stroke="currentColor" stroke-width="1.8" />
    <path
      d="M14 13C14 12.2044 13.5786 11.4413 12.8284 10.8787C12.0783 10.3161 11.0609 10 10 10C8.93913 10 7.92172 10.3161 7.17157 10.8787C6.42143 11.4413 6 12.2044 6 13"
      stroke="currentColor"
      stroke-width="1.8"
    />
    <path d="M5 15L15 15" stroke="currentColor" stroke-width="2" />
  </svg>
</template>
<script>
export default {
  name: 'MonoAddressBook',
  props: {
    size: {
      default: 24,
      type: [Number, String],
    },
    color: {
      default: '',
      type: String,
    },
  },
}
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
