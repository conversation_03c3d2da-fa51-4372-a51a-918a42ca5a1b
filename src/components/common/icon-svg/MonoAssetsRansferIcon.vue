<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_5106_8595)">
      <path
        d="M4.5 6.28557H11.5L8.3 3.57129"
        stroke="currentColor"
        stroke-width="1.5"
        stroke-linejoin="bevel"
      />
      <path
        d="M12 9.71443L5 9.71443L8.2 12.4287"
        stroke="currentColor"
        stroke-width="1.5"
        stroke-linejoin="bevel"
      />
      <circle cx="8" cy="8" r="7.3" stroke="currentColor" stroke-width="1.4" />
    </g>
    <defs>
      <clipPath id="clip0_5106_8595">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
</template>
<script>
export default {
  name: 'MonoAssetsRansferIcon',
  props: {
    size: {
      default: 24,
      type: [Number, String],
    },
    color: {
      default: '',
      type: String,
    },
  },
}
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
