<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.5791 9.65942L9.65992 9.65942L9.65992 13.5786"
      stroke="currentColor"
      stroke-width="1.5"
    />
    <path
      d="M2.5 6.41943L6.41918 6.41943L6.41918 2.50026"
      stroke="currentColor"
      stroke-width="1.5"
    />
    <path
      d="M6.41895 13.5789L6.41895 9.65968L2.49977 9.65968"
      stroke="currentColor"
      stroke-width="1.5"
    />
    <path
      d="M9.65967 2.5L9.65967 6.41918H13.5788"
      stroke="currentColor"
      stroke-width="1.5"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoCancelscreen',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
