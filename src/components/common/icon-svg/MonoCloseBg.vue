<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.3335 15C12.1995 15 15.3335 11.866 15.3335 8C15.3335 4.13401 12.1995 1 8.3335 1C4.4675 1 1.3335 4.13401 1.3335 8C1.3335 11.866 4.4675 15 8.3335 15ZM6.38873 4.99482L8.33327 6.93936L10.2778 4.99482C10.5707 4.70192 11.0456 4.70192 11.3385 4.99482C11.6314 5.28771 11.6314 5.76258 11.3385 6.05548L9.39393 8.00002L11.3385 9.94456C11.6314 10.2375 11.6314 10.7123 11.3385 11.0052C11.0456 11.2981 10.5707 11.2981 10.2778 11.0052L8.33327 9.06068L6.38873 11.0052C6.09584 11.2981 5.62096 11.2981 5.32807 11.0052C5.03518 10.7123 5.03518 10.2375 5.32807 9.94456L7.27261 8.00002L5.32807 6.05548C5.03518 5.76258 5.03518 5.28771 5.32807 4.99482C5.62096 4.70192 6.09584 4.70192 6.38873 4.99482Z"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoCloseBg',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
