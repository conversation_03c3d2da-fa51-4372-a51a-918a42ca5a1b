<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M7.24334 7.24735C7.20693 7.32656 7.13188 7.38108 7.0453 7.39123L1.02979 8.09657C0.817602 8.12145 0.732364 8.38379 0.889399 8.52863L5.34146 12.6351C5.40554 12.6942 5.43421 12.7824 5.41711 12.8679L4.22903 18.807C4.18712 19.0164 4.41028 19.1786 4.59657 19.074L9.87781 16.1088C9.95382 16.0661 10.0466 16.0661 10.1226 16.1088L15.4038 19.074C15.5901 19.1786 15.8133 19.0164 15.7714 18.807L14.5833 12.8679C14.5662 12.7824 14.5949 12.6942 14.6589 12.6351L19.111 8.52863C19.268 8.38379 19.1828 8.12145 18.9706 8.09657L12.9551 7.39123C12.8685 7.38108 12.7935 7.32656 12.7571 7.24735L10.2274 1.74421C10.1381 1.55011 9.86228 1.55011 9.77305 1.74422L7.24334 7.24735ZM10.0002 6.03854L9.06053 8.08269C8.73283 8.79556 8.05746 9.28625 7.27821 9.37762L5.04374 9.63962L6.69747 11.165C7.27418 11.6969 7.53215 12.4909 7.37825 13.2602L6.93694 15.4663L8.89867 14.3649C9.5828 13.9808 10.4176 13.9808 11.1017 14.3649L13.0635 15.4663L12.6222 13.2602C12.4682 12.4909 12.7262 11.6969 13.3029 11.165L14.9567 9.63962L12.7222 9.37762C11.9429 9.28625 11.2676 8.79556 10.9399 8.08269L10.0002 6.03854Z"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoCollect',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
