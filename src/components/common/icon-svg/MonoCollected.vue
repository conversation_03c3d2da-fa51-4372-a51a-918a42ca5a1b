<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_691_72621)">
      <path
        d="M7.81101 7.50832L10 2.74639L12.189 7.50832C12.3164 7.78555 12.5791 7.97637 12.8821 8.0119L18.0874 8.62224L14.235 12.1756C14.0107 12.3825 13.9104 12.6912 13.9702 12.9904L14.9983 18.1296L10.4284 15.5637C10.1623 15.4144 9.83768 15.4144 9.57162 15.5637L5.00171 18.1296L6.02976 12.9904C6.08961 12.6912 5.98929 12.3825 5.76501 12.1756L1.91259 8.62224L7.11788 8.0119C7.42092 7.97637 7.68357 7.78555 7.81101 7.50832Z"
        fill="currentColor"
        stroke="currentColor"
        stroke-width="1.25"
      />
    </g>
    <defs>
      <clipPath id="clip0_691_72621">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoCollected',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
