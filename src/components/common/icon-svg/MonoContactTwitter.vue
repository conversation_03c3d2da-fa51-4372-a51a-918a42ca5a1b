<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.4375 18.75C20.4375 18.9571 20.2696 19.125 20.0625 19.125H19.125V18.375H20.0625C20.2696 18.375 20.4375 18.5429 20.4375 18.75Z"
      fill="currentColor"
    />
    <path
      d="M19.125 20.625V19.875H20.4375C20.6446 19.875 20.8125 20.0429 20.8125 20.25C20.8125 20.4571 20.6446 20.625 20.4375 20.625H19.125Z"
      fill="currentColor"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M19.3036 4.44818C18.8185 3.80768 18.2363 3.30141 17.5484 2.96428C16.7359 2.56603 15.8382 2.43437 14.8899 2.52964C13.3289 2.68646 12.2324 3.5636 11.6124 4.83569C11.111 5.8643 10.9278 7.1258 10.9659 8.45926C9.75637 8.87003 8.68929 8.60043 7.72113 7.98236C6.54654 7.23251 5.55528 5.97908 4.8496 4.82883C4.63995 4.4871 4.24648 4.30362 3.84994 4.36267C3.4534 4.42172 3.13046 4.71189 3.02947 5.09987C2.52785 7.02709 1.97286 9.57449 3.06399 12.409C3.65007 13.9314 4.82661 15.2003 6.47592 16.2536C4.88854 16.9057 3.63945 17.0802 2.68735 16.8987C2.20463 16.8067 1.7266 17.0791 1.55963 17.5413C1.39266 18.0035 1.58624 18.5185 2.0163 18.7562L2.09555 18.8002C2.69694 19.135 4.70099 20.2505 7.18957 20.9362C9.63883 21.611 12.7531 21.9231 15.4848 20.4496C15.9137 22.27 17.5486 23.625 19.5 23.625C21.7782 23.625 23.625 21.7782 23.625 19.5C23.625 17.4123 22.0741 15.6868 20.0616 15.4129C21.1299 12.9035 21.1125 10.3476 21.1023 8.82997L21.1014 8.69437C21.1597 8.59916 21.2324 8.49361 21.3277 8.3577L21.3454 8.33235C21.4913 8.12442 21.6798 7.85583 21.8532 7.52732C22.2335 6.80714 22.5001 5.88537 22.5001 4.47186C22.5001 4.0746 22.265 3.71501 21.901 3.55574C21.5371 3.39648 21.1134 3.46775 20.8216 3.73731C20.2079 4.30423 19.7183 4.47495 19.3036 4.44818ZM18.1492 18.375C18.5139 17.9826 18.8337 17.5759 19.1142 17.1604C19.1213 17.1891 19.125 17.2191 19.125 17.25V17.625H19.3125V17.25C19.3125 17.0429 19.4804 16.875 19.6875 16.875C19.8946 16.875 20.0625 17.0429 20.0625 17.25V17.625C20.6838 17.625 21.1875 18.1287 21.1875 18.75C21.1875 18.9505 21.135 19.1387 21.0431 19.3018C21.3555 19.5017 21.5625 19.8517 21.5625 20.25C21.5625 20.8713 21.0588 21.375 20.4375 21.375H20.0625V21.75C20.0625 21.9571 19.8946 22.125 19.6875 22.125C19.4804 22.125 19.3125 21.9571 19.3125 21.75V21.375H19.125V21.75C19.125 21.9571 18.9571 22.125 18.75 22.125C18.5429 22.125 18.375 21.9571 18.375 21.75V21.375H18C17.7929 21.375 17.625 21.2071 17.625 21C17.625 20.7929 17.7929 20.625 18 20.625H18.375V18.375H18.1492Z"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoContactTwitter',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
