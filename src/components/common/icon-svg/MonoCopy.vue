<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.75 1.5C7.50736 1.5 6.5 2.50736 6.5 3.75V5H8.5V3.75C8.5 3.61193 8.61193 3.5 8.75 3.5H16.25C16.3881 3.5 16.5 3.61193 16.5 3.75V11.25C16.5 11.3881 16.3881 11.5 16.25 11.5H14.7749V13.5H16.25C17.4926 13.5 18.5 12.4926 18.5 11.25V3.75C18.5 2.50736 17.4926 1.5 16.25 1.5H8.75Z"
      fill="currentColor"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M1.13672 8.32373C1.13672 6.94302 2.25601 5.82373 3.63672 5.82373H10.8335C12.2143 5.82373 13.3335 6.94302 13.3335 8.32373V15.5206C13.3335 16.9013 12.2143 18.0206 10.8335 18.0206H3.63672C2.256 18.0206 1.13672 16.9013 1.13672 15.5206V8.32373ZM3.63672 7.82373H10.8335C11.1097 7.82373 11.3335 8.04759 11.3335 8.32373V15.5206C11.3335 15.7967 11.1097 16.0206 10.8335 16.0206H3.63672C3.36058 16.0206 3.13672 15.7967 3.13672 15.5206V8.32373C3.13672 8.04759 3.36057 7.82373 3.63672 7.82373Z"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoCopy',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
