<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="34"
    height="34"
    viewBox="0 0 34 34"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M28.5054 20.1824V13.6488C28.5054 7.28007 23.3425 2.11719 16.9738 2.11719V2.11719C10.605 2.11719 5.44214 7.28007 5.44214 13.6488V20.1824"
      stroke="currentColor"
      stroke-width="3.47779"
    />
    <rect
      x="2.23889"
      y="18.6373"
      width="6.40645"
      height="8.01825"
      rx="1.73889"
      stroke="currentColor"
      stroke-width="3.47779"
    />
    <rect
      x="25.3021"
      y="18.6373"
      width="6.40645"
      height="8.01825"
      rx="1.73889"
      stroke="currentColor"
      stroke-width="3.47779"
    />
    <path
      d="M13.679 31.6752L21.9158 28.3906"
      stroke="currentColor"
      stroke-width="3.47779"
    />
  </svg>
</template>
<script>
export default {
  name: 'MonoCustom',
  props: {
    size: {
      default: 24,
      type: [Number, String],
    },
    color: {
      default: '',
      type: String,
    },
  },
}
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
