<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M7.1875 0.875C6.63522 0.875 6.1875 1.32272 6.1875 1.875C6.1875 2.42728 6.63522 2.875 7.1875 2.875H12.8125C13.3648 2.875 13.8125 2.42728 13.8125 1.875C13.8125 1.32272 13.3648 0.875 12.8125 0.875H7.1875ZM2.5 3.6875C1.94772 3.6875 1.5 4.13522 1.5 4.6875C1.5 5.23979 1.94772 5.6875 2.5 5.6875H3.41588L3.41656 5.70566L3.84384 15.9605C3.85619 17.5377 5.1386 18.8125 6.71875 18.8125H13.2812C14.8614 18.8125 16.1438 17.5377 16.1562 15.9605L16.5834 5.70566L16.5841 5.6875H17.5C18.0523 5.6875 18.5 5.23979 18.5 4.6875C18.5 4.13522 18.0523 3.6875 17.5 3.6875H14.6476H5.35238H2.5ZM5.84288 15.8959L5.41753 5.6875H14.5825L14.1571 15.8959L14.1562 15.9167V15.9375C14.1562 16.4208 13.7645 16.8125 13.2812 16.8125H6.71875C6.2355 16.8125 5.84375 16.4208 5.84375 15.9375V15.9167L5.84288 15.8959ZM8.925 8.4375C8.925 7.99567 8.56683 7.6375 8.125 7.6375C7.68317 7.6375 7.325 7.99567 7.325 8.4375V14.0625C7.325 14.5043 7.68317 14.8625 8.125 14.8625C8.56683 14.8625 8.925 14.5043 8.925 14.0625V8.4375ZM12.675 8.4375C12.675 7.99567 12.3168 7.6375 11.875 7.6375C11.4332 7.6375 11.075 7.99567 11.075 8.4375V14.0625C11.075 14.5043 11.4332 14.8625 11.875 14.8625C12.3168 14.8625 12.675 14.5043 12.675 14.0625V8.4375Z"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoDelete',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
