<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3.9001 6.0001C3.9001 4.8403 4.8403 3.9001 6.0001 3.9001H6.5001C6.99715 3.9001 7.4001 3.49715 7.4001 3.0001C7.4001 2.50304 6.99715 2.1001 6.5001 2.1001H6.0001C3.84619 2.1001 2.1001 3.84619 2.1001 6.0001V14.0001C2.1001 16.154 3.84619 17.9001 6.0001 17.9001H14.0001C16.154 17.9001 17.9001 16.154 17.9001 14.0001V6.0001C17.9001 3.84619 16.154 2.1001 14.0001 2.1001H13.5001C13.003 2.1001 12.6001 2.50304 12.6001 3.0001C12.6001 3.49715 13.003 3.9001 13.5001 3.9001H14.0001C15.1599 3.9001 16.1001 4.8403 16.1001 6.0001V14.0001C16.1001 15.1599 15.1599 16.1001 14.0001 16.1001H6.0001C4.8403 16.1001 3.9001 15.1599 3.9001 14.0001V6.0001Z"
      fill="currentColor"
    />
    <path
      d="M9.0501 3.8001C9.0501 3.35827 9.40827 3.0001 9.8501 3.0001C10.2919 3.0001 10.6501 3.35827 10.6501 3.8001V11.5445L12.3627 9.83193C12.6751 9.51951 13.1816 9.51951 13.494 9.83193C13.8065 10.1443 13.8065 10.6509 13.494 10.9633L10.5713 13.886C10.1808 14.2765 9.54764 14.2765 9.15712 13.886L6.23441 10.9633C5.92199 10.6509 5.92199 10.1443 6.23441 9.83193C6.54683 9.51951 7.05336 9.51951 7.36578 9.83193L9.0501 11.5162V3.8001Z"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoDownload',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
