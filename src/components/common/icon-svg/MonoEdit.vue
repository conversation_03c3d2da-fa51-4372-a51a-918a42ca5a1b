<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7.85821 2.68186H4.44631C3.31571 2.68186 2.39917 3.59839 2.39917 4.729V11.5529C2.39917 12.6835 3.31571 13.6 4.44631 13.6H11.2701C12.4007 13.6 13.3172 12.6835 13.3172 11.5529L13.3172 8.14093M5.81107 10.188L8.29379 9.68779C8.42559 9.66123 8.5466 9.59633 8.64165 9.50123L14.1994 3.94038C14.4659 3.67377 14.4657 3.2416 14.199 2.97521L13.0217 1.7992C12.7551 1.53292 12.3232 1.5331 12.0568 1.79961L6.49843 7.36103C6.40357 7.45594 6.3388 7.57671 6.31222 7.70824L5.81107 10.188Z"
      stroke="currentColor"
      stroke-width="1.6"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoEdit',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
