<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M4.20552 5.03657L5.2713 4.04215C6.808 2.60835 9.19224 2.60835 10.7289 4.04215L15.4036 8.40377L11.3525 12.1836L9.94943 10.7805C10.5925 10.2303 11 9.41278 11 8.50012C11 6.84327 9.65685 5.50012 8 5.50012C7.08734 5.50012 6.26987 5.90767 5.71964 6.55069L4.20552 5.03657ZM2.186 6.92087L0.59668 8.40377L5.2713 12.7654C6.29874 13.724 7.70505 14.0417 8.98361 13.7185L6.01244 10.7473C5.92072 10.6661 5.83401 10.5794 5.75282 10.4877L2.186 6.92087Z"
      fill="currentColor"
    />
    <rect
      x="1.49414"
      y="4.08008"
      width="2.13362"
      height="15.026"
      transform="rotate(-45 1.49414 4.08008)"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoEyeClose',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
