<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M0.59668 8.40377L5.2713 4.04215C6.808 2.60835 9.19224 2.60835 10.7289 4.04215L15.4036 8.40377L10.7289 12.7654C9.19224 14.1992 6.808 14.1992 5.2713 12.7654L0.59668 8.40377ZM11 8.50012C11 10.157 9.65685 11.5001 8 11.5001C6.34315 11.5001 5 10.157 5 8.50012C5 6.84327 6.34315 5.50012 8 5.50012C9.65685 5.50012 11 6.84327 11 8.50012ZM8 10.0001C8.82843 10.0001 9.5 9.32855 9.5 8.50012C9.5 7.6717 8.82843 7.00012 8 7.00012C7.17157 7.00012 6.5 7.6717 6.5 8.50012C6.5 9.32855 7.17157 10.0001 8 10.0001Z"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoEyeOpen',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
