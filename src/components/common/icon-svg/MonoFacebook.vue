<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.4996 1.50025L13.5494 1.5H18C18.5523 1.5 19 1.94772 19 2.5V6.29985C19 6.85213 18.5523 7.29985 18 7.29985H15.0101V9.7337H18C18.2918 9.7337 18.569 9.86113 18.759 10.0826C18.9489 10.304 19.0327 10.5974 18.9884 10.8857L18.404 14.6842C18.329 15.172 17.9092 15.5322 17.4156 15.5322H15.0101V21.5C15.0101 22.0523 14.5624 22.5 14.0101 22.5H9.62255C9.07027 22.5 8.62255 22.0523 8.62255 21.5V15.5322H6C5.44772 15.5322 5 15.0844 5 14.5322V10.7337C5 10.1814 5.44772 9.7337 6 9.7337H8.58865L8.62248 7.12674L8.61595 6.48371C8.58842 3.75903 10.7749 1.52785 13.4996 1.50025Z"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoFacebook',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
