<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    viewBox="0 0 1024 1024"
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    fill="none"
  >
    <path
      d="M512 682.666667c52.622222 0 98.133333 35.555556 109.511111 85.333333H1024v56.888889H621.511111c-12.8 49.777778-56.888889 85.333333-109.511111 85.333333s-98.133333-35.555556-109.511111-85.333333H0v-56.888889h402.488889c12.8-49.777778 56.888889-85.333333 109.511111-85.333333z m0 56.888889c-31.288889 0-56.888889 25.6-56.888889 56.888888s25.6 56.888889 56.888889 56.888889 56.888889-25.6 56.888889-56.888889-25.6-56.888889-56.888889-56.888888z m512-256v56.888888H0v-56.888888h1024zM512 113.777778c52.622222 0 96.711111 35.555556 109.511111 85.333333H1024v56.888889H621.511111c-11.377778 49.777778-56.888889 85.333333-109.511111 85.333333s-98.133333-35.555556-109.511111-85.333333H0v-56.888889h402.488889c11.377778-49.777778 56.888889-85.333333 109.511111-85.333333z m0 56.888889c-31.288889 0-56.888889 25.6-56.888889 56.888889s25.6 56.888889 56.888889 56.888888 56.888889-25.6 56.888889-56.888888-25.6-56.888889-56.888889-56.888889z"
      p-id="5526"
      fill="currentColor"
    ></path>
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoFibonacci',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
