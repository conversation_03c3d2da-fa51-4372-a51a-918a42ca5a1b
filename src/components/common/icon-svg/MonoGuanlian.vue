<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.2361 0.894758C9.45334 -0.1345 7.17377 0.476306 6.14451 2.25903L4.95133 4.32569L6.68338 5.32569L7.87656 3.25903C8.35354 2.43289 9.40992 2.14984 10.2361 2.62681L11.417 3.30863C12.2431 3.7856 12.5262 4.84198 12.0492 5.66812L10.8561 7.73478L12.5881 8.73478L13.7813 6.66812C14.8105 4.8854 14.1997 2.60583 12.417 1.57657L11.2361 0.894758Z"
      fill="currentColor"
    />
    <path
      d="M4.12656 9.75422L5.61804 7.17091L3.88599 6.17091L2.39451 8.75422C1.36525 10.537 1.97606 12.8165 3.75879 13.8458L4.93973 14.5276C6.72246 15.5569 9.00203 14.946 10.0313 13.1633L11.5228 10.58L9.79071 9.58L8.29923 12.1633C7.82226 12.9895 6.76587 13.2725 5.93973 12.7955L4.75879 12.1137C3.93265 11.6368 3.64959 10.5804 4.12656 9.75422Z"
      fill="currentColor"
    />
    <path
      d="M10.1668 6.15589L8.39537 5.13316L5.6681 9.85693L7.43951 10.8797L10.1668 6.15589Z"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoGuanlian',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
