<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    t="1730088094187"
    class="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="4689"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    width="16"
    height="16"
    fill="none"
  >
    <path
      d="M512 349.866667m-59.733333 0a59.733333 59.733333 0 1 0 119.466666 0 59.733333 59.733333 0 1 0-119.466666 0Z"
      p-id="4690"
      fill="currentColor"
    ></path>
    <path
      d="M512 452.266667c-34.133333 0-59.733333 25.6-59.733333 59.733333v170.666667c0 34.133333 25.6 59.733333 59.733333 59.733333s59.733333-25.6 59.733333-59.733333V512c0-34.133333-25.6-59.733333-59.733333-59.733333z"
      p-id="4691"
      fill="currentColor"
    ></path>
    <path
      d="M512 0C230.4 0 0 230.4 0 512s230.4 512 512 512 512-230.4 512-512-230.4-512-512-512z m0 930.133333c-230.4 0-418.133333-187.733333-418.133333-418.133333S281.6 93.866667 512 93.866667s418.133333 187.733333 418.133333 418.133333-187.733333 418.133333-418.133333 418.133333z"
      p-id="4692"
      fill="currentColor"
    ></path>
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoInfo',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
