<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4.08468 0.771196C4.08468 0.345277 4.42996 0 4.85588 0C5.2818 0 5.62707 0.345276 5.62707 0.771196V2.00001H7.83332C8.47766 2.00001 9 2.52235 9 3.16668V12.8333C9 13.4777 8.47766 14 7.83332 14H5.62707V15.2288C5.62707 15.6547 5.2818 16 4.85588 16C4.42996 16 4.08468 15.6547 4.08468 15.2288V14H2.16668C1.52234 14 1 13.4777 1 12.8333V3.16668C1 2.52235 1.52234 2.00001 2.16668 2.00001H4.08468V0.771196Z"
      fill="currentColor"
    />
    <path
      d="M12 5.49993C12 5.22379 12.2239 4.99993 12.5 4.99993C12.7761 4.99993 13 5.22379 13 5.49993V6.99996H14.282C14.6786 6.99996 15 7.3214 15 7.71792V12.282C15 12.6785 14.6786 13 14.282 13H13V14.4999C13 14.7761 12.7761 14.9999 12.5 14.9999C12.2239 14.9999 12 14.7761 12 14.4999V13H10.718C10.3214 13 10 12.6785 10 12.282V7.71792C10 7.3214 10.3214 6.99996 10.718 6.99996H12V5.49993Z"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoKlineCoin',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
