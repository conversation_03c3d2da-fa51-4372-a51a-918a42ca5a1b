<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M3.8 1C2.2536 1 1 2.2536 1 3.8V12.2C1 13.7464 2.2536 15 3.8 15H12.2C13.7464 15 15 13.7464 15 12.2V3.8C15 2.2536 13.7464 1 12.2 1H3.8ZM13.117 6.68202C13.3821 6.37895 13.3514 5.91829 13.0484 5.65311C12.7453 5.38793 12.2846 5.41864 12.0195 5.7217L9.49371 8.60826L6.96797 5.7217C6.82951 5.56346 6.62948 5.4727 6.41922 5.4727C6.20895 5.4727 6.00893 5.56346 5.87047 5.7217L2.79597 9.23541C2.53079 9.53848 2.5615 9.99914 2.86457 10.2643C3.16764 10.5295 3.6283 10.4988 3.89348 10.1957L6.41922 7.30917L8.94496 10.1957C9.08342 10.354 9.28345 10.4447 9.49371 10.4447C9.70398 10.4447 9.90401 10.354 10.0425 10.1957L13.117 6.68202Z"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoKlineFang',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
