<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_696_72792)">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M12 0C5.37258 0 0 5.37258 0 12C0 18.6274 5.37258 24 12 24C18.6274 24 24 18.6274 24 12C24 5.37258 18.6274 0 12 0ZM20.7719 9.74054C21.2265 9.221 21.1739 8.43129 20.6543 7.97669C20.1348 7.52209 19.3451 7.57473 18.8905 8.09428L14.5606 13.0427L10.2308 8.09428C9.99345 7.82301 9.65054 7.66741 9.29009 7.66741C8.92964 7.66741 8.58673 7.82301 8.34937 8.09428L3.07881 14.1178C2.62421 14.6373 2.67685 15.427 3.1964 15.8816C3.71595 16.3362 4.50565 16.2836 4.96025 15.764L9.29009 10.8157L13.6199 15.764C13.8573 16.0353 14.2002 16.1909 14.5606 16.1909C14.9211 16.1909 15.264 16.0353 15.5014 15.764L20.7719 9.74054Z"
        fill="currentColor"
      />
    </g>
    <defs>
      <clipPath id="clip0_696_72792">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoKlineRotate',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
