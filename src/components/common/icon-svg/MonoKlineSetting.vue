<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M4.5 1V4.5" stroke="currentColor" stroke-width="1.5" />
    <path d="M4.5 12.6665V14.9998" stroke="currentColor" stroke-width="1.5" />
    <rect
      x="2.75"
      y="4.0835"
      width="3.5"
      height="7.83333"
      stroke="currentColor"
      stroke-width="1.5"
    />
    <path d="M11.5 2.1665V5.08317" stroke="currentColor" stroke-width="1.5" />
    <path d="M11.5 11.8892V13.8336" stroke="currentColor" stroke-width="1.5" />
    <rect
      x="9.75"
      y="4.86084"
      width="3.5"
      height="6.27778"
      stroke="currentColor"
      stroke-width="1.5"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoKlineSetting',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
