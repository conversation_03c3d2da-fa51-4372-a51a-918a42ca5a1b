<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M10 19C14.9706 19 19 14.9706 19 10C19 5.02944 14.9706 1 10 1C5.02944 1 1 5.02944 1 10C1 14.9706 5.02944 19 10 19ZM6.50811 10.5002C6.58474 12.8601 7.20068 15.0251 8.18896 16.7635C5.35597 16.0068 3.23065 13.5175 3.0176 10.5002H6.50811ZM7.82223 10.5002C7.91887 12.9768 8.72848 15.1902 9.97121 16.7521C11.2344 15.2015 12.0638 12.9858 12.1748 10.5002H7.82223ZM12.1538 9.00024H7.85157C8.03342 6.68875 8.83855 4.6372 10.0288 3.17611C11.1995 4.64751 11.9859 6.69712 12.1538 9.00024ZM13.4894 10.5002C13.4013 12.8705 12.7691 15.0412 11.7626 16.7762C14.6198 16.0351 16.7681 13.5347 16.9824 10.5002H13.4894ZM16.9291 9.00024H13.472C13.3414 6.84451 12.7598 4.86683 11.8587 3.24946C14.5104 3.97793 16.5326 6.22691 16.9291 9.00024ZM6.5325 9.00024H3.07085C3.46981 6.21037 5.51381 3.95112 8.18877 3.23659C7.27071 4.85158 6.674 6.83472 6.5325 9.00024Z"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoLanguage',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
