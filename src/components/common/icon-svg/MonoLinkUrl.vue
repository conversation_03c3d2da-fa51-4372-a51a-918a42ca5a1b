<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_521_205808)">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M11.2362 0.894642C9.45349 -0.134615 7.17392 0.476191 6.14466 2.25892L4.95148 4.32557L6.68353 5.32557L7.87672 3.25892C8.35369 2.43278 9.41007 2.14972 10.2362 2.62669L11.4172 3.30851C12.2433 3.78548 12.5264 4.84187 12.0494 5.66801L10.8562 7.73466L12.5883 8.73466L13.7814 6.66801C14.8107 4.88528 14.1999 2.60572 12.4172 1.57646L11.2362 0.894642ZM4.12672 9.75411L5.61819 7.17079L3.88614 6.17079L2.39466 8.75411C1.36541 10.5368 1.97622 12.8164 3.75894 13.8457L4.93988 14.5275C6.72261 15.5567 9.00218 14.9459 10.0314 13.1632L11.5229 10.5799L9.79086 9.57989L8.29938 12.1632C7.82241 12.9893 6.76602 13.2724 5.93988 12.7954L4.75894 12.1136C3.9328 11.6366 3.64974 10.5803 4.12672 9.75411ZM10.1669 6.15577L8.39552 5.13304L5.66825 9.85682L7.43967 10.8795L10.1669 6.15577Z"
        fill="currentColor"
      />
    </g>
    <defs>
      <clipPath id="clip0_521_205808">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoLinkUrl',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
