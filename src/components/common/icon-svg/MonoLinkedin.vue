<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1.5 4C1.5 2.34315 2.84315 1 4.5 1C6.15685 1 7.5 2.34315 7.5 4C7.5 5.65685 6.15685 7 4.5 7C2.84315 7 1.5 5.65685 1.5 4Z"
      fill="currentColor"
    />
    <path
      d="M1.5 9C1.5 8.44772 1.94772 8 2.5 8H6.5C7.05228 8 7.5 8.44772 7.5 9V21.5C7.5 22.0523 7.05228 22.5 6.5 22.5H2.5C1.94772 22.5 1.5 22.0523 1.5 21.5V9Z"
      fill="currentColor"
    />
    <path
      d="M9.5 21.5C9.5 22.0523 9.9477 22.5 10.5 22.5H14C14.5523 22.5 15 22.0523 15 21.5V14.4062C15 13.854 15.4477 13.4062 16 13.4062C16.5523 13.4062 17 13.854 17 14.4062V21.5C17 22.0523 17.4477 22.5 18 22.5H21.5C22.0523 22.5 22.5 22.0523 22.5 21.5V14.5C22.5 10.9102 19.5898 8 16 8C12.4102 8 9.5 10.9102 9.5 14.5V21.5Z"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoLinkedin',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
