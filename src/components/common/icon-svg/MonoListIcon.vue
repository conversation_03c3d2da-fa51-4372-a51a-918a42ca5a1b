<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4.78955 11.2C4.34772 11.2 3.98955 11.5582 3.98955 12C3.98955 12.4418 4.34772 12.8 4.78955 12.8H11.4211C11.863 12.8 12.2211 12.4418 12.2211 12C12.2211 11.5582 11.863 11.2 11.4211 11.2L4.78955 11.2Z"
      fill="currentColor"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M4 3C2.34315 3 1 4.34315 1 6V15C1 16.6569 2.34315 18 4 18H16C17.6569 18 19 16.6569 19 15V6C19 4.34315 17.6569 3 16 3H4ZM16 5H4C3.44772 5 3 5.44772 3 6V7.19976H17V6C17 5.44772 16.5523 5 16 5ZM3 15V8.79976H17V15C17 15.5523 16.5523 16 16 16H4C3.44772 16 3 15.5523 3 15Z"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoListIcon',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
