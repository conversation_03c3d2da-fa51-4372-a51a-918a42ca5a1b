<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="3"
      y="1"
      width="14"
      height="17"
      rx="1"
      stroke="currentColor"
      stroke-width="2"
    />
    <circle cx="7" cy="13" r="1" fill="currentColor" />
    <circle cx="7" cy="10" r="1" fill="currentColor" />
    <circle cx="10" cy="13" r="1" fill="currentColor" />
    <circle cx="10" cy="10" r="1" fill="currentColor" />
    <circle cx="13" cy="13" r="1" fill="currentColor" />
    <circle cx="13" cy="10" r="1" fill="currentColor" />
    <rect x="6" y="5" width="8" height="2" fill="currentColor" />
  </svg>
</template>
<script>
export default {
  name: 'MonoMathIcon',
  props: {
    size: {
      default: 24,
      type: [Number, String],
    },
    color: {
      default: '',
      type: String,
    },
  },
}
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
