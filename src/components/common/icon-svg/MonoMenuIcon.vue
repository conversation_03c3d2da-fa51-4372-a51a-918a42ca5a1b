<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M2.99951 4.75C2.99951 4.19772 3.44723 3.75 3.99951 3.75H14.2001C14.7524 3.75 15.2001 4.19772 15.2001 4.75C15.2001 5.30228 14.7524 5.75 14.2001 5.75H3.99951C3.44723 5.75 2.99951 5.30228 2.99951 4.75ZM2.99951 10.0003C2.99951 9.448 3.44723 9.00029 3.99951 9.00029H11.4999C12.0522 9.00029 12.4999 9.448 12.4999 10.0003C12.4999 10.5526 12.0522 11.0003 11.4999 11.0003H3.99951C3.44723 11.0003 2.99951 10.5526 2.99951 10.0003ZM2.99951 15.2506C2.99951 14.6983 3.44723 14.2506 3.99951 14.2506H16.0002C16.5524 14.2506 17.0002 14.6983 17.0002 15.2506C17.0002 15.8029 16.5524 16.2506 16.0002 16.2506H3.99951C3.44723 16.2506 2.99951 15.8029 2.99951 15.2506Z"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoMoon',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
