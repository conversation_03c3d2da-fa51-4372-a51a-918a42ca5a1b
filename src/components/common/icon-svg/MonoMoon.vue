<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      opacity="0.9"
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M14.4026 15.1795C11.8085 13.9747 9.71702 11.8081 8.83376 8.90559C8.35924 7.34627 8.29365 5.76149 8.56645 4.24915C5.92857 5.01314 4 7.44682 4 10.3309C4 13.8274 6.83447 16.6619 10.331 16.6619C11.8802 16.6619 13.3009 16.1056 14.4026 15.1795ZM16.51 15.9189C14.9856 17.6035 12.7819 18.6619 10.331 18.6619C5.72989 18.6619 2 14.932 2 10.3309C2 6.11034 5.13856 2.62279 9.20908 2.07489C9.576 2.0255 9.9505 2 10.331 2C10.7006 2 11.0646 2.02408 11.4215 2.07074C11.0914 2.68966 10.8365 3.33994 10.6647 4.0086C10.3055 5.40615 10.3091 6.88396 10.7471 8.32334C11.5006 10.7994 13.385 12.6539 15.7613 13.588C16.3923 13.8361 17.0579 14.0192 17.7463 14.1318C17.4125 14.7818 16.9957 15.3821 16.51 15.9189Z"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoMoon',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
