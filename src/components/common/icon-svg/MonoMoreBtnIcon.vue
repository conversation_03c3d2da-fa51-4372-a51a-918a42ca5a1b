<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17 10V3.66667C17 2.74619 16.2538 2 15.3333 2H4.66667C3.74619 2 3 2.74619 3 3.66667V16.3333C3 17.2538 3.74619 18 4.66667 18H10"
      stroke="currentColor"
      stroke-width="1.66667"
    />
    <path
      d="M5.99994 7L12.9999 7"
      stroke="currentColor"
      stroke-width="1.33333"
    />
    <path
      d="M5.99994 10H12.9999"
      stroke="currentColor"
      stroke-width="1.33333"
    />
    <circle
      cx="15.4999"
      cy="15.5"
      r="3.875"
      stroke="currentColor"
      stroke-width="1.25"
    />
    <path
      d="M15.5 13.5V16L17.5 16.5"
      stroke="currentColor"
      stroke-width="1.25"
    />
  </svg>
</template>
<script>
export default {
  name: 'MonoMoreBtnIcon',
  props: {
    size: {
      default: 24,
      type: [Number, String],
    },
    color: {
      default: '',
      type: String,
    },
  },
}
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
