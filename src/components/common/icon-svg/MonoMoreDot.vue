<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="26"
    height="29"
    viewBox="0 0 26 29"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5 12.76C5.42 12.76 5.78 12.9 6.08 13.22C6.38 13.5 6.54 13.86 6.54 14.3C6.54 14.72 6.38 15.08 6.08 15.38C5.78 15.68 5.42 15.84 5 15.84C4.56 15.84 4.2 15.68 3.92 15.38C3.6 15.08 3.46 14.72 3.46 14.3C3.46 13.86 3.6 13.5 3.92 13.22C4.2 12.9 4.56 12.76 5 12.76ZM13 12.76C13.42 12.76 13.78 12.9 14.08 13.22C14.38 13.5 14.54 13.86 14.54 14.3C14.54 14.72 14.38 15.08 14.08 15.38C13.78 15.68 13.42 15.84 13 15.84C12.56 15.84 12.2 15.68 11.92 15.38C11.6 15.08 11.46 14.72 11.46 14.3C11.46 13.86 11.6 13.5 11.92 13.22C12.2 12.9 12.56 12.76 13 12.76ZM21 12.76C21.42 12.76 21.78 12.9 22.08 13.22C22.38 13.5 22.54 13.86 22.54 14.3C22.54 14.72 22.38 15.08 22.08 15.38C21.78 15.68 21.42 15.84 21 15.84C20.56 15.84 20.2 15.68 19.92 15.38C19.6 15.08 19.46 14.72 19.46 14.3C19.46 13.86 19.6 13.5 19.92 13.22C20.2 12.9 20.56 12.76 21 12.76Z"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoMoreDot',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
