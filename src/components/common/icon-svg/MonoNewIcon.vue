<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M3.8 1C2.2536 1 1 2.2536 1 3.8V12.2C1 13.7464 2.2536 15 3.8 15H12.2C13.7464 15 15 13.7464 15 12.2V3.8C15 2.2536 13.7464 1 12.2 1H3.8ZM3.22263 7.378L4.91462 10H5.65863V5.728H4.82463V8.344L3.13263 5.728H2.38862V10H3.22263V7.378ZM6.05425 5.728V10H8.86825V9.256H6.88825V8.218H8.57425V7.474H6.88825V6.472H8.8236L9.76506 10H10.4611L11.2471 7.408L12.0331 10H12.7291L13.8751 5.728H13.0051L12.3391 8.422L11.5591 5.728H10.9351L10.1551 8.422L9.49506 5.728H6.05425Z"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoNewIcon',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
