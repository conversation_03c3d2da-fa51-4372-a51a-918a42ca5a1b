<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="16"
    height="17"
    viewBox="0 0 16 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M11.3833 0.868652L3.73672 8.51523C3.48199 8.76996 3.30955 9.09527 3.24172 9.44908L2.68413 12.3572C2.55104 13.0513 3.16042 13.6607 3.85454 13.5276L6.76268 12.97C7.11648 12.9022 7.44179 12.7298 7.69652 12.475L15.3431 4.82845L11.3833 0.868652ZM4.86809 9.6466L11.3833 3.13139L13.0804 4.82845L6.56515 11.3437C6.53685 11.372 6.5007 11.3911 6.46139 11.3987L4.42209 11.7897L4.81309 9.75036C4.82063 9.71105 4.83979 9.6749 4.86809 9.6466Z"
      fill="currentColor"
    />
    <path d="M15 14H1V16H15V14Z" fill="currentColor" />
  </svg>
</template>
<script>
export default {
  name: 'MonoNormalEdit',
  props: {
    size: {
      default: 24,
      type: [Number, String],
    },
    color: {
      default: '',
      type: String,
    },
  },
}
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
