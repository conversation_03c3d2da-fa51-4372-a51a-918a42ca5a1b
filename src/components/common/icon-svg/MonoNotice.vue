<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.0002 1.66675C8.19209 1.66675 6.7143 2.81539 5.76498 3.79845C5.27467 4.3062 4.88585 4.81065 4.61988 5.18699C4.56007 5.27161 4.5062 5.35015 4.45852 5.42125H2.50016C1.5797 5.42125 0.833496 6.16742 0.833496 7.08791V12.8497C0.833496 13.7702 1.5797 14.5164 2.50016 14.5164H4.45443C4.5022 14.5888 4.55619 14.6688 4.61617 14.7551C4.88183 15.1373 5.27029 15.6497 5.76022 16.1655C6.70756 17.1628 8.18664 18.3334 10.0002 18.3334C10.4604 18.3334 10.8335 17.9603 10.8335 17.5001V2.50008C10.8335 2.03984 10.4604 1.66675 10.0002 1.66675Z"
      fill="currentColor"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M12.7119 5.69504C13.0185 5.35175 13.5453 5.32196 13.8886 5.62851C14.1921 5.89959 14.467 6.20288 14.7082 6.53308C15.4158 7.502 15.8335 8.70328 15.8335 10.0001C15.8335 11.2857 15.423 12.4773 14.7265 13.4418C14.481 13.7818 14.1999 14.0937 13.8886 14.3717C13.5453 14.6782 13.0185 14.6484 12.7119 14.3051C12.4054 13.9619 12.4352 13.4351 12.7785 13.1285C12.9997 12.9309 13.2 12.7088 13.3753 12.4661C13.3753 12.4661 13.3753 12.4661 13.3753 12.4661C13.8719 11.7784 14.1668 10.9266 14.1668 10.0001C14.1668 9.06549 13.8668 8.20684 13.3623 7.5161C13.1901 7.28038 12.9942 7.06434 12.7785 6.87167C12.4352 6.56512 12.4054 6.03833 12.7119 5.69504Z"
      fill="currentColor"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M13.6694 2.4696C13.9116 2.0782 14.4251 1.9572 14.8165 2.19933C17.4256 3.81335 19.1668 6.70302 19.1668 10.0001C19.1668 13.3492 17.3702 16.2779 14.6922 17.8763C14.297 18.1122 13.7854 17.9831 13.5496 17.5879C13.3137 17.1927 13.4428 16.6811 13.838 16.4452C16.0331 15.135 17.5002 12.7384 17.5002 10.0001C17.5002 7.30421 16.0783 4.93971 13.9397 3.6167C13.5483 3.37457 13.4273 2.861 13.6694 2.4696Z"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoNotice',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
