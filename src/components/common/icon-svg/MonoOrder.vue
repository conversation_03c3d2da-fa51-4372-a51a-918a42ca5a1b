<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    t="1735196977101"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="13083"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    width="16"
    height="16"
  >
    <path
      d="M640 384c40.704 0 79.644444 3.854222 105.841778 14.762667 11.975111 4.992 16.782222 9.671111 18.531555 11.946666 0.896 1.166222 3.626667 4.707556 3.626667 15.957334v398.222222a28.444444 28.444444 0 0 1-28.444444 28.444444H284.444444a28.444444 28.444444 0 0 1-28.444444-28.444444V199.111111c0-15.729778 12.728889-28.444444 28.416-28.444444H512c28.117333 0 36.892444 5.802667 37.546667 6.257777 0.341333 0.227556 0.384 0.270222 0.597333 0.597334 0.355556 0.540444 1.422222 2.346667 2.602667 6.371555 2.659556 9.130667 4.124444 23.082667 3.925333 44.657778-0.099556 10.211556-0.512 20.622222-0.995556 32.312889l-0.028444 0.682667C555.192889 272.952889 554.666667 286.008889 554.666667 298.666667a85.333333 85.333333 0 0 0 85.333333 85.333333z m1.877333-170.666667c-1.763556-66.275556-21.248-128-129.877333-128H284.416A113.749333 113.749333 0 0 0 170.666667 199.111111v625.777778a113.777778 113.777778 0 0 0 113.777777 113.777778h455.111112a113.777778 113.777778 0 0 0 113.777777-113.777778V426.666667c0-113.777778-128-128-213.333333-128 0-11.036444 0.469333-22.798222 0.967111-34.901334 0.668444-16.497778 1.365333-33.607111 0.910222-50.432z"
      p-id="13084"
      fill="currentColor"
    ></path>
    <path
      d="M341.333333 512a42.666667 42.666667 0 0 1 42.666667-42.666667h128a42.666667 42.666667 0 1 1 0 85.333334h-128a42.666667 42.666667 0 0 1-42.666667-42.666667zM341.333333 682.666667a42.666667 42.666667 0 0 1 42.666667-42.666667h256a42.666667 42.666667 0 1 1 0 85.333333H384a42.666667 42.666667 0 0 1-42.666667-42.666666z"
      p-id="13085"
      fill="currentColor"
    ></path>
  </svg>
</template>
<script>
export default {
  name: 'MonoOrder',
  props: {
    size: {
      default: 24,
      type: [Number, String],
    },
    color: {
      default: '',
      type: String,
    },
  },
}
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
