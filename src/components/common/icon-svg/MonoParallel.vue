<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    viewBox="0 0 1024 1024"
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    fill="none"
  >
    <path
      d="M918.755556 65.422222l39.822222 39.822222-291.555556 291.555556c9.955556 17.066667 15.644444 36.977778 15.644445 58.311111 0 62.577778-51.2 113.777778-113.777778 113.777778-21.333333 0-41.244444-5.688889-58.311111-15.644445l-241.777778 241.777778c9.955556 17.066667 15.644444 36.977778 15.644444 58.311111 0 62.577778-51.2 113.777778-113.777777 113.777778s-113.777778-51.2-113.777778-113.777778 51.2-113.777778 113.777778-113.777777c21.333333 0 41.244444 5.688889 58.311111 15.644444l241.777778-241.777778c-9.955556-17.066667-15.644444-36.977778-15.644445-58.311111 0-62.577778 51.2-113.777778 113.777778-113.777778 21.333333 0 41.244444 5.688889 58.311111 15.644445L918.755556 65.422222zM170.666667 796.444444c-31.288889 0-56.888889 25.6-56.888889 56.888889s25.6 56.888889 56.888889 56.888889 56.888889-25.6 56.888889-56.888889-25.6-56.888889-56.888889-56.888889z m398.222222-398.222222c-31.288889 0-56.888889 25.6-56.888889 56.888889s25.6 56.888889 56.888889 56.888889 56.888889-25.6 56.888889-56.888889-25.6-56.888889-56.888889-56.888889zM520.533333 65.422222l39.822223 39.822222-291.555556 291.555556c9.955556 17.066667 15.644444 36.977778 15.644444 58.311111 0 62.577778-51.2 113.777778-113.777777 113.777778s-113.777778-51.2-113.777778-113.777778 51.2-113.777778 113.777778-113.777778c21.333333 0 41.244444 5.688889 58.311111 15.644445L520.533333 65.422222zM170.666667 398.222222c-31.288889 0-56.888889 25.6-56.888889 56.888889s25.6 56.888889 56.888889 56.888889 56.888889-25.6 56.888889-56.888889-25.6-56.888889-56.888889-56.888889z"
      fill="currentColor"
    ></path>
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoParallel',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
