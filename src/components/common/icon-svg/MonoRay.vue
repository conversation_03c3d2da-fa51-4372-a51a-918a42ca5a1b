<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    viewBox="0 0 1024 1024"
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    fill="none"
  >
    <path
      d="M553.244444 510.577778L958.577778 105.244444l-39.822222-39.822222-405.333334 405.333334c-17.066667-9.955556-36.977778-15.644444-58.311111-15.644445-62.577778 0-113.777778 51.2-113.777778 113.777778 0 21.333333 5.688889 41.244444 15.644445 58.311111l-128 128c-17.066667-9.955556-36.977778-15.644444-58.311111-15.644444-62.577778 0-113.777778 51.2-113.777778 113.777777s51.2 113.777778 113.777778 113.777778 113.777778-51.2 113.777777-113.777778c0-21.333333-5.688889-41.244444-15.644444-58.311111l128-128c17.066667 9.955556 36.977778 15.644444 58.311111 15.644445 62.577778 0 113.777778-51.2 113.777778-113.777778 0-21.333333-5.688889-41.244444-15.644445-58.311111zM512 568.888889c0 31.288889-25.6 56.888889-56.888889 56.888889s-56.888889-25.6-56.888889-56.888889 25.6-56.888889 56.888889-56.888889 56.888889 25.6 56.888889 56.888889zM227.555556 853.333333c0 31.288889-25.6 56.888889-56.888889 56.888889s-56.888889-25.6-56.888889-56.888889 25.6-56.888889 56.888889-56.888889 56.888889 25.6 56.888889 56.888889z"
      fill="currentColor"
    ></path>
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoRay',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
