<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_5322_221107)">
      <path
        d="M12.4475 20.8052C12.2221 20.8057 11.9988 20.7607 11.7911 20.6728C11.6908 20.6304 11.5948 20.5784 11.5045 20.5175C11.415 20.457 11.3315 20.3881 11.2551 20.3118C11.1787 20.2354 11.1098 20.1519 11.0494 20.0624C10.9884 19.9721 10.9364 19.8761 10.894 19.7757C10.8062 19.5681 10.7612 19.3448 10.7617 19.1193C10.7612 18.8938 10.8062 18.6706 10.894 18.4629C10.9364 18.3626 10.9884 18.2666 11.0494 18.1763C11.1098 18.0868 11.1787 18.0032 11.2551 17.9268C11.3315 17.8505 11.415 17.7817 11.5045 17.7212C11.5948 17.6602 11.6908 17.6082 11.7911 17.5658C11.9988 17.4779 12.2221 17.4329 12.4475 17.4334C12.673 17.4329 12.8963 17.478 13.104 17.5658C13.2043 17.6082 13.3003 17.6602 13.3906 17.7212C13.4801 17.7816 13.5636 17.8505 13.64 17.9268C13.7164 18.0032 13.7852 18.0868 13.8457 18.1763C13.9067 18.2666 13.9587 18.3625 14.0011 18.4629C14.0889 18.6706 14.1339 18.8938 14.1334 19.1193C14.1339 19.3448 14.0889 19.5681 14.0011 19.7757C13.9587 19.8761 13.9067 19.9721 13.8457 20.0624C13.7852 20.1519 13.7164 20.2354 13.64 20.3118C13.5636 20.3882 13.4801 20.457 13.3906 20.5175C13.3003 20.5784 13.2043 20.6304 13.104 20.6728C12.8963 20.7607 12.673 20.8057 12.4475 20.8052ZM16.7996 18.7003L12.3316 14.2323C11.0711 14.2672 9.87349 14.7905 8.99153 15.6917L7.86359 14.5638L7.87125 14.5561V14.5507C8.16416 14.2579 8.48433 13.9938 8.82743 13.7619C9.17202 13.5283 9.5384 13.3286 9.92145 13.1656C10.246 13.0291 10.5811 12.9193 10.9236 12.8374L8.73553 10.6494C7.67304 11.1162 6.70804 11.7791 5.89107 12.6033L4.77517 11.4874L4.78173 11.4809L4.78939 11.4732C5.27997 10.9827 5.81629 10.5401 6.39104 10.1516C6.76443 9.89934 7.1532 9.67061 7.55508 9.46676L5.30577 7.21745C5.08697 7.34654 4.87801 7.48329 4.67124 7.62333C3.93303 8.124 3.24431 8.69402 2.61447 9.32563L1.50732 8.20535L1.51826 8.19441C1.86726 7.84651 2.23594 7.51064 2.61229 7.19666C3.10424 6.78792 3.62246 6.41186 4.16361 6.07091L2.88798 4.79528L4.0006 3.68266L17.9177 17.5877L16.805 18.7003H16.7996ZM18.9876 12.5946C18.1319 11.7307 17.1131 11.0455 15.9904 10.5786C14.8677 10.1117 13.6635 9.8725 12.4475 9.87483H12.4246L10.9531 8.40337C11.0822 8.38586 11.2135 8.36945 11.3404 8.35742C11.7079 8.32025 12.0771 8.30163 12.4465 8.30162C12.8158 8.30163 13.185 8.32025 13.5525 8.35742C13.9131 8.39413 14.2717 8.4489 14.6268 8.52152C15.3228 8.6641 16.0029 8.87518 16.6573 9.15168C17.3008 9.42408 17.9163 9.75822 18.4953 10.1494C19.073 10.5371 19.6123 10.9793 20.1057 11.4699L20.1134 11.4776L18.9975 12.5935L18.9876 12.5946ZM22.2697 9.3125C20.9838 8.0169 19.4534 6.98959 17.7673 6.29021C16.0812 5.59082 14.2729 5.23329 12.4475 5.23836C11.0732 5.23695 9.70632 5.44048 8.39201 5.84226L7.1481 4.59726C7.8603 4.33677 8.59095 4.12979 9.33396 3.97805C9.84028 3.8745 10.3515 3.79637 10.8656 3.74393C11.3909 3.69073 11.9185 3.66408 12.4465 3.66406C12.974 3.66411 13.5013 3.69077 14.0262 3.74393C14.5403 3.79637 15.0515 3.87451 15.5579 3.97805C18.5195 4.58482 21.237 6.0501 23.3714 8.19113L23.3801 8.19988L22.2631 9.31688L22.2697 9.3125Z"
        fill="currentColor"
        stroke="currentColor"
        stroke-width="0.915659"
      />
    </g>
    <defs>
      <clipPath id="clip0_5322_221107">
        <rect width="24" height="24" fill="white" transform="translate(0.5)" />
      </clipPath>
    </defs>
  </svg>
</template>
<script>
export default {
  name: 'MonoReseveIconFour',
  props: {
    size: {
      default: 24,
      type: [Number, String],
    },
    color: {
      default: '',
      type: String,
    },
  },
}
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
