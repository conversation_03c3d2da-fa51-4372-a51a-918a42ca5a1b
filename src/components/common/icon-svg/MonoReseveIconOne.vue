<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M21.0246 1.90625H9.14532C8.04316 1.90625 7.14994 2.79967 7.14994 3.90163V4.96587H4.39626C3.29934 4.96567 2.40814 5.85103 2.40088 6.94794V20.4768C2.40088 21.5787 3.2943 22.4721 4.39626 22.4721H16.2756C17.3775 22.4721 18.2709 21.5787 18.2709 20.4768V19.4125H21.0246C22.1215 19.4125 23.0127 18.5272 23.02 17.4304V3.90163C23.02 2.79967 22.1266 1.90625 21.0246 1.90625ZM16.9407 20.477C16.9407 20.8442 16.6428 21.1421 16.2756 21.1421H4.39626C4.02881 21.1421 3.73113 20.8442 3.73113 20.477V6.94794C3.73113 6.77269 3.80152 6.60489 3.92676 6.48227C3.98869 6.42156 4.06212 6.37383 4.14274 6.34186C4.22336 6.3099 4.30955 6.29435 4.39626 6.29613H16.2756C16.6378 6.29592 16.9334 6.58573 16.9407 6.94794V20.477ZM21.6895 17.4304C21.6896 17.5172 21.6724 17.6031 21.6388 17.683C21.6052 17.763 21.5559 17.8355 21.4939 17.8961C21.432 17.9568 21.3586 18.0046 21.278 18.0366C21.1973 18.0685 21.1111 18.0841 21.0244 18.0823H18.2709V6.94774C18.2637 5.85083 17.3723 4.96567 16.2756 4.96567H8.48019V3.90143C8.48019 3.53397 8.77807 3.2363 9.14532 3.2363H21.0246C21.3919 3.2363 21.6898 3.53418 21.6898 3.90143V17.4304H21.6895ZM15.2779 17.7366C15.2779 18.1038 14.98 18.4017 14.6127 18.4017H6.05907C5.69162 18.4017 5.39395 18.1038 5.39395 17.7366C5.39395 17.3691 5.69182 17.0715 6.05907 17.0715H14.6127C14.98 17.0713 15.2779 17.3691 15.2779 17.7366ZM15.2779 13.719C15.2779 14.0863 14.98 14.3841 14.6127 14.3841H6.05907C5.69162 14.3841 5.39395 14.0863 5.39395 13.719C5.39395 13.3516 5.69182 13.0539 6.05907 13.0539H14.6127C14.98 13.0539 15.2779 13.3518 15.2779 13.719ZM15.2779 9.68831C15.2779 10.0558 14.98 10.3534 14.6127 10.3534H6.05907C5.69162 10.3534 5.39395 10.0556 5.39395 9.68831C5.39395 9.32106 5.69182 9.02318 6.05907 9.02318H14.6127C14.98 9.02318 15.2779 9.32106 15.2779 9.68831Z"
      fill="currentColor"
      stroke="currentColor"
      stroke-width="0.904026"
    />
  </svg>
</template>
<script>
export default {
  name: 'MonoReseveIconOne',
  props: {
    size: {
      default: 24,
      type: [Number, String],
    },
    color: {
      default: '',
      type: String,
    },
  },
}
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
