<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    t="1724728257601"
    class="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="52079"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    width="16"
    height="16"
  >
    <path
      d="M426.666667 597.333333l410.638222-410.638222a27.648 27.648 0 0 1 42.012444 35.669333L484.693333 772.451556c-19.484444 27.136-29.212444 40.704-41.130666 45.297777a42.666667 42.666667 0 0 1-32.270223-0.682666c-11.719111-5.091556-20.878222-19.057778-39.182222-46.990223L176.213333 471.068444a44.373333 44.373333 0 0 1 68.494223-55.694222L426.666667 597.333333z"
      p-id="52080"
      fill="currentColor"
    ></path>
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoRigthChecked',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
