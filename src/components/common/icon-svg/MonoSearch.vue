<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M9.53333 4C6.47736 4 4 6.47736 4 9.53333C4 12.5893 6.47736 15.0667 9.53333 15.0667C11.04 15.0667 12.406 14.4645 13.4038 13.4877C13.4053 13.4862 13.4069 13.4847 13.4085 13.4832C14.4318 12.479 15.0667 11.0803 15.0667 9.53333C15.0667 6.47736 12.5893 4 9.53333 4ZM15.4891 14.1469C16.478 12.8722 17.0667 11.2715 17.0667 9.53333C17.0667 5.37279 13.6939 2 9.53333 2C5.37279 2 2 5.37279 2 9.53333C2 13.6939 5.37279 17.0667 9.53333 17.0667C11.2344 17.0667 12.8038 16.5028 14.0649 15.5519L16.3047 17.7187C16.7016 18.1027 17.3347 18.0922 17.7187 17.6953C18.1027 17.2984 18.0922 16.6653 17.6953 16.2813L15.4891 14.1469Z"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoSearch',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
