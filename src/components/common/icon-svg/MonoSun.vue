<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.4738 1.69556V4.30425H8.87383V1.69556H10.4738Z"
      fill="currentColor"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M9.99987 14.0871C12.3411 14.0871 14.239 12.1892 14.239 9.84802C14.239 7.50681 12.3411 5.60889 9.99987 5.60889C7.65867 5.60889 5.76074 7.50681 5.76074 9.84802C5.76074 12.1892 7.65867 14.0871 9.99987 14.0871ZM9.99987 12.0871C11.2365 12.0871 12.239 11.0847 12.239 9.84802C12.239 8.61138 11.2365 7.60889 9.99987 7.60889C8.76324 7.60889 7.76074 8.61138 7.76074 9.84802C7.76074 11.0847 8.76324 12.0871 9.99987 12.0871Z"
      fill="currentColor"
    />
    <path
      d="M14.1593 6.49383L15.5427 5.11036L14.4114 3.97899L13.0279 5.36246L14.1593 6.49383Z"
      fill="currentColor"
    />
    <path
      d="M4.93629 3.97899L6.31976 5.36246L5.18839 6.49383L3.80492 5.11036L4.93629 3.97899Z"
      fill="currentColor"
    />
    <path d="M4.45652 9.0479H2.5V10.6479H4.45652V9.0479Z" fill="currentColor" />
    <path
      d="M10.4738 15.3914V18.0001H8.87383V15.3914H10.4738Z"
      fill="currentColor"
    />
    <path
      d="M4.93612 15.717L6.31959 14.3335L5.18822 13.2021L3.80475 14.5856L4.93612 15.717Z"
      fill="currentColor"
    />
    <path
      d="M14.1594 13.2021L15.5429 14.5856L14.4115 15.717L13.0281 14.3335L14.1594 13.2021Z"
      fill="currentColor"
    />
    <path d="M17.5 9.0479H15.5435V10.6479H17.5V9.0479Z" fill="currentColor" />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoSun',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
