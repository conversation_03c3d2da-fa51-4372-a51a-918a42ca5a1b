<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M9.69385 3C8.66174 3 7.82506 3.83669 7.82506 4.86879C7.82506 5.9009 8.66174 6.73759 9.69385 6.73759C10.726 6.73759 11.5626 5.9009 11.5626 4.86879C11.5626 3.83669 10.726 3 9.69385 3ZM5.82506 4.86879C5.82506 2.73212 7.55717 1 9.69385 1C11.8305 1 13.5626 2.73212 13.5626 4.86879C13.5626 7.00547 11.8305 8.73759 9.69385 8.73759C7.55717 8.73759 5.82506 7.00547 5.82506 4.86879ZM2.00032 14.431C2.00056 12.2945 3.73261 10.5626 5.86912 10.5626H13.5187C15.6555 10.5626 17.3876 12.295 17.3874 14.4318L17.3872 17.3003C17.3871 17.8526 16.9394 18.3003 16.3871 18.3002C15.8348 18.3002 15.3871 17.8524 15.3872 17.3001L15.3874 14.4316C15.3875 13.3994 14.5508 12.5626 13.5187 12.5626H5.86912C4.83709 12.5626 4.00044 13.3992 4.00032 14.4312L4 17.3003C3.99994 17.8526 3.55217 18.3003 2.99989 18.3002C2.4476 18.3002 1.99994 17.8524 2 17.3001L2.00032 14.431Z"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoUserInfo',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
