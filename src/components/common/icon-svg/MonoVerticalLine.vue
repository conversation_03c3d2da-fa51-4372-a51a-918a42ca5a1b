<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    viewBox="0 0 1024 1024"
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    fill="none"
  >
    <path
      d="M483.555556 56.888889h56.888888v345.6c49.777778 12.8 85.333333 56.888889 85.333334 109.511111s-35.555556 98.133333-85.333334 109.511111V967.111111h-56.888888V621.511111c-49.777778-12.8-85.333333-56.888889-85.333334-109.511111s35.555556-98.133333 85.333334-109.511111V56.888889z m28.444444 512c31.288889 0 56.888889-25.6 56.888889-56.888889s-25.6-56.888889-56.888889-56.888889-56.888889 25.6-56.888889 56.888889 25.6 56.888889 56.888889 56.888889z"
      fill="currentColor"
    ></path>
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoVerticalLine',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
