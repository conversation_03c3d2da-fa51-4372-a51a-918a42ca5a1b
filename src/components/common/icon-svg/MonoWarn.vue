<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.3335 15C12.1995 15 15.3335 11.866 15.3335 8C15.3335 4.13401 12.1995 1 8.3335 1C4.4675 1 1.3335 4.13401 1.3335 8C1.3335 11.866 4.4675 15 8.3335 15ZM7.5 4.75C7.5 4.33579 7.83579 4 8.25 4C8.66421 4 9 4.33579 9 4.75V9.25C9 9.66421 8.66421 10 8.25 10C7.83579 10 7.5 9.66421 7.5 9.25V4.75ZM7.5 11.75C7.5 11.3358 7.83579 11 8.25 11C8.66421 11 9 11.3358 9 11.75C9 12.1642 8.66421 12.5 8.25 12.5C7.83579 12.5 7.5 12.1642 7.5 11.75Z"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoWarn',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
