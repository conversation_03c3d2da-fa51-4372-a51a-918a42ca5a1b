<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M0.800049 1H1.86663C3.91348 1 5.57278 2.6593 5.57278 4.70614V9.59091H0.800049V1Z"
      fill="currentColor"
    />
    <path
      d="M11.3008 1H10.2342C8.18735 1 6.52805 2.6593 6.52805 4.70614V9.59091H11.3008V1Z"
      fill="currentColor"
    />
    <path
      d="M4.81519 9.31641H7.28577V9.93405C7.28577 10.6163 6.73271 11.1693 6.05048 11.1693C5.36825 11.1693 4.81519 10.6163 4.81519 9.93405V9.31641Z"
      fill="currentColor"
    />
  </svg>
</template>
<script>
export default {
  name: 'MonoWhiteBookIcon',
  props: {
    size: {
      default: 24,
      type: [Number, String],
    },
    color: {
      default: '',
      type: String,
    },
  },
}
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
