<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M1.6955 7.8654L1.69524 7.86844C1.56516 9.41591 1.49902 10.7816 1.49902 11.9619C1.49902 13.1566 1.56677 14.5412 1.70002 16.112C1.86724 18.0833 3.45044 19.6363 5.42465 19.7654C7.81153 19.9215 10.0034 20 11.999 20C13.9949 20 16.1875 19.9215 18.5757 19.7653C20.5506 19.6362 22.1342 18.0823 22.3006 16.1102C22.4322 14.5504 22.499 13.1666 22.499 11.9619C22.499 10.7718 22.4338 9.40688 22.3053 7.87029C22.1384 5.87341 20.5197 4.30822 18.5183 4.20849C15.7396 4.07006 13.5638 4 11.999 4C10.4344 4 8.25938 4.07 5.48204 4.20844C3.48244 4.30812 1.86467 5.87079 1.6955 7.8654ZM9.74319 9.00745C10.1834 8.35275 11.071 8.17883 11.7258 8.61904L15.0244 10.8371C15.1812 10.9425 15.3155 11.078 15.4192 11.235C15.8542 11.8931 15.6735 12.7794 15.0152 13.2145L11.7166 15.3947C11.4828 15.5492 11.2086 15.6316 10.9287 15.6316C10.1397 15.6316 9.50012 14.9919 9.50012 14.203V9.80456C9.50012 9.52049 9.58506 9.24281 9.74319 9.00745Z"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoYouTube',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
