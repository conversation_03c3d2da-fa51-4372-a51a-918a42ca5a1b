<template>
  <div class="detail-exchange-box">
    <div class="exchnage-title fit-tc-primary font-size-18 pd-b16">{{ $t('交易') }} {{ coin }}</div>
    <ul>
      <li v-for="(item, index) in filterPairList" @click="toTrade(item)">
        <div class="flex-box space-between">
          <span class="fit-tc-primary flex-box" v-if="item.includes('_SWAP')">{{ item.replace('_SWAP', '').replace('_', '') }}<span class="fit-tc-secondary mg-l4" style="display:block;width:80px;">{{ $t('永续') }}</span></span>
          <span class="fit-tc-primary flex-box" v-else>{{ item.replace('_', '/') }}<span class="fit-tc-secondary mg-l4" style="display:block;width:80px;"></span></span>
          <div>
            <span class="fit-tc-primary mg-r8">{{ (markets[item] || {}).last }}</span>
            <span :class="className((markets[item] || {}).change)">{{ format((markets[item] || {}).change * 100, 2, true) }}%</span>
          </div>
          <MonoRightArrowShort size="14" class="fit-tc-secondary" />
        </div>
      </li>
    </ul>
  </div>
</template>
<script lang="ts" setup>
  import { commonStore } from '~/stores/commonStore'
  import { format } from '~/utils'
  const router = useRouter()
  const { locale, t } = useI18n()
  const store = commonStore()
  const { getAllPairList } = store
  const { allPairList } = storeToRefs(store)
  const props = defineProps({
    coin: {
      type: String,
      default: ''
    },
    markets: {
      type: Object,
      default(){
        return {}
      }
    }
  })
  const filterPairList = computed(() => {
    return allPairList.value.filter((item) => {
      return item.split('_')[0] === props.coin
    })
  })
  const className = (c) => {
    if (c * 1 < 0) {
      return 'fit-fall'
    } else {
      return 'fit-rise'
    }
  }
  const toTrade = (pair) => {
    if (pair.includes('_SWAP')) {
      router.push(`/${locale.value}/future/${pair}`)
    } else {
      router.push(`/${locale.value}/exchange/${pair}`)
    }
  }
  onMounted(() => {
    getAllPairList()
  })
</script>
<style lang="scss" scoped>
  .detail-exchange-box{
    padding:16px 16px 12px;
    border:1px solid;
    border-radius:12px;
    margin-bottom:20px;
    @include border-color(border);
    ul{
      li{
        padding:12px 20px;
        border-radius:6px;
        font-size:14px;
        cursor:pointer;
        margin-bottom:8px;
        @include bg-color(bg-secondary);
      }
    }
  }
</style>