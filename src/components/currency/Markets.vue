<template>
  <div class="markets-wrap">
    <div class="markets-top-wrap">
      <div class="exchange-markets-search">
        <el-input
          ref="searchInputRef"
          v-model="search"
          :placeholder="$t('搜索')"
          clearable
          class="search-input-box">
          <template #prepend>
            <MonoSearch size="16" />
          </template>
        </el-input>
      </div>
    </div>
    <div class="markets_wrap-content">
      <el-table :data="filterResult" :row-class-name="getRowClass" height="400px" @row-click="changePair">
        <template #empty>
          <div style="height:400px;">
            <BoxNoData :text="$t('暂无数据')" />
          </div>
        </template>
        <el-table-column :label="$t('币种')" align="left">
          <template #default="{row}">
            <div class="flex-box">
              <BoxCoinIcon :icon="row.icon_url" class="font-size-24 mg-r8" />
              <span class="fit-tc-primary font-size-14">{{ row.pair.split('_')[0] }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('价格')" align="right">
          <template #default="{row}">
            <div :class="Number(markets[row.pair] ? markets[row.pair].change : row.change) < 0 ? 'fit-fall' : 'fit-rise'">
              {{ format(markets[row.pair] ? markets[row.pair].last : row.last, (pairInfo[row.pair] || {}).price_scale, true) }} / {{ useCommon.convert((markets[row.pair] ? markets[row.pair].last : row.last), 'USDT') }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { imgDmain } from '~/config'
  import { ElInput, ElTable } from 'element-plus'
  import { getAllPairs } from '~/api/public'
  import { useCommonData } from '~/composables/index'
  import { commonStore } from '~/stores/commonStore'
  const store = commonStore()
  const { pairInfo } = storeToRefs(store)
  const router = useRouter()
  const { locale, t } = useI18n()
  const useCommon = useCommonData()
  const tableList = ref([])
  const search = ref('')
  const props = defineProps({
    pair: {
      type: String,
      default: ''
    },
    markets: {
      type: Object,
      default () {
        return {}
      }
    }
  })
  const filterResult = computed(() => {
    return tableList.value.filter((item) => {
      return item.pair.includes(search.value.toUpperCase())
    })
  })
  const changePair = (row) => {
    router.push(`/${locale.value}/detail/${row.pair.split('_')[0]}`)
  }
  const getRowClass = ({ row = {} }) => {
    return (row.pair === props.pair) ? 'active cursor-pointer' : ' cursor-pointer'
  }
  const getAllPair = async() => {
    const { data } = await getAllPairs()
    if (data) {
      tableList.value = data.spot
    }
  }
  onMounted(() => {
    getAllPair()
  })
</script>
<style lang="scss" scoped>
.markets-wrap{
  padding:20px;
}
</style>
<style lang="scss">
.markets-wrap{
  .el-table{
    th{
      @include color(tc-secondary);
    }
    th,td{
      border-bottom:0;
    }
    tr{
      cursor:pointer;
      &.active{
        td{
          @include bg-color(bg-quaternary);
        }
      }
    }
  }
}
</style>