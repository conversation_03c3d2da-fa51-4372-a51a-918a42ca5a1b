<template>
  <div v-if="noteList.length > 0" class="activity">
    <div class="fit-tc-primary tw-7 mg-b24 activity-title">{{ $t('热门活动') }}</div>
    <div class="list">
      <template v-for="(item, index) in noteList">
        <a v-if="item.context[locale]" :key="index" :href="item.context[locale].url" target="_blank"
            class="list-item mg-b20"
        >
          <div class="font-size-18 mg-b8 fit-tc-primary">{{ item.context[locale].title }}</div>
          <div class="font-size-14 fit-tc-secondary">{{ timeFormat(item.push_time, 'yyyy-MM-dd hh:mm:ss') }}
          </div>
        </a>
      </template>
    </div>
    <!-- <a :href="``" target="_blank">
      <el-button type="primary" style="border-radius: 4px;width:100%;">{{ $t('查看更多') }}
      </el-button>
    </a> -->
  </div>
</template>
<script lang="ts" setup>
  import { timeFormat } from '~/utils'
  const props = defineProps({
    noteList: {
      type: Array,
      default(){
        return []
      }
    }
  })
  const { locale, t } = useI18n()
</script>
<style lang="scss" scoped>
  .activity {
    margin-bottom: 40px;
    .activity-title{
      font-size:20px;
    }
    .list {
      .list-item {
        display: block;
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
</style>