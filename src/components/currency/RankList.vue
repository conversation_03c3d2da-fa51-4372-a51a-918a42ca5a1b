<template>
<div class="currency-list-cont">
  <div class="rank-list-wrap isPc">
    <ul>
      <li class="item-title flex-box space-between">
        <div class="flex-box">
          {{ $t('成交榜') }}
        </div>
      </li>
      <li v-for="(item, dealKey) in dealList" :key="dealKey" class="item-li flex-box space-between" @click="toTrade(item)">
        <div class="li-left flex-box">
          <BoxCoinIcon :icon="item.icon_url" size="24" class="mg-r16" />
          <div class="left-text">
            <p class="fit-tc-primary">{{ item.pair.replace('_', '/') }}</p>
            <p class="fit-tc-secondary">{{ item.general_name }}</p>
          </div>
        </div>
        <div class="li-right">
          <div class="right-text">
            <p class="fit-tc-primary">{{ markets[item.pair] ? markets[item.pair].last : item.last }}</p>
            <p :class="className(markets[item.pair] ? markets[item.pair].change : item.change)">{{ (markets[item.pair] ? markets[item.pair].change : item.change) * 1 > 0 ? '+' : '' }}{{ format(((markets[item.pair] ? markets[item.pair].change : item.change) * 100), 2, true) }}%</p>
          </div>
        </div>
      </li>
    </ul>
    <ul>
      <li class="item-title flex-box space-between">
        <div class="flex-box">
          {{ $t('涨幅榜') }}
        </div>
      </li>
      <li v-for="(item, upKey) in upList" :key="upKey" class="item-li flex-box space-between" @click="toTrade(item)">
        <div class="li-left flex-box">
          <BoxCoinIcon :icon="item.icon_url" size="24" class="mg-r16" />
          <div class="left-text">
            <p class="fit-tc-primary">{{ item.pair.replace('_', '/') }}</p>
            <p class="fit-tc-secondary">{{ item.general_name }}</p>
          </div>
        </div>
        <div class="li-right">
          <div class="right-text">
            <p class="fit-tc-primary">{{ markets[item.pair] ? markets[item.pair].last : item.last }}</p>
            <p :class="className(markets[item.pair] ? markets[item.pair].change : item.change)">{{ (markets[item.pair] ? markets[item.pair].change : item.change) * 1 > 0 ? '+' : '' }}{{ format(((markets[item.pair] ? markets[item.pair].change : item.change) * 100), 2, true) }}%</p>
          </div>
        </div>
      </li>
    </ul>
  </div>
  <Swiper
      class="rank-list-wrap isMobile"
      ref="mySwiperRef"
      :modules="[SwiperPagination]"
      :slides-per-view="1" 
      :space-between="40" 
      navigation
      :breakpoints="breakpoints" 
      :pagination="{ clickable: true}"
    >
    <SwiperSlide>
      <ul>
        <li class="item-title flex-box space-between">
          <div class="flex-box">
            {{ $t('成交榜') }}
          </div>
        </li>
        <li v-for="(item, dealKey) in dealList" :key="dealKey" class="item-li flex-box space-between" @click="toTrade(item)">
          <div class="li-left">
            <div class="left-text">
              <p class="fit-tc-primary">{{ item.pair.replace('_', '/') }}</p>
              <p class="fit-tc-secondary">{{ item.general_name }}</p>
            </div>
          </div>
          <div class="li-right">
            <div class="right-text">
              <p class="fit-tc-primary">{{ markets[item.pair] ? markets[item.pair].last : item.last }}</p>
              <p :class="className(markets[item.pair] ? markets[item.pair].change : item.change)">{{ (markets[item.pair] ? markets[item.pair].change : item.change) * 1 > 0 ? '+' : '' }}{{ format(((markets[item.pair] ? markets[item.pair].change : item.change) * 100), 2, true) }}%</p>
            </div>
          </div>
        </li>
      </ul>
    </SwiperSlide>
    <SwiperSlide>
      <ul>
        <li class="item-title flex-box space-between">
          <div class="flex-box">
            {{ $t('涨幅榜') }}
          </div>
        </li>
        <li v-for="(item, upKey) in upList" :key="upKey" class="item-li flex-box space-between" @click="toTrade(item)">
          <div class="li-left">
            <div class="left-text">
              <p class="fit-tc-primary">{{ item.pair.replace('_', '/') }}</p>
              <p class="fit-tc-secondary">{{ item.general_name }}</p>
            </div>
          </div>
          <div class="li-right">
            <div class="right-text">
              <p class="fit-tc-primary">{{ markets[item.pair] ? markets[item.pair].last : item.last }}</p>
              <p :class="className(markets[item.pair] ? markets[item.pair].change : item.change)">{{ (markets[item.pair] ? markets[item.pair].change : item.change) * 1 > 0 ? '+' : '' }}{{ format(((markets[item.pair] ? markets[item.pair].change : item.change) * 100), 2, true) }}%</p>
            </div>
          </div>
        </li>
      </ul>
    </SwiperSlide>
  </Swiper>
</div>
</template>
<script setup lang="ts">
  import MonoFire from '~/components/common/icon-svg/MonoFire.vue'
  import MonoKlineRotate from '~/components/common/icon-svg/MonoKlineRotate.vue'
  import { getPairSortList } from '~/api/public'
  import { useCommonData } from '~/composables/index'
  import { MarketsStore } from '~/stores/marketsStore'
  const marketStore = MarketsStore()
  const { getRankList } = marketStore
  const { rankObj } = storeToRefs(marketStore)
  const router = useRouter()
  const { locale, t } = useI18n()
  const useCommon = useCommonData()
  const breakpoints = ref(null)
  const props = defineProps({
    markets: {
      type: Object,
      default () {
        return {}
      }
    }
  })
  breakpoints.value = { 
    768: {  //当屏幕宽度大于等于768 
      slidesPerView: 2,
      spaceBetween: 20
    }
  }
  const getSortList = async(sortBy, orderBy) => {
    const { data, error } = await getPairSortList({
      sort_by: sortBy,
      order_by: orderBy,
      level: 5
    })
    if (data) {
      return data
    } else {
      return []
    }
  }
  const dealList = computed(() => {
    return rankObj.value[0] || []
  })
  const upList = computed(() => {
    return rankObj.value[1] || []
  })
  const className = (c) => {
    if (c * 1 < 0) {
      return 'fit-fall'
    } else {
      return 'fit-rise'
    }
  }
  const toTrade = (item) => {
    router.push(`/${locale.value}/exchange/${item.pair}`)
  }
  onBeforeMount(async () => {
    await getRankList()
  })
</script>
<style lang="scss">
  .currency-list-cont{
    .rank-list-wrap{
      &.isPc{
        display:block;
      }
      &.isMobile{
        display:none;
      }
      .swiper-button-prev, .swiper-button-next{
        display:none;
      }
      ul{
        padding:12px 0;
        border-bottom:1px solid;
        @include border-color(border);
        &:last-child{
          border-bottom:0;
        }
        .item-title{
          height:48px;
          padding:0;
          font-size:20px;
          @include color(tc-primary);
          .more-icon{
            @include color(tc-secondary);
          }
        }
        .item-li{
          padding:8px 0px;
          cursor:pointer;
          @include pc-hover{
            &:hover{
              @include bg-color(bg-quaternary);
            }
          }
          &:active{
            @include bg-color(bg-quaternary);
          }
          .left-text{
            p{
              &:first-child{
                font-size:14px;
              }
              &:last-child{
                font-size:12px;
              }
            }
          }
          .right-text{
            text-align:right;
            p{
              &:first-child{
                font-size:16px;
              }
              &:last-child{
                font-size:14px;
                font-weight:600;
              }
            }
          }
        }
      }
    }
  }
  @include md {
    .currency-list-cont{
      width:100%;
      margin-bottom:12px;
      border:0;
      &.flex-1{
        flex:none;
      }
      .rank-list-wrap{
        padding-bottom:40px;
        &.isPc{
          display:none;
        }
        &.isMobile{
          display:block;
        }
        ul{
          border:1px solid;
          border-radius:20px;
          @include border-color(border);
          &:last-child{
            border-bottom:1px solid;
            @include border-color(border);
          }
          .item-title{
            height:40px;
            padding:0 24px;
            font-size:14px;
            .more-icon{
              font-size:12px;
            }
          }
          .item-li{
            padding:8px 24px;
            .right-text{
              text-align:right;
              p{
                &:first-child{
                  font-size:14px;
                }
              }
            }
          }
        }
        .swiper-pagination{
          .swiper-pagination-bullet{
            width:8px;
            height:4px;
            margin:0 5px;
            border-radius:8px;
            opacity:1;
            @include bg-color(bg-secondary);
            &.swiper-pagination-bullet-active{
              width:8px;
              height:4px;
              border-radius:8px;
              @include bg-color(theme);
              &:after{
                display:none;
              }
            }
          }
        }
      }
    }
  }
  @include mb {
    .rank-list-cont{
      width:100%;
      margin-bottom:12px;
      border:0;
      &.flex-1{
        flex:none;
      }
      .rank-list-wrap{
        padding-bottom:28px;
        &.isPc{
          display:none;
        }
        &.isMobile{
          display:block;
        }
        ul{
          border:1px solid;
          border-radius:20px;
          @include border-color(border);
          &:last-child{
            border-bottom:1px solid;
            @include border-color(border);
          }
          .item-title{
            height:40px;
            padding:0 24px;
            font-size:14px;
            .more-icon{
              font-size:12px;
            }
          }
          .item-li{
            padding:8px 24px;
            .right-text{
              text-align:right;
              p{
                &:first-child{
                  font-size:14px;
                }
              }
            }
          }
        }
        .swiper-pagination{
          .swiper-pagination-bullet{
            width:8px;
            height:4px;
            margin:0 5px;
            border-radius:8px;
            opacity:1;
            @include bg-color(bg-secondary);
            &.swiper-pagination-bullet-active{
              width:8px;
              height:4px;
              border-radius:8px;
              @include bg-color(theme);
              &:after{
                display:none;
              }
            }
          }
        }
      }
    }
  }
</style>
