<template>
  <div class="exchange-charts-container">
    <div class="exchange-charts-wrapper">
      <div class="exchange-charts-nav">
        <el-tabs v-model="subTab">
          <el-tab-pane :name="1">
              <template #label>
                {{ $t('图表') }}
              </template>
            </el-tab-pane>
            <el-tab-pane :name="2">
              <template #label>
                {{ $t('盘口') }}
              </template>
            </el-tab-pane>
            <el-tab-pane :name="3">
              <template #label>
                {{ $t('最新成交') }}
              </template>
            </el-tab-pane>
            <el-tab-pane v-if="isFuture" :name="6">
              <template #label>
                {{ $t('合约信息') }}
              </template>
            </el-tab-pane>
            <el-tab-pane :name="4">
              <template #label>
                {{ $t('币种信息') }}
              </template>
            </el-tab-pane>
        </el-tabs>
      </div>
      <!-- <li :class="{'active': subTab === 5}" @click="subTab = 5">{{ $t('交易数据') }}</li> -->
      <ExchaneCoinInfo v-if="subTab === 4" :coinInfo="coinInfo" :ticker="ticker" />
      <ClientOnly v-if="subTab === 1"><ExchangeChartsKline :depths="propsDepth" :ticker="ticker" :pair="pair" :priceScale="priceScale" @changePeriod="(params) => {emit('changePeriod', params)}"/></ClientOnly>
      <ExchangeData v-if="subTab === 5" />
      <ExchangeDepth v-if="subTab === 2 && !isFuture" :priceScale="priceScale" :quantityScale="quantityScale" :pair="pair" :ticker="ticker" @change-amount="changeAmount" @change-price="changePrice" />
      <FutureDepth v-if="subTab === 2 && isFuture" :priceScale="priceScale" :quantityScale="quantityScale" :pair="pair" :ticker="ticker" @change-amount="changeAmount" @change-price="changePrice" />
      <ExchangeDeals v-if="subTab === 3 && !isFuture" :pair="pair" :priceScale="priceScale" :quantityScale="quantityScale" />
      <FutureDeals
        v-show="subTab === 3 && isFuture"
        :deals="deals"
        :pair="pair"
        :isCBCUnitUSD="isCBCUnitUSD"
        :isCBUUnitUSDT="isCBUUnitUSDT"
        :quantityScale="quantityScale"
        :priceScale="priceScale"
        :futuresType="futuresType"  
      />
      <FutureInfo v-if="subTab === 6" :pair="pair" :ticker="ticker" />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ElTabs, ElTabPane } from 'element-plus'
  import BoxXOverflow from '~/components/common/BoxXOverflow.vue'
  import ExchaneCoinInfo from './charts/CoinIfon.vue'
  import ExchangeChartsKline from './ExchangeChartsKline.vue'
  import ExchangeData from './ExchangeData.vue'
  import ExchangeDepth from './trades/Depth.vue'
  import FutureDepth from '~/components/future/trades/Depth.vue'
  import FutureDeals from '~/components/future/trades/Deals.vue'
  import ExchangeDeals from './trades/Deals.vue'
  import FutureInfo from '~/components/future/trades/futureInfo.vue'
  const props = defineProps({
    coinInfo: {
      type: Object,
      default () {
        return {}
      }
    },
    deals: {
      type: Object,
      default () {
        return []
      }
    },
    futuresType: {
      type: String,
      default: ''
    },
    pair: {
      type: String,
      default: ''
    },
    isCBCUnitUSD: {
      type: Boolean,
      default: false
    },
    isCBUUnitUSDT: {
      type: Boolean,
      default: false
    },
    priceScale: {
      type: [Number, String],
      default: ''
    },
    quantityScale: {
      type: [Number, String],
      default: ''
    },
    propsDepth: {
      type: Object,
      default(){
        return {}
      }
    },
    ticker: {
      type: Object,
      default () {
        return {}
      }
    },
    isFuture: {
      type: Boolean,
      default: false
    }
  })
  const emit = defineEmits(['changePeriod'])
  const subTab = ref(1)
  const changeAmount = (data) => {
    emit('change-amount', data)
  }
  const changePrice = (data) => {
    emit('change-price', data)
  }
</script>
<style lang="scss">
.exchange-charts-container{
  height:100%;
  .exchange-charts-wrapper{
    height:100%;
    position:relative;
    .exchange-charts-nav{
      padding:0 20px;
      height:auto;
      border-bottom:1px solid;
      @include border-color(border);
      .el-tabs__header{
        margin:0;
        .el-tabs__nav-wrap{
          &.is-scrollable{
            padding:0 20px 0 0;
          }
          &:after{
            display:none;
          }
          .el-tabs__nav-next, .el-tabs__nav-prev{
            line-height:46px;
            top:3px;
          }
          .el-tabs__nav-prev{
            z-index:9;
            left:-4px;
            @include bg-color(bg-primary);
            &.is-disabled{
              display:none;
            }
          }
          .el-tabs__nav{
            margin:0;
            .el-tabs__active-bar{
              display:none;
            }
            .el-tabs__item{
              border:0;
              margin-right:20px;
              height:46px;
              line-height:46px;
              position:relative;
              padding:0;
              cursor:pointer;
              @include color(tc-secondary);
              &:nth-child(3), &:nth-child(4) {
                display:none;
              }
              &:nth-child(3){
                margin-left:20px;
              }
              &:last-child{
                margin-right:0;
              }
              &.is-active{
                @include color(tc-primary);
                &:after{
                  content: '';
                  position:absolute;
                  width:16px;
                  height:2px;
                  display:block;
                  bottom:0;
                  left:50%;
                  margin-left:-8px;
                  @include bg-color(theme);
                }
              }
            }
          }
        }
      }
      li{
        margin-right:20px;
        font-size:14px;
        height:46px;
        line-height:46px;
        cursor:pointer;
        @include color(tc-secondary);
        &:nth-child(2), &:nth-child(3) {
          display:none;
        }
        &.active{
          position:relative;
          @include color(tc-primary);
          &:after{
            content: '';
            display:block;
            position:absolute;
            bottom:0;
            left:50%;
            margin-left:-8px;
            width:16px;
            height:2px;
            @include bg-color(theme);
          }
        }
      }
    }
  }
}
@include mb {
  .exchange-charts-container{
    height:500px;
    overflow-y:auto;
    .exchange-charts-wrapper{
      height:calc(100% - 46px);
      .exchange-charts-nav{
        padding:0 16px;
        height:auto;
        border-bottom:1px solid;
        @include border-color(border);
        .el-tabs__header{
        margin:0;
        .el-tabs__nav-wrap{
          &.is-scrollable{
            padding:0 20px 0 0;
          }
          &:after{
            display:none;
          }
          .el-tabs__nav-next, .el-tabs__nav-prev{
            line-height:46px;
            top:3px;
          }
          .el-tabs__nav-prev{
            z-index:9;
            left:-4px;
            @include bg-color(bg-primary);
            &.is-disabled{
              display:none;
            }
          }
          .el-tabs__nav{
            margin:0;
            .el-tabs__active-bar{
              display:none;
            }
            .el-tabs__item{
              border:0;
              margin-right:20px;
              height:46px;
              line-height:46px;
              position:relative;
              padding:0;
              cursor:pointer;
              @include color(tc-secondary);
              &:nth-child(3), &:nth-child(4) {
                display:block;
              }
              &:nth-child(2){
                margin-right:0;
              }
              &:nth-child(3){
                margin-left:20px;
              }
              &:last-child{
                margin-right:0;
              }
              &.is-active{
                @include color(tc-primary);
                &:after{
                  content: '';
                  position:absolute;
                  width:16px;
                  height:2px;
                  display:block;
                  bottom:0;
                  left:50%;
                  margin-left:-8px;
                  @include bg-color(theme);
                }
              }
            }
          }
        }
      }
        li{
          margin-right:20px;
          font-size:14px;
          height:46px;
          line-height:46px;
          cursor:pointer;
          @include color(tc-secondary);
          &:nth-child(2), &:nth-child(3) {
            display:block;
          }
          &.active{
            position:relative;
            @include color(tc-primary);
            &:after{
              content: '';
              display:block;
              position:absolute;
              bottom:0;
              left:50%;
              margin-left:-8px;
              width:16px;
              height:2px;
              @include bg-color(theme);
            }
          }
        }
      }
    }
  }
}
</style>