<template>
  <div class="landing-pair-wrap">
    <BoxCoinIcon :icon="coinInfo.icon_url" class="font-size-48" />
    <div class="flex-box mg-t8">
      <span class="font-size-20 tw-5 fit-tc-primary">{{ coinSymbol }}</span>
    </div>
    <div
      class="flex-box mg-t16 font-size-14 fit-tc-primary"
    >{{ $t('距离 {pair} 交易开放时间({time}), 还有', { pair: data.pair.includes('_SWAP') ? data.pair.replace('_SWAP', '').replace('_', '') : data.pair.replace('_', '/'), time: timeFormat((Number(data.time_left) + Number(data.system_time)) || 0, 'yyyy.MM.dd hh:mm') }) }}</div>
    <div class="mg-t16 tw-b fit-tc-primary countTime-cont">
      <ul class="flex-box align-start">
        <li class="num">
          <div class="num-time">{{ timeLeft.d }}</div>
          <span>{{ $t('天') }}</span>
        </li>
        <li><div class="null">:</div></li>
        <li class="num">
          <div class="num-time">{{ timeLeft.h }}</div>
          <span>{{ $t('时') }}</span>
        </li>
        <li><div class="null">:</div></li>
        <li class="num">
          <div class="num-time">{{ timeLeft.m }}</div>
          <span>{{ $t('分钟') }}</span>
        </li>
        <li><div class="null">:</div></li>
        <li class="num">
          <div class="num-time">{{ timeLeft.s }}</div>
          <span>{{ $t('秒') }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { timeFormat, getTimeLeft } from '~/utils'
  const props = defineProps({
    coinInfo: {
      type: Object,
      default () {
        return {}
      }
    },
    data: {
      type: Object,
      default() {
        return {}
      }
    }
  })
  const emit = defineEmits(['timerEnd'])
  const timer = ref(null)
  const timeLeft = ref({
    d: '--',
    h: '--',
    m: '--',
    s: '--'
  })
  const coinSymbol = computed(() => {
    return (props.data.pair || '').split('_')[0]
  })
  onMounted(() => {
    const time = new Date().getTime()
    const leftTime = props.data.time_left - (time - (props.data.local_time || props.data.system_time))
    timeLeft.value = getTimeLeft(leftTime)
    timer.value = setInterval(() => {
      const time = new Date().getTime()
      const leftTime = props.data.time_left - (time - (props.data.local_time || props.data.system_time))
      timeLeft.value = getTimeLeft(leftTime)
      if (Object.values(timeLeft.value).every(v => Number(v) <= 0)) {
        const timer = setTimeout(() => {
          emit('timerEnd')
          clearTimeout(timer)
          timer.value && clearInterval(timer.value)
        }, 1000)
      }
    }, 1000)
  })
</script>
<style lang="scss" scoped>
.landing-pair-wrap {
  height:100%;
  display: flex !important;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align:center;
  .countTime-cont{
    ul{
      li{
        font-size:34px;
        .num-time{
          width:60px;
          height:60px;
          line-height:60px;
          border-radius:6px;
          @include bg-color(bg-quaternary);
        }
        .null{
          height:60px;
          line-height:60px;
          padding:0 10px;
        }
        span{
          font-size:14px;
          @include color(tc-secondary);
        }
      }
    }
  }
}
@include mb{
  .landing-pair-wrap {
  height:100%;
  display: flex !important;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-top:1px solid;
  border-bottom:1px solid;
  @include border-color(border);
  text-align:center;
  .countTime-cont{
    ul{
      li{
        font-size:34px;
        .num-time{
          width:60px;
          height:60px;
          line-height:60px;
          border-radius:6px;
          @include bg-color(bg-quaternary);
        }
        .null{
          height:60px;
          line-height:60px;
          padding:0 10px;
        }
        span{
          font-size:14px;
          @include color(tc-secondary);
        }
      }
    }
  }
}
}
</style>