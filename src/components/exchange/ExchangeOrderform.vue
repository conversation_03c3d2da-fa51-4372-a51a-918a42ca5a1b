<template>
  <div class="orderform-container animate__animated" :class="{'animate__fadeOutDown': !isShowTrade, 'animate__fadeInUp': isShowTrade}">
    <div class="orderform-wrapper">
      <div class="orderform-title flex-box space-between">
        {{ $t('现货交易') }}
        <MonoClose @click="isShowTrade = false" />
      </div>
      <div class="orderform-cont-box">
        <ul class="orderform-tab">
          <li :class="{'active': type === 'buy'}" @click="changeType('buy')">{{ $t('买入') }}</li>
          <li :class="{'active': type === 'sell'}" @click="changeType('sell')">{{ $t('卖出') }}</li>
        </ul>
        <ul class="orderform-subTab">
          <li :class="{'active': orderType === 'limit'}" @click="orderType = 'limit'">{{ $t('限价单') }}</li>
          <li :class="{'active': orderType === 'market'}" @click="orderType = 'market'">{{ $t('市价单') }}</li>
        </ul>
        <div class="input-orderform-cont">
          <el-form class="input-orderform-wrap">
            <el-form-item :label="$t('价格')">
              <el-input v-if="orderType === 'limit'" v-model="form.price" type="number">
                <template #append>{{ pair.split('_')[1] }}</template>
              </el-input>
              <el-input v-if="orderType === 'market'" v-model="form.price" :placeholder="$t('市价')" :disabled="orderType === 'market'">
                <template #append>{{ pair.split('_')[1] }}</template>
              </el-input>
            </el-form-item>
            <el-form-item :label="$t('数量')">
              <!-- <template #label>
                <div class="input-label flex-box space-between mg-t12" style="margin-bottom:-2px;">
                  <div class="fit-tc-primary font-size-14">{{ $t('数量') }}</div>
                  <div class="flex-box">
                    <span class="font-size-12 fit-tc-secondary">{{ amountPlaceholder }} {{ pair.split('_')[0] }}</span>
                  </div>
                </div>
              </template> -->
              <div class="number-cont">
                <el-input v-model="form.quantity" type="number" min="0" @focus="sliderValue = 0" @input="updateMoney">
                  <template #append>{{ pair.split('_')[0] }}</template>
                </el-input>
                <div class="slider-demo-block">
                  <BoxSlider v-model="sliderValue" :max="max" typeStr="%" :marksValue="marksArr" />
                </div>
                <!-- <p v-if="isShowTips" class="font-size-12 fit-warn pd-t12">{{ amountPlaceholder }} {{ pair.split('_')[0] }}</p> -->
              </div>
            </el-form-item>
            <el-form-item :label="$t('金额')">
              <template #label>
                <div class="input-label flex-box space-between mg-t12" style="margin-bottom:-2px;">
                  <div class="fit-tc-primary font-size-14">{{ $t('金额') }}</div>
                  <div class="flex-box">
                    <span class="font-size-12 fit-tc-secondary">{{ $t('最小') }} 10 USDT</span>
                  </div>
                </div>
              </template>
              <el-input v-model="form.money" type="number" @input="isFocusMoney = true; updateQuantity()">
                <template #append>{{ pair.split('_')[1] }}</template>
              </el-input>
            </el-form-item>
            <div class="banlance-box">
              <div class="banlance-left mg-t12 flex-box space-between">
                <span class="fit-tc-secondary">{{ $t('可用') }}</span>
                <div class="fit-tc-primary flex-box">
                  {{isLogin ? (type === 'buy' ? assets.currency : assets.coin) : 0 }} {{ type === 'buy' ? pair.split('_')[1] : pair.split('_')[0] }}
                  <MonoAssetsRansferIcon v-if="isLogin" size="16" class="fit-theme mg-l8 cursor-pointer" @click="transferFun()" />
                </div>
              </div>
              <!-- <div v-if="sliderValue > 0 " class="banlance-left flex-box space-between mg-r24 mg-t4">
                <span class="fit-tc-secondary">{{ $t('可买') }}</span>
                <span class="fit-tc-primary">{{ Number(type === 'buy' ? assets.currency : assets.coin) || (isLogin ? 0 : '-') }} {{ type === 'buy' ? pair.split('_')[1] : pair.split('_')[0] }}</span>
              </div> -->
            </div>
            <template v-if="isLogin">
              <el-button v-if="type === 'buy'" type="primary" :loading="isLoading" class="input-btn rise" @click="trade">{{ $t('买入') }} {{ pair.split('_')[0] }}</el-button>
              <el-button v-if="type === 'sell'" type="primary" :loading="isLoading" class="input-btn fall" @click="trade">{{ $t('卖出') }} {{ pair.split('_')[0] }}</el-button>
            </template>
            <div v-else class="no-login-btn">
              <NoLoginCont />
            </div>
          </el-form>
        </div>
      </div>
    </div>
  </div>
  <div class="orderform-btn-box flex-box space-between">
    <template v-if="isLogin">
      <div class="flex-1">
        <el-button type="primary" class="rise-btn" @click="showTrade('buy')">{{ $t('买入') }}</el-button>
      </div>
      <div class="flex-1">
        <el-button type="primary" class="fall-btn" @click="showTrade('sell')">{{ $t('卖出') }}</el-button>
      </div>
    </template>
    <div v-else class="no-login-btn flex-1">
      <NoLoginCont />
    </div>
  </div>
  <TransferDialog v-if="isShowTransfer" :visibleDialog="isShowTransfer" :defaultSymbol="defaultSymbol" @close="isShowTransfer = false" />
</template>
<script lang="ts" setup>
import BigNumber from 'bignumber.js'
import { format } from '~/utils'
import { ElForm, ElFormItem, ElInput, ElButton } from 'element-plus'
import BoxSlider from '~/components/common/BoxSlider.vue'
import TransferDialog from '~/components/common/TransferDialog.vue'
import NoLoginCont from '~/components/common/NoLoginCont.vue'
import MonoAssetsRansferIcon from '~/components/common/icon-svg/MonoAssetsRansferIcon.vue'
import { useCommonData } from '~/composables/index'
import { openOrder } from '~/api/order'
const { locale, t } = useI18n()
const props = defineProps({
  price: {
    type: String,
    default: ''
  },
  amount: {
    type: [Number, String],
    default: ''
  },
  isLogin: {
    type: Boolean,
    default: false
  },
  pairPricePrecision: {
    type: [Number, String],
    default: ''
  },
  quantityScale: {
    type: [Number, String],
    default: ''
  },
  pair: {
    type: String,
    default: ''
  },
  tradeAssetObj: {
    type:Object,
    default(){
      return{}
    }
  },
  ticker: {
    type:Object,
    default(){
      return{}
    }
  },
  pairInfo: {
    type: Object,
    default(){
      return{}
    }
  }
})
const emit = defineEmits(['orderSuccess'])
const useCommon = useCommonData()
const type = ref('buy')
const sliderValue = ref<number>(0)
const min = ref(0);
const max = ref(100)
const marks = reactive<Marks>({
  0: '0%',
  25: '',
  50: '',
  75: '',
  100: '100%'
})
const marksArr = ref([1, 25, 50, 75, 100])
const isShowTrade = ref(false)
const isShowTransfer = ref(false)
const style = computed(() => {
  const length = max.value - min.value,
  progress = sliderValue.value - min.value,
  left = (progress / length) * 100;
  return {
    paddingLeft: `${left}%`
  }
})
const changeValue = (num) => {
  sliderValue.value = num
}
const showTrade = (typeP) => {
  isShowTrade.value = true
  type.value = typeP
}
const assets = computed(() => {
  const result = {
    coin: (props.tradeAssetObj[props.pair.split('_')[0]] ? format(props.tradeAssetObj[props.pair.split('_')[0]].withdrawable, 10, true, true) : 0) || '-',
    currency: (props.tradeAssetObj[props.pair.split('_')[1]] ? format(props.tradeAssetObj[props.pair.split('_')[1]].withdrawable, 10, true, true) : 0) || '-',
    uExperience: '-',
    coinExperience: '-'
  }
  console.log(result, 'dhdiehdeijeijdiejie')
  return result
})
const orderType = ref('limit')
const form = ref({
  symbol: '',
  price: '',
  quantity: '',
  money: ''
})
const maxTrade = computed(() => {
  const price = orderType.value === 'limit' ? form.value.price : (props.ticker || {}).last
  const assetP = Number(type.value === 'buy' ? assets.value.currency.replace(/,/g, '') : assets.value.coin.replace(/,/g, ''))
  const result = assetP
  return (!isFinite(result) || isNaN(result) || !result) ? 0 : result
})
const isShowTips = ref(false)
const val = computed(() => {
  return format(((props.pairInfo[props.pair] || {}).min_order_size || 0), props.quantityScale, true)
})
const amountPlaceholder = computed(() => {
  return val.value ? t('最小') + ' ' + val.value : ''
})
const curPrice = computed(() => {
  return orderType.value === 'limit' ? form.value.price : (props.ticker || {}).last
})
const defaultSymbol = ref('')
const transferFun = () => {
  isShowTransfer.value = true
  defaultSymbol.value = type.value === 'buy' ? 'USDT' : props.pair.split('_')[0]
}
watch(() => orderType.value, (val) => {
  form.value.price = ''
  form.value.quantity = ''
  form.value.money = ''
  sliderValue.value = 0
})
watch(() => props.pair, (val) => {
  sliderValue.value = 0
  form.value = {
    symbol: val,
    price: '',
    quantity: '',
    money: ''
  }
  orderType.value = 'limit'
})
watch(() => props.ticker.last, (val) => {
  if (val && !form.value.price && orderType.value === 'limit') {
    form.value.price = val
  }
})
watch(() => form.value.price, (val, oldVal) => {
  if (isNaN(val) || Number(val) < 0 || val === '00') {
    form.value.price = oldVal
    return
  }
  if (val && val.split('.')[1] && val.split('.')[1].length > props.pairPricePrecision) {
    form.value.price = toFixedF(val, props.pairPricePrecision)
    return
  }
  if (form.value.quantity !== '' && sliderValue.value) {
    form.value.quantity = toFixedF(new BigNumber(maxTrade.value).multipliedBy(sliderValue.value / 100), props.quantityScale)
  }
  updateMoney()
})
watch(() => sliderValue.value, (val, oldVal) => {
  if (val && curPrice.value) {
    if (type.value === 'buy') {
      form.value.money = toFixedF(new BigNumber(maxTrade.value).multipliedBy(val / 100), props.pairPricePrecision)
      form.value.quantity = toFixedF(new BigNumber(form.value.money).dividedBy(curPrice.value), props.quantityScale)
    } else {
      form.value.quantity = toFixedF(new BigNumber(maxTrade.value).multipliedBy(val / 100), props.quantityScale)
      form.value.money = toFixedF(new BigNumber(form.value.money).dividedBy(curPrice.value), props.pairPricePrecision)
    }
  } else {
    form.value.money = ''
    form.value.quantity = ''
  }
})
const isFocusMoney = ref(false)
watch(() => form.value.quantity, (num, oldVal) => {
  if (isNaN(num) || Number(num) < 0 || num === '00') {
    form.value.quantity = oldVal
  }
  if (num && num.split('.')[1] && num.split('.')[1].length > props.quantityScale) {
    form.value.quantity = toFixedF(num, props.quantityScale)
  }
  if (!isFocusMoney.value) {
    updateMoney()
  }
})
watch(() => form.value.money, (val, oldVal) => {
  if (isNaN(val) || Number(val) < 0 || val === '00') {
    form.value.money = oldVal
    return
  }
})
watch(() => props.price, (val) => {
  if (val && Number(val) > 0 && orderType.value === 'limit') {
    form.value.price = val
  }
})
watch(() => orderType.value, (val) => {
  if (val === 'limit') {
    form.value.price = props.ticker.last
  }
})
watch(() =>props.amount, (val) => {
  if (val && Number(val) > 0) {
    form.value.quantity = val
  }
})
const inputMoney = false
const updateMoney = () => {
  if (form.value.quantity && curPrice.value) {
    form.value.money = toFixedF(new BigNumber(form.value.quantity).multipliedBy(curPrice.value), props.pairPricePrecision);
  } else {
    form.value.money = '';
  }
}
const updateQuantity = () => {
  if (form.value.money && curPrice.value) {
    form.value.quantity = toFixedF(new BigNumber(form.value.money).dividedBy(curPrice.value), props.quantityScale);
  } else {
    form.value.quantity = '';
  }
}
const changeType = (typeP) => {
  sliderValue.value = 0
  type.value = typeP
  form.value = {
    symbol: props.pair,
    price: '',
    quantity: '',
    money: ''
  }
  orderType.value = 'limit'
}
const toFixedF = (num, decimal) => {
  num = num.toString()
  if (!num) {
    return ''
  }
  if (decimal === 0 && num.endsWith('.')) {
    return num.replace('.', '')
  }
  if (decimal === 0) {
    return num.split('.')[0]
  }
  if (num.endsWith('.')) {
    return num
  }
  const test = new RegExp(`^\\d*(\\.?\\d{0,${decimal}})`, 'g')
  const bigNumberResult = new BigNumber(num).toFixed(decimal, BigNumber.ROUND_DOWN)
  const result = ((num + '').match(test) || [])[0]
  return Number(bigNumberResult) !== Number(result) ? bigNumberResult : result
}
const verifyForm = () => {
  if (!Number(form.value.price) && orderType.value === 'limit') {
    useCommon.showMsg('error', t('请输入委托价'))
    return false
  } else if (form.value.quantity === '') {
    useCommon.showMsg('error', t('请输入数量'))
    return false
  } else if (Number(form.value.quantity) < val.value * 1) {
    useCommon.showMsg('error', `${amountPlaceholder.value} ${props.pair.split('_')[0]}`)
    return false
  } else if (form.value.money < 10) {
    useCommon.showMsg('error', t('下单金额不能低于{valT}', { valT: '10 USDT' }))
    return false
  }
  return true
}
const isLoading = ref(false)
const trade = async() => {
  if (!props.isLogin || !verifyForm()) {
    return false
  }
  isLoading.value = true
  const { data, error } = await openOrder({
    type: orderType.value,
    price: form.value.price,
    quantity: type.value === 'buy' ? form.value.quantity : -form.value.quantity,
    symbol: props.pair
  })
  if (data) {
    isLoading.value = false
    useCommon.showMsg('success', t('下单成功！'))
    form.value = {
      symbol: props.pair,
      price: '',
      quantity: '',
      money: ''
    }
    isShowTrade.value = false
    emit('orderSuccess')
  } else {
    isLoading.value = false
    console.info(error, 'error.codeerror.code')
    useCommon.showMsg('error', useCommon.err(error.code, error))
  }
}
</script>
<style lang="scss">
@import url('@/assets/style/animate.scss');
.orderform-container{
  height:100%;
  &.animate__animated{
    animation-duration: 0s;
  }
  &.animate__fadeOutDown{
    animation-name: fadeInUp;
  }
  .orderform-wrapper{
    height:100%;
    .orderform-title{
      height:46px;
      line-height:46px;
      border-bottom:1px solid;
      padding:0 16px;
      font-size:14px;
      @include border-color(border);
      @include color(tc-primary);
      svg{
        display:none;
        &.setting-cont{
          display:block;
          cursor:pointer;
        }
      }
    }
    .order-cont-box{
      height:calc(100% - 40px);
      overflow:auto;
    }
    .orderform-cont-box{
      .orderform-tab{
        padding:12px 16px 0;
        display:flex;
        flex:1 1;
        li{
          height:40px;
          line-height:40px;
          text-align:center;
          border-radius:4px;
          flex:1;
          font-size:14px;
          cursor:pointer;
          @include bg-color(tc-tertiary);
          @include color(tc-button);
          &:first-child{
            margin-right:6px;
            &.active{
              @include bg-color(rise);
            }
          }
          &:last-child{
            margin-left:6px;
            &.active{
              @include bg-color(fall);
            }
          }
        }
      }
      .orderform-subTab{
        height:46px;
        line-height:46px;
        display:flex;
        padding:0 16px;
        border-bottom:1px solid;
        @include border-color(border);
        li{
          font-size:14px;
          margin-right:20px;
          cursor:pointer;
          height:46px;
          line-height:46px;
          position:relative;
          @include color(tc-secondary);
          &.active{
            @include color(tc-primary);
            &:after{
              display:block;
              content: '';
              position:absolute;
              bottom:0;
              left:50%;
              margin-left:-8px;
              width:16px;
              height:2px;
              @include bg-color(theme);
            }
          }
        }
      }
      .input-orderform-cont{
        padding:10px 16px 0;
        .banlance-box{
          font-size:14px;
          position:relative;
          .banlance-right{
            position:absolute;
            right:0;
            bottom:-30px;
            cursor:pointer;
            @include color(theme);
          }
        }
        .el-form{
          &.input-orderform-wrap{
            .el-form-item{
              margin-bottom:0;
              .number-cont{
                width:100%;
              }
              .el-form-item__label{
                font-size:14px;
                margin:0;
                display:block;
                width:100%;
                height:36px;
                line-height:36px;
                padding:0;
              }
              .el-input-group--append>.el-input__wrapper{
                border-radius:4px;
              }
              .el-input{
                .el-input__wrapper{
                  .el-input__inner{
                    height:42px !important;
                    padding-right:56px;
                  }
                }
                .el-input-group__append{
                  width:56px;
                  padding-left:12px;
                  border-left:1px solid;
                  @include border-color(border);
                }
              }
            }
            .el-button {
              &.input-btn, &.el-button--primary{
                margin-top:38px;
                width:100%;
                color:#ffffff !important;
                height:40px;
                &.rise{
                  @include bg-color(rise);
                  @include border-color(rise);
                }
                &.fall{
                  @include bg-color(fall);
                  @include border-color(fall);
                }
              }
            }
          }
        }
      }
    }
  }
}
.orderform-btn-box{
  display:none;
}
@include mb {
  .orderform-container{
    display:flex;
    position:fixed;
    top:0;
    left:0;
    bottom:0;
    right:0;
    background-color: rgba(0,0,0,0.4);
    align-items: flex-end;
    z-index:1900 !important;
    &.animate__animated{
      animation-duration: 0s;
    }
    &.animate__fadeOutDown{
      animation-name: fadeOutDown;
    }
    .orderform-wrapper{
      width:100%;
      border-radius: 20px 20px 0px 0px;
      height:80%;
      @include bg-color(bg-primary);
      .orderform-title{
        svg{
          display:block;
          font-size:16px;
          @include color(tc-primary);
        }
      }
    }
  }
  .orderform-btn-box{
    display:flex;
    padding:12px 6px;
    .no-login-btn{
      margin-top:0;
    }
    .flex-1{
      padding:0 6px;
    }
    .el-button{
      width:100%;
      height:40px;
      &.el-button--primary{
        color:#ffffff !important;
      }
      &.rise-btn{
        @include bg-color(rise);
        @include border-color(rise);
      }
      &.fall-btn{
        @include bg-color(fall);
        @include border-color(fall);
      }
    }
  }
}
</style>