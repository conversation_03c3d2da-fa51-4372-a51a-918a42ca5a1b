<template>
  <div class="exchange-orders">
    <div class="orders-title flex-box space-between">
      <div class="title-menu-box">
        <div class="title-left">
          <el-tabs v-model="orderType">
            <el-tab-pane :name="4">
              <template #label>
                {{ $t('资产') }}
              </template>
            </el-tab-pane>
            <el-tab-pane :name="1">
              <template #label>
                {{ $t('当前委托') }}
                <span v-if="times > 0"> · {{ times }}</span>
              </template>
            </el-tab-pane>
            <el-tab-pane :name="2">
              <template #label>
                {{ $t('历史委托') }}
              </template>
            </el-tab-pane>
            <el-tab-pane :name="3">
              <template #label>
                {{ $t('成交记录') }}
              </template>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <div class="title-right flex-box">
        <div class="check-box">
          <el-checkbox v-model="isShowOnlyPair">{{ $t('只显示当前交易对') }}</el-checkbox>
        </div>
        <a v-if="orderType === 4" class="fit-theme flex-box transfer-text cursor-pointer" @click="isShowTransfer = true">
          <span style="white-space:nowrap;" class="font-size-14">{{ $t('划转') }}</span>
          <MonoAssetsRansferIcon size="16" class="fit-theme mg-l4 cursor-pointer" />
        </a>
        <el-button v-if="orderType * 1 === 1 && times >= 2" type="primary" size="small" @click="isShowCancellOrder = true">{{ $t('批量撤单') }}</el-button>
        <div  class="more-txt">
          <nuxtLink :to="orderType === 1 ? `/${locale}/my/orders/spot${isShowOnlyPair ? `?pair=${pair}` : ''}` : ( orderType === 2 ? `/${locale}/my/orders/spot/history${isShowOnlyPair ? `?pair=${pair}` : ''}` : `/${locale}/my/orders/spot/deal${isShowOnlyPair ? `?pair=${pair}` : ''}`)" class="flex-box">
            <MonoMoreBtnIcon size="16" />
          </nuxtLink>
        </div>
      </div>
    </div>
    <template v-if="isLogin">
      <ExchangeCurrentOrders
        v-if="orderType * 1 === 1"
        :isLoading="isLoading"
        :isLogin="isLogin"
        :pair="pair"
        :currentList="currentList"
      />
      <ExchangeHistoryOrders
        v-if="orderType * 1 === 2"
        :isLoading="isLoading"
        :isLogin="isLogin"
        :currentList="historyList"
        :pair="pair"
        :isShowOnlyPair="isShowOnlyPair"
        @goDealList="orderType = 3"
      />
      <ExchangeDealsOrders
        v-if="orderType * 1 === 3"
        :isLoading="isLoading"
        :currentList="dealsList"
        :isLogin="isLogin"
        :pair="pair"
        :isShowOnlyPair="isShowOnlyPair"
      />
      <AssetsOrders
        v-if="orderType * 1 === 4"
        :pair="pair"
        @changePair="emit('changePair')" />
    </template>
    <div v-else style="height:100%;" class="flex-box space-center">
      <NoLoginCont />
    </div>
  </div>
  <BatchOrderDialog
    v-if="isShowCancellOrder"
    :dialogVisible="isShowCancellOrder"
    :times="times"
    :buyTimes="buyTimes"
    :sellTimes="sellTimes"
    :pair="pair"
    :isShowOnlyPair="isShowOnlyPair"
    @close="isShowCancellOrder = false"
    @successBatch="getOrderList('unsettled', pair)"
  />
  <TransferDialog v-if="isShowTransfer" :visibleDialog="isShowTransfer" @close="isShowTransfer = false" />
</template>

<script lang="ts" setup>
import { ElTabs, ElTabPane } from 'element-plus'
import MonoMoreBtnIcon from '~/components/common/icon-svg/MonoMoreBtnIcon.vue'
import TransferDialog from '~/components/common/TransferDialog.vue'
import BoxXOverflow from '~/components/common/BoxXOverflow.vue'
import ExchangeCurrentOrders from './orders/CurrentOrders.vue'
import ExchangeHistoryOrders from './orders/HistoryOrders.vue'
import ExchangeDealsOrders from './orders/DealOrders.vue'
import BatchOrderDialog from './orders/BatchOrderDialog.vue'
import AssetsOrders from './orders/AssetsOrders.vue'
import { getCurrenOrderList, getMyDealList }  from '~/api/order'
import MonoAssetsRansferIcon from '~/components/common/icon-svg/MonoAssetsRansferIcon.vue'
import { commonStore } from '~/stores/commonStore'
import NoLoginCont from '~/components/common/NoLoginCont.vue'
import { useUserStore } from '~/stores/useUserStore'
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const store = commonStore()
const { isChangeOrder, orderChangeObj } = storeToRefs(store)
const { locale, t } = useI18n()
const props = defineProps({
  pair: {
    type: String,
    default: ''
  },
  isLogin: {
    type: Boolean,
    default: false
  }
})
const isShowTransfer = ref(false)
const emit = defineEmits(['changePair'])
const orderType = ref(1)
const isShowOnlyPair = ref(false)
const isShowCancellOrder = ref(false)
const times = ref(0)
const buyTimes = ref(0)
const sellTimes = ref(0)
const currentList = ref([])
const currentObj = ref({})
const historyList = ref([])
const dealsList = ref([])
const isLoading = ref(false)
watch(() => isShowOnlyPair.value, (val) => {
  localStorage.setItem('isShowOnlyPairSpot', val)
})
const getOrderList = async(typeP, pairP) => {
  const { data } = await getCurrenOrderList({
    market: 'spot',
    status: typeP,
    symbol: isShowOnlyPair.value ? pairP : undefined
  })
  if (data) {
    let sell = 0
    let buy = 0
    data.forEach((item) => {
      if (item.side === 'buy') {
        buy++
      } else {
        sell++
      }
    })
    if (typeP === 'unsettled') {
      times.value = data.length
      buyTimes.value = buy
      sellTimes.value = sell
    }
    typeP === 'unsettled' ? currentList.value = data : historyList.value = data
    isLoading.value = false
  }
}
const getDealsListFun = async() => {
  isLoading.value = true
  const { data } = await getMyDealList({
    symbol: isShowOnlyPair.value ? props.pair : undefined
  })
  if (data) {
    isLoading.value = false
    dealsList.value = data
  }
}
watch(() => isShowOnlyPair.value, (val) => {
  if (orderType.value * 1 === 1 && props.isLogin) {
    getOrderList('unsettled', props.pair)
  } else if (orderType.value * 1 === 2 && props.isLogin) {
    getOrderList('settled', props.pair)
  } else {
    getDealsListFun()
  }
})
watch(() => orderType.value, (val) => {
  isLoading.value = true
  if (val * 1 === 1 && props.isLogin) {
    getOrderList('unsettled', props.pair)
  } else if (val * 1 === 2 && props.isLogin) {
    getOrderList('settled', props.pair)
  } else {
    getDealsListFun()
  }
})
watch(() => userInfo.value, (val) => {
  if (JSON.stringify(val) !== '{}') {
    isLoading.value = true
    if (orderType.value * 1 === 2) {
      getOrderList('settled', props.pair)
    } else {
      getDealsListFun()
    }
    getOrderList('unsettled', props.pair)
  }
})
watch(() => props.pair, (val) => {
  if (val && props.isLogin) {
    isLoading.value = true
    getOrderList('unsettled', props.pair)
  }
})
const requestAnimationFrameInterval = ref(null)
const socketDateAnimation = async() => {
  if (isChangeOrder.value) {
    if (orderType.value * 1 === 1 && props.isLogin) {
      await getOrderList('unsettled', props.pair)
      isChangeOrder.value = false
    } else if (orderType.value * 1 === 2 && props.isLogin) {
      await getOrderList('settled', props.pair)
      isChangeOrder.value = false
    }
  }
  requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
}
onMounted(() => {
  isShowOnlyPair.value = localStorage.getItem('isShowOnlyPairSpot') === 'true' ? true : false
  socketDateAnimation()
  requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
})
</script>

<style lang="scss">
.exchange-orders{
  height:100%;
  .orders-title{
    height:46px;
    border-bottom:1px solid;
    padding:0 16px;
    font-size:14px;
    @include border-color(border);
    .title-left{
      .el-tabs__header{
        margin:4px 0;
        .el-tabs__nav-wrap{
          &.is-scrollable{
            padding:0 20px 0 0;
          }
          &:after{
            display:none;
          }
          .el-tabs__nav-next, .el-tabs__nav-prev{
            line-height:46px;
            top:3px;
          }
          .el-tabs__nav-prev{
            z-index:9;
            left:-4px;
            @include bg-color(bg-primary);
            &.is-disabled{
              display:none;
            }
          }
          .el-tabs__nav{
            margin:0;
            .el-tabs__active-bar{
              display:none;
            }
            .el-tabs__item{
              border:0;
              margin-right:20px;
              height:46px;
              line-height:46px;
              position:relative;
              padding:0;
              cursor:pointer;
              @include color(tc-secondary);
              &:nth-child(2){
                margin-right:0;
              }
              &:nth-child(3){
                margin-left:20px;
              }
              &:last-child{
                margin-right:0;
              }
              &.is-active{
                @include color(tc-primary);
                &:after{
                  content: '';
                  position:absolute;
                  width:16px;
                  height:2px;
                  display:block;
                  bottom:0;
                  left:50%;
                  margin-left:-8px;
                  @include bg-color(theme);
                }
              }
            }
          }
        }
      }
      li{
        margin-right:20px;
        height:46px;
        line-height:46px;
        position:relative;
        cursor:pointer;
        @include color(tc-secondary);
        &.active{
          @include color(tc-primary);
          &:after{
            content: '';
            position:absolute;
            width:16px;
            height:2px;
            display:block;
            bottom:0;
            left:50%;
            margin-left:-8px;
            @include bg-color(theme);
          }
        }
      }
    }
    .title-right{
      .transfer-text{
        padding:0 12px;
        position:relative;
        &:before{
          content: '';
          display:block;
          position:absolute;
          left:0;
          top:50%;
          bottom:0;
          width:1px;
          height:16px;
          margin-top:-8px;
          @include bg-color(border);
        }
      }
      .check-box{
        margin-right:12px;
      }
      .el-button{
        margin-right:12px;
      }
      .more-txt{
        padding-left:12px;
        position:relative;
        &:after{
          content: '';
          display:block;
          width:1px;
          height:16px;
          position:absolute;
          left:0;
          top:50%;
          margin-top:-8px;
          @include bg-color(border);
        }
        a{
          cursor:pointer;
          @include color(tc-primary);
          &:hover{
            @include color(theme);
          }
        }
      }
    }
  }
}
@include mb {
  .exchange-orders{
    .orders-title{
      height:auto;
      font-size:14px;
      position:relative;
      &.flex-box{
        flex-direction: column;
        align-items:flex-start;
      }
      .title-menu-box{
        width:85%;
      }
      .title-left{
        width:100%;
      }
      .title-right{
        width:100%;
        padding:8px 0;
        justify-content: space-between;
        .transfer-text{
          padding:0;
          &:before{
            display:none;
          }
        }
        .check-box{
          margin-right:0;
        }
        .el-button{
          margin-right:0;
        }
        .more-txt{
          padding-left:0;
          padding-right:0;
          position:absolute;
          top:17px;
          right:16px;
          &:after{
            display:none;
          }
          a{
            cursor:pointer;
            @include color(tc-primary);
            &:hover{
              @include color(theme);
            }
          }
        }
      }
    }
  }
}
</style>