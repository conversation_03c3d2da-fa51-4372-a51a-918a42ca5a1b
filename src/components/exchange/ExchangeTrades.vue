<template>
  <ul class="trade-nav flex-box">
    <li :class="{'active': type === 'depth'}" @click="changeType('depth')">{{ $t('盘口') }}</li>
    <li :class="{'active': type === 'deals'}" @click="changeType('deals')">{{ $t('最新成交') }}</li>
    <!-- <li :class="{'active': type === 'myDeals'}" @click="changeType('myDeals')">{{ $t('我的成交') }}</li> -->
  </ul>
  <div class="tarde-cont">
    <ExchangeDepth v-show="type === 'depth' || (isIpad && isLoading)" :priceScale="priceScale" :ticker="ticker" :quantityScale="quantityScale" :pair="pair" @change-amount="changeAmount" @change-price="changePrice" />
    <ExchangeDeals v-show="type === 'deals' || (isIpad && isLoading)" :pair="pair" :deals="deals" :priceScale="priceScale" :quantityScale="quantityScale" />
    <!-- <ExchangeMyDeals v-show="type === 'myDeals' || (isIpad && isLoading)" :pair="pair" :priceScale="priceScale" :quantityScale="quantityScale" /> -->
  </div>
</template>
<script lang="ts" setup>
import ExchangeDeals from './trades/Deals.vue'
import ExchangeDepth from './trades/Depth.vue'
import ExchangeMyDeals from './trades/MyDeals.vue'
const props = defineProps({
  pair: {
    type: String,
    default: ''
  },
  ticker: {
    type: Object,
    default(){
      return {}
    }
  },
  deals: {
    type: Object,
    default () {
      return []
    }
  },
  priceScale: {
    type: [Number, String],
    default: ''
  },
  quantityScale: {
    type: [Number, String],
    default: ''
  }
})
const emit = defineEmits(['change-amount', 'change-price'])
const type = ref('depth')
const isLoading = ref(false)
const isIpad = ref(false)
const screenWidth = ref(0)
const changeAmount = (data) => {
  emit('change-amount', data)
}
const changePrice = (data) => {
  emit('change-price', data)
}
const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth
  isIpad.value = screenWidth.value <= 1064 && screenWidth.value > 768
}
const changeType = (name) => {
  if (isIpad.value) {
    return false
  }
  type.value = name
}
onMounted(() => {
  isLoading.value = true
  updateScreenWidth()
  window.addEventListener('resize', updateScreenWidth)
})
</script>

