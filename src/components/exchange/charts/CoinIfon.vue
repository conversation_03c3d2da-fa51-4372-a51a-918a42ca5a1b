<template>
  <div class="coinInfo-container">
    <div class="coinInfo-title flex-box">
      <BoxCoinIcon :icon="coinInfo.icon_url" class="icon-box"/>
      <span>{{ coinInfo.coin_symbol }}</span>
      <em>{{ coinInfo.name }}</em>
    </div>
    <div class="coinInfo-wrapper">
      <ul class="coinInfo-left flex-1">
        <!-- <li class="flex-box space-between">
          <span>{{ $t('市值排行') }}</span>
          <span>1</span>
        </li> -->
        <li class="flex-box space-between">
          <span>{{ $t('市值总额') }}</span>
          <span>{{ format(coinInfo.market_value, 4, true) }} USDT</span>
        </li>
        <li class="flex-box space-between">
          <span>{{ $t('24H交易量') }}</span>
          <span>{{ format((Number((ticker || {}).volume) < 0 ? -Number((ticker || {}).volume) : Number((ticker || {}).volume)), 2, true) || '--' }} {{ coinInfo.coin_symbol }}</span>
        </li>
        <li class="flex-box space-between">
          <span>{{ $t('24H交易额') }}</span>
          <span>{{ format((Number((ticker || {}).amount) < 0 ? -Number((ticker || {}).amount) : Number((ticker || {}).amount)), 4, true) || '--' }} USDT</span>
        </li>
        <li class="flex-box space-between">
          <span>{{ $t('最大发行量') }}</span>
          <span>{{ coinInfo.total_amount }} {{ coinInfo.coin_symbol }}</span>
        </li>
        <li class="flex-box space-between">
          <span>{{ $t('当前流通总量') }}</span>
          <span>{{ coinInfo.supply_amount }} {{ coinInfo.coin_symbol }}</span>
        </li>
      </ul>
      <div class="coinInfo-right flex-1">
        <div class="info-title">{{ $t('友情链接') }}</div>
        <div class="link-box">
          <ul>
            <li>
              <a :href="coinInfo.website" target="_blank" class="fit-tc-primary">
                {{ $t('官网') }}
              </a>
            </li>
            <li>
              <a :href="coinInfo.white_paper" target="_blank" class="fit-tc-primary">
                {{ $t('白皮书') }}
              </a>
            </li>
            <li>
              <a :href="coinInfo.explor_url" target="_blank" class="fit-tc-primary">
                {{ $t('区块链浏览器') }}
              </a>
            </li>
          </ul>
        </div>
        <div class="info-title">{{ $t('介绍') }}</div>
        <p class="info-text">{{ coinInfo && (coinInfo.decription || {})[locale] || (coinInfo.decription || {})[en] || '--' }}</p>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { format } from '~/utils'
  import { useCommonData } from '~/composables/index'
  const useCommon = useCommonData()
  const { locale } = useI18n()
  const props = defineProps({
    coinInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    ticker: {
      type: Object,
      default(){
        return {}
      }
    }
  })
</script>
<style lang="scss">
.coinInfo-container{
  height:100%;
  padding:0 20px;
  .coinInfo-title{
    height:56px;
    .icon-box{
      width:32px;
      height:auto;
      font-size:32px;
      margin-right:12px;
    }
    span{
      font-size:20px;
      font-weight:bold;
      margin-right:8px;
      @include color(tc-primary);
    }
    em{
      font-style:inherit;
      font-size:14px;
      @include color(tc-secondary);
    }
  }
  .coinInfo-wrapper{
    height:calc(100% - 56px);
    overflow-y: auto;
    .coinInfo-left{
      margin-right:2%;
      li{
        line-height:44px;
        height:44px;
      }
      span{
        &:first-child{
          font-size:12px;
          @include color(tc-secondary);
        }
        &:last-child{
          font-size:14px;
          @include color(tc-primary);
        }
      }
    }
    .coinInfo-right{
      margin-top:20px;
      .info-title{
        font-size:16px;
        padding-bottom:12px;
        @include color(tc-primary);
      }
      .link-box{
        padding-bottom:20px;
        ul{
          display:flex;
          align-items:center;
          li{
            padding:4px 12px;
            border-radius:4px;
            margin-right:12px;
            font-size:14px;
            @include color(tc-primary);
            @include bg-color(bg-quaternary);
          }
        }
      }
      .info-text{
        line-height:22px;
        font-size:14px;
        @include color(tc-secondary);
      }
    }
  }
}
@include mb {
  .coinInfo-container{
    .coinInfo-wrapper{
      height:calc(100% - 56px);
      overflow-y: auto;
      &.flex-box{
        flex-direction: column;
      }
      .coinInfo-left{
        margin-right:0;
        width:100%;
      }
      .coinInfo-right{
        margin-left:0;
        width:100%;
      }
    }
  }
}
</style>
