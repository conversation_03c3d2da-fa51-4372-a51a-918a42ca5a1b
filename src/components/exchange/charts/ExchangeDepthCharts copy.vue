<template>
  <div ref="depthChartContainer" class="madex-depth"></div>
</template>
<script lang="ts" setup>
import chart from '~/public/depthchart/index.js'
import { commonStore } from '~/stores/commonStore'
const store = commonStore()
const { pairInfo } = storeToRefs(store)
const colorMode = useColorMode()
const { locale } = useI18n()
const props = defineProps({
  pair: {
    type: String,
    default: ''
  },
  depths: {
    type: Object,
    default(){
      return {}
    }
  }
})
const depthChartContainer = ref(null)
const charts = ref(null)
const options = computed(() => {
  return {
    "priceFix": (pairInfo.value[props.pair] || {}).price_scale,
    "amountFix": (pairInfo.value[props.pair] || {}).quantity_scale,
    "lang": locale.value,
    "theme": colorMode.preference,
    "color": '#9497a0',
    "tipColor": "#CAD7E0",
    "bgColor": "rgba(0, 0, 0, 0.8)",
    "bidsLineColor": "rgba(60, 193, 136, 1)",
    "asksLineColor": "rgba(255, 98, 98, 1)",
    "axisColor": 'rgba(148,151,160, 0.3)',
    "langMap": {
      "zh": {
        "委托价": "委托价",
        "累计": "累计",
        "价差": "价差",
        "涨跌幅": "涨跌幅"
      },
      "ja": {
        "委托价": "委托价ja",
        "累计": "累计",
        "价差": "价差",
        "涨跌幅": "涨跌幅"
      },
      "ko": {
        "委托价": "委托价ko",
        "累计": "累计",
        "价差": "价差",
        "涨跌幅": "涨跌幅"
      },
      "en": {
        "委托价": "Price",
        "累计": "Volume",
        "价差": "Spread",
        "涨跌幅": "Change"
      },
    }
  }
})
const series = computed(() => {
  const asksData = []
  const bidsData = []
  const data = props.depths
  if (JSON.stringify(data) !== '{}') {
    // 处理 asks 数据
    const asksAry = Object.values(data.asks).sort((f, b) => f.price - b.price).splice(0, 5)
    if (asksAry.length > 1) {
      const asksMap = new Map(); // 使用 Map 来合并相同价格的数据
      asksAry.forEach((item) => {
        const price = Number(item.price);
        const volume = Number(item.volume) || 0;
        if (asksMap.has(price)) {
          asksMap.set(price, asksMap.get(price) + volume); // 累加相同价格的交易量
        } else {
          asksMap.set(price, volume); // 新增价格点
        }
      });
      asksMap.forEach((volume, price) => {
        asksData.push([price, volume]); // 将结果推入 asksData
      });
    }
  
    // 处理 bids 数据
    const bidsAry = Object.values(data.bids).sort((b, f) => f.price - b.price).splice(0, 5);
    if (bidsAry.length > 1) {
      const bidsMap = new Map(); // 使用 Map 来合并相同价格的数据
      bidsAry.forEach((item) => {
        const price = Number(item.price);
        const volume = Number(item.volume) || 0;
        if (bidsMap.has(price)) {
          bidsMap.set(price, bidsMap.get(price) + volume); // 累加相同价格的交易量
        } else {
          bidsMap.set(price, volume); // 新增价格点
        }
      });
      bidsMap.forEach((volume, price) => {
        bidsData.push([price, volume]); // 将结果推入 bidsData（注意 bids 是降序）
      });
    }
  }
  console.log(bidsData, asksData)
  return { bids: bidsData, asks: asksData };
})
watch(() => series.value, (val) => {
  if (JSON.stringify(val) !== '{}') {
    charts.value.putData(val)
  }
}, {
  deep: true
})
const initChart = () => {
  charts.value = chart.depthChart(
    depthChartContainer.value,
    options.value
  )
  charts.value.putData(series.value)
  window.addEventListener(
    'resize',
    () => {
      setTimeout(() => {
        charts.value.forceUpdate()
      }, 300)
    }
  )
}
onMounted(() => {
  initChart()
})
</script>
<style lang="scss" scoped>
  .madex-depth {
    height:calc(100% - 46px);
    width: 100%;
    // padding-right: 24px;
    display: flex;
    align-items: center;
  }
</style>