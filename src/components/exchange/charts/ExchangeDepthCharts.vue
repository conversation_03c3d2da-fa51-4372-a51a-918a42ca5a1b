<template>
  <div class="madex-depth">
    <div class="hightcharts-continer" id="hightcharts-continer"></div>
  </div>
</template>
<script lang="ts" setup>
// import chart from '~/public/depthchart/index.js'
import { commonStore } from '~/stores/commonStore'
const store = commonStore()
const { pairInfo } = storeToRefs(store)
const colorMode = useColorMode()
const { locale, t } = useI18n()
const props = defineProps({
  pair: {
    type: String,
    default: ''
  },
  ticker: {
    type: Object,
    default(){
      return {}
    }
  },
  depths: {
    type: Object,
    default(){
      return {}
    }
  }
})
const interval = ref(null)
let chart = null
const chartTheme = {
  dark: {
    colors: ['#3cc188', '#ff6262'],
    chart: {
      backgroundColor: '#181a1f'
    },
    xAxis: {
      gridLineWidth: 0,
      labels: {
        style: {
          color: '#9497a0'
        }
      },
      title: {
        style: {
          color: '#9497a0'
        }
      },
      lineColor: '#363a45',
      tickColor: '#363a45'
    },
    yAxis: {
      gridLineWidth: 0,
      lineWidth: 1,
      tickWidth: 1,
      labels: {
        style: {
          color: '#9497a0'
        }
      },
      title: {
        style: {
          color: '#9497a0'
        }
      },
      lineColor: '#363a45',
      tickColor: '#363a45'
    },
    plotOptions: {
      series: {
        fillOpacity: 0.16
      }
    },
    legend: {
      itemStyle: {
        color: '#9497a0',
        fontWeight: 'normal',
        lineHeight: '18px'
      },
      itemHoverStyle: {
        color: '#9497a0',
        fontWeight: 'normal',
        lineHeight: '18px'
      },
      align: 'center', // 水平方向位置
      verticalAlign: 'top', // 垂直方向位置
      x: 0, // 距离x轴的距离
      y: 20 // 距离Y轴的距离
    }
  },
  light: {
    colors: ['#3cc188', '#ff6262'],
    chart: {
      backgroundColor: '#FFFFFF'
    },
    xAxis: {
      gridLineWidth: 0,
      labels: {
        style: {
          color: '#9497a0'
        }
      },
      title: {
        style: {
          color: '#9497a0'
        }
      },
      lineColor: '#eeeeee',
      tickColor: '#eeeeee'
    },
    yAxis: {
      gridLineWidth: 0,
      lineWidth: 1,
      tickWidth: 1,
      labels: {
        style: {
          color: '#9497a0'
        }
      },
      title: {
        style: {
          color: '#9497a0'
        }
      },
      lineColor: '#eeeeee',
      tickColor: '#eeeeee'
    },
    plotOptions: {
      series: {
        fillOpacity: 0.16
      }
    },
    legend: {
      itemStyle: {
        color: '#414655',
        fontWeight: 'normal',
        lineHeight: '18px'
      },
      itemHoverStyle: {
        color: '#414655',
        fontWeight: 'normal',
        lineHeight: '18px'
      },
      align: 'center', // 水平方向位置
      verticalAlign: 'top', // 垂直方向位置
      x: 0, // 距离x轴的距离
      y: 20 // 距离Y轴的距离
    }
  }
}
const seriesData = computed(() => {
  const asksData = []
  const bidsData = []
  const data = props.depths
  if (JSON.stringify(data) !== '{}') {
    // 处理 asks 数据
    const asksAry = Object.values(data.asks).sort((f, b) => f.price - b.price).splice(0, 51)
    if (!asksAry.length || asksAry.length === 1) {
      return [[], []]
    }
    if (asksAry.length > 1) {
      asksAry.reduce((pre, cur) => {
        const volume = (Number(pre.volume) || 0) + (Number(cur.volume) || 0)
        const result = {
          volume,
          price: Number(cur.price)
        }
        asksData.push([Number(pre.price), volume])
        return result
      })
    }
    // 处理 bids 数据
    const bidsAry = Object.values(data.bids).sort((f, b) => b.price - f.price).splice(0, 51)
    if (!bidsAry.length || bidsAry.length === 1) {
      return [[], []]
    }
    if (bidsAry.length > 1) {
      bidsAry.reduce((pre, cur) => {
        const volume = (Number(pre.volume)) + (Number(cur.volume) || 0)
        const result = {
          volume,
          price: Number(cur.price)
        }
        bidsData.unshift([Number(pre.price), volume])
        return result
      })
    }
  }
  return [bidsData, asksData]
})
const start = () => {
  interval.value && clearInterval(interval.value)
  interval.value = setInterval(() => {
    update()
  }, 10000)
}
const update = () => {
  if (!chart) {
    return false
  }
  let buy = '#3cc188'
  let sell = '#ff6262'
  chart.update({
    series: [
      {
        name: t('买盘'),
        data: seriesData.value[0],
        isBuy: true,
        color: buy,
        pointPlacement: -0.5
      },
      {
        name: t('卖盘'),
        isSell: true,
        data: seriesData.value[1],
        color: sell,
        pointPlacement: 0.5
      }
    ]
  })
}
const draw = () => {
  const Highcharts = window.Highcharts
  if (!Highcharts) {
    return
  }
  let buy = '#3cc188'
  let sell = '#ff6262'
  Highcharts.setOptions(chartTheme[colorMode.preference])
  chart = Highcharts.chart('hightcharts-continer', {
    chart: {
      type: 'area',
      zoomType: 'xy'
    },
    title: {
      text: ''
    },
    credits: {
      enabled: false
    },
    xAxis: {
      minPadding: 0,
      maxPadding: 0,
      labels: {
        formatter () {
          return this.value
        }
      }
    },
    yAxis: {
      title: {
        text: ''
      },
      labels: {
        formatter () {
          return this.value
        }
      },
      opposite: true,
      margin: 0,
      offset: 0
    },
    legend: {
      itemStyle: {
        color: '#9BB4CC',
        fontWeight: 'normal',
        lineHeight: '18px'
      },
      itemHoverStyle: {
        color: '#9BB4CC',
        fontWeight: 'normal',
        lineHeight: '18px'
      },
      align: 'center', // 水平方向位置
      verticalAlign: 'top', // 垂直方向位置
      x: 0, // 距离x轴的距离
      y: 20 // 距离Y轴的距离
    },
    plotOptions: {
      area: {
        fillOpacity: 0.2,
        lineWidth: 1,
        step: 'center',
        pointStart: 0,
        marker: {
          enabled: !1,
          symbol: 'circle',
          radius: 2,
          states: {
            hover: {
              enabled: !0
            }
          }
        }
      }
    },
    tooltip: {
      backgroundColor: '#384252',
      borderColor: '#384252',
      borderRadius: 3,
      shared: true,
      style: {
        color: '#F5FAFF'
      },
      formatter () {
        const ticker = (props.ticker || {}).last
        const [coinSymbol, currencySymbol] = props.pair.split('_')
        const pairDecimal = (pairInfo.value[props.pair] || {}).price_scale
        const amountFix = (pairInfo.value[props.pair] || {}).quantity_scale
        const color = this.points[0].series.userOptions.isSell ? sell : buy
        return `
          <span style="line-height: 20px; font-size: 14px;">${format(this.points[0].x, pairDecimal, true)}</span>
          <br />
          <span style="line-height: 16px; font-size: 12px; color: ${color};">${this.points[0].series.name}</span>    <span style="padding-left: 8px; line-height: 16px; font-size: 12px;">${format(this.points[0].y, amountFix, true)} ${(coinSymbol).replace(/4|5/, '')}</span>
          <br />
          <span style="line-height: 16px; font-size: 12px; color: ${color};">${t('金额')}</span>    <span style="padding-left: 8px; line-height: 16px; font-size: 12px;">${format(this.points[0].y * (ticker || 1), pairDecimal, true)} ${currencySymbol}</span>
        `
      }
    },
    series: [
      {
        name: t('买盘'),
        data: seriesData.value[0],
        isBuy: true,
        color: buy,
        pointPlacement: -0.5
      },
      {
        name: t('卖盘'),
        isSell: true,
        data: seriesData.value[1],
        color: sell,
        pointPlacement: 0.5
      }
    ]
  })
}
watch(() => colorMode.preference, () => {
  draw()
  start()
})
watch(() => seriesData.value, (val) => {
  if (JSON.stringify(val) !== '{}') {
    update()
    start()
  }
}, {
  deep: true
})
onMounted(() => {
  if (!document.getElementById('highcharts-script')) {
    const script = document.createElement('script')
    script.src = 'https://code.highcharts.com/highcharts.js'
    script.id = 'highcharts-script'
    script.onload = () => {
      draw()
      start()
    }
    document.head.appendChild(script)
  } else {
    draw()
    start()
  }
})
</script>
<style lang="scss">
  .madex-depth {
    height:calc(100% - 46px);
    width: 100%;
    position:relative;
    .hightcharts-continer {
      width: 100%;
      height: calc(100%);
      position: absolute;
      svg{
        width:100% !important;
        height:100% !important;
      }
    }
  }
</style>