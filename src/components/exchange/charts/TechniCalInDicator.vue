<template>
  <el-dialog v-model="visible" width="640px" :title="$t('技术指标')" @close="emit('close')">
    <div class="technical-indicator-container">
      <div class="technical-indicator-left bge-show-scrollbar">
        <div class="indicator-title">{{ $t('主图') }}({{ checkedIndicators.main.length }}/{{ maxCheckedCount.main }})</div>
        <el-checkbox-group v-model="checkedIndicators.main" @change="params => checkboxChange(params)">
          <el-checkbox v-for="indicator in technicalIndicatorTypes.main" :disabled="checkedIndicators.main.length >= maxCheckedCount.main && !checkedIndicators.main.includes(indicator)" :key="indicator" :label="indicator" :class="{'active': checkedIndicators.main.includes(indicator)}" class="side-item" style="width:50px;">
            <span class="fit-tc-primary" style="word-break: break-all; display: block;" @click.stop="settingActive = indicator">{{ indicator }}</span>
          </el-checkbox>
        </el-checkbox-group>
        <div class="indicator-title">{{ $t('副图') }}({{ checkedIndicators.sub.length }}/{{ maxCheckedCount.sub }})</div>
        <el-checkbox-group v-model="checkedIndicators.sub" @change="params => checkboxChange(params)">
          <el-checkbox v-for="indicator in technicalIndicatorTypes.sub" :disabled="checkedIndicators.sub.length >= maxCheckedCount.sub && !checkedIndicators.sub.includes(indicator)" :key="indicator" :label="indicator" :class="{'active': checkedIndicators.sub.includes(indicator)}" class="side-item" style="width:50px;">
            <span class="fit-tc-primary" style="word-break: break-all; display: block;" @click.stop="settingActive = indicator">{{ indicator }}</span>
          </el-checkbox>
        </el-checkbox-group>
      </div>

      <div class="technical-indicator-right bge-show-scrollbar">
        <div class="indicator-right-title">{{ $t(settingActiveForm && settingActiveForm.name) }}</div>
        <div v-for="(setting, key) in settingActiveForm.setting" :key="key" class="pd-tb8 flex-box gap-16 space-between">
          <div class="flex-box flex-1">
            <el-checkbox v-if="setting.show !== undefined" v-model="setting.show">{{ setting.name ? $t(setting.name) : key }}</el-checkbox>
            <div v-else class="bge-checkbox bge-checkbox_large">
              <span class="bge-checkbox__label checked"> {{ setting.name ? $t(setting.name) : key }} </span>
            </div>

          </div>
          <el-input
            class="flex-1"
            v-if="setting.value !== undefined" v-model="setting.value"
            type="number"
            size="small"
          />
          {{ setting.color }}
          <ColorPicker v-if="setting.color !== undefined" v-model:pureColor="setting.color" pickerType="chrome" shape="square" />

        </div>
        <div class="mg-t20 flex-box space-end">
          <el-button type="secondary" @click="onReset" style="min-width: 100px;">{{ $t('重置') }}</el-button>
          <el-button type="primary"  @click="onSave" style="min-width: 100px;">{{ $t('保存') }}</el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
import { ElButton, ElDialog, ElCheckbox, ElInput, ElCheckboxGroup } from 'element-plus'
import { ColorPicker } from "vue3-colorpicker"
import "vue3-colorpicker/style.css"
import { baseTechnicalIndicatorMap } from '~/composables/useOriginal'
const props = defineProps({
  isShow: {
    type: Boolean,
    default: false
  },
  originalTechnicalIndicatorSettings: {
    type: Object,
    default(){
      return {}
    }
  }
})
const emit = defineEmits(['updateOriginalTechnicalIndicatorSettings', 'close'])
const maxCheckedCount = {
  main: 2,
  sub: 3
}

const checkedIndicators = reactive({
  main: ['MA'],
  sub: ['VOL']
})

watch(() => [checkedIndicators.main, checkedIndicators.sub], ([main, sub], [oldMain, oldSub]) => {
  if (main.length > maxCheckedCount.main || sub.length > maxCheckedCount.sub) {
    checkedIndicators.main = oldMain
    checkedIndicators.sub = oldSub
    return false
  }
  // console.info(checkedIndicators, 'tetetetettet')
  // emit('updateOriginalTechnicalIndicatorSettings', {
  //   checkedIndicators,
  //   ...props.originalTechnicalIndicatorSettings.technicalIndicatorSettings
  // })
})

const settingActive = ref('MA')

const settingActiveForm: any = ref()

function copy(data: any) {
  return JSON.parse(JSON.stringify(data))
}

watch(settingActive, () => {
  const technicalIndicatorMap = copy(baseTechnicalIndicatorMap)
  const saveData: any = {}
  settingActiveForm.value = {
    ...technicalIndicatorMap[settingActive.value],
    ...saveData[settingActive.value]
  }
  console.info(settingActiveForm.value, 'ttttttttttt')
}, { immediate: true })
function onReset () {
  const technicalIndicatorMap = copy(baseTechnicalIndicatorMap)
  settingActiveForm.value = {
    ...technicalIndicatorMap[settingActive.value]
  }
  console.info({[settingActive.value]: settingActiveForm.value}, 'settingActiveForm')
}
function onSave() {
  console.info(settingActive.value, settingActiveForm.value, 'settingActive.value')
  emit('updateOriginalTechnicalIndicatorSettings', {
    checkedIndicators,
    technicalIndicatorSettings: {
      ...props.originalTechnicalIndicatorSettings.technicalIndicatorSettings,
      [settingActive.value]: settingActiveForm.value
    }
  })
  visible.value = false
}
const visible = ref(false)
watch(visible.value, (val) => {
  // emit('updateIsShowTechnicalIndicator', val)
})
const checkboxChange = (params) => {
  console.info(params, 'checkboxChange')
}
onMounted(() => {
  // emit('updateIsShowTechnicalIndicator', false)
  checkedIndicators.main = props.originalTechnicalIndicatorSettings.checkedIndicators.main
  checkedIndicators.sub = props.originalTechnicalIndicatorSettings.checkedIndicators.sub
  console.info(checkedIndicators.main, checkedIndicators.sub, 'checkedIndicatorscheckedIndicators')
})


watch(() => [props.isShow], ([val]) => {
  visible.value = val as boolean
},{ immediate: true })

const technicalIndicatorTypes = computed(() => {
  const result: any = {
    main: [],
    sub: []
  }
  for (const key in baseTechnicalIndicatorMap) {
    // if (props.type === 'index' && ['VOL', 'OBV', 'EMV', 'PVT'].includes(key)) { // 指数没有这些副指标
    //   continue
    // }
    const technicalIndicator = baseTechnicalIndicatorMap[key]
    if (technicalIndicator.type === 'main') {
      result.main.push(key)
    } else {
      result.sub.push(key)
    }
  }
  return result
})
</script>
<style lang="scss">
.technical-indicator-container {
  border-top: solid 1px;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  height: 100%;
  @include border-color(border);

  .technical-indicator-left {
    width: 180px;
    height: 100%;
    overflow-y: auto;

    .indicator-title {
      padding: 16px 32px 4px 32px;
      font-size:14px;
      @include color(tc-secondary);
    }

    .indicator-name {
      padding: 8px 16px 8px 32px;
      display: flex;
      justify-content: start;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      font-size:14px;
      @include color(tc-secondary);

      &.active {
        @include color(theme);
      }
    }
  }

  .technical-indicator-right {
    padding: 24px 24px 0 24px;
    flex: 1;
    overflow-y: auto;
    height: 100%;

    .indicator-right-title {
      font-size:16px;
      @include color(tc-primary);
    }

    .vc-color-wrap {
      width: 32px !important;
      height: 32px !important;
      border-radius:8px;
      border: solid 1px;
      margin-right: 0 !important;
      display: flex;
      align-items: center;
      justify-content: center;
      background: none;
      @include border-color(border);

      .current-color {
        width: 16px;
        height: 16px;
        border-radius: 8px;
      }
    }
  }
}
@include mb{
  .technical-indicator-container {
    border-top: 0;
    display: block;
    height: auto;
    @include border-color(border);

    .technical-indicator-left {
      width: 100%;
      height: 100%;
      overflow-y: auto;

      .indicator-title {
        padding: 16px 0px 4px;
        font-size:14px;
        @include color(tc-secondary);
      }

      .indicator-name {
        padding: 8px 16px 8px 32px;
        display: flex;
        justify-content: start;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        font-size:14px;
        @include color(tc-secondary);

        &.active {
          @include color(theme);
        }
      }
    }

    .technical-indicator-right {
      padding: 24px 0 0;
      flex: 1;
      overflow-y: auto;
      height: 100%;

      .indicator-right-title {
        font-size:16px;
        @include color(tc-primary);
      }

      .vc-color-wrap {
        width: 32px !important;
        height: 32px !important;
        border-radius:8px;
        border: solid 1px;
        margin-right: 0 !important;
        display: flex;
        align-items: center;
        justify-content: center;
        background: none;
        @include border-color(border);

        .current-color {
          width: 16px;
          height: 16px;
          border-radius: 8px;
        }
      }
    }
  }
}
</style>