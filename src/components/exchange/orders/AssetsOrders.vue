<template>
  <div class="assets-orders-container">
    <div class="assets-orders-wrapper">
      <div class="assets-orders-tab">
        <ul class="flex-box">
          <li class="first-child"><span>{{ $t('币种') }}</span></li>
          <div class="normal flex-box">
            <li>{{ $t('可用') }}</li>
            <li>{{ $t('总量') }}</li>
          </div>
          <div class="normal normal-right flex-box">
            <li>{{ $t('总额') }}(USDT)</li>
            <li>{{ $t('占比') }}</li>
          </div>
          <li>&nbsp;</li>
        </ul>
      </div>
      <div v-loadding="isLoading" class="assets-all-box">
        <div v-if="curAssetsList.length > 0" class="assets-orders-subTab">
          {{ $t('当前交易对资产') }}
        </div>
        <div class="assets-orders-list-wrap">
          <ul v-for="(item, index) in curAssetsList" class="flex-box">
            <li>
              <div class="symbol-cont flex-box" @click="showAssetList(pair.split('_')[0])">
                <div class="flex-box">
                  <BoxCoinIcon :icon="item.icon_url" class="icon-svg" />
                  <div style="text-align:left;">
                    <p class="font-size-16 fit-tc-primary">{{ item.asset }}</p>
                    <p class="font-size-12 fit-tc-secondary">{{ item.name }}</p>
                  </div>
                </div>
                <MonoRightArrowShort size="16" class="fit-tc-secondary more-icon" />
              </div>
            </li>
            <div class="normal flex-box">
              <div class="normal-title">
                {{ $t('可用') }}/{{ $t('总量') }}
              </div>
              <li>
                <div>{{ format(item.withdrawable, 10, true, true) }}</div>
              </li>
              <li>
                <div>{{ format(item.total, 10, true, true) }}</div>
              </li>
            </div>
            <div class="normal normal-right flex-box">
              <div class="normal-title">
                {{ $t('总额') }}(USDT)/{{ $t('占比') }}
              </div>
              <li>
                <div>{{ item.equsdt }}</div>
              </li>
              <li>
                <div>{{ item.percent }}%</div>
              </li>
            </div>
            <li class="last-child">
              <el-popover ref="popoverRef" trigger="click" popper-class="trade-select-list-popover">
                <template #reference>
                  <div class="fit-theme cursor-pointer" style="display:inline-block;">{{ $t('交易') }}</div>
                </template>
                <div class="trade-list-select-body">
                  <ul>
                    <li v-for="(ite, ind) in getCurCoinList(pair.split('_')[0])" @click="toTrade(ite)">
                      {{ ite.includes('_SWAP') ? ite.replace('_SWAP', '').replace('_', '') : ite.replace('_', '/') }}
                      <span class="fit-tc-secondary font-size-12">{{ ite.includes('_SWAP') ? $t('永续') : '' }}</span>
                    </li>
                  </ul>
                </div>
              </el-popover>
            </li>
          </ul>
        </div>
        <div v-if="otherAssetsList.length > 0" class="assets-orders-subTab">
          {{ $t('其他非零资产') }}
        </div>
        <div class="assets-orders-list-wrap">
          <ul v-for="(item, index) in otherAssetsList" class="flex-box">
            <li>
              <div class="symbol-cont flex-box" @click="showAssetList(item.asset)">
                <div class="flex-box">
                  <BoxCoinIcon :icon="item.icon_url" class="icon-svg" />
                  <div style="text-align:left;">
                    <p class="font-size-16 fit-tc-primary">{{ item.asset }}</p>
                    <p class="font-size-12 fit-tc-secondary">{{ item.name }}</p>
                  </div>
                </div>
                <MonoRightArrowShort size="16" class="fit-tc-secondary more-icon" />
              </div>
            </li>
            <div class="normal flex-box">
              <div class="normal-title">
                {{ $t('可用') }}/{{ $t('总量') }}
              </div>
              <li>
                <div>{{ item.withdrawable }}</div>
              </li>
              <li>
                <div>{{ item.total }}</div>
              </li>
            </div>
            <div class="normal normal-right flex-box">
              <div class="normal-title">
                {{ $t('总额') }}(USDT)/{{ $t('占比') }}
              </div>
              <li>
                <div>{{ item.equsdt }}</div>
              </li>
              <li>
                <div>{{ item.percent }}%</div>
              </li>
            </div>
            <li class="last-child">
              <el-popover ref="popoverRef" trigger="click" popper-class="trade-select-list-popover">
                <template #reference>
                  <div class="fit-theme cursor-pointer" style="display:inline-block;">{{ $t('交易') }}</div>
                </template>
                <div class="trade-list-select-body">
                  <ul>
                    <li v-for="(ite, ind) in getCurCoinList(item.asset)" @click="toTrade(ite)">
                      {{ ite.includes('_SWAP') ? ite.replace('_SWAP', '').replace('_', '') : ite.replace('_', '/') }}
                      <span class="fit-tc-secondary font-size-12">{{ ite.includes('_SWAP') ? $t('永续') : '' }}</span>
                    </li>
                  </ul>
                </div>
              </el-popover>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div v-if="isShowList" class="dialog-cont-black">
      <div class="dialog-cont-wrap">
        <div class="dialog-wrap-pd">
          <div class="dialog-title">
            {{ $t('去交易') }}
            <MonoClose size="24" class="close-btn-box fit-tc-primary" @click="isShowList = false" />
          </div>
          <ul>
            <li v-for="(ite, i) in curPairList" class="flex-box space-between" @click="toTrade(ite)">
              <span class="fit-tc-primary">
                {{ ite.includes('_SWAP') ? ite.replace('_SWAP', '').replace('_', '') : ite.replace('_', '/') }}
                <span class="fit-tc-secondary mg-l8">{{ ite.includes('_SWAP') ? $t('永续') : '' }}</span>
              </span>
              <span class="fit-theme">{{ $t('去交易') }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ElPopover } from 'element-plus'
  import { format } from '~/utils'
  import { commonStore } from '~/stores/commonStore'
  import { useUserStore } from '~/stores/useUserStore'
  const userStore = useUserStore()
  const { userInfo } = storeToRefs(userStore)
  const router = useRouter()
  const store = commonStore()
  const { locale, t } = useI18n()
  const { getAssetsByCoin, getCoinList, getAllPairList } = store
  const { assetAllCoinMap, tradeAssetObj, coinList, allPairList } = storeToRefs(store)
  const props = defineProps({
    pair: {
      type: String,
      default: ''
    }
  })
  const emit = defineEmits(['changePair'])
  const isLoading = ref(false)
  const arrayToObject = (arr) => {
    return arr.reduce(function(obj, item) {
      var key = item.general_name;
      var value = item
      obj[key] = value
      return obj
    }, {})
  }
  const allAssetList = computed(() => {
    const arr = Object.values(tradeAssetObj.value) || []
    let num = 0
    arr.forEach((it) => {
      num += Number(it.equsdt)
    })
    arr.forEach((item) => {
      if (arrayToObject(coinList.value)[item.asset]) {
        item.icon_url = arrayToObject(coinList.value)[item.asset].icon_url
        item.name = arrayToObject(coinList.value)[item.asset].name
      }
      item.percent = format((item.equsdt * 1 / num) * 100, 2, true)
    })
    console.log(arr, 'djhdeihjdiejdiejiej')
    return arr
  })
  const curAssetsList = computed(() => {
    const arr = allAssetList.value || []
    return arr.filter((item) => {
      return item.asset === props.pair.split('_')[0] || item.asset === props.pair.split('_')[1]
    })
  })
  const otherAssetsList = computed(() => {
    const arr = allAssetList.value || []
    return arr.filter((item) => {
      return !(item.asset === props.pair.split('_')[0] || item.asset === props.pair.split('_')[1]) && item.equsdt * 1 > 0
    })
  })
  const getCurCoinList = (curCoin) => {
    return allPairList.value.filter((it) => {
      return curCoin === it.split('_')[0]
    })
  }
  const toTrade = (p) => {
    if (p.includes('_SWAP')) {
      router.push(`/${locale.value}/future/${p}`)
    } else {
      router.push(`/${locale.value}/exchange/${p}`)
    }
    isShowList.value = false
  }
  watch(() => props.pair, async(val) => {
    await getAllPairList()
    await getCoinList()
    await getAssetsByCoin()
  })
  const isShowList = ref(false)
  const curPairList = ref([])
  const showAssetList = (curCoin) => {
    isShowList.value = true
    curPairList.value = getCurCoinList(curCoin)
  }
  onBeforeMount(async() => {
    isLoading.value = true
    await getAllPairList()
    await getCoinList()
    await getAssetsByCoin()
    isLoading.value = false
  })
</script>
<style lang="scss" scoped>
  .assets-orders-container{
    height:100%;
    .assets-orders-wrapper{
      height:100%;
      .assets-all-box{
        height:calc(100% - 88px);
        overflow:auto;
      }
      .assets-orders-tab{
        ul{
          padding:0 20px;
          .normal{
            flex:2;
          }
          li{
            flex:1;
            font-size:12px;
            line-height:44px;
            text-align:center;
            @include color(tc-secondary);
            &.first-child{
              text-align:left;
              span{
                padding-left:42px;
              }
            }
          }
        }
      }
      .assets-orders-subTab{
        padding:0 20px;
        font-size:14px;
        line-height:38px;
        @include color(tc-primary);
      }
      .assets-orders-list-wrap{
        ul{
          padding:0 20px;
          border-bottom:1px solid;
          @include border-color(border);
          &:hover{
            @include bg-color(bg-quaternary);
          }
          &:last-child{
            border-bottom:0;
          }
          .normal{
            flex:2;
            .normal-title{
              display:none;
            }
          }
          li{
            flex:1;
            padding:12px 0;
            font-size:14px;
            text-align:center;
            @include color(tc-primary);
            .icon-svg{
              font-size:30px;
              height:30px;
              height:30px;
              margin-right:12px;
            }
            p{
              line-height:18px;
            }
            .more-icon{
              display:none;
            }
            &.last-child{
              text-align:right;
            }
          }
        }
      }
    }
  }
  .trade-list-select-body{
    li{
      line-height:38px;
      margin:0 -12px;
      padding:0 16px;
      @include color(tc-primary);
      cursor:pointer;
      &:hover{
        @include bg-color(bg-quaternary);
      }
    }
  }
  .dialog-cont-black{
    width:100%;
    height:100%;
    position:fixed;
    top:0;
    left:0;
    bottom:0;
    right:0;
    z-index:999999;
    display:flex;
    align-items:flex-end;
    background-color: rgba(0,0,0,0.5);
    .dialog-cont-wrap{
      width:100%;
      height:auto;
      overflow:hidden;
      border-top-right-radius:16px;
      border-top-left-radius:16px;
      @include bg-color(bg-primary);
      .dialog-wrap-pd{
        position:relative;
        padding:20px;
        .dialog-title{
          text-align:center;
          font-size:16px;
          @include color(tc-primary);
          .close-btn-box{
            position:absolute;
            top:0;
            right:0;
            margin:16px;
            cursor:pointer;
          }
        }
        ul{
          width:100%;
          li{
            border:1px solid;
            padding: 12px 16px;
            border-radius:12px;
            cursor:pointer;
            font-size:14px;
            margin-top:16px;
            @include border-color(border);
          }
        }
      }
    }
  }
  @include mb{
    .assets-orders-container{
      .assets-orders-wrapper{
        .assets-all-box{
          height:auto;
          overflow:auto;
        }
        .assets-orders-tab{
          display:none;
        }
        .assets-orders-subTab{
          @include color(tc-secondary);
        }
        .assets-orders-list-wrap{
          ul{
            display:block;
            flex-wrap:wrap;
            padding:16px 20px 0;
            border-bottom:1px solid;
            height:auto;
            overflow:hidden;
            @include border-color(border);
            &:hover{
              @include bg-color(bg-primary);
            }
            .normal{
              width:50%;
              float:left;
              flex:inherit;
              display:block;
              padding:8px 0 16px;
              .normal-title{
                display:block;
                padding-bottom:4px;
                font-size:14px;
                @include color(tc-secondary);
              }
              li{
                text-align:left;
              }
              &.normal-right{
                .normal-title{
                  text-align:right;
                }
                li{
                  div{
                    text-align:right;
                  }
                }
              }
            }
            li{
              width:100%;
              flex:inherit;
              padding:0;
              font-size:14px;
              text-align:center;
              @include color(tc-primary);
              &:first-child{
                flex:inherit;
                width:100%;
                .symbol-cont{
                  justify-content: space-between;
                }
              }
              .icon-svg{
                font-size:30px;
                height:30px;
                height:30px;
                margin-right:12px;
              }
              p{
                line-height:18px;
                &:last-child{
                  display:none;
                }
              }
              .more-icon{
                display:block;
              }
              &.last-child{
                display:none;
              }
            }
          }
        }
      }
    }
  }
</style>
