<template>
  <div class="ordersCont">
    <OrderstableBox v-if="isLogin" :isLoading="isLoading" :mSortList="mSortList" :headers="headersList" :list="currentList">
      <template #updateTime="scope">
        <span class="fit-tc-secondary" style="text-align:left;">{{ timeFormat(scope.data.updateTime, 'yyyy-MM-dd hh:mm:ss') }}</span>
      </template>
      <template #product="scope">
        <NuxtLink :to="`/${locale}/exchange/${scope.data.product}`" class="fit-tc-primary">
          <span>{{ scope.data.product.replace('_', '/') }}</span>
        </NuxtLink>
      </template>
      <template #side="scope">
        <span :class="{'fit-rise': scope.data.side === 'buy', 'fit-fall': scope.data.side === 'sell'}">{{ scope.data.side === 'buy' ? $t('买入') : $t('卖出') }}</span>
      </template>
      <template #price="scope">
        <span>{{ format(scope.data.price, Number((pairInfo[scope.data.product] || {}).price_scale), true) }}</span>
      </template>
      <template #F="scope">
        <span>{{ format((Number(scope.data.quantity) * 1 < 0 ? new BigNumber(Number(scope.data.price)).times(-Number(scope.data.quantity)) : new BigNumber(Number(scope.data.price)).times(Number(scope.data.quantity))), Number((pairInfo[scope.data.product] || {}).price_scale), true) }}</span>
      </template>
      <template #quantity="scope">
        <span>{{ format((Number(scope.data.quantity) < 0 ? -Number(scope.data.quantity) : Number(scope.data.quantity)), Number((pairInfo[scope.data.product] || {}).quantity_scale), true) }}</span>
      </template>
      <template #status="scope">
        <span class="fit-tc-secondary">{{ $t(ORDER_STATUS_MAP[scope.data.status]) }}</span>
      </template>
      <template #type="scope">
        <span :class="{'fit-rise': scope.data.side === 'buy', 'fit-fall': scope.data.side === 'sell'}">{{ scope.data.type === 'limit' ? $t('限价单') : $t('市价单') }}</span>
      </template>
      <!-- <template #executedQty="scope">
        <span class="fit-tc-secondary">{{ scope.data.executedQty * 1 === 0 ? '--' : scope.data.executedQty }}</span>
      </template>
      <template #executedCost="scope">
        <span class="fit-tc-secondary">{{ scope.data.executedCost * 1 === 0 ? '--' : scope.data.executedCost }}</span>
      </template> -->
      <template #orderId="scope">
        <span v-loading="isLoadingData[scope.data.orderId]" v-if="scope.data.status === 'accepted' || scope.data.status === 'partially-filled'" class="fit-theme cursor-pointer" @click="cancelOrder(scope.data)">{{ $t('撤单') }}</span>
        <span v-else>--</span>
      </template>
    </OrderstableBox>
    <div v-else class="no-login-cont-box">
      <NoLoginCont />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import BigNumber from 'bignumber.js'
  import BoxNoData from '~/components/common/BoxNoData.vue'
  import NoLoginCont from '~/components/common/NoLoginCont.vue'
  import OrderstableBox from '~/components/common/OrderstableBox.vue'
  import { timeFormat, format } from '~/utils'
  import { ORDER_STATUS_MAP } from '~/config'
  import { useCommonData } from '~/composables/index'
  import { deleteDetail } from '~/api/order'
  import { commonStore } from '~/stores/commonStore'
  const store = commonStore()
  const { locale, t } = useI18n()
  const useCommon = useCommonData()
  const { pairInfo } = storeToRefs(store)
  const props = defineProps({
    isLoading: {
      type: Boolean,
      default: false
    },
    currentList: {
      type: Object,
      default(){
        return []
      }
    },
    pair: {
      type: String,
      default: ''
    },
    isLogin: {
      type: Boolean,
      default: false
    }
  })
  const emit = defineEmits(['getOrderList'])
  const mSortList = ref([
    { text: t('时间'), key: 'updateTime', align: 'left', wid: '',style: 'auto' },
    { text: t('委托操作'), key: 'orderId', align: 'right', wid: '',style: 'auto' },
    { text: t('交易对'), key: 'product', align: 'left', wid: '',style: 'auto' },
    { text: t('方向'), key: 'side', align: 'left', wid: '',style: 'auto' },
    { text: t('委托价格'), key: 'price', align: 'left', wid: '',style: 'auto' },
    // { text: '成交价格', key: 'executedCost', align: 'left', wid: '',style: 'auto' },
    { text: t('委托数量'), key: 'quantity', align: 'left', wid: '',style: 'auto' },
    // { text: '成交数量', key: 'executedQty', align: 'left', wid: '',style: 'auto' },
    { text: t('委托金额'), key: 'F', align: 'left', wid: '',style: 'auto' },
    { text: t('订单成交度'), key: 'status', align: 'left', wid: 'flex-2',style: 'auto' },
    { text: t('委托类型'), key: 'type', align: 'left', wid: '',style: 'auto' }
  ])
  const headersList = ref([
    { text: t('时间'), key: 'updateTime', align: 'left', wid: 'flex-2',style: 'auto' },
    { text: t('交易对'), key: 'product', align: 'left', wid: '',style: 'auto' },
    { text: t('方向'), key: 'side', align: 'left', wid: '',style: 'auto' },
    { text: t('委托价格'), key: 'price', align: 'left', wid: '',style: 'auto' },
    // { text: '成交价格', key: 'executedCost', align: '', wid: '',style: 'auto' },
    { text: t('委托数量'), key: 'quantity', align: 'left', wid: '',style: 'auto' },
    // { text: '成交数量', key: 'executedQty', align: '', wid: '',style: 'auto' },
    { text: t('委托金额'), key: 'F', align: 'left', wid: '',style: 'auto' },
    { text: t('订单成交度'), key: 'status', align: 'left', wid: 'flex-2',style: 'auto' },
    { text: t('委托类型'), key: 'type', align: 'left', wid: '',style: 'auto' },
    { text: t('委托操作'), key: 'orderId', align: 'right', wid: '',style: 'auto' }
  ])
  const isLoadingData = ref({})
  const cancelOrder = async(row) => {
    isLoadingData.value[row.orderId] = true
    const { data, error } = await deleteDetail({
      market: 'spot',
      id: row.orderId
    })
    if (data) {
      isLoadingData.value[row.orderId] = false
      emit('getOrderList')
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
</script>

<style scoped lang="scss">
  .no-login-cont-box{
    height:100%;
    display:flex;
    align-items:center;
    justify-content: center;
  }
  .ordersCont{
    height:100%;
    overflow:auto;
  }
  @include mb {
    .ordersCont{
      padding:0 20px;
    }
  }
</style>