<template>
  <div class="ordersCont">
    <OrderstableBox :isLoading="isLoading" :mSortList="mSortList" :headers="headersList" :list="currentList">
      <template #time="scope">
        <span class="fit-tc-secondary" style="text-align:left;">{{ timeFormat(scope.data.time, 'yyyy-MM-dd hh:mm:ss') }}</span>
      </template>
      <template #product="scope">
        <NuxtLink :to="`/${locale}/exchange/${scope.data.product}`" class="fit-tc-primary">
          <span>{{ scope.data.product.replace('_', '/') }}</span>
        </NuxtLink>
      </template>
      <template #side="scope">
        <span :class="{'fit-rise': Number(scope.data.quantity) >= 0, 'fit-fall': Number(scope.data.quantity) < 0}">{{ Number(scope.data.quantity) < 0 ? $t('卖出') : $t('买入') }}</span>
      </template>
      <template #price="scope">
        <span>{{ format(scope.data.price, Number((pairInfo[scope.data.product] || {}).price_scale), true) }}</span>
      </template>
      <template #F="scope">
        <span>{{ format((Number(scope.data.quantity) < 0 ? (Number(scope.data.price) * -Number(scope.data.quantity)) : (Number(scope.data.price) * Number(scope.data.quantity))), Number((pairInfo[scope.data.product] || {}).price_scale), true) }}</span>
      </template>
      <template #fees="scope">
        <div class="flex-box space-between">
          {{ scope.data.fees.length > 0 ? scope.data.fees[0].amount : 0 }} {{ scope.data.fees.length > 0 ? scope.data.fees[0].asset : '' }}
          <!-- <div class="mg-r4">
            {{ format(totalFeesValue(scope.data.fees), 4, true) || '--' }}
          </div>
          <el-tooltip
            :content="$t('详情')"
            placement="top">
            <div @click="showOrderDetails(scope.data)" class="flex-box">
              <MonoOrder size="16" class="cursor-pointer fit-theme" />
            </div>
          </el-tooltip> -->
        </div>
      </template>
      <template #quantity="scope">
        <span>{{ format((Number(scope.data.quantity) < 0 ? -Number(scope.data.quantity) : Number(scope.data.quantity)), Number((pairInfo[scope.data.product] || {}).quantity_scale), true) }}</span>
      </template>
      <template #status="scope">
        <span class="fit-tc-secondary">{{ $t('已成交') }}</span>
      </template>
      <template #orderId="scope">
        <span v-if="scope.data.status === 'accepted'" class="fit-theme cursor-pointer" @click="cancelOrder(scope.data)">{{ $t('撤单') }}</span>
        <span v-else>--</span>
      </template>
    </OrderstableBox>
  </div>
  <FillsListDialog 
    v-if="isShowOrderDetail"
    :isShow="isShowOrderDetail"
    :data="curDetail"
    @close="isShowOrderDetail = false" />
</template>

<script lang="ts" setup>
  import { ElTooltip } from 'element-plus'
  import BoxNoData from '~/components/common/BoxNoData.vue'
  import NoLoginCont from '~/components/common/NoLoginCont.vue'
  import OrderstableBox from '~/components/common/OrderstableBox.vue'
  import MonoOrder from '~/components/common/icon-svg/MonoOrder.vue'
  import FillsListDialog from '~/components/exchange/orders/FillsListDialog.vue'
  import { timeFormat, format } from '~/utils'
  import { ORDER_STATUS_MAP } from '~/config'
  import { useCommonData } from '~/composables/index'
  import { deleteDetail } from '~/api/order'
  import { commonStore } from '~/stores/commonStore'
  const store = commonStore()
  const { locale, t } = useI18n()
  const { pairInfo } = storeToRefs(store)
  const useCommon = useCommonData()
  const props = defineProps({
    isLoading: {
      type: Boolean,
      default: false
    },
    currentList: {
      type: Object,
      default(){
        return []
      }
    },
    pair: {
      type: String,
      default: ''
    },
    isLogin: {
      type: Boolean,
      default: false
    }
  })
  const emit = defineEmits(['getOrderList'])
  const mSortList = ref([
    { text: t('时间'), key: 'time', align: 'left', wid: '',style: 'auto' },
    { text: t('状态'), key: 'status', align: 'left', wid: '',style: 'auto' },
    { text: t('交易对'), key: 'product', align: 'left', wid: '',style: 'auto' },
    { text: t('方向'), key: 'side', align: 'left', wid: '',style: 'auto' },
    { text: t('成交价格'), key: 'price', align: 'left', wid: '',style: 'auto' },
    { text: t('成交数量'), key: 'quantity', align: 'left', wid: 'flex-2',style: 'auto' },
    { text: t('成交金额'), key: 'F', align: 'left', wid: 'flex-2',style: 'auto' },
    { text: t('手续费'), key: 'fees', align: 'right', wid: 'flex-2',style: 'auto' }
  ])
  const headersList = ref([
    { text: t('时间'), key: 'time', align: 'left', wid: '',style: 'auto' },
    { text: t('交易对'), key: 'product', align: 'left', wid: '',style: 'auto' },
    { text: t('方向'), key: 'side', align: 'left', wid: '',style: 'auto' },
    { text: t('成交价格'), key: 'price', align: 'left', wid: '',style: 'auto' },
    { text: t('成交数量'), key: 'quantity', align: 'left', wid: '',style: 'auto' },
    { text: t('成交金额'), key: 'F', align: 'left', wid: '',style: 'auto' },
    { text: t('手续费'), key: 'fees', align: 'left', wid: '',style: 'auto' },
    { text: t('状态'), key: 'status', align: 'right', wid: 'flex-2',style: 'auto' }
  ])
  const cancelOrder = async(row) => {
    console.info(row.i, 'dhueuehuduheuheuuhe')
    const { data, error } = await deleteDetail({
       id: row.i
    })
    if (data) {
      emit('getOrderList')
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const totalFeesValue = (fees) => {
    let num = 0
    fees.forEach((item) => {
      num += Number(item.value) > 0 ? Number(item.value) : Number(item.amount)
    })
    return num
  }
  const isShowOrderDetail = ref(false)
  const curDetail = ref({})
  const showOrderDetails = (row) => {
    curDetail.value = row
    isShowOrderDetail.value = true
  }
</script>

<style scoped lang="scss">
  .ordersCont{
    height:100%;
    overflow:auto;
  }
  @include mb {
    .ordersCont{
      padding:0 20px;
    }
  }
</style>