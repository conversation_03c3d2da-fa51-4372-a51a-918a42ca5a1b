<template>
  <el-dialog v-model="visible" :title="$t('手续费详情')" @close="emit('close')">
    <OrderstableBox :isLoading="false" :mSortList="sortList" :headers="sortList" :list="feesList">
      <template #asset="scope">
        {{ scope.data.asset === 'KtxQ1' ? t('体验金') : (scope.data.asset === 'KtxQ2' ? t('抵扣金') : scope.data.asset) }}
      </template>
      <template #amount="scope">
        {{ scope.data.amount }} {{ (scope.data.asset === 'KtxQ1' || scope.data.asset === 'KtxQ2') ? 'USDT' : scope.data.asset }}
      </template>
      <template #value="scope">
        {{ scope.data.value > 0 ? format(scope.data.value, 4, true) : format(scope.data.amount, 4, true) }}
      </template>
    </OrderstableBox>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElDialog } from 'element-plus'
  import { format } from '~/utils'
  import OrderstableBox from '~/components/common/OrderstableBox.vue'
  const { locale, t } = useI18n()
  const props = defineProps({
    isShow: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default () {
        return {}
      }
    }
  })
  const emit = defineEmits(['close'])
  const visible = ref(false)
  const sortList = ref([
    { text: t('币种'), key: 'asset', align: 'left', wid: '', style: 'auto' },
    { text: t('手续费'), key: 'amount', align: 'center', wid: '',style: 'auto' },
    { text: t('手续费(USDT)'), key: 'value', align: 'right', wid: '',style: 'auto' }
  ])
  const feesList = ref([])
  onMounted(() => {
    visible.value = props.isShow
    feesList.value = props.data.fees
  })
</script>
<style lang="scss">
</style>