<template>
  <div class="ordersCont">
    <OrderstableBox :isLoading="isLoading" :mSortList="mSortList" :headers="headersList" :list="currentList">
      <template #updateTime="scope">
        <span class="fit-tc-secondary" style="text-align:left;">{{ timeFormat(scope.data.updateTime, 'yyyy-MM-dd hh:mm:ss') }}</span>
      </template>
      <template #product="scope">
        <NuxtLink :to="`/${locale}/exchange/${scope.data.product}`" class="fit-tc-primary">
          <span>{{ scope.data.product.replace('_', '/') }}</span>
        </NuxtLink>
      </template>
      <template #side="scope">
        <span :class="{'fit-rise': scope.data.side === 'buy', 'fit-fall': scope.data.side === 'sell'}">{{ scope.data.side === 'buy' ? $t('买入') : $t('卖出') }}</span>
      </template>
      <template #price="scope">
        <span>{{ format(scope.data.price, (pairInfo[scope.data.product] || {}).price_scale, true) }}</span>
      </template>
      <template #F="scope">
        <span>{{ format((Number(scope.data.executedQty) * 1 < 0 ? new BigNumber(-Number(scope.data.executedCost)).div(-Number(scope.data.executedQty)) : new BigNumber(Number(scope.data.executedCost)).div(Number(scope.data.executedQty))), Number((pairInfo[scope.data.product] || {}).price_scale), true) }}</span>
      </template>
      <template #quantity="scope">
        <span>{{ format((Number(scope.data.quantity) < 0 ? -Number(scope.data.quantity) : Number(scope.data.quantity)), (pairInfo[scope.data.product] || {}).quantity_scale, true) }}</span>
      </template>
      <template #status="scope">
        <div class="flex-box space-between" style="width:100%;">
          <span class="fit-tc-secondary mg-r4">{{ $t(ORDER_STATUS_MAP[scope.data.status]) }}</span>
          <el-tooltip
            :content="$t('详情')"
            placement="top"
            >
            <div @click="showOrderDetails(scope.data)" class="flex-box">
              <MonoOrder size="16" class="cursor-pointer fit-theme" />
            </div>
          </el-tooltip>
        </div>
      </template>
      <template #type="scope">
        <span :class="{'fit-rise': scope.data.side === 'buy', 'fit-fall': scope.data.side === 'sell'}">{{ scope.data.type === 'limit' ? $t('限价单') : $t('市价单') }}</span>
      </template>
      <template #executedQty="scope">
        <span>{{ format((Number(scope.data.executedQty) < 0 ? -Number(scope.data.executedQty) : scope.data.executedQty), (pairInfo[scope.data.product] || {}).quantity_scale, true) }}</span>
      </template>
      <template #executedCost="scope">
        <span>{{ format((Number(scope.data.executedCost) < 0 ? -Number(scope.data.executedCost) : scope.data.executedCost), (pairInfo[scope.data.product] || {}).price_scale, true) }}</span>
      </template>
      <template #orderId="scope">
        <span v-if="scope.data.status === 'accepted'" class="fit-theme cursor-pointer" @click="cancelOrder(scope.data)">{{ $t('撤单') }}</span>
        <span v-else>--</span>
      </template>
    </OrderstableBox>
  </div>
  <OrderDetailDialog
    v-if="isShowOrderDetail"
    :isShow="isShowOrderDetail"
    :data="curDetail"
    @close="isShowOrderDetail = false"
    @goDealList="emit('goDealList')"
  />
</template>

<script lang="ts" setup>
  import { ElTooltip } from 'element-plus'
  import BigNumber from 'bignumber.js'
  import BoxNoData from '~/components/common/BoxNoData.vue'
  import NoLoginCont from '~/components/common/NoLoginCont.vue'
  import OrderstableBox from '~/components/common/OrderstableBox.vue'
  import MonoOrder from '~/components/common/icon-svg/MonoOrder.vue'
  import OrderDetailDialog from '~/components/exchange/orders/OrderDetailDialog.vue'
  import { timeFormat, format } from '~/utils'
  import { ORDER_STATUS_MAP } from '~/config'
  import { useCommonData } from '~/composables/index'
  import { deleteDetail } from '~/api/order'
  import { commonStore } from '~/stores/commonStore'
  const store = commonStore()
  const { locale, t } = useI18n()
  const { pairInfo } = storeToRefs(store)
  const useCommon = useCommonData()
  const props = defineProps({
    isLoading: {
      type: Boolean,
      default: false
    },
    currentList: {
      type: Object,
      default(){
        return []
      }
    },
    pair: {
      type: String,
      default: ''
    },
    isLogin: {
      type: Boolean,
      default: false
    }
  })
  const emit = defineEmits(['getOrderList', 'goDealList'])
  const mSortList = ref([
    { text: t('时间'), key: 'updateTime', align: 'left', wid: '',style: 'auto' },
    { text: t('订单成交度'), key: 'status', align: 'left', wid: '',style: 'auto' },
    { text: t('交易对'), key: 'product', align: 'left', wid: '',style: 'auto' },
    { text: t('方向'), key: 'side', align: 'left', wid: '',style: 'auto' },
    { text: t('委托价格'), key: 'price', align: 'left', wid: '',style: 'auto' },
    { text: t('成交价格'), key: 'F', align: 'left', wid: '',style: 'auto' },
    { text: t('委托数量'), key: 'quantity', align: 'left', wid: '',style: 'auto' },
    { text: t('成交数量'), key: 'executedQty', align: 'left', wid: '',style: 'auto' },
    { text: t('委托金额'), key: 'executedCost', align: 'left', wid: '',style: 'auto' },
    { text: t('委托类型'), key: 'type', align: 'left', wid: '',style: 'auto' }
  ])
  const headersList = ref([
    { text: t('时间'), key: 'updateTime', align: 'left', wid: 'flex-2',style: 'auto' },
    { text: t('交易对'), key: 'product', align: 'left', wid: '',style: 'auto' },
    { text: t('方向'), key: 'side', align: 'left', wid: '',style: 'auto' },
    { text: t('委托价格'), key: 'price', align: 'left', wid: '',style: 'auto' },
    { text: t('成交价格'), key: 'F', align: '', wid: '',style: 'auto' },
    { text: t('委托数量'), key: 'quantity', align: 'left', wid: '',style: 'auto' },
    { text: t('成交数量'), key: 'executedQty', align: '', wid: '',style: 'auto' },
    { text: t('委托金额'), key: 'executedCost', align: 'left', wid: '',style: 'auto' },
    { text: t('委托类型'), key: 'type', align: 'left', wid: '',style: 'auto' },
    { text: t('订单成交度'), key: 'status', align: 'left', wid: 'flex-2',style: 'auto' }
  ])
  const cancelOrder = async(row) => {
    console.info(row.i, 'dhueuehuduheuheuuhe')
    const { data, error } = await deleteDetail({
       id: row.i
    })
    if (data) {
      emit('getOrderList')
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const curDetail = ref({})
  const isShowOrderDetail = ref(false)
  const showOrderDetails = (row) => {
    curDetail.value = row
    isShowOrderDetail.value = true
  }
</script>

<style scoped lang="scss">
  .ordersCont{
    height:100%;
    overflow:auto;
  }
  @include mb {
    .ordersCont{
      padding:0 20px;
    }
  }
</style>