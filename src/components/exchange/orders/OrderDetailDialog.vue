<template>
  <el-dialog v-model="visible" :title="$t('委托详情')" @close="emit('close')">
    <div class="check-data">
      <div class="flex-box mg-b16 data-symbol-info">
        <div class="flex-box font-size-16 w100-info mg-r12">
          <!-- <BoxCoinIcon size="20" :icon="data.product.split('_')[0]" class="mg-r8" /> -->
          <span v-if="data.product.includes('_SWAP')">{{ data.product.replace('_SWAP', '').replace('_', '') }}</span>
          <span v-else>{{ data.product.replace('_', '/') }}</span>
        </div>
        <div class="flex-1 flex-box space-between w100-info">
          <div class="flex-box">
            <template v-if="data.product.includes('_SWAP')">
              <span class="mg-r8" v-if="(data.type === 'stop' || data.type === 'stop-limit' || data.type === 'take-profit' || data.type === 'take-profit-limit')"
                :class="data.type === 'take-profit-limit' || data.type === 'take-profit' ? 'fit-rise' : 'fit-fall'">
                {{ Number(data.quantity) < 0 ? $t('平多') : $t('平空') }}/{{ $t(PROFIT_LOSS_MAP[data.type]) }}
              </span>
              <span v-else class="mg-r8" :class="{'fit-rise': !data.close && Number(data.quantity) > 0 || data.close && Number(data.quantity) > 0, 'fit-fall': !data.close && Number(data.quantity) < 0 || data.close && Number(data.quantity) < 0}">
                <template v-if="!data.close && Number(data.quantity) < 0">{{ $t('开空') }}</template>
                <template v-if="!data.close && Number(data.quantity) > 0">{{ $t('开多') }}</template>
                <template v-if="data.close && Number(data.quantity) > 0">{{ $t('平空') }}</template>
                <template v-if="data.close && Number(data.quantity) < 0">{{ $t('平多') }}</template>
              </span>
            </template>
            <div v-else class="font-size-14 mg-r12" :class="{'fit-rise': Number(data.quantity) > 0,'fit-fall': Number(data.quantity) < 0}">
              <span>
                {{ data.side === 'buy' ? $t('买入') : $t('卖出') }}
                <span class="mg-l12">{{ data.type === 'limit' ? $t('限价单') : $t('市价单') }}</span>
              </span>
            </div>
            <div class="fit-tc-secondary font-size-14">
              {{ $t(ORDER_STATUS_MAP[data.status]) }}
            </div>
          </div>
          <div class="fit-tc-secondary">
            {{ timeFormat(data.updateTime, 'yyyy-MM-dd hh:mm:ss') }}
          </div>
        </div>
      </div>
      <div v-if="data.type !== 'trailing-stop'" class="flex-box space-between font-size-14 pd-b4">
        <span class="fit-tc-secondary">{{ $t('委托价格') }}</span>
        <span class="fit-tc-primary">{{ data.price ? format(data.price, (pairInfo[data.product] || {}).price_scale, true) : $t('市价') }} {{ data.price ? 'USDT' : '' }}</span>
      </div>
      <div v-if="!(data.type === 'limit' || data.type === 'market')" class="flex-box space-between font-size-14">
        <span class="fit-tc-secondary">{{ data.type === 'trailing-stop' ? $t('回调幅度') : $t('触发价格') }}</span>
        <span class="fit-tc-primary">
          <template v-if="data.type === 'trailing-stop'">
            {{ format(data.triggerValue / 100, 2, true)}}%
          </template>
          <template v-else>
            {{ format(data.triggerValue, (pairInfo[data.product] || {}).price_scale, true) }} USDT
          </template>
        </span>
      </div>
      <div class="flex-box space-between font-size-14 pd-b4">
        <span class="fit-tc-secondary">{{ $t('委托量') }}</span>
        <span class="fit-tc-primary">{{ format((Number(data.quantity) < 0 ? -Number(data.quantity) : Number(data.quantity)), (pairInfo[data.product] || {}).quantity_scale, true) }} {{ coinSymbol }}</span>
      </div>
      <div class="flex-box space-between font-size-14">
        <span class="fit-tc-secondary">{{ $t('成交价格') }}</span>
        <span class="fit-tc-primary">{{ format((Number(data.executedQty) * 1 < 0 ? new BigNumber(-Number(data.executedCost)).div(-Number(data.executedQty)) : new BigNumber(Number(data.executedCost)).div(Number(data.executedQty))), Number((pairInfo[data.product] || {}).price_scale), true) }} USDT</span>
      </div>
      <div class="flex-box space-between font-size-14">
        <span class="fit-tc-secondary">{{ $t('成交量') }}</span>
        <span class="fit-tc-primary">{{ format((Number(data.executedQty) < 0 ? -Number(data.executedQty) : data.executedQty), (pairInfo[data.product] || {}).quantity_scale, true) }} {{ coinSymbol }}</span>
      </div>
      <div class="flex-box space-between font-size-14">
        <div class="fit-tc-secondary flex-box">
          {{ $t('手续费估值') }}
          <div v-if="data.fees.length > 0  && data.product.includes('_SWAP')" class="fit-theme mg-l12 cursor-pointer flex-box font-size-12" @click="isShowFees = !isShowFees">
            {{ $t('查看详情') }}
            <MonoDownArrowShort size="12" class="mg-l4 more-btn-svg" :class="{'show': isShowFees}" />
          </div>
        </div>
        <div v-if="data.product.includes('_SWAP')" class="fit-tc-primary flex-box">
          {{ format(feeTotalUST, 4, true) }} USDT
        </div>
        <div v-else class="fit-tc-primary flex-box">
          {{ data.fees && data.fees[0] && data.fees[0].amount || 0 }} {{ data.fees && data.fees[0] && data.fees[0].asset || 'USDT' }}
        </div>
      </div>
      <div v-if="isShowFees" style="margin-left:-20px;margin-right:-20px;">
        <OrderstableBox :isLoading="isLoading" :mSortList="mSortList" :headers="headersList" :list="feesList">
          <template #asset="scope">
            {{ scope.data.asset === 'KtxQ1' ? t('体验金') : (scope.data.asset === 'KtxQ2' ? t('抵扣金') : scope.data.asset) }}
          </template>
          <template #amount="scope">
            {{ scope.data.amount }} {{ (scope.data.asset === 'KtxQ1' || scope.data.asset === 'KtxQ2') ? 'USDT' : scope.data.asset }}
          </template>
          <template #value="scope">
            {{ format(scope.data.value, 4, true) }}
          </template>
        </OrderstableBox>
      </div>
      <h2 class="font-size-16 fit-tc-primary pd-t16 flex-box space-between">
        {{ $t('成交详情') }}
        <span class="fit-theme font-size-14 cursor-pointer flex-box" @click="moreBtn()">
          {{ $t('查看更多') }}
          <MonoRightArrow size="14" class="fit-theme mg-l4" />
        </span>
      </h2>
      <div class="order-margin">
        <OrderstableBox :isLoading="false" :mSortList="DealList" :headers="DealList" :list="fillList">
          <template #time="scope">
            <span style="text-align:left;">
              {{ timeFormat(scope.data.time, 'yyyy-MM-dd hh:mm:ss') }}
            </span>
          </template>
          <template #price="scope">
            {{ format(scope.data.price, (pairInfo[data.product] || {}).price_scale, true) }}
          </template>
          <template #quantity="scope">
            {{ Number(scope.data.quantity) < 0 ? -Number(scope.data.quantity) : Number(scope.data.quantity) }}
          </template>
          <template #fees="scope">
            {{ data.product.includes('_SWAP') ? format(totalFeesValue(scope.data.fees), 4, true) : scope.data.fees[0].amount }}
          </template>
        </OrderstableBox>
      </div>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
  import BigNumber from 'bignumber.js'
  import { ElDialog } from 'element-plus'
  import { ORDER_STATUS_MAP, PROFIT_LOSS_MAP } from '~/config'
  import { timeFormat, format } from '~/utils'
  import { commonStore } from '~/stores/commonStore'
  import OrderstableBox from '~/components/common/OrderstableBox.vue'
  import MonoDownArrowShort from '~/components/common/icon-svg/MonoDownArrowShort.vue'
  import MonoRightArrow from '~/components/common/icon-svg/MonoRightArrow.vue'
  const store = commonStore()
  const { pairInfo } = storeToRefs(store)
  const { locale, t } = useI18n()
  const props = defineProps({
    isShow: {
      type: Boolean,
      default: false
    },
    isDeal: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default () {
        return {}
      }
    }
  })
  const emit = defineEmits(['close', 'goDealList'])
  const visible = ref(false)
  const isLoading = ref(false)
  const mSortList = ref([
    { text: t('币种'), key: 'asset', align: 'left', wid: '' },
    { text: t('手续费'), key: 'amount', align: 'center', wid: '',style: 'auto' },
    { text: t('手续费估值'), key: 'value', align: 'right', wid: '',style: 'auto' }
  ])
  const headersList = ref([
    { text: t('币种'), key: 'asset', align: 'left', wid: '',style: 'auto' },
    { text: t('手续费'), key: 'amount', align: 'center', wid: '',style: 'auto' },
    { text: t('手续费估值'), key: 'value', align: 'right', wid: '',style: 'auto' }
  ])
  const DealList = ref([
    { text: t('成交时间'), key: 'time', align: 'left', wid: '',style: 'auto' },
    { text: t('成交价格'), key: 'price', align: 'left', wid: '',style: 'auto' },
    { text: t('成交数量'), key: 'quantity', align: 'center', wid: '',style: 'auto' },
    { text: t('手续费'), key: 'fees', align: 'right', wid: '',style: 'auto' }
  ])
  const fillList = ref([])
  const coinSymbol = computed(() => {
    return props.data.product.split('_')[0]
  })
  const feeTotalUST = computed(() => {
    let num = 0
    props.data.fees.forEach((item) => {
      num += Number(item.value) > 0 ? Number(item.value) : Number(item.amount)
    })
    return num
  })
  const feesList = ref([])
  const isShowFees= ref(false)
  const totalFeesValue = (fees) => {
    let num = 0
    fees.forEach((item) => {
      num += Number(item.value) > 0 ? Number(item.value) : Number(item.amount)
    })
    return num
  }
  const moreBtn = () => {
    emit('close')
    emit('goDealList')
  }
  onMounted(() => {
    visible.value = props.isShow
    feesList.value = props.data.fees
    fillList.value = props.data.fills
  })
</script>
<style lang="scss" scoped>
  .more-btn-svg{
    &.show{
      transform: rotate(180deg);
    }
  }
  .order-margin{
    margin-left:-20px;
    margin-right:-20px;
  }
  @include mb {
    .check-data{
      .data-symbol-info{
        flex-direction: column;
        .w100-info{
          width:100%;
          &.mg-r12{
            margin-right:0 !important;
          }
        }
      }
    }
    .order-margin{
      margin-left:0;
      margin-right:0;
    }
  }
</style>
