<template>
  <div class="orders-search-container">
    <div v-if="!isMobile && isLoading" class="orders-search-wrapper-pc">
      <DropdownSelect isSearch v-model="search.coin_symbol" :list="filterCoinList" :labelName="$t('交易对')" wid="200" @change="changeRequest">
        <div class="orders-search-box-input">
          <el-input v-model="searchInput" clearable>
            <template #prepend>
              <MonoSearch size="16" />
            </template>
          </el-input>
        </div>
      </DropdownSelect>
      <DropdownSelect v-if="isType" v-model="search.type" :list="orderTypeOption" :labelName="$t('委托类型')" />
      <DropdownSelect v-if="isSide" v-model="search.side" :list="orderSideOption" :labelName="$t('委托方向')" />
      <DropdownSelect v-if="isMode" v-model="search.margin_method" :list="orderModeOption" :labelName="$t('仓位模式')" />
      <DropdownSelect v-if="isStatus" v-model="search.order_status" :list="orderStatusOption" :labelName="$t('订单状态')" />
      <DataSelect v-if="curUrl === `/${locale}/my/orders/spot/history` || curUrl === `/${locale}/my/orders/spot/deal` || curUrl === `/${locale}/my/orders/future/history` || curUrl === `/${locale}/my/orders/future/deals`" :isReset="isResetDate" @resetFun="isResetDate = false" @change="changeDate" />
      <el-button type="primary" @click="resetFun()">{{ $t('重置') }}</el-button>
      <!-- <el-checkbox class="checkbox-value" v-if="curUrl === `/${locale}/my/orders/spot/history` || curUrl === `/${locale}/my/orders/future/history`" v-model="search.hide_cancel">{{ $t('隐藏已撤销') }}</el-checkbox> -->
    </div>
    <div v-if="isMobile && isLoading" class="orders-search-wrapper-m flex-box space-between">
      <div class="select-wrap flex-box" :class="{'fit-tc-primary': search.coin_symbol}" @click="isShowCoinList = true">
        {{ search.coin_symbol ? (search.coin_symbol.includes('_SWAP') ? search.coin_symbol.replace('_SWAP', '').replace('_', '') : search.coin_symbol.replace('_', '/')) : $t('所有交易对') }}
        <MonoDownArrowMin :size="12" class="fit-tc-secondary mg-l4" :class="{'fit-tc-primary': search.coin_symbol}" />
      </div>
      <div class="flex-box">
        <div class="tag-icon select-icon-m" @click="showParamsDialog"></div>
      </div>
    </div>
  </div>
  <OrderParmasSelect
    v-if="isShowParamsSelect"
    :isType="isType"
    :isSide="isSide"
    :isMode="isMode"
    :isDate="isDate"
    :isFuture="isFuture"
    :isStatus="isStatus"
    :defaultSearch="search"
    :typeList="orderTypeOption"
    :isShowSlectParamsCont="isShowSlectParamsCont"
    :sideList="orderSideOption"
    :modeList="orderModeOption"
    :statusList="orderStatusOption"
    @closeDialog="closeParamsDialog"
    @confirmParams="changeParams"
    @change="searchInput = ''"
    >
    <div class="orders-search-box-input">
      <el-input v-model="searchInput" clearable>
        <template #prepend>
          <MonoSearch size="16" />
        </template>
      </el-input>
    </div>
  </OrderParmasSelect>
  <SelectDialog
    v-if="isShowCoinList"
    :isShowDialog="isShowCoinList"
    :title="$t('交易对')"
    :isShowInput="true"
    :defaultValue="search.coin_symbol"
    :list="filterCoinList"
    @close="isShowCoinList = false"
    @changeItem="changeFun"
    >
    <div>
      <el-input v-model="searchInput" clearable>
        <template #prepend>
          <MonoSearch size="16" />
        </template>
      </el-input>
    </div>
  </SelectDialog>
  <OrderDateSelect :isShowSlectDateCont="isShowSlectDateCont" :isShowDateSelect="isShowDateSelect" v-if="isShowDateSelect" @confirmChange="changeDate" @closeDialog="closeDateDialog()" />
</template>
<script lang="ts" setup>
  import { cookies } from '~/utils/cookies'
  import { ElInput, ElButton, ElCheckbox } from 'element-plus'
  import { ORDER_TYPE_SIGN_MAP } from '~/config'
  import DropdownSelect from '~/components/common/DropdownSelect'
  import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
  import DataSelect from '~/components/common/DataSelect.vue'
  import OrderParmasSelect from '~/components/common/OrderParmasSelect.vue'
  import SelectDialog from '~/components/common/SelectDialog.vue'
  import OrderDateSelect from '~/components/common/OrderDateSelect.vue'
  import { timeFormat } from '~/utils'
  import { commonStore } from '~/stores/commonStore'
  import { useCommonData } from '~/composables/index'
  const useCommon = useCommonData()
  const store = commonStore()
  const { getAllPairList } = store
  const { allPairList } = storeToRefs(store)
  const { locale, t } = useI18n()
  const router = useRouter()
  const props = defineProps({
    isFuture: {
      type: Boolean,
      default: false
    },
    isDate: {
      type: Boolean,
      default: false
    },
    isDeal: {
      type: Boolean,
      default: false
    },
    isMode: {
      type: Boolean,
      default: false
    },
    isType: {
      type: Boolean,
      default: false
    },
    isSide: {
      type: Boolean,
      default: false
    },
    isStatus: {
      type: Boolean,
      default: false
    }
  })
  const emit = defineEmits(['request'])
  const nuxtpageRef= ref(null)
  const searchInput = ref('')
  const isShowDateSelect = ref(false)
  const isShowSlectDateCont = ref(false)
  const isShowParamsSelect = ref(false)
  const isShowSlectParamsCont = ref(false)
  const curUrl = computed(() => {
    return router.currentRoute.value.path
  })
  const data = {
    name: 'hhhhhhs'
  }
  const isResetDate = ref(false)
  const orderModeOption = ref([
    {
      label: t('全部'),
      value: ''
    },
    {
      label: t('全仓'),
      value: 2
    },
    {
      label: t('逐仓'),
      value: 1
    }
  ])
  const orderStatusOption = ref([
    {
      label: t('全部'),
      value: ''
    },
    {
      label: t('部分成交'),
      value: 4
    },
    {
      label: t('完全成交'),
      value: 3
    },
    {
      label: t('已撤销'),
      value: 2
    },
  ])
  const search = ref({
    coin_symbol: '',
    type: '',
    side: '',
    margin_method: '',
    order_status: '',
    timeType: 0,
    startDate: '',
    endDate: '',
    hide_cancel: true
  })
  const isShowCoinList= ref(false)
  const showParamsDialog = () => {
    isShowParamsSelect.value = true
    setTimeout(() => {
      isShowSlectParamsCont.value = true
    })
  }
  const closeParamsDialog = () => {
    isShowSlectParamsCont.value = false
    setTimeout(() => {
      isShowParamsSelect.value = false
    })
  }
  const showDateDialog = () => {
    isShowDateSelect.value = true
    setTimeout(() => {
      isShowSlectDateCont.value = true
    })
  }
  const closeDateDialog = () => {
    isShowSlectDateCont.value = false
    setTimeout(() => {
      isShowDateSelect.value = false
    })
  }
  const filterCoinList = computed(() => {
    const arr = useCommon.formatPairList(allPairList.value).filter((v) => {
      return props.isFuture ? v.value.includes('_SWAP') : !v.value.includes('_SWAP')
    })
    arr.unshift({
      label: t('全部'), value: ''
    })
    if (searchInput.value !== '') {
      return arr.filter(v => {
        return v.value.includes(searchInput.value.toUpperCase())
      })
    } else {
      return arr
    }
  })
  const orderTypeOption = computed(() => {
    if (props.isFuture) {
      return [
        { label: t('全部'), value: '' },
        { label: t('限价'), value: '1' },
        { label: t('市价'), value: '0' },
        { label: t('止盈止损'), value: '16,17,32,33' },
        { label: t('计划委托'), value: '64,65' },
        { label: t('跟踪委托'), value: '48' }
      ]
    } else {
      return [
        {
        label: t('全部'),
          value: ''
        },
        { label: t('限价单'), value: 1 },
        { label: t('市价单'), value: 0 }
      ]
    }
  })
  const orderSideOption = computed(() => {
  if (props.isFuture && props.isDeal) {
    return [
      { label: t('全部'), value: '' },
      {
        label: t('买入'),
        value: 1
      },
      {
        label: t('卖出'),
        value: -1
      }
    ]
    } else if (props.isFuture && !props.isDeal) {
      return [
        { label: t('全部'), value: '' },
        {
          label: `${t('开多')}/${t('平空')}`,
          value: 1
        },
        {
          label: `${t('开空')}/${t('平多')}`,
          value: -1
        }
      ]
    } else {
      return [
        { label: t('全部'), value: '' },
        {
          label: t('买入'),
          value: 1
        },
        {
          label: t('卖出'),
          value: -1
        }
      ]
    }
  })
  const isLoading = ref(false)
  const isMobile = ref(false)
  const screenWidth = ref(0)
  
  const changeRequest = (item) => {
    searchInput.value = ''
  }
  const resetFun = () => {
    isResetDate.value = true
    search.value = {
      coin_symbol: '',
      type: '',
      side: '',
      margin_method: '',
      order_status: '',
      startDate: '',
      endDate: '',
      hide_cancel: true
    }
  }
  const changeParams = (item) => {
    search.value.type = item.order_type
    search.value.side = item.side || ''
    search.value.margin_method = item.margin_method || ''
    search.value.order_status = item.status || ''
    search.value.startDate = item.start
    search.value.endDate = item.end
    isShowParamsSelect.value = false
  }
  const changeDate = (item) => {
    search.value.timeType = item.time
    search.value.startDate = item.start
    search.value.endDate = item.end
    console.info(item, 'datedatedate')
  }
  const updateScreenWidth = () => {
    screenWidth.value = window.innerWidth
    isMobile.value = screenWidth.value <= 768
  }
  const subtractFromCurrentDate = (type, amount) => {
    const currentDate = new Date()
    let newDate = new Date(currentDate)
    if (type === 'months') {
        // 设置月份时，需要注意JavaScript中的月份是从0开始的
        newDate.setMonth(newDate.getMonth() - amount)
    } else if (type === 'days') {
        newDate.setDate(newDate.getDate() - amount)
    } else if(type === 'years') {
      newDate.setDate(newDate.getYear() - amount)
    } else {
        throw new Error('Invalid type. Expected "months" or "days".')
    }
    return newDate
  }
  const urlSymbol = computed(() => {
    return router.currentRoute.value.query.pair
  })
  const initParams = () => {
    const start = timeFormat(subtractFromCurrentDate('days', 7), 'yyyy-MM-dd')
    const end = timeFormat(new Date(), 'yyyy-MM-dd')
    search.value = {
      coin_symbol: urlSymbol.value || '',
      side: '',
      type: '',
      margin_method: '',
      order_status: '',
      timeType: 7,
      startDate: start,
      endDate: end,
      hide_cancel: true
    }
  }
  watch(() => search.value, (val) => {
    emit('request', search.value)
  }, {
    deep: true
  })
  const isShowAd = ref(0)
  const requestAnimationFrameInterval = ref(null)
  const socketDateAnimation = () => {
    isShowAd.value = cookies.get('showMessage')
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  }
  const changeFun = (item) => {
    search.value.coin_symbol = item.value
  }
  const curSymbol = computed(() => {
    return router.currentRoute.value.query.pair || ''
  })
  watch(curSymbol.value, (newVal) => {
    if (newVal) {
      console.log(newVal, 'ddddddddddddd')
      search.value.coin_symbol = newVal
    }
  }, { immediate: true })
  onMounted(() => {
    socketDateAnimation()
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
    initParams()
    isLoading.value = true
    updateScreenWidth()
    window.addEventListener('resize', updateScreenWidth)
    getAllPairList()
  })
</script>
<style lang="scss" scoped>
@import url('@/assets/style/orders/index.scss');
</style>