<template>
  <div class="exchnage-deals-wrap">
    <div class="deals-row fit-tc-primary head">
      <div class="deals-row__item left">{{ $t('时间') }}</div>
      <div class="deals-row__item right">{{ $t('价格') }}</div>
      <div class="deals-row__item right">{{ $t('数量') }}</div>
    </div>
    <div class="deals-cont-wrap">
      <template v-if="isLogin">
        <el-scrollbar v-if="filterDeals.length > 0" wrap-class="deals-area" tag="ul">
          <li v-for="(item, index) in filterDeals" :key="index">
            <span class="left">{{ timeFormat(item.time, 'hh:mm:ss') }}</span>
            <span class="right" :class="Number(item.quantity) < 0 ? 'fit-fall' : 'fit-rise' ">{{ format(item.price, priceScale, true) }}</span>
            <span class="right" :class="Number(item.quantity) < 0 ? 'fit-fall' : 'fit-rise' ">{{ format((Number(item.quantity) < 0 ? -Number(item.quantity) : item.quantity), quantityScale, true) }}</span>
          </li>
        </el-scrollbar>
        <BoxNoData v-else :text="$t('暂无数据')" />
      </template>
      <template v-else>
        <NoLoginCont />
      </template>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { format } from '~/utils'
import { ElScrollbar } from 'element-plus'
import BoxNoData from '~/components/common/BoxNoData.vue'
import NoLoginCont from '~/components/common/NoLoginCont.vue'
import { useUserStore } from '~/stores/useUserStore'
import { getMyDealList }  from '~/api/order'
import { timeFormat } from '~/utils'
const props = defineProps({
  pair: {
    type: String,
    default: ''
  },
  priceScale: {
    type: [Number, String],
    default: ''
  },
  quantityScale: {
    type: [Number, String],
    default: ''
  }
})
const store = useUserStore()
const { isLogin } = storeToRefs(store)
const filterDeals = ref([])
const isLoading = ref(true)
const getDealsListFun = async() => {
  isLoading.value = true
  const { data } = await getMyDealList({
    symbol: props.pair
  })
  console.info(data, 'filterDeals.value')
  if (data) {
    isLoading.value = false
    filterDeals.value = data
  }
}
watch(() => props.pair, (val) => {
  if (val) {
    getDealsListFun()
  }
})
</script>
<style lang="scss">
.el-scrollbar{
  width:100%;
}
@import url('@/assets/style/exchange/deals.scss');
</style>
