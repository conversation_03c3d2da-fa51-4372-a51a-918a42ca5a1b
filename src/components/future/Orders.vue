<template>
  <div class="exchange-orders">
    <div class="orders-title flex-box space-between">
      <div class="title-menu-box">
        <div class="title-left">
          <el-tabs v-model="orderType">
            <el-tab-pane :name="1">
              <template #label>
                {{ $t('仓位') }}
                <span v-if="isShowOnlyPair ? curPosition.length > 0 : cbuPositions.length > 0"> · {{ isShowOnlyPair ? curPosition.length : cbuPositions.length }}</span>
              </template>
            </el-tab-pane>
            <el-tab-pane :name="2">
              <template #label>
                {{ $t('当前委托') }}
                <span v-if="currentList.length"> · {{ currentList.length }}</span>
              </template>
            </el-tab-pane>
            <el-tab-pane :name="5">
              <template #label>
                {{ $t('历史委托') }}
              </template>
            </el-tab-pane>
            <el-tab-pane :name="6">
              <template #label>
                {{ $t('成交记录') }}
              </template>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <div class="title-right flex-box">
        <div class="check-box">
          <el-checkbox v-model="isShowOnlyPair">{{ $t('只显示当前交易对') }}</el-checkbox>
        </div>
        <!-- <el-button v-if="orderType * 1 === 1 && isLogin" type="primary" size="small" @click="isShowCancellOrder = true">{{ $t('一键平仓') }}</el-button> -->
        <el-button v-if="orderType * 1 === 2 && isLogin && times >= 2" type="primary" size="small" @click="isShowCancellOrder = true">{{ $t('批量撤单') }}</el-button>
        <div  class="more-txt">
          <nuxtLink :to="orderType === 1 || orderType === 2 ? `/${locale}/my/orders/future/current${isShowOnlyPair ? `?pair=${pair}` : ''}` : ( orderType === 5 ? `/${locale}/my/orders/future/history${isShowOnlyPair ? `?pair=${pair}` : ''}` : `/${locale}/my/orders/future/deals${isShowOnlyPair ? `?pair=${pair}` : ''}`)" class="flex-box">
            <MonoMoreBtnIcon size="16" />
          </nuxtLink>
        </div>
      </div>
    </div>
    <template v-if="isLogin">
      <future-orders-position
        v-if="orderType * 1 === 1"
        :pair="pair"
        :isLogin="isLogin"
        :isLoading="isLoading"
        :coinInfo="coinInfo"
        :cbuPositions="isShowOnlyPair ? curPosition : cbuPositions"
        :curPosition="curPosition"
        :futuresType="futuresType"
        :price="price"
        :amount="amount"
        :ticker="ticker"
        :isCBCUnitUSD="isCBCUnitUSD"
        :isCBUUnitUSDT="isCBUUnitUSDT"
        :priceScale="priceScale"
        :quantityScale="quantityScale"
        :tradeAssetObj="tradeAssetObj"
        :isShowOnlyPair="isShowOnlyPair"
        :tradeArr="tradeArr"
      />
      <future-orders-current
        v-if="orderType * 1 === 2"
        :isOrder="false"
        :isLogin="isLogin"
        :isLoading="isLoading"
        :currentList="currentList"
        @checkCurType="checkCurType"
        @getOrderList="getOrderList('unsettled', pair)" />
      <future-orders-plan v-if="orderType * 1 === 3" />
      <future-orders-follow v-if="orderType * 1 === 4" />
      <future-orders-history
        v-if="orderType * 1 === 5"
        :isLogin="isLogin"
        :isLoading="isLoading"
        :currentList="historyList"
        @goDealList="orderType = 6"
      />
      <future-orders-deals
        v-if="orderType * 1 === 6"
        :isLogin="isLogin"
        :isLoading="isLoading"
        :currentList="dealsList"
      />
    </template>
    <div v-else style="height:100%;" class="flex-box space-center">
      <NoLoginCont />
    </div>
  </div>
  <BatchOrderDialog
    v-if="isShowCancellOrder"
    :dialogVisible="isShowCancellOrder"
    :times="times"
    :openLongCount="openLongCount"
    :openShortCount="openShortCount"
    :closeLongCount="closeLongCount"
    :closeShortCount="closeShortCount"
    :pair="pair"
    :isShowOnlyPair="isShowOnlyPair"
    @close="isShowCancellOrder = false"
    @successBatch="getOrderList('unsettled', pair)"
  />
</template>
<script lang="ts" setup>
  import { ElTabs, ElTabPane } from 'element-plus'
  import MonoMoreBtnIcon from '~/components/common/icon-svg/MonoMoreBtnIcon.vue'
  import BoxXOverflow from '~/components/common/BoxXOverflow.vue'
  import { getCurrenOrderList, getMyDealList }  from '~/api/order'
  import FutureOrdersPosition from '~/components/future/orders/position.vue'
  import FutureOrdersCurrent from '~/components/future/orders/current.vue'
  import FutureOrdersPlan from '~/components/future/orders/plan.vue'
  import FutureOrdersFollow from '~/components/future/orders/follow.vue'
  import FutureOrdersHistory from '~/components/future/orders/history.vue'
  import FutureOrdersDeals from '~/components/future/orders/deals.vue'
  import NoLoginCont from '~/components/common/NoLoginCont.vue'
  import BatchOrderDialog from '~/components/future/orders/BatchOrderDialog.vue'
  import { futureStore } from '~/stores/futureStore'
  import { commonStore } from '~/stores/commonStore'
  import { useUserStore } from '~/stores/useUserStore'
  const { locale, t } = useI18n()
  const userStore = useUserStore()
  const { userInfo } = storeToRefs(userStore)
  const store = futureStore()
  const { getCBUPositions, subFutureInfoSocket } = store
  const { cbuPositions } = storeToRefs(store)
  const publicStore = commonStore()
  const { isChangePosition, isChangeFutureOrder  } = storeToRefs(publicStore)
  const props = defineProps({
    pair: {
      type: String,
      default: ''
    },
    price: {
      type: String,
      default: ''
    },
    amount: {
      type: [Number, String],
      default: ''
    },
    coinInfo: {
      type: Object,
      default () {
        return {}
      }
    },
    tradeAssetObj: {
      type:Object,
      default(){
        return{}
      }
    },
    isLogin: {
      type: Boolean,
      default: false
    },
    futuresType: {
      type: String,
      default: 'lpc'
    },
    ticker: {
      type: Object,
      default () {
        return {}
      }
    },
    tradeArr: {
      type: Array,
      default () {
        return []
      }
    },
    isCBCUnitUSD: {
      type: Boolean,
      default: false
    },
    isCBUUnitUSDT: {
      type: Boolean,
      default: false
    },
    quantityScale: {
      type: [String, Number],
      default: 0
    },
    priceScale: {
      type: [String, Number],
      default: 0
    }
  })
  const orderType = ref(1)
  const currentList = ref([])
  const historyList = ref([])
  const dealsList = ref([])
  const isLoading = ref(false)
  const isShowOnlyPair = ref(false)
  const isShowCancellOrder = ref(false)
  const times = ref(0)
  const openLongCount = ref(0)
  const openShortCount = ref(0)
  const closeLongCount = ref(0)
  const closeShortCount = ref(0)
  const requestAnimationFrameInterval = ref(null)
  const curType = ref(0)
  const curPosition = computed(() => {
    return cbuPositions.value.filter((item) => {
      return item.symbol === props.pair
    })
  })
  watch(() => isShowOnlyPair.value, (val) => {
    localStorage.setItem('isShowOnlyPairFuture', val)
  })
  const getOrderList = async(typeP, pairP, before = '', limit = '') => {
    const { data } = await getCurrenOrderList({
      status: typeP,
      symbol: isShowOnlyPair.value ? pairP : undefined,
      market: props.futuresType,
      before: typeP === 'settled' ? before : undefined,
      limit: typeP === 'settled' ? limit : undefined
    })
    if (data) {
      if (typeP === 'unsettled') {
        let ol = 0
        let os = 0
        let cl = 0
        let cs = 0
        data.forEach((item) => {
          if (!item.close && item.positionMerge === 'short') {
            os++
          } else if (!item.close && item.positionMerge === 'long') {
            ol++
          } else if (item.close && item.positionMerge === 'short') {
            cs++
          } else if (item.close && item.positionMerge === 'long') {
            cl++
          }
        })
        times.value = data.length
        openLongCount.value = ol
        openShortCount.value = os
        closeLongCount.value = cl
        closeShortCount.value = cs
      }
      typeP === 'unsettled' ? currentList.value = data : historyList.value = data.filter((item) => {
        return Number(item.quantity) * 1 !== 0
      })
      isLoading.value = false
    }
  }
  const getDealsListFun = async() => {
    isLoading.value = true
    const { data } = await getMyDealList({
      symbol: isShowOnlyPair.value ? props.pair : undefined,
      market: props.futuresType
    })
    if (data) {
      isLoading.value = false
      dealsList.value = data
    }
  }
  watch(() => props.pair , (val) => {
    if (val !== '') {
      checkCurType(curType.value)
      getOrderList('unsettled', val)
      getCBUPositions(undefined, props.futuresType)
    }
  })
  watch(() =>cbuPositions.value, (val) => {
    val.forEach((item) => {
      subFutureInfoSocket(item.symbol)
    })
  })
  watch(() => userInfo.value, (val) => {
    if (JSON.stringify(val) !== '{}') {
      isLoading.value = true
      if (orderType.value * 1 === 1) {
        getOrderList('unsettled', props.pair)
      } else if (orderType.value * 1 === 2) {
        getOrderList('unsettled', props.pair)
      } else if (orderType.value * 1 === 5) {
        getOrderList('settled', props.pair)
      } else if (orderType.value * 1 === 6) {
        getDealsListFun()
      }
      getCBUPositions(undefined, props.futuresType)
    }
  }, {
    immediate: true
  })
  watch(() => [orderType.value, props.isLogin], ([val, valLogin]) => {
    isLoading.value = true
    if (val * 1 === 1 && valLogin) {
      getOrderList('unsettled', props.pair)
      getCBUPositions(undefined, props.futuresType)
    } else if (val * 1 === 2 && valLogin) {
      getOrderList('unsettled', props.pair)
    } else if (val * 1 === 5 && valLogin) {
      getOrderList('settled', props.pair)
    } else if (val * 1 === 6 && valLogin) {
      getDealsListFun()
    }
  }, {
    immediate: true
  })
  watch(() => isShowOnlyPair.value, (val) => {
    isLoading.value = true
    if (orderType.value * 1 === 1) {
      getOrderList('unsettled', props.pair)
      getCBUPositions(undefined, props.futuresType)
    } else if (orderType.value * 1 === 2) {
      getOrderList('unsettled', props.pair)
    } else if (orderType.value * 1 === 5) {
      getOrderList('settled', props.pair)
    } else if (orderType.value * 1 === 6) {
      getDealsListFun()
    }
  })
  watch(() => props.pair, (val) => {
    if (val) {
      getCBUPositions(undefined, props.futuresType)
    }
  })
  watch(() =>isChangeFutureOrder.value, async(val) => {
    if (val) {
      await getOrderList('unsettled', props.pair)
      isChangeFutureOrder.value = false
    }
  })
  watch(() =>isChangePosition.value, val => {
    if (val) {
      isChangePosition.value = false
      getCBUPositions(undefined, props.futuresType)
    }
  })
  const socketDateAnimation = () => {
    if (isChangePosition.value) {
      isChangePosition.value = false
      getCBUPositions(undefined, props.futuresType)
    }
    if (isChangeFutureOrder.value) {
      isChangeFutureOrder.value = false
      getOrderList('unsettled', props.pair)
    }
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  }
  const checkCurType = (val) => {
    curType.value = val
  }
  onMounted(() => {
    checkCurType(curType.value)
    isShowOnlyPair.value = localStorage.getItem('isShowOnlyPairFuture') === 'true' ? true : false
    socketDateAnimation()
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  })
</script>
<style lang="scss">
  .exchange-orders{
  height:100%;
  .orders-title{
    height:46px;
    border-bottom:1px solid;
    padding:0 16px;
    font-size:14px;
    @include border-color(border);
    .title-left{
      .el-tabs__header{
        margin:4px 0;
        .el-tabs__nav-wrap{
          &.is-scrollable{
            padding:0 20px 0 0;
          }
          &:after{
            display:none;
          }
          .el-tabs__nav-next, .el-tabs__nav-prev{
            line-height:46px;
            top:3px;
          }
          .el-tabs__nav-prev{
            z-index:9;
            left:-4px;
            @include bg-color(bg-primary);
            &.is-disabled{
              display:none;
            }
          }
          .el-tabs__nav{
            margin:0;
            .el-tabs__active-bar{
              display:none;
            }
            .el-tabs__item{
              border:0;
              margin-right:20px;
              height:46px;
              line-height:46px;
              position:relative;
              padding:0;
              cursor:pointer;
              @include color(tc-secondary);
              &:nth-child(2){
                margin-right:0;
              }
              &:nth-child(3){
                margin-left:20px;
              }
              &:last-child{
                margin-right:0;
              }
              &.is-active{
                @include color(tc-primary);
                &:after{
                  content: '';
                  position:absolute;
                  width:16px;
                  height:2px;
                  display:block;
                  bottom:0;
                  left:50%;
                  margin-left:-8px;
                  @include bg-color(theme);
                }
              }
            }
          }
        }
      }
      li{
        margin-right:20px;
        height:46px;
        line-height:46px;
        position:relative;
        cursor:pointer;
        @include color(tc-secondary);
        &.active{
          @include color(tc-primary);
          &:after{
            content: '';
            position:absolute;
            width:16px;
            height:2px;
            display:block;
            bottom:0;
            left:50%;
            margin-left:-8px;
            @include bg-color(theme);
          }
        }
      }
    }
    .title-right{
      .check-box{
        margin-right:12px;
      }
      .el-button{
        margin-right:12px;
      }
      .more-txt{
        padding-left:12px;
        position:relative;
        &:after{
          content: '';
          display:block;
          width:1px;
          height:16px;
          position:absolute;
          left:0;
          top:50%;
          margin-top:-8px;
          @include bg-color(border);
        }
        a{
          cursor:pointer;
          @include color(tc-primary);
          &:hover{
            @include color(theme);
          }
        }
      }
    }
  }
}
@include mb {
  .exchange-orders{
      .orders-title{
        height:auto;
        font-size:14px;
        position:relative;
        &.flex-box{
          flex-direction: column;
          align-items: start;
        }
        .title-menu-box{
          width:85%;
        }
        .title-left{
          width:100%;
          li{
            &.active{
              &:after{
                margin-left:-16px;
              }
            }
          }
        }
        .title-right{
          width:100%;
          padding:8px 0;
          justify-content: space-between;
          .check-box{
            margin-right:0;
          }
          .el-button{
            margin-right:0;
          }
          .more-txt{
            padding-left:0;
            position:absolute;
            top:14px;
            right:16px;
            &:after{
              display:none;
            }
            a{
              cursor:pointer;
              @include color(tc-primary);
              &:hover{
                @include color(theme);
              }
            }
          }
        }
      }
    }
  }
</style>
