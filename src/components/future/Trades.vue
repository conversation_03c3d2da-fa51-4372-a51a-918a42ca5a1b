<template>
  <ul class="trade-nav flex-box">
    <li :class="{'active': type === 'depth'}" @click="changeType('depth')">{{ $t('盘口') }}</li>
    <li :class="{'active': type === 'deals'}" @click="changeType('deals')">{{ $t('最新成交') }}</li>
  </ul>
  <div class="tarde-cont">
    <FutureDepth v-show="type === 'depth' || (isIpad && isLoading)"
      :isCBCUnitUSD="isCBCUnitUSD"
      :isCBUUnitUSDT="isCBUUnitUSDT"
      :quantityScale="quantityScale"
      :ticker="ticker"
      :priceScale="priceScale"
      :futuresType="futuresType"
      :pair="pair"
      @change-amount="changeAmount"
      @change-price="changePrice" />
    <FutureDeals
      v-show="type === 'deals' || (isIpad && isLoading)"
      :deals="deals"
      :pair="pair"
      :isCBCUnitUSD="isCBCUnitUSD"
      :isCBUUnitUSDT="isCBUUnitUSDT"
      :quantityScale="quantityScale"
      :priceScale="priceScale"
      :futuresType="futuresType"
    />
  </div>
</template>
<script lang="ts" setup>
import FutureDeals from './trades/Deals.vue'
import FutureDepth from './trades/Depth.vue'
const props = defineProps({
  pair: {
    type: String,
    default: ''
  },
  ticker: {
    type: Object,
    default(){
      return {}
    }
  },
  deals: {
    type: Object,
    default () {
      return []
    }
  },
  futuresType: {
    type: String,
    default: ''
  },
  isCBCUnitUSD: {
    type: Boolean,
    default: false
  },
  isCBUUnitUSDT: {
    type: Boolean,
    default: false
  },
  priceScale: {
    type: [Number, String],
    default: ''
  },
  quantityScale: {
    type: [Number, String],
    default: ''
  }
})
const emit = defineEmits(['change-amount', 'change-price'])
const type = ref('depth')
const isLoading = ref(false)
const isIpad = ref(false)
const screenWidth = ref(0)
const changeAmount = (data) => {
  emit('change-amount', data)
}
const changePrice = (data) => {
  emit('change-price', data)
}
const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth
  isIpad.value = screenWidth.value <= 1064 && screenWidth.value > 768
}
const changeType = (name) => {
  if (isIpad.value) {
    return false
  }
  type.value = name
}
onMounted(() => {
  isLoading.value = true
  updateScreenWidth()
  window.addEventListener('resize', updateScreenWidth)
})
</script>
