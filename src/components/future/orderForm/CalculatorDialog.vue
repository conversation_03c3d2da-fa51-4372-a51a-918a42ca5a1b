<template>
  <el-dialog v-model="visible" @close="emit('close')" width="800" class="calculator-dialog-wrap">
    <div class="calculator-title flex-box">
      <h2>{{ $t('合约计算器') }}</h2>
      <div v-if="JSON.stringify(currentPair) !== '{}'" class="flex-box cursor-pointer pair-cont" @click="isShowCoinList = true">
        <BoxCoinIcon :icon="currentPair.icon_url" size="32" />
        <span class="fit-tc-primary mg-l8 font-size-16">{{ currentPair.pair.replace('_SWAP', '').replace('_', '') }}</span>
        <span class="fit-tc-secondary font-size-14 mg-l4">{{ $t('永续') }}</span>
        <MonoDownArrowMin :size="14" class="fit-tc-primary mg-l8" />
      </div>
      <el-dropdown @command="changePair" trigger="click">
        <div v-if="JSON.stringify(currentPair) !== '{}'" class="flex-box cursor-pointer">
          <BoxCoinIcon :icon="currentPair.icon_url" size="32" />
          <span class="fit-tc-primary mg-l8 font-size-16">{{ currentPair.pair.replace('_SWAP', '').replace('_', '') }}</span>
          <span class="fit-tc-secondary font-size-14 mg-l4">{{ $t('永续') }}</span>
          <MonoDownArrowMin :size="14" class="fit-tc-primary mg-l8" />
        </div>
        <template #dropdown>
          <el-dropdown-menu style="height:500px;width:200px;">
            <el-dropdown-item v-for="(item, index) in futureList" :key="index" :command="item.pair" :class="{'active': item.pair === currentPair.pair}">
              <div class="flex-box space-between" style="width:100%;">
                <div class="flex-box">
                  <BoxCoinIcon :icon="item.icon_url" size="32" />
                  <span class="fit-tc-primary mg-l8">
                    {{ item.pair.replace('_SWAP', '').replace('_', '') }}
                  </span>
                </div>
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <div class="calculator-nav-list">
      <ul class="flex-box">
        <li :class="{'active': calType === 'profit'}" @click="calType = 'profit'">{{ $t('盈亏计算') }}</li>
        <li :class="{'active': calType === 'forced'}" @click="calType = 'forced'">{{ $t('平仓计算') }}</li>
        <li :class="{'active': calType === 'total'}" @click="calType = 'total'">{{ $t('目标收益') }}</li>
      </ul>
    </div>
    <div class="calculator-content">
      <div v-if="calType === 'profit'" class="calculator-type-cont flex-box align-start">
        <div class="calculator-type-left">
          <div class="calculator-type-nav flex-box">
            <ul class="flex-box">
              <li :class="{'active': profitType === 'long'}" @click="profitType = 'long'">{{ $t('多仓') }}</li>
              <li :class="{'active': profitType === 'short'}" @click="profitType = 'short'">{{ $t('空仓') }}</li>
            </ul>
          </div>
          <div class="calculator-type-form">
            <dl>
              <dt>{{ $t('杠杆倍数') }}</dt>
              <dd>
                <el-input v-model="profitParams.level" type="number" :placeholder="`1-${leverageMax}x`" :max="leverageMax">
                  <template #append>
                    <span class="fit-tc-secondary font-size-14">X</span>
                  </template>
                </el-input>
              </dd>
            </dl>
            <dl>
              <dt>{{ $t('开仓价格') }}</dt>
              <dd>
                <el-input v-model="profitParams.openPrice">
                  <template #append>
                    <span class="fit-tc-secondary font-size-14">USDT</span>
                  </template>
                </el-input>
              </dd>
            </dl>
            <dl>
              <dt>{{ $t('平仓价格') }}</dt>
              <dd>
                <el-input v-model="profitParams.forcePrice">
                  <template #append>
                    <span class="fit-tc-secondary font-size-14">USDT</span>
                  </template>
                </el-input>
              </dd>
            </dl>
            <dl>
              <dt>{{ $t('仓位') }}</dt>
              <dd>
                <el-input v-model="profitParams.position">
                  <template #append>
                    <span class="fit-tc-secondary font-size-14">
                      {{ useCommon.getFuturesSymbol(pair, isCBUUnitUSDT) }}
                    </span>
                  </template>
                </el-input>
              </dd>
            </dl>
            <dl class="fixed-btn">
              <dt>&nbsp;</dt>
              <dd class="fixed-btn">
                <el-button @click="emit('close')">{{ $t('取消') }}</el-button>
                <el-button :disabled="Object.values(profitParams).some(v => !v)" type="primary" @click="calProfit">{{ $t('开始计算')}}</el-button>
              </dd>
            </dl>
          </div>
        </div>
        <div class="calculator-type-right">
          <div class="calculator-info">
            <h3>{{ $t('计算结果') }}</h3>
            <dl>
              <dt>{{ $t('占用保证金') }}</dt>
              <dd>{{ format(profitResult.margin, 4, true) }}</dd>
            </dl>
            <dl>
              <dt>{{ $t('仓位价值') }}</dt>
              <dd>{{ format(profitResult.positionValue, 4, true) }}</dd>
            </dl>
            <dl>
              <dt>{{ $t('盈亏') }}</dt>
              <dd>{{ format(profitResult.profit, 4, true) }}</dd>
            </dl>
            <dl>
              <dt>{{ $t('收益率') }}</dt>
              <dd>{{ format(profitResult.rate, 2, true) }}%</dd>
            </dl>
          </div>
        </div>
      </div>
      <div v-if="calType === 'forced'" class="calculator-type-cont flex-box align-start">
        <div class="calculator-type-left">
          <div class="flex-box mg-b16">
            <el-select v-model="forcedParams.mode" class="flex-1 mg-r12">
              <el-option v-for="(item, index) in modeList" :value="item.value" :label="item.label">{{ item.label }}</el-option>
            </el-select>
            <el-select v-model="forcedParams.type" class="flex-1">
              <el-option v-for="(item, index) in typeList" :value="item.value" :disabled="item.value === 'two' && forcedParams.mode === 'isolate'" :label="item.label">{{ item.label }}</el-option>
            </el-select>
          </div>
          <div v-if="forcedParams.type === 'one'" class="calculator-type-nav flex-box">
            <ul class="flex-box">
              <li :class="{'active': forcedType === 'long'}" @click="forcedType = 'long'">{{ $t('多仓') }}</li>
              <li :class="{'active': forcedType === 'short'}" @click="forcedType = 'short'">{{ $t('空仓') }}</li>
            </ul>
          </div>
          <div class="calculator-type-form">
            <dl>
              <dt>{{ $t('杠杆倍数') }}</dt>
              <dd>
                <el-input v-model="forcedParams.level" type="number" :placeholder="`1-${leverageMax}x`" :max="leverageMax">
                  <template #append>
                    <span class="fit-tc-secondary font-size-14">X</span>
                  </template>
                </el-input>
              </dd>
            </dl>
            <template v-if="forcedParams.type === 'one'">
              <dl>
                <dt>{{ $t('开仓价格') }}</dt>
                <dd>
                  <el-input v-model="forcedParams.openPrice">
                    <template #append>
                      <span class="fit-tc-secondary font-size-14">USDT</span>
                    </template>
                  </el-input>
                </dd>
              </dl>
              <dl>
                <dt>{{ $t('仓位') }}</dt>
                <dd>
                  <el-input v-model="forcedParams.position">
                    <template #append>
                      <span class="fit-tc-secondary font-size-14">
                        {{ useCommon.getFuturesSymbol(pair, isCBUUnitUSDT) }}
                      </span>
                    </template>
                  </el-input>
                </dd>
              </dl>
            </template>
            <template v-if="forcedParams.type === 'two'">
              <p class="fit-rise pd-b8">{{ $t('做多') }}</p>
              <dl>
                <dt>{{ $t('开仓价格') }}</dt>
                <dd>
                  <el-input v-model="forcedParams.longOpenPrice">
                    <template #append>
                      <span class="fit-tc-secondary font-size-14">USDT</span>
                    </template>
                  </el-input>
                </dd>
              </dl>
              <dl>
                <dt>{{ $t('仓位') }}</dt>
                <dd>
                  <el-input v-model="forcedParams.longPosition">
                    <template #append>
                      <span class="fit-tc-secondary font-size-14">
                        {{ useCommon.getFuturesSymbol(pair, isCBUUnitUSDT) }}
                      </span>
                    </template>
                  </el-input>
                </dd>
              </dl>
              <p class="fit-fall pd-b8">{{ $t('做空') }}</p>
              <dl>
                <dt>{{ $t('开仓价格') }}</dt>
                <dd>
                  <el-input v-model="forcedParams.shortOpenPrice">
                    <template #append>
                      <span class="fit-tc-secondary font-size-14">USDT</span>
                    </template>
                  </el-input>
                </dd>
              </dl>
              <dl>
                <dt>{{ $t('仓位') }}</dt>
                <dd>
                  <el-input v-model="forcedParams.shortPosition">
                    <template #append>
                      <span class="fit-tc-secondary font-size-14">
                        {{ useCommon.getFuturesSymbol(pair, isCBUUnitUSDT) }}
                      </span>
                    </template>
                  </el-input>
                </dd>
              </dl>
            </template>
            <dl>
              <dt>{{ $t('可用保证金') }}</dt>
              <dd>
                <el-input v-model="forcedParams.banlance">
                  <template #append>
                    <span class="fit-tc-secondary font-size-14">USDT</span>
                  </template>
                </el-input>
                <span v-if="tipsError !== ''" class="fit-warn font-size-14">{{ tipsError }}</span>
              </dd>
            </dl>
            <dl class="fixed-btn">
              <dt>&nbsp;</dt>
              <dd class="fixed-btn">
                <el-button @click="emit('close')">{{ $t('取消') }}</el-button>
                <el-button :disabled="isDisabledForced" type="primary" @click="calForced">{{ $t('开始计算')}}</el-button>
              </dd>
            </dl>
          </div>
        </div>
        <div class="calculator-type-right">
          <div class="calculator-info">
            <h3>{{ $t('计算结果') }}</h3>
            <dl>
              <dt>{{ $t('强平价格') }}</dt>
              <dd>{{ format(forcedResult.forcePrice, (pairInfo[currentPair.pair] || {}).price_scale, true) }}</dd>
            </dl>
            <dl>
              <dt>{{ $t('仓位价值') }}</dt>
              <dd>{{ format(forcedResult.positionValue, 4, true) }}</dd>
            </dl>
          </div>
        </div>
      </div>
      <div v-if="calType === 'total'" class="calculator-type-cont flex-box align-start">
        <div class="calculator-type-left">
          <div class="calculator-type-nav flex-box">
            <ul class="flex-box">
              <li :class="{'active': totalType === 'long'}" @click="totalType = 'long'">{{ $t('多仓') }}</li>
              <li :class="{'active': totalType === 'short'}" @click="totalType = 'short'">{{ $t('空仓') }}</li>
            </ul>
          </div>
          <div class="calculator-type-form">
            <dl>
              <dt>{{ $t('杠杆倍数') }}</dt>
              <dd>
                <el-input v-model="totalParams.level" type="number" :placeholder="`1-${leverageMax}x`" :max="leverageMax">
                  <template #append>
                    <span class="fit-tc-secondary font-size-14">X</span>
                  </template>
                </el-input>
              </dd>
            </dl>
            <dl>
              <dt>{{ $t('开仓价格') }}</dt>
              <dd>
                <el-input v-model="totalParams.openPrice">
                  <template #append>
                    <span class="fit-tc-secondary font-size-14">USDT</span>
                  </template>
                </el-input>
              </dd>
            </dl>
            <dl>
              <dt>{{ $t('仓位') }}</dt>
              <dd>
                <el-input v-model="totalParams.position">
                  <template #append>
                    <span class="fit-tc-secondary font-size-14">
                      {{ useCommon.getFuturesSymbol(pair, isCBUUnitUSDT) }}
                    </span>
                  </template>
                </el-input>
              </dd>
            </dl>
            <dl>
              <dt>{{ $t('目标收益额') }}</dt>
              <dd>
                <el-input v-model="totalParams.total">
                  <template #append>
                    <span class="fit-tc-secondary font-size-14">USDT</span>
                  </template>
                </el-input>
              </dd>
            </dl>
            <dl class="fixed-btn">
              <dt>&nbsp;</dt>
              <dd class="fixed-btn">
                <el-button @click="emit('close')">{{ $t('取消') }}</el-button>
                <el-button :disabled="Object.values(totalParams).some(v => !v)" type="primary" @click="calTotal">{{ $t('开始计算')}}</el-button>
              </dd>
            </dl>
          </div>
        </div>
        <div class="calculator-type-right">
          <div class="calculator-info">
            <h3>{{ $t('计算结果') }}</h3>
            <dl>
              <dt>{{ $t('占用保证金') }}</dt>
              <dd>{{ format(totalResult.margin, 4, true) }}</dd>
            </dl>
            <!-- <dl>
              <dt>{{ $t('目标平仓价值') }}</dt>
              <dd>{{ format(totalResult.forcePrice, 4, true) }}</dd>
            </dl> -->
            <dl>
              <dt>{{ $t('收益率') }}</dt>
              <dd>{{ format(totalResult.rate * 100, 2, true) }}%</dd>
            </dl>
          </div>
        </div>
      </div>
      <p class="fit-tc-secondary font-size-14 info-text-cont">{{ $t('注: 实际交易中因存在手续费和资金费率, 可能导致计算结果与交易结果不完全一致。') }}</p>
    </div>
  </el-dialog>
  <SelectDialog
    v-if="isShowCoinList"
    :isShowDialog="isShowCoinList"
    :title="$t('合约')"
    :isShowInput="true"
    :isShowIcon="true"
    :defaultValue="currentPair.pair"
    :list="filterFutureList"
    label="pair"
    val="pair"
    @close="isShowCoinList = false"
    @changeItem="symbolChange"
    >
    <div>
      <el-input v-model="searchInput" clearable>
        <template #prepend>
          <MonoSearch size="16" />
        </template>
      </el-input>
    </div>
  </SelectDialog>
</template>
<script lang="ts" setup>
  import BigNumber from 'bignumber.js'
  import { ElDialog, ElInput, ElButton, ElDropdown, ElDropdownMenu, ElDropdownItem, ElSelect, ElOption } from 'element-plus'
  import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
  import MonoRigthChecked from '~/components/common/icon-svg/MonoRigthChecked.vue'
  import { futureStore } from '~/stores/futureStore'
  import { format } from '~/utils'
  import { useCommonData } from '~/composables/index'
  import { commonStore } from '~/stores/commonStore'
  import SelectDialog from '~/components/common/SelectDialog.vue'
  const useCommon = useCommonData()
  const publicStore = commonStore()
  const store = futureStore()
  const { futureInfo } = storeToRefs(store)
  const { subFutureInfoSocket } = store
  const { subAllTickerSocket, cancelAllTicker } = publicStore
  const { marketsObj } = storeToRefs(publicStore)
  const { locale, t } = useI18n()
  const props = defineProps({
    pair: {
      type: String,
      default: ''
    },
    futureList: {
      type: Object,
      default: []
    },
    isShow: {
      type: Boolean,
      default: false
    },
    pairInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    isCBUUnitUSDT: {
      type: Boolean,
      default: false
    },
    cbuCrossBalance: {
      default: '',
      type: [Number, String]
    }
  })
  const emit = defineEmits(['close'])
  const visible = ref(false)
  const currentPair = ref({})
  const calType = ref('profit')
  const profitType = ref('long')
  const forcedType = ref('long')
  const totalType = ref('long')
  const isShowCoinList = ref(false)
  const profitParams = ref({
    level: '',
    openPrice: '',
    forcePrice: '',
    position: ''
  })
  const profitResult = ref({
    margin: 0,
    positionValue: 0,
    profit: 0,
    rate: 0
  })
  const modeList = ref([
    {
      label: t('全仓'),
      value: 'cross'
    },
    {
      label: t('逐仓'),
      value: 'isolate'
    }
  ])
  const typeList = ref([
    {
      label: t('单向持仓'),
      value: 'one'
    },
    {
      label: t('双向持仓'),
      value: 'two'
    },
  ])
  const forcedParams = ref({
    mode: 'cross',
    type: 'one',
    level: '',
    openPrice: '',
    position: '',
    longOpenPrice: '',
    longPosition: '',
    shortOpenPrice: '',
    shortPosition: '',
    banlance: ''
  })
  const forcedResult = ref({
    forcePrice: 0,
    positionValue: 0
  })
  const totalParams = ref({
    level: '',
    openPrice: '',
    position: '',
    total: ''
  })
  const totalResult = ref({
    margin: 0,
    forcePrice: 0,
    rate: 0
  })
  const tipsError = ref('')
  const markets = ref({})
  const isDisabledForced = computed(() => {
    if (forcedParams.value.type === 'one') {
      const fieldsToCheck = ['mode', 'type', 'level', 'openPrice', 'position', 'banlance']
      return fieldsToCheck.some(field => !forcedParams.value[field])
    } else {
      const fieldsToCheck = ['mode', 'type', 'level', 'longOpenPrice', 'longPosition', 'shortOpenPrice', 'shortPosition', 'banlance']
      return fieldsToCheck.some(field => !forcedParams.value[field])
    }
  })
  const leverageMax = computed(() => {
    return new BigNumber(1).div((props.pairInfo[currentPair.value.pair] || {}).base_initial_margin)
  })
  const nowPrice = computed(() => {
    return markets.value && markets.value[currentPair.value.symbol] && markets.value[currentPair.value.symbol].last || 0
  })
  watch(() => forcedParams.value.mode, (val) => {
    forcedParams.value.type = 'one'
  })
  watch(() => calType.value, (val) => {
    profitType.value = 'long'
    forcedType.value = 'long'
    totalType.value = 'long'
    profitParams.value = {
      level: '',
      openPrice: '',
      forcePrice: '',
      position: ''
    }
    profitResult.value = {
      margin: 0,
      positionValue: 0,
      profit: 0,
      rate: 0
    }
    forcedParams.value = {
      mode: 'cross',
      type: 'one',
      level: '',
      openPrice: '',
      position: '',
      longOpenPrice: '',
      longPosition: '',
      shortOpenPrice: '',
      shortPosition: '',
      banlance: ''
    }
    forcedResult.value = {
      forcePrice: 0,
      positionValue: 0
    }
    totalParams.value = {
      level: '',
      openPrice: '',
      position: '',
      total: ''
    }
    totalResult.value = {
      margin: 0,
      forcePrice: 0,
      rate: 0
    }
    tipsError.value = ''
    forcedParams.value.banlance = format(props.cbuCrossBalance, 4, true, true)
  })
  watch(() => forcedParams.value.type, (val) => {
    forcedParams.value.openPrice = ''
    forcedParams.value.position = ''
    forcedParams.value.longOpenPrice = ''
    forcedParams.value.longPosition = ''
    forcedParams.value.shortOpenPrice = ''
    forcedParams.value.shortPosition = ''
    tipsError.value = ''
  })
  const getAmount = (quantity, price) => {
    return !props.isCBUUnitUSDT ? quantity : (quantity / price)
  }
  const calProfit = () => {
    const {
      position, // 仓位
      openPrice, // 开仓价
      level, // 杠杆
      forcePrice // 平仓价
    } = profitParams.value
    const margin = getAmount(position, openPrice) / level * openPrice
    const positionValue = getAmount(position, openPrice) * openPrice
    const profit = profitType.value === 'long' ? (forcePrice * getAmount(position, openPrice)) - (openPrice * getAmount(position, openPrice)) : (openPrice * getAmount(position, openPrice)) - (forcePrice * getAmount(position, openPrice))
    const rate = profit / margin * 100
    profitResult.value = {
      margin,
      positionValue,
      profit,
      rate
    }
  }
  const cbuMt = computed(() => {
    const {
      mode,
      type,
      position, // 仓位
      openPrice, // 开仓价
      level, // 杠杆
      longOpenPrice,
      longPosition,
      shortOpenPrice,
      shortPosition
    } = forcedParams.value
    let mt = 0 // 持仓维持保障金
    const maintMargin = (props.pairInfo[currentPair.value.pair] || {}).base_maintenance_margin || 0
    if (type === 'one') {
      let fundNumber = 0
      if (((futureInfo.value[currentPair.value.pair] || {}).fundingRate < 0 && forcedType.value === 'short') || ((futureInfo.value[currentPair.value.pair] || {}).fundingRate > 0 && forcedType.value === 'long')) {
        fundNumber = Number(openPrice) * getAmount(position, openPrice) * Math.abs((futureInfo.value[currentPair.value.pair] || {}).fundingRate)
      } else {
        fundNumber = 0
      }
      mt = getAmount(position, openPrice) * openPrice * maintMargin + fundNumber
    } else {
      let fundNumber = 0
      if (((futureInfo.value[currentPair.value.pair] || {}).fundingRate < 0)) {
        fundNumber = Number(shortOpenPrice) * getAmount(shortPosition, shortOpenPrice) * Math.abs((futureInfo.value[currentPair.value.pair] || {}).fundingRate)
      } else if ((futureInfo.value[currentPair.value.pair] || {}).fundingRate > 0) {
        fundNumber = Number(longOpenPrice) * getAmount(longPosition, longOpenPrice) * Math.abs((futureInfo.value[currentPair.value.pair] || {}).fundingRate)
      } else {
        fundNumber = 0
      }
      mt = (getAmount(longPosition, longOpenPrice) * longOpenPrice * maintMargin) + ( shortOpenPrice * getAmount(shortPosition, shortOpenPrice) * maintMargin) + fundNumber
    }
    return mt
  })
  const calForced = () => {
    const {
      mode,
      type,
      position, // 仓位
      openPrice, // 开仓价
      level, // 杠杆
      longOpenPrice,
      longPosition,
      shortOpenPrice,
      shortPosition,
      banlance
    } = forcedParams.value
    const posMargin = getAmount(position, openPrice) / level * openPrice
    console.log(getAmount(position, openPrice), posMargin, 'psppspspsp')
    if (banlance * 1 < posMargin * 1) {
      tipsError.value = t('可用保证金不满足开此仓位最少起始保证金')
      return false
    } else {
      tipsError.value = ''
    }
    const Hl = type === 'one' ? (forcedType.value === 'long' ? getAmount(position, openPrice) : 0)  : getAmount(longPosition, longOpenPrice) // 持有多仓
    const Hs = type === 'one' ? (forcedType.value === 'short' ? getAmount(position, openPrice) : 0)  : getAmount(shortPosition, shortOpenPrice) // 持有空仓
    const Pl = type === 'one' ? (forcedType.value === 'long' ? openPrice : 0)  : longOpenPrice // 多仓开仓均价
    const Ps = type === 'one' ? (forcedType.value === 'short' ? openPrice : 0)  : shortOpenPrice // 空仓开仓均价
    const positionValue = type === 'one' ? getAmount(position, openPrice) * openPrice : ((longOpenPrice * getAmount(longPosition, longOpenPrice)) + (getAmount(shortPosition, shortOpenPrice) * shortOpenPrice))
    const maintMargin = (props.pairInfo[currentPair.value.pair] || {}).base_maintenance_margin * positionValue || 0
    const cwf = new BigNumber(new BigNumber(banlance).minus(maintMargin)).div(getAmount(position, openPrice))
    console.log(Number(cwf), 'jdjdieidijeijijeije')
    const entryPriceCross = type === 'one' ? openPrice : (longPosition > shortPosition ? longOpenPrice : shortOpenPrice)
    const closePosition = forcedType.value === 'short' ? position * -1 : position
    const closableQty = type === 'one' ? getAmount(closePosition, openPrice) : (longPosition * 1 === shortPosition * 1 ? 0 : (Math.abs(getAmount(shortPosition, shortOpenPrice) - getAmount(longPosition, longOpenPrice))) )
    const curUprifit = Math.min(Hl, Hs) * (Ps - Pl)
    let forcePrice = 0
    if (mode === 'isolate' && forcedType.value === 'long') { // 逐仓多
      forcePrice = new BigNumber(openPrice).minus(cwf)
    } else if (mode === 'isolate' && forcedType.value === 'short') { // 逐仓空
      forcePrice = new BigNumber(openPrice).plus(cwf)
    } else if (mode === 'cross') { // 全仓
      console.log(entryPriceCross, banlance, curUprifit, cbuMt.value, longPosition, shortPosition,  closableQty, 'djdjiejidijeijeij')
      const result = new BigNumber(entryPriceCross).minus(new BigNumber(new BigNumber(banlance).plus(curUprifit).minus(cbuMt.value)).div(closableQty))
      forcePrice = closableQty * 1 === 0 ? 0 : (result <= 0 ? 0 : result)
    }
    forcedResult.value = {
      forcePrice,
      positionValue
    }
  }
  const calTotal = () => {
    const {
      level, // 杠杆
      openPrice, // 开仓价
      position, // 仓位
      total // 目标收益额
    } = totalParams.value
    const margin = getAmount(position, openPrice) * openPrice / level
    const rate = total / margin
    const forcePrice = totalType.value === 'short' ? (openPrice * (1 - rate / level)) : (openPrice * (1 + rate / level))
    totalResult.value = {
      margin,
      forcePrice,
      rate
    }
  }
  const changePair = (pair) => {
    subFutureInfoSocket(pair)
    currentPair.value = props.futureList.filter((item) => {
      return pair === item.pair
    })[0]
  }
  const searchInput = ref('')
  const filterFutureList = computed(() => {
    if (searchInput.value !== '') {
      return props.futureList.filter(v => {
        return v.pair.replace('_SWAP', '').replace('_', '').includes(searchInput.value.toUpperCase())
      })
    } else {
      return props.futureList
    }
  })
  const symbolChange = (item) => {
    subFutureInfoSocket(item.pair)
    currentPair.value = item
  }
  const requestAnimationFrameInterval = ref(null)
  const socketDateAnimation = () => {
    markets.value = marketsObj.value
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  }
  onBeforeMount(() => {
    socketDateAnimation()
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
    currentPair.value = props.futureList.filter((item) => {
      return props.pair === item.pair
    })[0]
    visible.value = props.isShow
  })
  onBeforeUnmount(() => {
    cancelAllTicker()
    requestAnimationFrameInterval.value && cancelAnimationFrame(requestAnimationFrameInterval.value)
  })
</script>
<style lang="scss">
  .el-dialog{
    &.calculator-dialog-wrap{
      .calculator-title{
        margin-top:-24px;
        h2{
          font-size:24px;
          margin-right:40px;
          @include color(tc-primary);
        }
      }
      .pair-cont{
        display:none;
      }
      .calculator-nav-list{
        padding:32px 0;
        ul{
          li{
            font-size:18px;
            line-height:34px;
            margin-right:20px;
            position:relative;
            cursor:pointer;
            @include color(tc-secondary);
            &.active{
              @include color(tc-primary);
              &:after{
                content: '';
                display:block;
                width:16px;
                height:2px;
                position:absolute;
                bottom:0px;
                left:50%;
                margin-left:-8px;
                @include bg-color(theme);
              }
            }
          }
        }
      }
      .calculator-content{
        .calculator-type-cont{
          padding-bottom:40px;
          .calculator-type-left{
            flex:1;
            .calculator-type-nav{
              padding:4px;
              height:36px;
              border-radius:6px;
              @include bg-color(bg-quaternary);
              ul{
                height:28px;
                width:100%;
                li{
                  height:28px;
                  line-height:28px;
                  flex:1;
                  border-radius:6px;
                  text-align:center;
                  cursor:pointer;
                  @include color(tc-primary);
                  &.active{
                    @include bg-color(theme);
                    @include color(tc-primary);
                  }
                }
              }
            }
            .calculator-type-form{
              padding-top:16px;
              dl{
                display:flex;
                margin-bottom:16px;
                dt{
                  min-width:80px;
                  margin-right:12px;
                  display:flex;
                  align-items:center;
                }
                dd{
                  flex:1;
                  &.fixed-btn{
                    .el-button{
                      &:first-child{
                        display:none;
                      }
                    }
                  }
                }
              }
            }
          }
          .calculator-type-right{
            width:280px;
            margin-left:60px;
            border-radius:12px;
            @include bg-color(bg-quaternary);
            .calculator-info{
              padding:23px 32px;
              h3{
                font-size:16px;
                @include color(tc-primary);
              }
              dl{
                padding-top:16px;
                dt{
                  font-size:14px;
                  @include color(tc-secondary);
                }
                dd{
                  font-size:14px;
                  @include color(tc-primary);
                }
              }
            }
          }
        }
      }
    }
  }
  @include mb{
    .el-dialog{
      &.calculator-dialog-wrap{
        border-radius:0;
        position:fixed;
        top:0;
        bottom:0;
        right:0;
        left:0;
        width:100% !important;
        margin-top:0 !important;
        margin-bottom:0 !important;
        .el-dialog__header{
          display:none;
        }
        .calculator-title{
          margin-top:16px;
          &.flex-box{
            justify-content:space-between;
          }
          h2{
            font-size:18px;
            margin-right:0;
          }
        }
        .pair-cont{
          display:block;
          padding-top:0;
          .font-size-32{
            font-size:20px;
          }
        }
        .el-dropdown{
          display:none;
        }
        .calculator-nav-list{
          padding:12px 0;
          ul{
            li{
              font-size:14px;
              &.active{
                @include color(tc-primary);
                &:after{
                  width:14px;
                  margin-left:-7px;
                }
              }
            }
          }
        }
        .calculator-content{
          .calculator-type-cont{
            padding-bottom:12px;
            &.flex-box{
              flex-direction: column;
            }
            .calculator-type-left{
              flex:1;
              width:100%;
              .mg-b16{
                margin-bottom:12px !important;
              }
              .calculator-type-nav{
                padding:4px;
                height:36px;
                border-radius:6px;
                @include bg-color(bg-quaternary);
                ul{
                  height:28px;
                  width:100%;
                  li{
                    height:28px;
                    line-height:28px;
                    flex:1;
                    border-radius:6px;
                    text-align:center;
                    cursor:pointer;
                    @include color(tc-primary);
                    &.active{
                      @include bg-color(theme);
                      @include color(tc-primary);
                    }
                  }
                }
              }
              .calculator-type-form{
                padding-top:16px;
                dl{
                  margin-bottom:12px;
                  &.fixed-btn{
                    margin-bottom:0;
                    dt{
                      display:none;
                    }
                  }
                  dt{
                    min-width:80px;
                    margin-right:12px;
                    margin-bottom:8px;
                    display:flex;
                    align-items:center;
                    font-size:12px;
                  }
                  dd{
                    flex:1;
                    &.fixed-btn{
                      position:fixed;
                      bottom:0;
                      left:0;
                      right:0;
                      z-index:9999;
                      padding:12px;
                      display:flex;
                      @include bg-color(bg-primary);
                      .el-button{
                        &:first-child{
                          display:block;
                        }
                      }
                    }
                  }
                }
              }
            }
            .calculator-type-right{
              width:100%;
              margin-left:0;
              margin-bottom:0px;
              border-radius:12px;
              @include bg-color(bg-quaternary);
              .calculator-info{
                padding:12px;
                h3{
                  font-size:16px;
                  @include color(tc-primary);
                }
                dl{
                  padding-top:8px;
                  display:flex;
                  justify-content:space-between;
                  dt{
                    font-size:12px;
                    margin-right:12px;
                    @include color(tc-secondary);
                  }
                  dd{
                    font-size:14px;
                    @include color(tc-primary);
                  }
                }
              }
            }
          }
        }
        .info-text-cont{
          font-size:12px !important;
        }
      }
    }
  }
</style>
