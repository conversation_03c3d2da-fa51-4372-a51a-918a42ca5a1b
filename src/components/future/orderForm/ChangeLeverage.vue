<template>
  <el-dialog v-model="visible" :title="$t('仓位模式和杠杆调整')" width="480" @close="emit('closeDialog')">
    <div class="change-container">
      <div class="change-title">
        {{ $t('当前仓位模式：') }}
        <span v-if="changeMode === 1">{{ $t('全仓') }}</span>
        <span v-if="changeMode === 2">{{ $t('逐仓') }}</span>
        <span v-if="changeMode === 3">{{ $t('全仓') }}/{{ $t('分仓') }}</span>
        <span v-if="changeMode === 4">{{ $t('逐仓') }}/{{ $t('分仓') }}</span>
      </div>
      <div class="change-tab-cont">
        <div class="main-tab">
          <ul class="flex-box">
            <li class="flex-1" :class="{'cur': modeType === 2 }" @click="modeType = 2; model = 1">
              <el-button :type="modeType === 2 ? 'primary' : ''">{{ $t('全仓') }}</el-button>
            </li>
            <li class="flex-1" :class="{'cur': modeType === 1 }" @click="modeType = 1">
              <el-button :type="modeType === 1 ? 'primary' : ''">{{ $t('逐仓') }}</el-button>
            </li>
          </ul>
        </div>
        <div v-if="false" class="sub-tab">
          <ul class="flex-box">
            <li class="flex-1" :class="{'cur': model === 1}">
              <el-button :type="model === 1 ? 'primary' : ''" @click="model = 1">{{ $t('并仓') }}</el-button>
            </li>
            <li class="flex-1" :class="{'cur': model === 2}">
              <el-button :type="model === 2 ? 'primary' : ''" @click="model = 2">{{ $t('分仓') }}</el-button>
            </li>
          </ul>
        </div>
      </div>
      <div v-if="false" class="font-size-12 pd-t8 cursor-pointer fit-theme" @click="fcInfoFun">{{ $t('什么是分仓') }}？</div>
      <div class="change-title">{{ $t('当前杠杆倍数：') }}</div>
      <div class="change-leverage">
        <div>
          <el-input v-model="newLeverage" class="number-input" size="medium" @focus="isFocus=true" @blur="isFocus=false">
            <template #prepend>
              <span class="num-input-bg fit-icon black-text-hover flex-box space-center" @click="minus">
                <MonoReduce size="14" />
              </span>
            </template>
            <template #append>
              <span class="num-input-bg fit-icon black-text-hover flex-box space-center" @click="plus">
                <MonoPlus size="14" />
              </span>
            </template>
          </el-input>
        </div>
        <div class="pd-b12">
          <BoxSlider v-model="multiple" :defaultValue="multiple" :max="leverageMax" />
        </div>
        <div class="mg-t16">
          <span class="ts-12 fit-tc-secondary mg-r8">{{ $t('最高可开') }}</span>
          <span class="tw-5 ts-12 fit-tc-primary">
            <template v-if="futuresType === 'CBC'">{{ holdMax }}</template>
            <template v-else>{{ holdMax }}</template>
            {{ pair.split('_')[0] }}
          </span>
        </div>
        <div v-if="parseInt(newLeverage) >= 80" class="fit-warn ts-12 mg-t8">{{ $t('当前杠杆倍数较高, 请注意风险') }}</div>
        <div class="change-btn flex-box space-end">
          <el-button @click="$emit('closeDialog')">{{ $t('取消') }}</el-button>
          <el-button type="primary" :loading="isLoading" @click="submit">{{ $t('确认') }}</el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElDialog, ElButton, ElSlider, ElInput, ElMessageBox } from 'element-plus'
  import { useCommonData } from '~/composables/index'
  import BoxSlider from '~/components/common/BoxSlider.vue'
  import MonoReduce from '~/components/common/icon-svg/MonoReduce.vue'
  import MonoPlus from '~/components/common/icon-svg/MonoPlus.vue'
  import { saveUserLeverageApi } from '~/api/user'
  const { t, locale } = useI18n()
  const useCommon = useCommonData()
  const props = defineProps({
    isShow: {
      type: Boolean,
      default: false
    },
    leverage: {
      default: '',
      type: [Number, String]
    },
    mode: {
      type: Number,
      default: 0
    },
    holdMax: {
      type: Number,
      default: 0
    },
    pair: {
      type: String,
      default: ''
    },
    futuresType: {
      type: String,
      default: ''
    },
    leverageMax: {
      type: [String, Number],
      default: 0
    }
  })
  const emit = defineEmits(['closeDialog', 'changeMode'])
  const visible = ref(false)
  const changeMode = ref(0)
  const model = ref(0)
  const modeType = ref(0)
  const newLeverage = ref('')
  const multiple = ref('')
  const isFocus = ref(false)
  const isLoading = ref(false)
  const oldMode = ref(-1)
  const submit = async() => {
    if (newLeverage.value === '') {
      useCommon.showMsg('error', t('杠杆倍数不能为空'))
      return false
    }
    isLoading.value = true
    const { data, error } = await saveUserLeverageApi({
      product: props.pair,
      position_method: changeMode.value * 1 === 1 ? 'cross' : (changeMode.value * 1 === 2 ? 'isolate' : ''),
      position_merge: 'longshort',
      leverage: Number(newLeverage.value.replace('x', ''))
    })
    if (data) {
      useCommon.showMsg('success', t('设置成功！'))
      emit('closeDialog')
      emit('changeMode', {
        leverage: Number(newLeverage.value.replace('x', '')),
        mode: changeMode.value
      })
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
    isLoading.value = false
  }
  const changeModel = (model) => {
    useCommon.showMsg('info', t('分仓模式即将开通，敬请期待'))
    return false
  }
  const fcInfoFun = () => {
    ElMessageBox({
      title: t('什么是分仓'),
      showCancelButton: true,
      confirmButtonText: t('分仓说明'),
      cancelButtonText: t('明白了'),
      message: h('div', { class: 'pd-lr8' }, [
        h('div', { class: 'flex-box font-size-14 space-between' }, [
          h('span', { class: 'fit-tc-secondary' }, t('同一个合约代码下相同方向、开多个仓位时,不合并仓位,每个仓位有独立的仓位ID.'))
        ])
      ])
    }).then(() => {
      window.open('')
    })
  }
  watch(() => visible.value, (val) => {
    if (val) {
      newLeverage.value = (Math.max(1, props.leverage)).toFixed(0) + 'x'
      if (props.mode === 1) {
        modeType.value = 2
        model.value = 1
      } else if (props.mode === 2) {
        modeType.value = 1
        model.value = 1
      } else if (props.mode === 3) {
        modeType.value = 2
        model.value = 2
      } else if (props.mode === 4) {
        modeType.value = 1
        model.value = 2
      }
    }
  })
  watch(() => newLeverage.value, (val, oldVal) => {
    let newLeverageV = isNaN((val.replace('x', '')) * 1) ? (oldVal.replace('x', '') * 1) : (val.replace('x', '')) * 1
      if (newLeverageV > props.leverageMax) {
        newLeverageV = props.leverageMax
      } else if (newLeverageV < 1) {
        newLeverageV = 1
      }
      multiple.value = newLeverageV
      newLeverage.value = newLeverageV + (isFocus.value ? '' : 'x')
  })
  watch(() => isFocus.value, (val) => {
    if (val) {
      newLeverage.value = newLeverage.value.replace('x', '')
    } else {
      newLeverage.value = newLeverage.value + 'x'
    }
  })
  watch(() =>modeType.value, (val) => {
    if (val === 1 && model.value === 1) {
      changeMode.value = 2
    } else if (val === 2 && model.value === 1) {
      changeMode.value = 1
    } else if (val === 1 && model.value === 2) {
      changeMode.value = 4
    } else if (val === 2 && model.value === 2) {
      changeMode.value = 3
    }
  })
  watch(() => model.value, (val) => {
    if (val === 1 && modeType.value === 1) {
      changeMode.value = 2
    } else if (val === 2 && modeType.value === 1) {
      changeMode.value = 4
    } else if (val === 1 && modeType.value === 2) {
      changeMode.value = 1
    } else if (val === 2 && modeType.value === 2) {
      changeMode.value = 3
    }
  })
  watch(() => multiple.value, (val) => {
    newLeverage.value = val + 'x'
  })
  const plus = () => {
    let newLeverageV = (newLeverage.value.replace('x', '')) * 1 + 1
    if (newLeverageV > props.leverageMax) {
      newLeverageV = props.leverageMax
    }
    newLeverage.value = newLeverageV + 'x'
  }
  const minus = () => {
    let newLeverageV = (newLeverage.value.replace('x', '')) * 1 - 1
    if (newLeverageV < 1) {
      newLeverageV = 1
    }
    newLeverage.value = newLeverageV + 'x'
  }
  onMounted(() => {
    visible.value = props.isShow
    oldMode.value = props.mode
  })
</script>
<style lang="scss">
  .change-container {
    margin-top: -20px;
    .change-title {
      height: 56px;
      line-height: 56px;
      font-weight: 600;
      @include color(tc-primary);
      font-size: 16px;
    }
    .change-tab-cont {
      width: 100%;
      height: auto;
      @include bg-color(bg-secondary);
      border: 1px solid;
      @include border-color(border);
      border-radius: 4px;
      .main-tab {
        ul {
          li {
            padding: 8px 12px;
            @include bg-color(bg-tertiary);
            .el-button {
              width: 100%;
              height: 38px;
              line-height: 38px;
              padding: 0 !important;
              border: 0;
              font-size: 16px;
              background: none;
              &:hover,
              &:active,
              &:visited,
              &:focus {
                background: none;
              }
            }
            &.cur {
              @include bg-color(bg-secondary);
              .el-button {
                background: #2271e6;
                border: 1px;
                &:hover,
                &:active,
                &:visited {
                  background: #4e8deb;
                }
                &:focus {
                  background: #2271e6;
                }
              }
            }
          }
        }
      }
      .sub-tab {
        ul {
          li {
            padding: 10px 12px;
            .el-button {
              &:hover,
              &:active,
              &:visited,
              &:focus {
              }
              width: 100%;
              height: 34px;
              line-height: 32px;
              padding: 0 !important;
              font-size: 14px;
              &.el-button--primary {
                span {
                  font-weight: 700;
                }
              }
            }
          }
        }
      }
    }
  }
  .change-leverage {
    .now,
    .after {
      border-radius: 8px;
      border: 1px solid rgba(131, 134, 143, 0.16);
      li {
        &.item-border {
          border-top: 1px solid rgba(131, 134, 143, 0.16);
        }
      }
    }
    .now {
      .title {
        @include bg-color(bg-quaternary);
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        border: 1px solid rgba(131, 134, 143, 0.16);
      }
    }
    .number-input {
      .el-input__prefix {
        left: 8px !important;
      }
      .el-input__suffix {
        right: 8px !important;
      }
      .el-input__inner {
        text-align: center;
        height: 40px;
        line-height: 40px;
      }
      .num-input-bg {
        width: 24px;
        height: 24px;
        @include bg-color(bg-secondary);
        font-size: 12px;
        border: 4px;
        line-height: 24px;
        cursor: pointer;
      }
    }
    .after {
      .title {
        @include bg-color(theme);
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        border: 1px solid rgba(131, 134, 143, 0.16);
      }
    }
  }
  .change-btn {
    margin: 16px 0 0;
    padding: 12px 0 0;
  }
</style>