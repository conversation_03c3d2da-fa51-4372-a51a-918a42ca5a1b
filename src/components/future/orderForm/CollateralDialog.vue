<template>
  <el-dialog v-model="visible" @close="emit('close')">
    <div class="collateral-title font-size-16 flex-box space-between">
      <span class="fit-tc-primary">{{ $t('已开启抵押资产') }}</span>
      <span class="fit-theme cursor-pointer" @click="router.push(`/${locale}/my/assets/trade`)">{{ $t('管理') }}</span>
    </div>
    <el-table :data="dataList" class="mg-t12">
      <el-table-column :label="$t('币种')" align="left">
        <template #default="{ row }">
          <div class="flex-box">
            <BoxCoinIcon :icon="row.icon_url" :size="24" class="mg-r8" />
            <span class="fit-tc-primary">{{ row.asset }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column  align="left">
        <template #header>
          <p>{{ $t('总量') }}</p>
          <p>{{ $t('估值') }}</p>
        </template>
        <template #default="{ row }">
          <p>{{ row.total }}</p>
          <p>{{ row.equsdt }}</p>
        </template>
      </el-table-column>
      <el-table-column align="right">
        <template #header>
          <p>{{ $t('可用') }}</p>
          <p>{{ $t('冻结') }}</p>
        </template>
        <template #default="{ row }">
          <p>{{ row.withdrawable }}</p>
          <p>{{ row.holds }}</p>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElDialog, ElTable } from 'element-plus'
  import { commonStore } from '~/stores/commonStore'
  const router = useRouter()
  const { locale, t } = useI18n()
  const store = commonStore()
  const { allAsset } = storeToRefs(store)
  const props = defineProps({
    isShowCollateral: {
      type: Boolean,
      default: false
    },
    dataAssetsList: {
      type: Object,
      default () {
        return []
      }
    }
  })
  const emit = defineEmits(['close'])
  const visible = ref(false)

  const dataList = computed(() => {
    return props.dataAssetsList.map(item => ({
      ...item,
      icon_url: allAsset.value.assetmap?.[item.asset]?.icon_url
    })).filter((item) => {
      return !(item.asset === 'KtxQ1' || item.asset === 'KtxQ2')
    })
  })
  onBeforeMount(() => {
    visible.value = props.isShowCollateral
  })
</script>
<style lang="scss" scoped>
  .collateral-title{
    margin-top:-28px;
    margin-right:48px;
  }
  @include mb {
    .collateral-title{
      margin-top:-10px;
      margin-right:32px;
    }
  }
</style>