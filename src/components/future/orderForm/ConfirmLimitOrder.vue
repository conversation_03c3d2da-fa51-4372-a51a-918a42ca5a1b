<template>
  <el-dialog v-model="visible" :title="`${$t('下单二次确认')}-${entrustType * 1 === 1 ? $t('普通委托') : (entrustType * 1 === 2) ? $t('计划委托') : $t('跟踪委托')}`" width="480" class="comfirm-limit-dialog" @close="handleClose(false)">
    <div class="confirm-title flex-box">
      <div class="flex-box">
        <BoxCoinIcon :icon="coinInfo.icon_url" class="icon-box mg-r8"/>
        <span class="font-size-18 fit-tc-primary mg-r4">{{ pair.replace('_SWAP', '').replace('_', '') }}</span>
        <span class="font-size-14 fit-tc-secondary">{{ $t('永续') }}</span>
      </div>
      <div class="flex-box">
        <div class="gg-box ts-16 fit-tc-primary">
          <b v-if="mode === 1" class="tw-5 ts-16">{{ $t('全仓') }}</b>
          <b v-if="mode === 2" class="tw-5 ts-16">{{ $t('逐仓') }}</b>
          <b v-if="mode === 3" class="tw-5 ts-16">{{ $t('全仓') }}/{{ $t('分仓') }}</b>
          <b v-if="mode === 4" class="tw-5 ts-16">{{ $t('逐仓') }}/{{ $t('分仓') }}</b>
          <span class="tw-5 ts-16">{{ leverage }}x</span>
        </div>
        <div class="type-box ts-16 tw-6" :class="{'fit-tc-rise': form.order_side === 1 || form.order_side === 4, 'fit-tc-fall': form.order_side === 2 || form.order_side === 3}">
          {{ form.order_side === 1 ? $t('开多') : '' }}
          {{ form.order_side === 2 ? $t('开空') : '' }}
          {{ form.order_side === 3 ? $t('平多') : '' }}
          {{ form.order_side === 4 ? $t('平空') : '' }}
        </div>
      </div>
    </div>
    <div class="confirm-content">
      <div v-if="entrustType * 1 === 2" class="order-box flex-box space-between">
        <div class="left">{{ $t('触发价格') }}</div>
        <div class="right">{{ form.trigger_price }} USDT</div>
      </div>
      <div v-if="entrustType * 1 === 3" class="order-box flex-box space-between">
        <div class="left">{{ $t('回调幅度') }}</div>
        <div class="right">{{ form.trigger_price }} %</div>
      </div>
      <div v-if="entrustType * 1 === 1 || entrustType * 1 === 2" class="order-box flex-box space-between">
        <div class="left">{{ $t('委托价格') }}</div>
        <div class="right">{{ form.price }} USDT</div>
      </div>
      <div class="order-box flex-box space-between">
        <div class="left">{{ $t('委托数量') }}</div>
        <div class="right">
          {{ amountShow }}
          <template v-if="futuresType === 'ipc'">{{ useCommon.getFuturesSymbol(pair, isCBCUnitUSD) }}</template>
          <template v-else>{{ entrustType * 1 === 3 ? pair.split('_')[0] : useCommon.getFuturesSymbol(pair, isCBUUnitUSDT) }}</template>
        </div>
      </div>
      <div v-if="entrustType * 1 === 1 || entrustType * 1 === 2" class="order-box flex-box space-between">
        <div class="left">{{ $t('价值') }}</div>
        <div v-if="futuresType === 'lpc'" class="right">{{ cbuTrueAmount }} USDT</div>
        <div v-if="futuresType === 'ipc'" class="right">{{ cbcTrueAmount }} USD</div>
      </div>
    </div>
    <div class="pd-t24 pd-b24">
      <el-checkbox v-model="isNotShowAgain" class="noTips">{{ $t('当前登录周期内不再提示') }}</el-checkbox>
    </div>
    <el-button type="primary" :loading="isLimitLoading" @click="handleClose(true)">{{ $t('确认') }}</el-button>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { cookies, format } from '~/utils'
  import { ElDialog, ElButton, ElCheckbox } from 'element-plus'
  import { useCommonData } from '~/composables/index'
  import { futureStore } from '~/stores/futureStore'
  const store = futureStore()
  const { futureInfo } = storeToRefs(store)
  const useCommon = useCommonData()
  const props = defineProps({
    pair: {
      type: String,
      default: ''
    },
    coinInfo: {
      type: Object,
      default () {
        return {}
      }
    },
    mode: {
      type: [Number, String],
      default: 0
    },
    entrustType: {
      type: [Number, String],
      default: 1
    },
    leverage: {
      type: [Number, String],
      default: -1
    },
    form: {
      type: Object,
      default () {
        return {}
      }
    },
    ticker: {
      type: Object,
      default () {
        return {}
      }
    },
    isLimitLoading: {
      type: Boolean,
      default: false
    },
    futuresType: {
      type: String,
      default: ''
    },
    isShow: {
      type: Boolean,
      default: false
    },
    cbuTrueAmount: {
      default: '',
      type: [Number, String]
    },
    cbcTrueAmount: {
      default: '',
      type: [Number, String]
    },
    isCBUUnitUSDT: {
      type: Boolean,
      default: false
    },
    leftAvailableAmount: {
      default: 0,
      type: [Number, String]
    },
    rightAvailableAmount: {
      default: 0,
      type: [Number, String]
    },
    priceScale: {
      type: [String, Number],
      default: 0
    },
    quantityScale: {
      type: [String, Number],
      default: 0
    }
  })
  const emit = defineEmits(['closeDialog', 'updateAssets'])
  const visible = ref(false)
  const isNotShowAgain = ref(false)
  const amountShow = computed(() => {
    console.log(props.priceScale , props.quantityScale, 'dheudheuhduehduheu')
    const availableAmount = props.form.order_side === 1 || props.form.order_side === 4 ? props.leftAvailableAmount : props.rightAvailableAmount
    const formatAvailableAmount = format(useCommon.cbuConver(availableAmount, props.isCBUUnitUSDT, (props.ticker.last || (futureInfo.value[props.pair] || {}).markPrice * 1)), (props.isCBUUnitUSDT ? props.priceScale : props.quantityScale), true, true)
    let amountValue = props.form.persent >= 1 ? formatAvailableAmount : props.form.amount
    return amountValue
  })
  const handleClose = (isConfirm) => {
    if (isConfirm === true) {
      cookies.set(`futures${props.futuresType}NotOrderConfirmAgain`, isNotShowAgain.value, Infinity)
      emit('updateAssets')
      const timer = setTimeout(() => {
        emit('updateAssets')
        clearTimeout(timer)
      }, 2000)
    }
    emit('closeDialog', isConfirm)
  }
  onMounted(() => {
    visible.value = props.isShow
  })
</script>
<style lang="scss" scoped>
  .confirm-title{
    .icon-box{
      width:32px;
      height:auto;
      font-size:32px;
    }
    .gg-box{
      padding:4px 8px;
      border-radius:4px;
      font-size:14px;
      margin-left:8px;
      @include color(tc-secondary);
      @include bg-color(bg-secondary);
    }
    .type-box{
      padding:4px 8px;
      border-radius:4px;
      font-size:14px;
      margin-left:8px;
      &.fit-tc-rise{
        background: rgba(59, 193, 137, 0.1);
        @include color(rise);
      }
      &.fit-tc-fall{
        background: rgba(255, 98, 98, 0.1);
         @include color(fall);
      }
    }
  }
  .confirm-content{
    padding: 24px 0 0;
    .order-box{
      padding-bottom:8px;
      .left, .right{
        font-size:14px;
      }
      .left{
        @include color(tc-secondary);
      }
      .right{
        @include color(tc-primary);
      }
    }
  }
  @include mb {
    .confirm-title{
      &.flex-box{
        flex-direction: column;
        align-items:start;
      }
      .gg-box{
        margin-left:0;
      }
      .gg-box, .type-box{
        margin-top:12px;
      }
    }
  }
</style>
<style lang="scss">
  @include mb {
    .el-dialog{
      &.comfirm-limit-dialog{
        .el-dialog__header{
          .el-dialog__headerbtn{
            top:0;
            right:-16px;
          }
        }
        .el-dialog__body{
        }
      }
    }
  }
</style>
