<template>
  <el-dialog v-model="visible" :title="$t('合约设置')" width="480px" class="futures-setting-dialog" @close="emit('closeDialog')">
    <div class="cont-wrap">
      <ul class="ul-reset pd-tb24 contract-setting">
        <li class="flex-box space-between">
          <span class="font-size-14 fit-tc-primary">{{ $t('计价单位') }}</span>
          <div class="flex-box choise-box">
            <template v-if="futuresType === 'ipc'">
              <span
                class="item flex-1 text-center pd-tb4 cursor-pointer"
                :class="{'active': valuationCBCUnit === 'coinSymbol'}"
                @click="valuationCBCUnit = 'coinSymbol'"
              >{{ pair.replace('_SWAP', '').split('_')[0] }}</span>
              <span class="item flex-1 text-center pd-tb4 cursor-pointer" :class="{'active': valuationCBCUnit === 'USD'}" @click="valuationCBCUnit = 'USD'">USD</span>
            </template>
            <template v-else>
              <span
                class="item flex-1 text-center pd-tb4 cursor-pointer"
                :class="{'active': valuationCBUUnit === 'coinSymbol'}"
                @click="valuationCBUUnit = 'coinSymbol'"
              >{{ pair.replace('_SWAP', '').split('_')[0] }}</span>
              <span class="item flex-1 text-center pd-tb4 cursor-pointer" :class="{'active': valuationCBUUnit === 'USDT'}" @click="valuationCBUUnit = 'USDT'">USDT</span>
            </template>
          </div>
        </li>
        <li class="pd-t24 border-t flex-box space-between">
          <span class="ts-14 fit-tc-primary">{{ $t('下单二次确认') }}</span>
          <el-switch v-model="tradeConfirm" />
        </li>
      </ul>
    </div>
    <div class="flex-box space-between btn-cont">
      <el-button @click="emit('closeDialog')">{{ $t('取消') }}</el-button>
      <el-button type="primary" @click="save">{{ $t('确认') }}</el-button>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { cookies } from '~/utils/cookies'
  import { ElDialog, ElSwitch } from 'element-plus'
  import { futureStore } from '~/stores/futureStore'
  const store = futureStore()
  const { setCbcTokenUnit, setCbuTokenUnit } = store
  const { cbcTokenUnit, cbuTokenUnit } = storeToRefs(store)
  const props = defineProps({
    pair: {
      type: String,
      default: ''
    },
    isShow: {
      type: Boolean,
      default: false
    },
    futuresType: {
      type: String,
      default: ''
    }
  })
  const emit = defineEmits(['closeDialog'])
  const visible = ref(false)
  const valuationCBCUnit = ref('')
  const valuationCBUUnit = ref('')
  const tradeConfirm = ref(false)
  const save = () => {
    cookies.set(`futures${props.futuresType}NotOrderConfirmAgain`, !tradeConfirm.value, Infinity)
    cookies.set(`futures${props.futuresType}ValuationUnit`, props.futuresType === 'ipc' ? valuationCBCUnit.value : valuationCBUUnit.value, Infinity)
    if (props.futuresType === 'ipc') {
      setCbcTokenUnit(valuationCBCUnit.value)
    } else {
      setCbuTokenUnit(valuationCBUUnit.value)
    }
    emit('closeDialog')
  }
  onMounted(() => {
    visible.value = props.isShow
    tradeConfirm.value = cookies.get(`futures${props.futuresType}NotOrderConfirmAgain`) !== 'true'
    valuationCBCUnit.value = cookies.get(`futuresipcValuationUnit`) || 'USD'
    valuationCBUUnit.value = cookies.get(`futureslpcValuationUnit`) || 'coinSymbol'
  })
</script>
<style lang="scss">
.futures-setting-dialog {
  .cont-wrap {
    width: 100%;
    overflow: auto;
    .choise-box {
      border-radius: 4px;
      width: 200px;
      text-align:center;
      .item {
        @include bg-color(bg-secondary);
        @include color(tc-secondary);
        &:first-child {
          border-top-left-radius: 4px;
          border-bottom-left-radius: 4px;
        }
        &:last-child {
          border-top-right-radius: 4px;
          border-bottom-right-radius: 4px;
        }
        &.active {
          @include bg-color(theme);
          @include color(tc-button);
        }
        &:not(.active):hover{
          [data-theme^="light"] & {
            color: #1A57B3;
          }
          [data-theme^="dark"] & {
            color: #3C81EA;
          }
        }
      }
    }
  }
  .btn-cont {
    border-top: 1px solid (131, 134, 143, 0.16);
    padding: 16px 0 0;
  }
}
</style>