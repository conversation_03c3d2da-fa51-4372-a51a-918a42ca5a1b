<template>
  <div class="info-container-new">
    <div class="info-wrapper">
      <ul class="info-tab flex-box space-between">
        <li :class="{ active: type === 1 }" @click="type=1">{{ $t('开仓') }}</li>
        <li id="step7" :class="{ active: type === 2 }" @click="type=2">{{ $t('平仓') }}</li>
      </ul>
      <div v-if="isLogin && leverage[pair]" class="leverage-wrapper flex-box">
        <div class="leverage-info flex-box space-between space-center flex-1" @click="isShowChangeLeverage = true">
          <div>
            <span v-if="mode[pair] === 1">{{ $t('全仓') }}</span>
            <span v-if="mode[pair] === 2">{{ $t('逐仓') }}</span>
            <span v-if="mode[pair] === 3">{{ $t('全仓') }}/{{ $t('分仓') }}</span>
            <span v-if="mode[pair] === 4">{{ $t('逐仓') }}/{{ $t('分仓') }}</span>
            <span>{{ (Math.max(1, leverage[pair])).toFixed(0) }}X</span>
          </div>
          <MonoDownArrowMin size="12" class="mg-l4"/>
        </div>
        <div v-if="futureList.length > 0" class="pd-l8 cursor-pointer mg-t8" @click="isShowCalculatorDialog = true">
          <MonoMathIcon size="20" class="fit-tc-primary" />
        </div>
      </div>
      <ul class="info-subTab flex-box space-between">
        <div class="flex-box">
          <li class="mg-r8" :class="{'active': entrustType === 1 && form.price_type * 1 === 2}" @click="entrustType = 1; form.price_type = 2">{{ $t('限价委托') }}</li>
          <li class="mg-r8" :class="{'active': entrustType === 1 && form.price_type * 1 === 1}" @click="entrustType = 1; form.price_type = 1">{{ $t('市价委托') }}</li>
          <li class="mg-r8" :class="{'active': entrustType === 2}" @click="entrustType = 2">{{ $t('计划委托') }}</li>
          <!-- <li class="mg-r8" :class="{'active': entrustType === 3}" @click="entrustType = 3">{{ $t('跟踪委托') }}</li> -->
        </div>
      </ul>
      <div class="info-cont">
        <div v-if="entrustType === 1" class="xj-info-wrap">
          <div class="input-item-cont">
            <div class="input-label flex-box space-between pd-b4">
              <div class="fit-tc-secondary font-size-12">{{ $t('价格') }}</div>
            </div>
            <div class="flex-box mg-b8">
              <el-input v-model="form.price" :disabled="form.price_type === 1" class="flex-1" :min="0" />
              <div v-if="form.price_type === 2" class="check-btn-box" @click="form.price_type = form.price_type === 1 ? 2 : 1">{{ $t('市价') }}</div>
              <div v-else class="check-btn-box active" @click="form.price_type = form.price_type === 1 ? 2 : 1">{{ $t('市价') }}</div>
            </div>
          </div>
          <div class="input-item-cont">
            <div class="input-label flex-box space-between pd-b4">
              <div class="fit-tc-secondary font-size-12">{{ $t('数量') }}</div>
              <div v-if="type * 1 === 1" class="flex-box">
                <span class="font-size-12 fit-tc-secondary">{{ miniAmount > min10U ? amountPlaceholder : min10UPlaceholder }}</span>
              </div>
            </div>
            <el-input v-model.lazy="form.amount" :min="0" @focus="focusFun()">
              <template #append><span class="cursor-pointer" @click="isShowSetting = true">{{ useCommon.getFuturesSymbol(pair, futuresType === 'ipc' ? isCBCUnitUSD : isCBUUnitUSDT) }}<MonoDownArrowMin size="12" class="mg-l4"/></span></template>
            </el-input>
          </div>
          <div class="slider-demo-block">
            <BoxSlider v-model="form.persent" :max="max" typeStr="%" :marksValue="marksArr" />
          </div>
          <p class="mg-b0 mg-t8 font-size-12 fit-tc-primary flex-box space-between">
            <span>
              {{ $t('买入') }}
              <span class="fit-tc-primary">
                <template v-if="futuresType === 'ipc'">{{ format(useCommon.cbcConver(leftAvailableAmount, isCBCUnitUSD, (ticker.last || (futureInfo[pair] || {}).markPrice * 1)), isCBCUnitUSD ? priceScale : quantityScale, true) }}</template>
                <template v-else>{{ format(useCommon.cbuConver(leftAvailableAmount, isCBUUnitUSDT, (ticker.last || (futureInfo[pair] || {}).markPrice * 1)), isCBUUnitUSDT ? priceScale : quantityScale, true) }}</template>
                {{ useCommon.getFuturesSymbol(pair, futuresType === 'ipc' ? isCBCUnitUSD : isCBUUnitUSDT) }}
              </span>
            </span>
            <span>
              {{ $t('卖出') }}
              <span class="fit-tc-primary">
                <template v-if="futuresType === 'ipc'">{{ format(useCommon.cbcConver(rightAvailableAmount, isCBCUnitUSD, (ticker.last || (futureInfo[pair] || {}).markPrice * 1)), isCBCUnitUSD ? priceScale : quantityScale, true) }}</template>
                <template v-else>{{ format(useCommon.cbuConver(rightAvailableAmount, isCBUUnitUSDT, (ticker.last || (futureInfo[pair] || {}).markPrice * 1)), isCBUUnitUSDT ? priceScale : quantityScale, true) }}</template>
                {{ useCommon.getFuturesSymbol(pair, futuresType === 'CBC' ? isCBCUnitUSD : isCBUUnitUSDT) }}
              </span>
            </span>
          </p>
        </div>
        <div v-if="entrustType === 2" class="xj-info-wrap">
          <div class="input-item-cont">
            <div class="input-label flex-box space-between pd-b4">
              <div class="fit-tc-secondary font-size-12">{{ $t('触发价') }}</div>
            </div>
            <el-input v-model="form.trigger_price" type="number" class="mg-b8" />
          </div>
          <div class="input-item-cont">
            <div class="input-label flex-box space-between pd-b4">
              <div class="fit-tc-secondary font-size-12">{{ $t('委托价') }}</div>
            </div>
            <div class="flex-box mg-b8">
              <el-input v-model="form.price" :disabled="form.price_type === 1" class="flex-1" :min="0" />
              <div v-if="form.price_type === 2" class="check-btn-box" @click="form.price_type = form.price_type === 1 ? 2 : 1">{{ $t('市价') }}</div>
              <div v-else class="check-btn-box active" @click="form.price_type = form.price_type === 1 ? 2 : 1">{{ $t('市价') }}</div>
            </div>
          </div>
          <div class="input-item-cont">
            <div class="input-label flex-box space-between pd-b4">
              <div class="fit-tc-secondary font-size-12">{{ $t('数量') }}</div>
              <div v-if="type * 1 === 1" class="flex-box">
                <span class="font-size-12 fit-tc-secondary">{{ miniAmount > min10U ? amountPlaceholder : min10UPlaceholder }}</span>
              </div>
            </div>
            <el-input v-model.lazy="form.amount" type="number" :min="0" @focus="focusFun()">
              <template #append><span class="cursor-pointer" @click="isShowSetting = true">{{ useCommon.getFuturesSymbol(pair, futuresType === 'ipc' ? isCBCUnitUSD : isCBUUnitUSDT) }}<MonoDownArrowMin size="12" class="mg-l4"/></span></template>
            </el-input>
          </div>
        </div>
        <div v-if="entrustType === 3" class="xj-info-wrap">
          <!-- <div class="input-item-cont">
            <div class="input-label flex-box space-between pd-b4">
              <div class="fit-tc-secondary font-size-12">{{ $t('激活价') }}</div>
            </div>
            <el-input v-model="form.price" type="number" class="mg-b8" />
          </div> -->
          <div class="input-item-cont">
            <div class="input-label flex-box space-between pd-b4">
              <div class="fit-tc-secondary font-size-12">{{ $t('回调幅度') }}</div>
              <div class="flex-box">
                <span class="font-size-12 fit-theme mg-r12 cursor-pointer" @click="form.trigger_price = 2">2%</span>
                <span class="font-size-12 fit-theme cursor-pointer" @click="form.trigger_price = 5">5%</span>
              </div>
            </div>
            <el-input v-model="form.trigger_price" type="number" class="mg-b8">
              <template #suffix>
                <span class="fit-tc-secondary">%</span>
              </template>
            </el-input>
          </div>
          <div class="input-item-cont">
            <div class="input-label flex-box space-between pd-b4">
              <div class="fit-tc-secondary font-size-12">{{ $t('数量') }}</div>
              <div v-if="type * 1 === 1" class="flex-box">
                <span class="font-size-12 fit-tc-secondary">{{ miniAmount > min10U ? amountPlaceholder : min10UPlaceholder }}</span>
              </div>
            </div>
            <el-input v-model="form.amount" type="number">
              <template #append><span class="cursor-pointer">{{ pair.split('_')[0] }}</span></template>
            </el-input>
          </div>
        </div>
      </div>
      <div class="info-buy-cont">
        <div v-if="isLogin" class="buy-wrap">
          <div class="flex-box">
            <el-button
              :loading="isLoadingOC"
              type="primary"
              class="mg-r8 flex-1 rise-btn"
              @click="open(1)"
            >{{ type === 2 ? $t('平空') : $t('开多') }}</el-button>
            <el-button
              type="primary"
              class="mg-l8 flex-1 fall-btn"
              :loading="isLoadingOL"
              @click="open(2)"
            >{{ type === 2 ? $t('平多') : $t('开空') }}</el-button>
          </div>
          <div v-if="entrustType === 1" class="font-size-12 flex-box space-between fit-tc-primary mg-t8">
            <span>
              {{ type === 2 ? $t('可平') : $t('可买') }}
              <span class="fit-tc-primary">
                <template v-if="futuresType === 'ipc'">
                  {{ useCommon.cbcConver(leftAmount, isCBCUnitUSD, ((futureInfo[pair] || {}).markPrice * 1)) }}
                </template>
                <template v-else>
                  {{ format(useCommon.cbuConver(leftAmount, isCBUUnitUSDT, ((futureInfo[pair] || {}).markPrice * 1)), (isCBUUnitUSDT ? priceScale : quantityScale), true, true) }}
                </template>
                {{ useCommon.getFuturesSymbol(pair, futuresType === 'ipc' ? isCBCUnitUSD : isCBUUnitUSDT) }}
              </span>
            </span>
            <span>
              {{ type === 2 ? $t('可平') : $t('可卖') }}
              <span class="fit-tc-primary">
                <template v-if="futuresType === 'ipc'">
                  {{ useCommon.cbcConver(rightAmount, isCBCUnitUSD, ((futureInfo[pair] || {}).markPrice * 1)) }}
                </template>
                <template v-else>
                  {{ format(useCommon.cbuConver(rightAmount, isCBUUnitUSDT, ((futureInfo[pair] || {}).markPrice * 1)), (isCBUUnitUSDT ? priceScale : quantityScale), true, true) }}
                </template>
                {{ useCommon.getFuturesSymbol(pair, futuresType === 'ipc' ? isCBCUnitUSD : isCBUUnitUSDT) }}
              </span>
            </span>
          </div>
        </div>
        <div v-else class="no-login-btn flex-1">
          <NoLoginCont />
        </div>
      </div>
    </div>
  </div>
  <CalculatorDialog
    v-if="isShowCalculatorDialog"
    :isShow="isShowCalculatorDialog"
    :pair="pair"
    :futureList="futureList"
    :pairInfo="pairInfo"
    :isCBUUnitUSDT="isCBUUnitUSDT"
    :cbuCrossBalance = "cbuCrossBalance"
    @close="isShowCalculatorDialog = false"
  />
  <ConfirmLimitOrder
    v-if="isShowLimitConfirm"
    :pair="pair"
    :form="form"
    :mode="mode[pair]"
    :entrustType="entrustType"
    :leverage="leverage[pair]"
    :cbuTrueAmount="cbuTrueAmount"
    :cbcTrueAmount="cbcTrueAmount"
    :coinInfo="coinInfo"
    :ticker="ticker"
    :isLimitLoading="isLimitLoading"
    :futuresType="futuresType"
    :isShow="isShowLimitConfirm"
    :isCBUUnitUSDT="isCBUUnitUSDT"
    :leftAvailableAmount="leftAvailableAmount"
    :rightAvailableAmount="rightAvailableAmount"
    :priceScale="priceScale"
    :quantityScale="quantityScale"
    @closeDialog="confirmLimitOrderClose"
    @updateAssets="emit('updateAssets')"
  />
  <ChangeLeverage
    v-if="isShowChangeLeverage"
    :mode="mode[pair]"
    :leverage="leverage[pair]"
    :leverageMax="leverageMax"
    :holdMax="holdMax"
    :is-show="isShowChangeLeverage"
    :pair="pair"
    @changeMode="changeMode"
    @closeDialog="isShowChangeLeverage = false" />
    <FuturesSettingDialog v-if="isShowSetting" :isShow="isShowSetting" :pair="pair" :futuresType="futuresType" @closeDialog="isShowSetting = false" />
</template>
<script lang="ts" setup>
  import { ElButton, ElTooltip, ElInput } from 'element-plus'
  import BigNumber from 'bignumber.js'
  import { format } from '~/utils'
  import { openOrder } from '~/api/order'
  import { getAllPairs } from '~/api/public'
  import BoxSlider from '~/components/common/BoxSlider.vue'
  import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
  import MonoMathIcon from '~/components/common/icon-svg/MonoMathIcon.vue'
  import ChangeLeverage from './ChangeLeverage.vue'
  import NoLoginCont from '~/components/common/NoLoginCont.vue'
  import { futureStore } from '~/stores/futureStore'
  import FuturesSettingDialog from '~/components/future/orderForm/FuturesSettingDialog.vue'
  import ConfirmLimitOrder from '~/components/future/orderForm/ConfirmLimitOrder.vue'
  import CalculatorDialog from '~/components/future/orderForm/CalculatorDialog.vue'
  import { useCommonData } from '~/composables/index'
  const useCommon = useCommonData()
  const { locale, t } = useI18n()
  const store = futureStore()
  const { setModeInfoState } = store
  const { mode, leverage, userRate, cbuTokenUnit, futureInfo } = storeToRefs(store)
  const isShowLimitConfirm = ref(false)
  const isLimitLoading = ref(false)
  const props = defineProps({
    coinInfo: {
      type: Object,
      default () {
        return {}
      }
    },
    pair: {
      type: String,
      default: ''
    },
    price: {
      type: String,
      default: ''
    },
    amount: {
      type: [Number, String],
      default: ''
    },
    ticker: {
      type: Object,
      default () {
        return {}
      }
    },
    pairInfo: {
      type: Object,
      default () {
        return {}
      }
    },
    isLogin: {
      type: Boolean,
      default: false
    },
    futuresType: {
      type: String,
      default: ''
    },
    isCBCUnitUSD: {
      type: Boolean,
      default: false
    },
    isCBUUnitUSDT: {
      type: Boolean,
      default: false
    },
    quantityScale: {
      type: [String, Number],
      default: 0
    },
    priceScale: {
      type: [String, Number],
      default: 0
    },
    cbuCrossBalance: {
      default: '',
      type: [Number, String]
    },
    cbcCrossBalance: {
      default: '',
      type: [Number, String]
    },
    canCloseBuyNum: {
      default: 0,
      type: Number
    },
    canCloseSellNum: {
      default: 0,
      type: Number
    }
  })
  const isShowCalculatorDialog = ref(false)
  const emit = defineEmits(['updateAssets'])
  const isShowSetting = ref(false)
  const type = ref(1)
  const entrustType = ref(1)
  const form = ref({
    trigger_price: '',
    price_type: 2, // 市价市价1 限价2
    order_side: 1, // 1 开多，2 开空 3 平多 4 平空
    price: '',
    amount: '',
    persent: 0
  })
  const marks = reactive<Marks>({
    0: '0%',
    25: '',
    50: '',
    75: '',
    100: '100%'
  })
  const marksArr = ref([1, 25, 50, 75, 100])
  const min = ref(0);
  const max = ref(100)
  const isShowChangeLeverage = ref(false)
  const isLoadingOC = ref(false)
  const isLoadingOL = ref(false)
  const isLimitConfirm = ref(false)
  const style = computed(() => {
    const length = max.value - min.value,
    progress = sliderValue.value - min.value,
    left = (progress / length) * 100;
    return {
      paddingLeft: `${left}%`
    }
  })
  const holdMax = computed(() => {
    return (props.pairInfo[props.pair] || {}).max_order_size || 0
  })
  const leverageMax = computed(() => {
    return new BigNumber(1).div((props.pairInfo[props.pair] || {}).base_initial_margin)
  })
  const changeMode = (item) => {
    setModeInfoState(props.pair, item.mode, item.leverage)
  }
  const changeValue = (num) => {
    form.value.persent = num
  }
  watch(() => [entrustType.value, form.value.price_type], ([val1, val2]) => {
    if (val1 * 1 === 1 && form.price_type * 1 === 2) {
      form.value.price = props.ticker.last
    }
  }) 
  watch(() => form.value.price_type, (val) => {
    if (val * 1 === 1) {
      form.value.price = t('市价')
    } else if (val * 1 === 2) {
      form.value.price = props.ticker.last
    }
  })
  watch(() => form.value.price, (val, oldVal) => {
    if (form.value.amount !== '' && form.value.persent >= 1) {
      const decimal = props.futuresType === 'ipc' && props.isCBCUnitUSD ? props.quantityScale : props.priceScale
      const amount = props.isCBUUnitUSDT ? leftAvailableAmount.value : useCommon.cbuConver(leftAvailableAmount.value, props.isCBUUnitUSDT, props.ticker.last)
      form.value.amount = form.value.persent + '%'
    }
    if (form.value.price_type) {
      return
    }
    if (isNaN(val)) {
      form.value.price = oldVal
      return
    }
    // if (val && val.split('.')[1] && val.split('.')[1].length > props.priceScale) {
    //   form.value.price = format(val, props.priceScale)
    // }
  })
  watch(() => props.price, (val) => {
    if (val && Number(val) > 0 && entrustType.value === 1 && form.value.price_type * 1 === 2) {
      form.value.price = val
    }
  })
  watch(() => props.pair, (val) => {
    form.value = {
      trigger_price: '',
      price_type: 2, // 市价市价1 限价2
      order_side: 1, // 1 开多，2 开空 3 平多 4 平空
      price: '',
      amount: '',
      persent: 0
    }
  })
  watch(() => props.ticker.last, (val) => {
    if (!form.value.price  && entrustType.value === 1 && form.value.price_type === 2) {
      form.value.price = val
    }
  })
  watch(() =>props.amount, (val) => {
    if (val && Number(val) > 0) {
      form.value.amount = val
    }
  })
  watch(() => form.value.amount, (val, oldVal) => {
    if (form.value.persent) {
      return
    }
    if (isNaN(val) || val === '00') {
      form.value.amount = oldVal
      return
    }
    let decimal
    if (props.futuresType === 'ipc') {
      decimal = props.isCBCUnitUSD ? props.quantityScale : props.priceScale
    } else {
      decimal = (!props.isCBUUnitUSDT && entrustType.value * 1 !== 3) || entrustType.value * 1 === 3 ? props.quantityScale : props.priceScale
    }
    if (decimal === 0 && val.endsWith('.')) {
      form.value.amount = oldVal
    }
    if (val && val.split('.')[1] && val.split('.')[1].length > decimal) {
      form.value.amount = toFixed(val, decimal)
    }
  })
  let isFocusing = false
  const focusFun = () => {
    isFocusing = true
    if (form.value.amount && form.value.amount.includes('%')) {
      form.value.amount = ''
      form.value.persent = 0
    }
    setTimeout(() => {
      isFocusing = false; // 稍后恢复监听
    }, 100)
  }
  watch(() => form.value.persent, (val) => {
    if (!isFocusing && val >= 1) {
      const decimal = props.futuresType === 'ipc' && props.isCBCUnitUSD ? props.quantityScale : props.priceScale
      if (props.futuresType === 'ipc') {
        const amount = props.isCBUUnitUSDT ? leftAvailableAmount.value : useCommon.cbcConver(leftAvailableAmount.value, props.isCBCUnitUSD, props.ticker.last)
        form.value.amount = val + '%'
      } else {
        const amount = props.isCBUUnitUSDT ? leftAvailableAmount.value : useCommon.cbuConver(leftAvailableAmount.value, props.isCBUUnitUSDT, props.ticker.last)
        form.value.amount = val + '%'
      }
    } else {
      form.value.amount = ''
    }
  })
  const val = computed(() => {
    return format(((props.pairInfo[props.pair] || {}).min_order_size || 0), props.quantityScale, true)
  })
  const miniAmount = computed(() => {
    const cbuUSDTMinValue = props.ticker.last ? new BigNumber(val.value).multipliedBy((props.ticker.last)).toFixed(props.isCBUUnitUSDT ? props.priceScale : props.quantityScale, 1) : 0
    return (!props.isCBUUnitUSDT && entrustType.value * 1 !== 3) || entrustType.value * 1 === 3 ? val.value : cbuUSDTMinValue
  })
  const amountPlaceholder = computed(() => {
    const symbol = ((props.futuresType === 'lpc' && cbuTokenUnit.value !== 'USDT' && entrustType.value * 1 !== 3) || entrustType.value * 1 === 3) ? coinSymbol.value : currencySymbol.value
    return miniAmount.value ? t('最小') + ' ' + `${miniAmount.value} ${symbol}` : ''
  })
  const roundUp = (num, precision) => {
    const factor = Math.pow(10, precision);
    return num < 0 ? Math.floor(num * factor) / factor : Math.ceil(num * factor) / factor;
  }
  const min10U = computed(() => {
    const coinNum = new BigNumber(10).div(truePrice.value ? truePrice.value : (props.ticker || {}).last)
    const num = (!props.isCBUUnitUSDT && entrustType.value * 1 !== 3) || entrustType.value * 1 === 3 ? coinNum : 10
    return roundUp(num, props.isCBUUnitUSDT ? props.priceScale : props.quantityScale)
  })
  const min10UPlaceholder = computed(() => {
    const symbol = ((props.futuresType === 'lpc' && cbuTokenUnit.value !== 'USDT' && entrustType.value * 1 !== 3) || entrustType.value * 1 === 3) ? coinSymbol.value : currencySymbol.value
    return min10U.value ? `${t('最小')} ${min10U.value} ${symbol}` : ''
  })
  const truePrice = computed(() => {
    if (entrustType.value === 1 || entrustType.value === 2) {
      if (form.value.price_type === 1) { // order_type为市价市价时 价格返回最新成交价
        return props.ticker.last || 0
      }
      return form.value.price
    }
    return form.value.price
  })
  const leftAmount = computed(() => {
    // x可买(多)) = b可用保证金 / ((p当前价格 / l杠杆倍数) + (p当前价格 * f手续费率 * 2) + (p当前价格 * m维持保证金率) - k标记价格 + p当前价格)
    // b = min(可用保证金,（用户资产*比例（假设0.96）- 当前仓位的维持保证金))
    if ((leverage.value[props.pair] === -1 && (props.ticker.last || (futureInfo.value[props.pair] || {}).markPrice))) {
      return 0
    }
    const b = props.cbuCrossBalance * 1 || 0
    const p = form.value.price_type * 1 === 1 ? (props.ticker.last * 1.005 * 1 || (futureInfo.value[props.pair] || {}).markPrice * 1.005 * 1 || 0) : (truePrice.value * 1 || props.ticker.last * 1 || (futureInfo.value[props.pair] || {}).markPrice * 1 || 0)
    const l = leverage.value[props.pair] * 1 || 0
    const fm = userRate.value[props.pair] && userRate.value[props.pair].maker_fee * 1 || 0
    const ft = userRate.value[props.pair] && userRate.value[props.pair].taker_fee * 1 || 0
    const m = 0
    const k = (futureInfo.value[props.pair] || {}).markPrice * 1 || 0
    const canOrderBuyNum = k <= p ? b / (p / l + p * ft + p * m - k + p) : b / (p * (1 / l + fm + m))
    const result = type.value === 2 ? props.canCloseBuyNum : canOrderBuyNum
    return result < 0 ? 0 : new BigNumber(result).toFixed(props.quantityScale, 1) || 0
  })
  const rightAmount = computed(() => {
    // x可卖(空))) = b可用保证金 / ((p当前价格 / l杠杆倍数) + (p当前价格 * f手续费率) + (p当前价格 * m维持保证金率) + k标记价格 - p当前价格)
    if ((leverage.value[props.pair] === -1 && (props.ticker.last || (futureInfo.value[props.pair] || {}).markPrice))) {
      return 0
    }
    const b = props.cbuCrossBalance * 1 || 0
    const p = form.value.price_type * 1 === 1 ? (props.ticker.last * 0.995 || (futureInfo.value[props.pair] || {}).markPrice * 0.995 || 0) : (truePrice.value || props.ticker.last || (futureInfo.value[props.pair] || {}).markPrice * 1 || 0)
    const l = leverage.value[props.pair] * 1 || 0
    const fm = userRate.value[props.pair] && userRate.value[props.pair].maker_fee * 1 || 0
    const ft = userRate.value[props.pair] && userRate.value[props.pair].taker_fee * 1 || 0
    const m = 0
    const k = (futureInfo.value[props.pair] || {}).markPrice * 1 || 0
    const canOrderSellNum = k >= p ? b / (p / l + p * ft + p * m + k - p) : b / (p * (1 / l + fm + m))
    const result = type.value === 2 ? props.canCloseSellNum : canOrderSellNum
    return result < 0 ? 0 : new BigNumber(result).toFixed(props.quantityScale, 1) || 0
  })
  const leftAvailableAmount = computed(() => {
    const decimal = props.quantityScale
    let result = new BigNumber(leftAmount.value).multipliedBy(form.value.persent || 0).dividedBy(100).toFixed(decimal, 1) || 0
    // if (result * 1 % val.value !== 0 && val.value > 1) {
    //   result = result * 1 - (result * 1 % val.value)
    // }
    return new BigNumber(result).toFixed(props.quantityScale, 1)
  })
  const rightAvailableAmount = computed(() => {
    const decimal = props.quantityScale
    let result = new BigNumber(rightAmount.value).multipliedBy(form.value.persent || 0).dividedBy(100).toFixed(decimal, 1) || 0
    // if (result * 1 % val.value !== 0 && val.value > 1) {
    //   result = result * 1 - (result * 1 % val.value)
    // }
    return new BigNumber(result).toFixed(props.quantityScale, 1)
  })
  const cbuTrueAmount = computed(() => { // 当U本位时计算下单USDT金额
    const p = truePrice.value || (props.ticker || {}).last || 0
    if (entrustType.value === 1 || entrustType.value === 2) {
      let amount = form.value.amount
      if (form.value.persent) {
        amount = form.value.order_side === 1 || form.value.order_side === 4 ? leftAvailableAmount.value : rightAvailableAmount.value
      }
      return props.isCBUUnitUSDT && !form.value.persent ? amount : new BigNumber(amount * 1).multipliedBy(p).toString()
    }
  })
  const cbcTrueAmount = computed(() => { // 当币本位时计算下单USD金额
    if (entrustType.value === 1 || entrustType.value === 2) {
      let amount = form.value.amount
      if (form.value.persent) {
        amount = form.value.order_side === 1 || form.value.order_side === 4 ? leftAvailableAmount.value : rightAvailableAmount.value
      }
      return (new BigNumber(amount * 1).multipliedBy(props.isCBCUnitUSD || form.value.persent ? 1 : props.ticker.last).toNumber()).toFixed(0)
    }
  })
  watch(() => entrustType.value, (val) => {
    console.log(val, form.value.price_type, 'djdjeijdiejidjeije')
    if (form.value.price_type * 1 === 1 && val === 1) {
      form.value = {
        trigger_price: '',
        price_type: 1, // 市价市价1 限价2
        order_side: 1, // 1 开多，2 开空 3 平多 4 平空
        price: t('市价'),
        amount: '',
        persent: 0
      }
    } else {
      form.value = {
        trigger_price: '',
        price_type: 2, // 市价市价1 限价2
        order_side: 1, // 1 开多，2 开空 3 平多 4 平空
        price: '',
        amount: '',
        persent: 0
      }
    }
  })
  const currencySymbol = computed(() => {
    return props.pair.split('_')[1]
  })
  const coinSymbol = computed(() => {
    return props.pair.split('_')[0]
  })
  const toFixed = (num, decimal) => {
    num = num.toString()
    if (!num) {
      return ''
    }
    if (decimal === 0 && num.endsWith('.')) {
      return num.replace('.', '')
    }
    if (num.endsWith('.')) {
      return num
    }
    const test = new RegExp(`^\\d*(\\.?\\d{0,${decimal}})`, 'g')
    const bigNumberResult = new BigNumber(num).toFixed(decimal, BigNumber.ROUND_DOWN)
    const result = ((num + '').match(test) || [])[0]
    return Number(bigNumberResult) !== Number(result) ? bigNumberResult : result
  }
  const validata = () => {
    if (!form.value.trigger_price && entrustType.value * 1 === 2) {
      useCommon.showMsg('error', t('请输入触发价格'))
      return false
    }
    if (!form.value.price && entrustType.value * 1 !== 3) {
      useCommon.showMsg('error', t('请输入价格'))
      return false
    }
    const availableAmount = form.value.order_side === 1 || form.value.order_side === 4 ? leftAvailableAmount.value : rightAvailableAmount.value
    let amount = form.value.persent >= 1 ? (cbuTokenUnit.value !== 'USDT' ? availableAmount : (new BigNumber(availableAmount).multipliedBy(truePrice.value ? truePrice.value : (props.ticker || {}).last))) : form.value.amount
    let maxAmount = form.value.order_side === 1 || form.value.order_side === 4 ? leftAmount.value : rightAmount.value
    if (props.futuresType === 'lpc' && props.isCBUUnitUSDT) {
      maxAmount = maxAmount * (truePrice.value || (props.ticker || {}).last || 0)
    }
    if (!amount) {
      useCommon.showMsg('error', t('请输入仓位'))
      return false
    }
    console.log(availableAmount, truePrice.value, Number(amount), maxAmount, 'ddhdhduududududuh')
    if (entrustType.value * 1 === 1 && amount * 1 > maxAmount * 1) {
      useCommon.showMsg('error', t('可用余额不足'))
      return false
    }
    const coinNum = roundUp(Number(new BigNumber(10).div(truePrice.value ? truePrice.value : (props.ticker || {}).last)), props.quantityScale)
    const min10UVal = ((props.futuresType === 'lpc' && cbuTokenUnit.value !== 'USDT' && entrustType.value * 1 !== 3) || entrustType.value * 1 === 3) ? coinNum : min10U.value
    const minAmountVal = Number(miniAmount.value) > Number(min10UVal) ? miniAmount.value : Number(min10UVal)
    if (amount * 1 < minAmountVal && type.value * 1 === 1) {
      const symbol = ((props.futuresType === 'lpc' && cbuTokenUnit.value !== 'USDT' && entrustType.value * 1 !== 3) || entrustType.value * 1 === 3) ? coinSymbol.value : currencySymbol.value
      useCommon.showMsg('error', t('最小') + ' ' + minAmountVal + ' ' + symbol)
      return false
    }
    isLimitConfirm.value = isLimitConfirm.value === false ? cookies.get(`futures${props.futuresType}NotOrderConfirmAgain`) === true : true
    if (isLimitConfirm.value === false) {
      isShowLimitConfirm.value = true
      return false
    }
    return true
  }
  const open = async(orderSide) => {
    if (type.value === 2 && (mode.value[props.pair] === 3 || mode.value[props.pair] === 4)) {
      useCommon.showMsg('info', t('分仓模式下请前往持仓列表平仓'))
      return false
    }
    if (orderSide) {
      form.value.order_side = type.value === 1 ? orderSide : (orderSide === 1 ? 4 : 3)
    }
    if (!validata()) {
      return false
    }
    if (orderSide === 1) {
      isLoadingOC.value = true
    } else if (orderSide === 2) {
      isLoadingOL.value = true
    } else {
      isLimitLoading.value = true
    }
    const availableAmount = form.value.order_side === 1 || form.value.order_side === 4 ? leftAvailableAmount.value : rightAvailableAmount.value
    let amount = form.value.persent ? availableAmount : form.value.amount
    if (props.futuresType === 'lpc' && props.isCBUUnitUSDT && !form.value.persent && entrustType.value * 1 !== 3) {
      amount = new BigNumber(amount * 1).dividedBy(form.value.price_type * 1 === 1 ? (props.ticker || {}).last * ((form.value.order_side === 1 || form.value.order_side === 4) ? 1.005 : 0.995) * 1 : (props.ticker || {}).last * 1).toFixed(props.quantityScale)
    }
    console.log(amount, Number(new BigNumber(amount * 1).dividedBy((props.ticker || {}).last * 1)), 'dhdhuehduehduheu')
    const planType = form.value.price === t('市价') ? 'trigger' : 'trigger-limit'
    const params = {
      trigger_price: entrustType.value * 1 === 2 ? form.value.trigger_price : Number(new BigNumber(form.value.trigger_price).multipliedBy(100)),
      symbol: props.pair,
      quantity: (form.value.order_side === 1 || form.value.order_side === 4) ? amount : (-Number(amount)).toString(),
      price: Number(form.value.price.replace(/,/g, '')),
      type: entrustType.value * 1 === 2 ? planType : (entrustType.value * 1 === 3 ? 'trailing-stop' : (form.value.price_type === 2 ? 'limit' : 'market')),
      market: props.futuresType,
      positionMerge: (mode.value[props.pair] === 1 || mode.value[props.pair] === 2) ? (form.value.order_side === 1 || form.value.order_side === 3) ? 'long' : 'short' : 'none',
      marginMethod: (mode.value[props.pair] === 1 || mode.value[props.pair] === 3) ? 'cross' : 'isolate',
      leverage: leverage.value[props.pair],
      close: form.value.order_side === 3 || form.value.order_side === 4
    }
    const { data, error } = await openOrder(params)
    if (data) {
      emit('updateAssets')
      useCommon.showMsg('success', t('下单成功'))
      if (orderSide === 1) {
        isLoadingOC.value = false
      } else if (orderSide === 2) {
        isLoadingOL.value = false
      } else {
        isLimitLoading.value = false
      }
      isShowLimitConfirm.value = false
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
      if (orderSide === 1) {
        isLoadingOC.value = false
      } else if (orderSide === 2) {
        isLoadingOL.value = false
      } else {
        isLimitLoading.value = false
      }
    }
    isLimitConfirm.value = false
  }
  const confirmLimitOrderClose = (isConfirm = false) => {
    if (isConfirm) {
      isLimitConfirm.value = isConfirm
      open()
    } else {
      isShowLimitConfirm.value = false
    }
  }
  const planOpen = () => {
  }
  const followOpen = () => {
    
  }
  const futureList = ref([])
  const getAllPairList = async() => {
    const { data } = await getAllPairs({
      uniq: 1
    })
    if (data) {
      futureList.value = data.contract
    }
  }
  onMounted(() => {
    getAllPairList()
    form.value.persent = 0
  })
</script>
<style lang="scss">
.slider-demo-block{
}
</style>
<style lang="scss" scoped>
  .leverage-wrapper{
    padding:12px 0px 0;
    .leverage-info{
      padding:4px 12px;
      border-radius:4px;
      font-size:14px;
      cursor:pointer;
      @include color(tc-primary);
      @include bg-color(bg-quaternary);
      svg{
        @include color(tc-secondary);
      }
    }
  }
  .info-container-new{
    padding:0 16px;
    .info-wrapper{
      height:auto;
      .info-tab{
        padding-top:10px;
        li{
          flex:1;
          height:40px;
          line-height:40px;
          font-size:14px;
          cursor:pointer;
          border-radius:4px;
          text-align:center;
          @include bg-color(tc-tertiary);
          @include color(tc-button);
          &:first-child {
            margin-right:12px;
          }
          &.active{
            &:first-child{
              @include bg-color(rise);
            }
            &:last-child{
              @include bg-color(fall);
            }
          }
        }
      }
      .info-subTab{
        li{
          font-size:14px;
          line-height:32px;
          height:32px;
          margin-bottom:8px;
          cursor:pointer;
          white-space:nowrap;
          @include color(tc-secondary);
          &.active{
            position:relative;
            @include color(tc-primary);
            &:after{
              content:'';
              width:14px;
              height:2px;
              position:absolute;
              bottom:0;
              left:50%;
              margin-left:-7px;
              @include bg-color(theme);
            }
          }
        }
      }
      .info-cont{
        .check-btn-box{
          height:44px;
          line-height:44px;
          font-size:14px;
          padding:0 12px;
          border:1px solid;
          cursor:pointer;
          border-radius:4px;
          margin-left:8px;
          @include border-color(border);
          @include color(tc-primary);
          &.active{
            @include color(theme);
            @include border-color(theme);
          }
        }
      }
      .info-buy-cont{
        .buy-wrap{
          padding-top:12px;
          .el-button{
            height:40px;
            color:#fff !important;
            &.rise-btn{
              @include border-color(rise);
              @include bg-color(rise);
            }
            &.fall-btn{
              @include border-color(fall);
              @include bg-color(fall);
            }
          }
        }
      }
    }
  }
</style>