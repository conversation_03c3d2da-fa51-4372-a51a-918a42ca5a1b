<template>
  <el-dialog v-model="isShow" :title="$t('批量撤单二次确认')" width="480" @close="emit('close')">
    <el-radio-group v-model="side" class="batch-radio">
      <el-radio :label="0">
        <h3>{{ $t('撤销全部委托单') }}</h3>
        <p>{{ times }}{{ $t('*撤单条数xx条', {times: times}) }}</p>
      </el-radio>
      <el-radio v-if="openLongCount > 0" label="openLong">
        <h3>{{ $t('撤销全部开多委托单') }}</h3>
        <p>{{ $t('*撤单条数xx条', {times: openLongCount}) }}</p>
      </el-radio>
      <el-radio v-if="openShortCount > 0" label="openShort">
        <h3>{{ $t('撤销全部开空委托单') }}</h3>
        <p>{{ $t('*撤单条数xx条', {times: openShortCount}) }}</p>
      </el-radio>
      <el-radio v-if="closeLongCount > 0" label="closeLong">
        <h3>{{ $t('撤销全部平多委托单') }}</h3>
        <p>{{ $t('*撤单条数xx条', {times: closeLongCount}) }}</p>
      </el-radio>
      <el-radio v-if="closeShortCount > 0" label="closeShort">
        <h3>{{ $t('撤销全部平空委托单') }}</h3>
        <p>{{ $t('*撤单条数xx条', {times: closeShortCount}) }}</p>
      </el-radio>
    </el-radio-group>
    <div class="button-box flex-box">
      <el-button class="flex-1" @click="emit('close')">{{ $t('取消') }}</el-button>
      <el-button type="primary" class="flex-1" :loading="isLoading" @click="confirmCancel">{{ $t('确认') }}</el-button>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElDialog, ElRadioGroup, ElRadio } from 'element-plus'
  import { useCommonData } from '~/composables/index'
  import { deleteOrders } from '~/api/order'
  const { locale, t } = useI18n()
  const useCommon = useCommonData()
  const props = defineProps({
    times: {
      type: [String || Number],
      default: 0
    },
    openLongCount: {
      type: [String || Number],
      default: 0
    },
    openShortCount: {
      type: [String || Number],
      default: 0
    },
    closeLongCount: {
      type: [String || Number],
      default: 0
    },
    closeShortCount: {
      type: [String || Number],
      default: 0
    },
    pair: {
      type: String,
      default: ''
    },
    isShowOnlyPair: {
      type: Boolean,
      default: false
    },
    dialogVisible: {
      type: Boolean,
      default: false
    }
  })
  const emit = defineEmits(['close', 'successBatch'])
  const side = ref(0)
  const isShow = ref(false)
  const isLoading = ref(false)
  const confirmCancel = async() => {
    isLoading.value = true
    const { data, error } = await deleteOrders({
      symbol: props.isShowOnlyPair ? props.pair : undefined,
      market: 'lpc',
      side: side.value ? side.value : undefined
    })
    if (data) {
      isLoading.value = false
      useCommon.showMsg('success',
        side.value === 'buy' ? t('撤销全部开多订单') : (side.value === 'sell' ? t('撤销全部开空订单') : t('撤销全部订单'))
      )
      emit('close')
      emit('successBatch')
    } else {
      isLoading.value = false
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  onMounted(() => {
    isShow.value = props.dialogVisible
  })
</script>
<style lang="scss" scoped>
.batch-radio{
  .el-radio{
    width:100%;
    padding-bottom:60px;
    h3{
      font-size:16px;
      @include color(tc-primary);
    }
    p{
      font-size:14px;
      @include color(tc-secondary);
    }
  }
}
</style>