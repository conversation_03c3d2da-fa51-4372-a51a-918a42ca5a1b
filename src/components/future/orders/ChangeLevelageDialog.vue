<template>
  <el-dialog v-model="visible" :title="$t('仓位杠杆调整')" width="480" @close="emit('close')">
    <div class="change-container">
      <div class="change-title">{{ $t('当前杠杆倍数：') }}</div>
      <div class="change-leverage">
        <div>
          <el-input v-model="newLeverage" class="number-input" size="medium" @focus="isFocus=true" @blur="isFocus=false">
            <template #prepend>
              <span class="num-input-bg fit-icon black-text-hover flex-box space-center" @click="minus">
                <MonoReduce size="14" />
              </span>
            </template>
            <template #append>
              <span class="num-input-bg fit-icon black-text-hover flex-box space-center" @click="plus">
                <MonoPlus size="14" />
              </span>
            </template>
          </el-input>
        </div>
        <div class="pd-b12">
          <BoxSlider v-model="multiple" :defaultValue="multiple" :max="leverageMax" />
        </div>
        <div class="mg-t16">
          <span class="ts-12 fit-tc-secondary mg-r8">{{ $t('杠杆倍数调整后最大可开') }}</span>
          <span class="tw-5 ts-12 fit-tc-primary">
            {{ format(maxUseAmount, quantityScale, true) }} {{ data.symbol.split('_')[0] }}
          </span>
        </div>
        <div class="mg-t8">
          <span class="ts-12 fit-tc-secondary mg-r8">{{ $t('所需保证金') }}</span>
          <span class="tw-5 ts-12 fit-tc-primary">
            {{ format(needBalance, 4, true) }} USDT
          </span>
        </div>
        <div class="mg-t8">
          <span class="ts-12 fit-tc-secondary mg-r8">{{ $t('预估强平价格') }}</span>
          <span class="tw-5 ts-12 fit-tc-primary">
            {{ format(curLPrice, 4, true) }} USDT
          </span>
        </div>
        <div v-if="needBalance * 1 > balanceMargin * 1" class="mg-t8">
          <span class="ts-12 fit-warn">{{ $t('杠杆倍数过低，账户中保证金不足，请提高杠杆倍数') }}</span>
        </div>
        <div v-if="newLeverage.replace('x', '') * 1 > 80" class="mg-t8">
          <span class="ts-12 fit-warn">{{ $t('当前杠杆倍数较高，请注意风险') }}</span>
        </div>
        <div class="change-btn flex-box space-end">
          <el-button @click="$emit('close')">{{ $t('取消') }}</el-button>
          <el-button type="primary" :disabled="isDisabled" :loading="isLoading" @click="submit">{{ $t('确认') }}</el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { format } from '~/utils'
  import BigNumber from 'bignumber.js'
  import { ElDialog, ElButton, ElSlider, ElInput, ElMessageBox } from 'element-plus'
  import { useCommonData } from '~/composables/index'
  import BoxSlider from '~/components/common/BoxSlider.vue'
  import MonoReduce from '~/components/common/icon-svg/MonoReduce.vue'
  import MonoPlus from '~/components/common/icon-svg/MonoPlus.vue'
  import { saveUserLeverageApi } from '~/api/user'
  import { changePositionLevalageApi } from '~/api/order'
  import { commonStore } from '~/stores/commonStore'
  import { futureStore } from '~/stores/futureStore'
  const storeF = futureStore()
  const { setModeInfoState } = storeF
  const { userRate, futureInfo } = storeToRefs(storeF)
  const store = commonStore()
  const { pairInfo, COLLATERALSymbol, posMapObj } = storeToRefs(store)
  const { t, locale } = useI18n()
  const useCommon = useCommonData()
  const props = defineProps({
    isShow: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default () {
        return {}
      }
    },
    cbuPositions: {
      type: Object,
      default () {
        return []
      }
    },
    ticker: {
      type: Object,
      default () {
        return {}
      }
    },
    marketPrice: {
      type: [String, Number],
      default: ''
    },
    tradeAssetObj: {
      type:Object,
      default(){
        return{}
      }
    },
    tradeArr: {
      type: Array,
      default () {
        return []
      }
    },
    quantityScale: {
      type: [String, Number],
      default: 0
    },
    priceScale: {
      type: [String, Number],
      default: 0
    }
  })
  const leverageMax = computed(() => {
    return new BigNumber(1).div((pairInfo.value[props.data.symbol] || {}).base_initial_margin)
  })
  const holdMax = computed(() => {
    return (pairInfo.value[props.data.symbol] || {}).max_order_size || 0
  })
  const coinSymbol = computed(() => {
    return props.data.symbol.split('_')[0]
  })
  const currencySymbol = computed(() => {
    return props.data.symbol.split('_')[1]
  })
  const emit = defineEmits(['close', 'changeMode'])
  const visible = ref(false)
  const newLeverage = ref('')
  const multiple = ref('')
  const isFocus = ref(false)
  const isLoading = ref(false)
  const symbolLast = ref({})
  const crossUnprofit = computed(() => { // 全仓未实现盈亏
    const posTs = props.cbuPositions
    let Unprofit = 0
    posTs && posTs.forEach(v => {
      if (v.marginMethod === 'cross') {
        if (Number(v.quantity) < 0) {
          Unprofit += ((-v.quantity * v.entryPrice) - (-v.quantity * (futureInfo.value[v.symbol] || {}).markPrice)) || 0
        } else {
          Unprofit += ((v.quantity * (futureInfo.value[v.symbol] || {}).markPrice) - (v.quantity * v.entryPrice)) || 0
        }
      }
    })
    return Unprofit
  })
  const maxUseAmount = computed(() => {
    console.log(rightAmount.value, leftAmount.value, 'dhdhfhhfhfhfhfhfhfh')
    return props.data.quantity * 1 < 0 ? rightAmount.value : leftAmount.value
  })
  const cost = computed(() => { // cost: 持仓价值 = 开仓均价* 持仓数量
    return props.data.entryPrice * Math.abs(props.data.quantity)
  })
  const markcost = computed(() => { // 标记价格计算持仓价值=标记价格 * 持仓数量
    return props.marketPrice * Math.abs(props.data.quantity)
  })
  const holdBalance = computed(() => { // 冻结持仓保证金holdBalance = 1 / leverage * cost
    return 1 / (newLeverage.value.replace('x', '') * 1) * cost.value
  })
  const premiumBalance = computed(() => { // 冻结溢价补偿 = cost - markcost
    return cost.value - markcost.value
  })
  const needBalance = computed(() => {
    if (props.data.leverage * 1 === newLeverage.value.replace('x', '') * 1) {
      return props.data.posMargin * 1
    } else {
      // needBalance = 冻结持仓保证金 + 冻结溢价补偿
      if ((props.data.quantity < 0 && props.data.entryPrice * 1 < props.marketPrice) || (props.data.quantity > 0 && props.data.entryPrice * 1 > props.marketPrice)) {
        return new BigNumber(holdBalance.value).plus(Math.abs(premiumBalance.value))
      } else {
        return holdBalance.value * 1
      }
    }
  })
  const cbuCrossBalance = computed(() => {
    if (props.data.marginMethod === 'cross') { // 全仓
      const balance = props.tradeAssetObj && props.tradeAssetObj[currencySymbol.value] && props.tradeAssetObj[currencySymbol.value].balance || 0
      let num = 0
      if (props.tradeArr.length > 0) {
        props.tradeArr.forEach((item) => {
          if (item.collateral && item.asset !== 'USDT') {
            num += item.balance * item.discountForMargin * (symbolLast.value && (symbolLast.value[item.asset] || {}).last || 1)
          }
        })
      }
      console.log('withdrawable: ' + balance, 'collateral' + num, 'crossUnprofit: ' + crossUnprofit.value, 'crossMargin: ' + (posMapObj.value[currencySymbol.value] || {}).crossMargin, 'dhduehduheudhuehduheuhdue')
      return Math.max(0, (balance * 1 + num * 1 + props.data.posMargin * 1 + crossUnprofit.value - (posMapObj.value[currencySymbol.value] || {}).crossMargin * 1 ) - needBalance.value)
    } else {
      const balance = props.tradeAssetObj && props.tradeAssetObj[currencySymbol.value] && props.tradeAssetObj[currencySymbol.value].withdrawable || 0
      return Math.max(0, balance * 1 + props.data.posMargin * 1 - needBalance.value)
    }
  })
  const balanceMargin = computed(() => {
    if (props.data.marginMethod === 'cross') { // 全仓
      const balance = props.tradeAssetObj && props.tradeAssetObj[currencySymbol.value] && props.tradeAssetObj[currencySymbol.value].balance || 0
      let num = 0
      if (props.tradeArr.length > 0) {
        props.tradeArr.forEach((item) => {
          if (item.collateral && item.asset !== 'USDT') {
            num += item.balance * item.discountForMargin * (symbolLast.value && (symbolLast.value[item.asset] || {}).last || 1)
          }
        })
      }
      console.log('withdrawable: ' + balance, 'collateral' + num, 'crossUnprofit: ' + crossUnprofit.value, 'crossMargin: ' + (posMapObj.value[currencySymbol.value] || {}).crossMargin, 'dhduehduheudhuehduheuhdue')
      return Math.max(0, (balance * 1 + num * 1 + props.data.posMargin * 1 + crossUnprofit.value - (posMapObj.value[currencySymbol.value] || {}).crossMargin * 1))
    } else {
      const balance = props.tradeAssetObj && props.tradeAssetObj[currencySymbol.value] && props.tradeAssetObj[currencySymbol.value].withdrawable || 0
      return Math.max(0, balance * 1 + props.data.posMargin * 1)
    }
  })
  const leftAmount = computed(() => {
    const b = cbuCrossBalance.value * 1 || 0
    const p = ((props.ticker || {}).last * 1.005 * 1 || props.marketPrice * 1.005 * 1 || 0)
    const l = newLeverage.value.replace('x', '') * 1 || 0
    const fm = userRate.value[props.data.symbol] && userRate.value[props.data.symbol].maker_fee * 1 || 0
    const ft = userRate.value[props.data.symbol] && userRate.value[props.data.symbol].taker_fee * 1 || 0
    const m = 0
    const k = props.marketPrice * 1 || 0
    const canOrderBuyNum = k <= p ? b / (p / l + p * ft + p * m - k + p) : b / (p * (1 / l + fm + m))
    return canOrderBuyNum < 0 ? 0 : new BigNumber(canOrderBuyNum).toFixed(props.quantityScale, 1) || 0
  })
  const rightAmount = computed(() => {
    const b = cbuCrossBalance.value * 1 || 0
    const p = (props.ticker || {}).last * 0.995 * 1 || props.marketPrice * 0.995 * 1 || 0
    const l = newLeverage.value.replace('x', '') * 1 || 0
    const fm = userRate.value[props.data.symbol] && userRate.value[props.data.symbol].maker_fee * 1 || 0
    const ft = userRate.value[props.data.symbol] && userRate.value[props.data.symbol].taker_fee * 1 || 0
    const m = 0
    const k = props.marketPrice * 1 || 0
    const canOrderSellNum = k >= p ? b / (p / l + p * ft + p * m + k - p) : b / (p * (1 / l + fm + m))
    return canOrderSellNum < 0 ? 0 : new BigNumber(canOrderSellNum).toFixed(props.quantityScale, 1) || 0
  })
  const isDisabled = computed(() => {
    return (needBalance.value * 1 > balanceMargin.value) || newLeverage.value === ''
  })
  const canUseBanlance = computed(() => { // 可用保证金
    const banlance = props.tradeAssetObj && props.tradeAssetObj[currencySymbol.value] && props.tradeAssetObj[currencySymbol.value].balance || 0
    let num = 0
    if (props.tradeArr.length > 0) {
      props.tradeArr.forEach((item) => {
        if (item.collateral && item.asset !== 'USDT') {
          num += item.balance * item.discountForMargin * (symbolLast.value && (symbolLast.value[item.asset] || {}).last || 1)
        }
      })
    }
    console.log(banlance, num, Math.max(0, (banlance * 1 + num * 1)), 'deidjeijdiejdiejiejie')
    return Math.max(0, (banlance * 1 + num * 1))
  })
  const cbuMt = computed(() => {
    let mt = 0 // 持仓维持保障金
    const posTs = props.cbuPositions
    posTs && posTs.forEach(v => {
      if (v.marginMethod === 'cross') { // 全仓
        let fundNumber = 0
        // mt = 持仓数量*开仓均价*维持保证金率+相同方向的资金费用累加
        console.log((futureInfo.value[v.symbol] || {}).fundingRate, Math.sign(v.quantity) === Math.sign((futureInfo.value[v.symbol] || {}).fundingRate), 'djejidjeidjiejeijie')
        const quantity = Number(v.quantity) < 0 ? -Number(v.quantity) : Number(v.quantity)
        if (Math.sign(v.quantity) === Math.sign((futureInfo.value[v.symbol] || {}).fundingRate)) {
          fundNumber += Number(v.entryPrice) * quantity * Math.abs((futureInfo.value[v.symbol] || {}).fundingRate)
        }
        mt += quantity * v.entryPrice * v.maintMargin + fundNumber
      }
    })
    return mt
  })
  const curLPrice = computed(() => {
    // 逐仓多仓：开仓均价-(持仓保证金-维持保证金)/持仓量
    // 逐仓空仓：开仓均价+(持仓保证金-维持保证金)/持仓量
    // 全仓多仓：开仓均价-(可用保证金 + 全仓持仓保证金 + 全仓未实现盈亏 + 其他交易对的全仓未实现盈亏 - 全仓维持保证金)/净持仓量
    let fundNumber = 0
    props.cbuPositions.forEach((item) => {
      if (item.symbol === props.data.symbol && Math.sign(props.data.quantity) === Math.sign((futureInfo.value[item.symbol] || {}).fundingRate)) {
        fundNumber += Number(item.entryPrice) * Math.abs(item.quantity) * Math.abs((futureInfo.value[item.symbol] || {}).fundingRate)
      }
    })
    const symbol = props.data.symbol
    const entryPrice = props.data.entryPrice || 0 // 逐仓开仓均价
    let entryPriceCross = 0
    const posMargin = needBalance.value // 持仓保证金
    const quantity = Number(props.data.quantity) < 0 ? -Number(props.data.quantity) : Number(props.data.quantity) // 持仓量
    const fundValue = fundNumber || 0 // 相同方向的资金费用累加
    const maintMargin = props.data.entryPrice * quantity * props.data.maintMargin + fundValue || 0 // 维持保证金 = 开仓均价*持仓数量*维持保证率+相同方向的资金费用累加
    // console.log(maintMargin, 'fundValuefundValuefundValuefundValue')
    const takerFee = props.data.entryPrice * quantity * (userRate.value[props.data.symbol] && userRate.value[props.data.symbol].taker_fee) || 0 // 手续费
    let closableQty = 0 // 净持仓量
    let longEntryPrice = 0 // 多开仓均价
    let longQuantity = 0 // 多仓的数量总和
    let shortEntryPrice = 0 // 空开仓均价
    let shortQuantity = 0 // 多仓的数量总和
    let Hl = 0 // 持有多仓
    let Hs = 0 // 持有空仓
    let Pl = 0 // 多仓开仓均价
    let Ps = 0 // 空仓开仓均价
    let otherCrossUnfrite = 0 // 其他交易对的全仓未实现盈亏
    props.cbuPositions.forEach((item) => {
      if (item.symbol === props.data.symbol) {
        closableQty += Number(item.closableQty)
        if (Number(item.quantity) < 0 && item.marginMethod === 'cross') {
          shortEntryPrice += (Number(item.entryPrice) * Number(item.quantity)) || 0
          shortQuantity += Number(item.quantity) || 0
          Hs += Math.abs(item.quantity) * 1 || 0
          Ps += item.entryPrice * 1 || 0
        } else if (Number(item.quantity) > 0 && item.marginMethod === 'cross') {
          longEntryPrice += (Number(item.entryPrice) * -Number(item.quantity)) || 0
          longQuantity += -Number(item.quantity) || 0
          Hl += Math.abs(item.quantity)
          Pl += item.entryPrice * 1 || 0
        }
      } else {
        if (item.marginMethod === 'cross') {
          if (Number(item.quantity) < 0) {
            otherCrossUnfrite += (Math.abs(item.quantity) * item.entryPrice) - (Math.abs(item.quantity) * (futureInfo.value[item.symbol] || {}).markPrice)
          } else {
            otherCrossUnfrite += (Math.abs(item.quantity) * (futureInfo.value[item.symbol] || {}).markPrice) - (item.quantity * item.entryPrice)
          }
        }
      }
    })
    const curUprifit = Math.min(Hl, Hs) * (Ps - Pl)
    const cwf = new BigNumber(new BigNumber(posMargin).minus(maintMargin)).div(quantity)// 持仓保证金-维持保证金
    const endLongEntryPrice = longEntryPrice / longQuantity
    const endShortEntryPrice = shortEntryPrice / shortQuantity
    entryPriceCross = closableQty < 0 ? endShortEntryPrice : endLongEntryPrice
    let num = 0
    if (props.data.marginMethod === 'isolate' && Number(props.data.quantity) > 0) { // 逐仓多仓
      num = new BigNumber(entryPrice).minus(cwf)
    } else if (props.data.marginMethod === 'isolate' && Number(props.data.quantity) < 0) { // 逐仓空仓
      num = new BigNumber(entryPrice).plus(cwf)
    } else if (props.data.marginMethod === 'cross') { // 全仓
      num = new BigNumber(entryPriceCross).minus(new BigNumber(new BigNumber(canUseBanlance.value).plus(curUprifit).plus(otherCrossUnfrite).minus(cbuMt.value)).div(closableQty))
    }
    return num < 0 || !num ? 0 : num
  })
  const submit = async() => {
    if (newLeverage.value === '') {
      useCommon.showMsg('error', t('杠杆倍数不能为空'))
      return false
    }
    if (isDisabled.value) {
      return false
    }
    const { data, error } = await saveUserLeverageApi({
      positionId: props.data.id,
      product: props.data.symbol,
      position_method: props.data.marginMethod,
      position_merge: 'longshort',
      leverage: newLeverage.value.replace('x', '') * 1
    })
    if (data) {
      emit('close')
      setModeInfoState(props.data.symbol, props.data.marginMethod === 'cross' ? 1 : 2, newLeverage.value.replace('x', '') * 1)
      useCommon.showMsg('success', t('修改成功'))
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  watch(() => newLeverage.value, (val, oldVal) => {
    let newLeverageV = isNaN((val.replace('x', '')) * 1) ? (oldVal.replace('x', '') * 1) : (val.replace('x', '')) * 1
      if (newLeverageV > props.leverageMax) {
        newLeverageV = props.leverageMax
      } else if (newLeverageV < 1) {
        newLeverageV = 1
      }
      multiple.value = newLeverageV
      newLeverage.value = newLeverageV + (isFocus.value ? '' : 'x')
  })
  watch(() => isFocus.value, (val) => {
    if (val) {
      newLeverage.value = newLeverage.value.replace('x', '')
    } else {
      newLeverage.value = newLeverage.value + 'x'
    }
  })
  watch(() => multiple.value, (val) => {
    newLeverage.value = val + 'x'
  })
  const plus = () => {
    let newLeverageV = (newLeverage.value.replace('x', '')) * 1 + 1
    if (newLeverageV > leverageMax.value) {
      newLeverageV = leverageMax.value
    }
    newLeverage.value = newLeverageV + 'x'
  }
  const minus = () => {
    let newLeverageV = (newLeverage.value.replace('x', '')) * 1 - 1
    if (newLeverageV < 1) {
      newLeverageV = 1
    }
    newLeverage.value = newLeverageV + 'x'
  }
  const requestAnimationFrameInterval = ref(null)
  const socketDateAnimation = () => {
    symbolLast.value = COLLATERALSymbol.value
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  }
  onBeforeMount(() => {
    socketDateAnimation()
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  })
  onBeforeUnmount(() => {
    requestAnimationFrameInterval.value && cancelAnimationFrame(requestAnimationFrameInterval.value)
  })
  onMounted(() => {
    visible.value = props.isShow
    newLeverage.value = props.data.leverage
  })
</script>
<style lang="scss">
  .change-container {
    margin-top: -20px;
    .change-title {
      height: 56px;
      line-height: 56px;
      font-weight: 600;
      @include color(tc-primary);
      font-size: 16px;
    }
    .change-tab-cont {
      width: 100%;
      height: auto;
      @include bg-color(bg-secondary);
      border: 1px solid;
      @include border-color(border);
      border-radius: 4px;
      .main-tab {
        ul {
          li {
            padding: 8px 12px;
            @include bg-color(bg-tertiary);
            .el-button {
              width: 100%;
              height: 38px;
              line-height: 38px;
              padding: 0 !important;
              border: 0;
              font-size: 16px;
              background: none;
              &:hover,
              &:active,
              &:visited,
              &:focus {
                background: none;
              }
            }
            &.cur {
              @include bg-color(bg-secondary);
              .el-button {
                background: #2271e6;
                border: 1px;
                &:hover,
                &:active,
                &:visited {
                  background: #4e8deb;
                }
                &:focus {
                  background: #2271e6;
                }
              }
            }
          }
        }
      }
      .sub-tab {
        ul {
          li {
            padding: 10px 12px;
            .el-button {
              &:hover,
              &:active,
              &:visited,
              &:focus {
              }
              width: 100%;
              height: 34px;
              line-height: 32px;
              padding: 0 !important;
              font-size: 14px;
              &.el-button--primary {
                span {
                  font-weight: 700;
                }
              }
            }
          }
        }
      }
    }
  }
  .change-leverage {
    .now,
    .after {
      border-radius: 8px;
      border: 1px solid rgba(131, 134, 143, 0.16);
      li {
        &.item-border {
          border-top: 1px solid rgba(131, 134, 143, 0.16);
        }
      }
    }
    .now {
      .title {
        @include bg-color(bg-quaternary);
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        border: 1px solid rgba(131, 134, 143, 0.16);
      }
    }
    .number-input {
      .el-input__prefix {
        left: 8px !important;
      }
      .el-input__suffix {
        right: 8px !important;
      }
      .el-input__inner {
        text-align: center;
        height: 40px;
        line-height: 40px;
      }
      .num-input-bg {
        width: 24px;
        height: 24px;
        @include bg-color(bg-secondary);
        font-size: 12px;
        border: 4px;
        line-height: 24px;
        cursor: pointer;
      }
    }
    .after {
      .title {
        @include bg-color(theme);
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        border: 1px solid rgba(131, 134, 143, 0.16);
      }
    }
  }
  .change-btn {
    margin: 16px 0 0;
    padding: 12px 0 0;
  }
</style>