<template>
  <el-dialog v-model="visible" :title="$t('调整保证金')" width="480px" @close="emit('closeDialog')">
    <div class="margin-tab-list">
      <ul class="flex-box">
        <li class="flex-1 mg-r16" :class="{'active': type === 0}" @click="type = 0;form.amount = ''">{{ $t('增加保证金') }}</li>
        <li class="flex-1" :class="{'active': type === 1}" @click="type = 1;form.amount = ''">{{ $t('减少保证金') }}</li>
      </ul>
    </div>
    <el-input v-model="form.amount">
      <template #suffix>
        <div class="flex-box transfer-input-tag">
          <span>USDT</span>
          <em class="cursor-pointer" @click="form.amount = Number(marginAssets)">{{ $t('全部') }}</em>
        </div>
      </template>
    </el-input>
    <p class="font-size-12 fit-tc-secondary mg-t8 text-right">
      {{ $t('可用') }}
      <span class="fit-tc-primary">{{ marginAssets }} USDT</span>
    </p>
    <ul class="change-margin_list mg-t24">
      <li class="ts-12 fit-tc-secondary text-center">
        <span class="tw-b fit-tc-primary text-left">{{ data.symbol.replace('_SWAP', '').replace('_', '') }}</span>
        <span>{{ $t('占用保证金') }}(USDT)</span>
        <span>{{ $t('爆仓价') }}</span>
      </li>
      <li class="ts-12 fit-tc-primary text-center">
        <span class="fit-tc-secondary text-left">{{ $t('当前') }}</span>
        <span>{{ format(data.posMargin, 4, true) }}</span>
        <span>{{ format(curLprice, 4, true) }}</span>
      </li>
      <li class="ts-12 tw-b text-center" :class="!type ? `fit-rise rise-border` : `fit-fall fall-border`">
        <span class="tw-n fit-tc-secondary text-left">{{ type === 0 ? $t('增加后') : $t('减少后') }}</span>
        <span>{{ format(afterMargin, 4, true) }}</span>
        <span>{{ format(lPrice, 4, true) }}</span>
      </li>
    </ul>
    <el-button type="primary" class="mg-t40" :loading="isLoading" :disabled="!form.amount"  @click="request()">{{ type === 0 ? $t('增加保证金') : $t('减少保证金') }}</el-button>
  </el-dialog>
</template>
<script lang="ts" setup>
  import BigNumber from 'bignumber.js'
  import { format } from '~/utils'
  import { ElDialog, ElInput } from 'element-plus'
  import { changePositionMarginApi } from '~/api/order'
  import { commonStore } from '~/stores/commonStore'
  import { useCommonData } from '~/composables/index'
  const { locale, t } = useI18n()
  const useCommon = useCommonData()
  const store = commonStore()
  const { getAssetsByCoin } = store
  const { tradeAssetObj, pairInfo } = storeToRefs(store)
  const props = defineProps({
    isShow: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default () {
        return {}
      }
    },
    curLprice: {
      type: [String, Number],
      default: 0
    },
    takerFee: {
      type: [String, Number],
      default: 0
    }
  })
  const emit = defineEmits(['closeDialog'])
  const visible = ref(false)
  const type = ref(0)
  const form = ref({
    amount: ''
  })
  const isLoading = ref(false)
  const minMargin = computed(() => {
    // 可减少保证金=当前保证金-(持仓价值*初始保证金率)
    const quantity = Number(props.data.quantity) < 0 ? -Number(props.data.quantity) : Number(props.data.quantity)
    const posValue = props.data.entryPrice * quantity
    const default_initial_margin = pairInfo.value && pairInfo.value[props.data.symbol] && props.data.initMargin || 0
    const result = new BigNumber(props.data.posMargin).minus(new BigNumber(posValue).times(default_initial_margin))
    return result || 0
  })
  const marginAssets = computed(() => {
    const balance = tradeAssetObj.value && tradeAssetObj.value['USDT'] && tradeAssetObj.value['USDT'].balance || 0
    const result = Math.max(0, !type.value ? balance : minMargin.value)
    return new BigNumber(result).toFixed((pairInfo.value[props.data.symbol] || {}).price_scale, 1)
  })
  const afterMargin = computed(() => {
    return type.value ? new BigNumber(props.data.posMargin).minus(form.value.amount || 0) : new BigNumber(props.data.posMargin).plus(form.value.amount || 0)
  })
  const lPrice = computed(() => {
    let num = 0
    const quantity = Number(props.data.quantity) < 0 ? -Number(props.data.quantity) : Number(props.data.quantity) // 持仓量
    const maintMargin = props.data.entryPrice * quantity * props.data.maintMargin || 0 // 维持保证金 = 开仓均价*持仓数量*维持保证率
    const takerFee = props.takerFee // 手续费
    const cwf = new BigNumber(new BigNumber(afterMargin.value).minus(maintMargin).minus(takerFee)).div(quantity)// 持仓保证金-维持保证金-手续费
    if (Number(props.data.quantity) > 0) { // 逐仓多仓
      num = new BigNumber(props.data.entryPrice).minus(cwf)
    } else if (Number(props.data.quantity) < 0) { // 逐仓空仓
      num = new BigNumber(props.data.entryPrice).plus(cwf)
    } 
    return num
  })
  const request = async() => {
    if (!form.value.amount) {
      useCommon.showMsg('error', t('请输入正确的数量'))
      return false
    }
    isLoading.value = true
    const amount = type.value ? form.value.amount * -1 : form.value.amount
    const { data, error } = await changePositionMarginApi({
      positionId: props.data.id,
      amount 
    })
    if (data) {
      isLoading.value = false
      useCommon.showMsg('success', t('调整成功！'))
      emit('closeDialog')
    } else {
      isLoading.value = false
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  onMounted(() => {
    visible.value = props.isShow
    getAssetsByCoin()
  })
</script>
<style lang="scss" scoped>
  .transfer-input-tag{
    font-size:14px;
    span{
      @include color(tc-primary);
    }
    em{
      font-style:inherit;
      cursor:pointer;
      padding-left:12px;
      @include color(theme);
    }
  }
  .margin-tab-list{
    ul{
      margin-bottom:12px;
      border-bottom:1px solid;
      @include border-color(border);
      li{
        text-align:center;
        line-height:40px;
        font-size:14px;
        cursor:pointer;
        @include color(tc-secondary);
        &.active{
          position:relative;
          @include color(tc-primary);
          &:after{
            content: '';
            display:block;
            width:14px;
            height:2px;
            position:absolute;
            bottom:0;
            left:50%;
            margin-left:-7px;
            @include bg-color(theme);
          }
        }
      }
    }
  }
  .text-right{
    text-align:right;
  }
  .change-margin_list {
    border-top: 1px solid;
    @include border-color(border);
    li {
      padding: 8px 0 10px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid;
      @include border-color(border);
      span {
        flex: 1;
      }
      &.rise-border {
        @include border-color(rise);
      }
      &.fall-border {
        @include border-color(fall);
      }
    }
  }
</style>