<template>
	<el-dialog v-model="visible" :title="curType * 1 === 3 ? $t('限价委托提示') : $t('止盈/止损限价委托提示')" width="400px" @close="emit('close')">
		<div class="limit-check-cont">
      <div class="cont">
        <p class="fit-tc-primary ts-14 pd-b16">{{ $t('由于市场行情波动的不可预测性，您设置的止盈/止损限价委托不一定会成交，请注意控制风险。') }}</p>
        <el-checkbox v-model="checked"><span>{{ $t('本次登录有效期内不再提示') }}</span></el-checkbox>
      </div>
      <el-button type="primary" class="mg-t24 mg-b8" @click="knowFun()">{{ $t('我知道了') }}</el-button>
    </div>
	</el-dialog>
</template>
<script lang="ts" setup>
  import { ElDialog, ElCheckbox } from 'element-plus'
	const props = defineProps({
		curType: {
			type: [Number, String],
      default: ''
		},
		isShow: {
			type: Boolean,
			default: false
		}
	})
	const emit = defineEmits(['close', 'confirmKnow'])
	const visible = ref(false)
	const checked = ref(false)
	const knowFun = () => {
		emit('confirmKnow', {
			type: props.curType,
			isChecked: checked.value ? 1 : 0
		})
	}
	onMounted(() => {
		visible.value = props.isShow
	})
</script>