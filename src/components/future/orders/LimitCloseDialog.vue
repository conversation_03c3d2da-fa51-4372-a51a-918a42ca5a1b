<template>
  <el-dialog v-model="visible" :title="$t('限价平仓')" @close="emit('close')">
    <div class="symbol-info font-size-14 flex-box space-between">
      <div class="text-left">
        <p class="fit-tc-secondary font-size-12">{{ $t('平仓信息') }}</p>
        <span class="fit-tc-primary">{{ data.symbol.replace('_SWAP', '').replace('_', '') }}</span>
      </div>
      <div class="text-right">
        <p class="fit-tc-secondary font-size-12">{{ $t('收益率') }}</p>
        <span 
          v-if="Number(data.quantity) < 0"
          :class="((-Number(data.quantity) * data.entryPrice) - (-Number(data.quantity) * (futureInfo[data.symbol] || {}).markPrice)) < 0 ? 'fit-fall' : 'fit-rise'"
          >
          {{ format((((-Number(data.quantity) * data.entryPrice) - (-Number(data.quantity) * (futureInfo[data.symbol] || {}).markPrice)) / data.posMargin * 100), 2, true) }} %
        </span>
        <span
          v-else-if="props.last !== '--'"
          :class="((data.quantity * (futureInfo[data.symbol] || {}).markPrice) - (data.quantity * data.entryPrice)) < 0 ? 'fit-fall' : 'fit-rise'"
          >
          {{ format((((data.quantity * (futureInfo[data.symbol] || {}).markPrice) - (data.quantity * data.entryPrice)) / data.posMargin * 100), 2, true) }} %
        </span>
      </div>
    </div>
    <div class="symbol-info">
    </div>
    <div class="symbol-form-box">
      <div class="mg-b12">
        <span class="fit-tc-secondary font-size-12">{{ $t('平仓价格') }}</span>
        <el-input v-model="params.order_price" type="number" :decimal="(pairInfo[data.symbol] || {}).price_scale" class="small-position-input">
          <template #suffix>
            USDT
          </template>
        </el-input>
      </div>
      <div class="mg-b24">
        <span class="fit-tc-secondary font-size-12">{{ $t('平仓数量') }}</span>
        <el-input v-model="params.order_amount" :decimal="(pairInfo[data.symbol] || {}).quantity_scale" class="small-position-input mg-r8">
          <template #suffix>
            <div class="font-size-12 flex-box">
              <span class="fit-tc-primary">
                {{ useCommon.getFuturesSymbol(data.symbol, isCBUUnitUSDT) }}
              </span>
              <div class="box-line mg-lr12"></div>
              <span
                v-for="(item, index) in positionPercent"
                :key="index"
                class="font-size-12 fit-tc-secondary cursor-pointer black-text-hover"
                :class="{'mg-l12': index !== 0}"
                @click="choisePercent(data, item)"
              >{{ item.label }}</span>
            </div>
          </template>
        </el-input>
      </div>
      <el-button
        type="primary"
        class="position-btn mg-r8"
        :class="Number(data.quantity) < 0 ? 'rise-btn' : 'fall-btn'"
        @click="closePosition()"
        >
        {{ Number(data.quantity) < 0 ? $t('平空') : $t('平多') }}
      </el-button>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElDialog, ElInput, ElDropdown, ElDropdownMenu, ElDropdownItem, ElButton } from 'element-plus'
  import { futureStore } from '~/stores/futureStore'
  import { commonStore } from '~/stores/commonStore'
  import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
  import { useCommonData } from '~/composables/index'
  import BigNumber from 'bignumber.js'
  const useCommon = useCommonData()
  const publicStore = commonStore()
  const { locale, t } = useI18n()
  const { pairInfo } = storeToRefs(publicStore)
  const store = futureStore()
  const { futureInfo } = storeToRefs(store)
  const props = defineProps({
    isShow: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default () {
        return {}
      }
    },
    last: {
      type: [Number, String],
      default: ''
    },
    futuresType: {
      type: String,
      default: ''
    },
    isCBUUnitUSDT: {
      type: Boolean,
      default: false
    }
  })
  const emit = defineEmits(['close', 'closePosition'])
  const positionPercent = computed(() => {
    return [
      {
        label: '10%',
        type: 0.1
      },
      {
        label: '20%',
        type: 0.2
      },
      {
        label: '50%',
        type: 0.5
      },
      {
        label: '100%',
        type: 1
      }
    ]
  })
  const visible = ref(false)
  const params = ref({
    order_price: '',
    order_amount: ''
  })
  const choisePercent = (row, item) => {
    const calculateAmount = new BigNumber(Number(row.closableQty) < 0 ? -Number(row.closableQty) : Number(row.closableQty)).multipliedBy(item.type)
    params.value.order_amount = props.isCBUUnitUSDT ? new BigNumber(calculateAmount).multipliedBy(props.last).toFixed((pairInfo.value[row.symbol] || {}).price_scale) : calculateAmount
  }
  const generateDecimal = (precision) => {
    return Math.pow(10, -precision);
  }
  const validata = (orderAmount) => {
    if (!params.value.order_price) {
      useCommon.showMsg('error', t('请输入平仓价格'))
      return false
    }
    if (!params.value.order_amount) {
      useCommon.showMsg('error', t('请输入平仓数量'))
      return false
    }
    if (params.value.order_amount * 1 === 0) {
      useCommon.showMsg('error', t('当前可平仓位为') + ' ' + 0)
      return false
    }
    const precision = pairInfo.value && pairInfo.value[props.data.symbol].quantity_scale || 0
    if (params.value.order_amount * 1 < generateDecimal(precision) * 1) {
      useCommon.showMsg('error', t('最小') + ' ' + generateDecimal(precision))
      return false
    }
    return true
  }
  const closePosition = () => {
    const amount = props.isCBUUnitUSDT ? new BigNumber(params.value.order_amount).dividedBy(props.last).toFixed((pairInfo.value[props.data.symbol] || {}).price_scale) : params.value.order_amount
    if (!validata(Number(amount))) {
      return false
    }
    const dataParams = {
      positionId: props.data.id,
      symbol: props.data.symbol,
      quantity: Number(props.data.quantity) > 0 ? format(new BigNumber(-amount), (pairInfo.value[props.data.symbol] || {}).quantity_scale, 1) : format(new BigNumber(amount), (pairInfo.value[props.data.symbol] || {}).quantity_scale, 1),
      price: params.value.order_price,
      type: 'limit',
      market: props.futuresType,
      positionMerge: props.data.mergeMode,
      marginMethod: props.data.marginMethod,
      leverage: Number(props.data.leverage),
      close: true
    }
    emit('closePosition', dataParams)
  }
  onMounted(() => {
    visible.value = props.isShow
    params.value.order_price = props.last
  })
</script>
<style lang="scss" scoped>
.box-line{
  width:1px;
  height:14px;
  @include bg-color(border);
}
.symbol-form-box{
  padding-top:12px;
  margin-top:12px;
  border-top:1px solid;
  @include border-color(border);
  .el-button{
    &.position-btn{
      color:#fff !important;
      &.rise-btn{
        @include border-color(rise);
        @include bg-color(rise);
      }
      &.fall-btn{
        @include border-color(fall);
        @include bg-color(fall);
      }
    }
  }
}
</style>