<template>
  <el-input v-model="inputValue">
    <template #append>
      <div class="flex-box cursor-pointer" @click="isShowType = true">
        <span class="fit-tc-primary font-size-14 mg-r8">{{ profit_type * 1 === 1 ? 'USDT' : '%' }}</span>
        <MonoDownArrowMin size="12" class="fit-tc-secondary" />
      </div>
    </template>
  </el-input>
  <el-dialog v-model="isShowType" :title="$t('止盈/止损设置')" class="setLossProfit">
    <dl :class="{'active': profit_type * 1 === 1}" @click="changeType(1)">
      <dt>{{ $t('盈亏') }}</dt>
      <dd>{{ $t('根据预估盈亏设置止盈/止损价格') }}</dd>
      <MonoRigthChecked size="24" class="fit-theme" />
    </dl>
    <dl :class="{'active': profit_type * 1 === 2 }" @click="changeType(2)">
      <dt>{{ $t('投资回报率') }}%</dt>
      <dd>{{ $t('根据预估投资回报率设置止盈/止损价格') }}</dd>
      <MonoRigthChecked size="24" class="fit-theme" />
    </dl>
    <dl :class="{'active': profit_type * 1 === 3 }" @click="changeType(3)">
      <dt>{{ $t('涨跌幅') }}%</dt>
      <dd>{{ $t('根据主订单价格或开仓价格相关的百分比变化来设置止盈/止损价格') }}</dd>
      <MonoRigthChecked size="24" class="fit-theme" />
    </dl>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElInput, ElDialog } from 'element-plus'
  import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
  import MonoRigthChecked from '~/components/common/icon-svg/MonoRigthChecked.vue'
  const inputValue = ref('')
  const props = defineProps({
    modelValue: {
      type: [String, Number],
      default: ''
    }
  })
  const emit = defineEmits(['update:modelValue'])
  const isShowType = ref(false)
  const profit_type = ref(1)
  const changeType = (val) => {
    profit_type.value = val
    isShowType.value = false
    emit('changeProfitType', val)
  }
  watch(() => inputValue.value, (val) => {
    console.log(val, 'shshysuueheueyheuyeue')
    emit('update:modelValue', val)
  })
  watch(() => props.modelValue, (val) => {
    console.log(val, 'shshysuueheueyheuyeue')
    inputValue.value = val
  })
</script>
<style lang="scss" scoped>
  .el-dialog{
    &.setLossProfit{
      dl{
        border:1px solid;
        border-radius:8px;
        padding:12px;
        margin-bottom:12px;
        cursor:pointer;
        position:relative;
        @include border-color(border);
        dt{
          font-size:18px;
          @include color(tc-primary);
        }
        dd{
          font-size:14px;
          @include color(tc-secondary);
        }
        &.active{
          @include border-color(theme);
          svg{
            display:block;
          }
        }
        svg{
          display:none;
          position:absolute;
          top:50%;
          margin-top:-12px;
          right:12px;
        }
      }
    }
  }
</style>