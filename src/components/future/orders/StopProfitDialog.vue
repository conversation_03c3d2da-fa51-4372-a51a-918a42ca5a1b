<template>
  <el-dialog v-model="visible" width="500" class="check-stop_dialog" append-to-body @close="emit('close')">
    <div class="check-stop-tab">
      <ul class="flex-box">
        <li :class="{'active': type * 1 === 1}" @click="type = 1">{{ $t('止盈止损') }}</li>
        <li :class="{'active': type * 1 === 2}" @click="type = 2">{{ $t('仓位止盈止损') }}</li>
      </ul>
    </div>
    <div class="check-stop-wrap">
      <div class="flex-box pd-b16">
        <span v-if="Number(data.quantity) < 0" class="side-tag fit-fall">{{ $t('空') }}</span>
        <span v-else class="side-tag fit-rise">{{ $t('多') }}</span>
        <span class="font-size-18 tw-5 fit-tc-primary mg-l8">
          {{ data.symbol.replace('_SWAP', '').replace('_', '') }}
          <span class="fit-tc-secondary font-size-12 mg-l4">{{ $t('永续') }}</span>
        </span>
        <span class="lever-block tw-5 mg-l12" :class="{'fit-fall': Number(data.quantity) < 0, 'fit-rise': Number(data.quantity) > 0}">
          <template v-if="data.marginMethod === 'cross' && data.mergeMode !== 'none'">{{ $t('全仓') }}</template>
          <template v-if="data.marginMethod === 'isolate' && data.mergeMode !== 'none'">{{ $t('逐仓') }}</template>
          <template v-if="data.marginMethod === 'cross' && data.mergeMode === 'none'">{{ $t('全仓') }}/{{ $t('分仓') }}</template>
          <template v-if="data.marginMethod === 'isolate' && data.mergeMode === 'none'">{{ $t('逐仓') }}/{{ $t('分仓') }}</template>
          {{ (Math.max(1, data.leverage)).toFixed(0) }}x
        </span>
      </div>
      <div class="flex-box space-between font-size-14 mg-b4">
        <span class="fit-tc-secondary tw-n">{{ $t('开仓均价') }}</span>
        <span>{{ data.entryPrice }}</span>
      </div>
      <div class="flex-box space-between font-size-14 mg-b4">
        <span class="fit-tc-secondary tw-n">{{ $t('最新成交价') }}</span>
        <span>{{ nowPrice }}</span>
      </div>
      <div v-if="type * 1 === 2" class="flex-box space-between font-size-14 mg-b4">
        <span class="fit-tc-secondary tw-n">{{ $t('当前持仓') }}</span>
        <span>
          {{ format(nowLc,(isCBUUnitUSDT ? (pairInfo[data.symbol] || {}).price_scale : (pairInfo[data.symbol] || {}).quantity_scale), true) }}
          <template v-if="futuresType === 'ipc'">{{ useCommon.getFuturesSymbol(data.symbol, isCBCUnitUSD) }}</template>
          <template v-else>{{ useCommon.getFuturesSymbol(data.symbol, isCBUUnitUSDT) }}</template>
        </span>
      </div>
      <div v-if="type * 1 === 1" class="check-stop-mode check-stop-mode-one">
        <div class="check-stop-input flex-box mg-t12">
          <div class="flex-1 mg-r12">
            <el-input v-model="batchDataProfitOrLoss.price_trigger" :placeholder="$t('触发价格')">
            </el-input>
          </div>
          <div class="flex-1">
            <SelectProfitLoss v-model="batchDataProfitOrLoss.price_type_value" @changeProfitType="(val) => {
              batchDataProfitOrLoss.price_type = val
            }" />
          </div>
        </div>
        <div class="check-stop-input flex-box mg-t12">
          <div class="flex-1 mg-r12">
            <el-input v-model="batchDataProfitOrLoss.price" :placeholder="$t('价格')">
            </el-input>
          </div>
          <div class="flex-1">
            <el-select v-model="orderType">
              <el-option v-for="(item, index) in orderTypeList" :key="index" :label="item.label" :value="item.value">
                {{ item.label }}
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="check-stop-input flex-box mg-t12">
          <div class="flex-1">
            <el-input v-model="batchDataProfitOrLoss.amount" :placeholder="$t('数量')" @focus="focusFun()">
              <template #suffix>
                <span class="fit-tc-primary">
                  {{ useCommon.getFuturesSymbol(data.symbol, isCBUUnitUSDT) }}
                </span>
              </template>
            </el-input>
          </div>
        </div>
        <div class="slider-demo-block">
          <BoxSlider v-model="batchDataProfitOrLoss.amount_percent" :max="100" typeStr="%" />
        </div>
        <div class="flex-box space-between mg-t24">
          <span class="fit-tc-secondary tw-n">{{ $t('仓位数量') }}:</span>
          <span>{{ nowAmount }} {{ useCommon.getFuturesSymbol(data.symbol, isCBUUnitUSDT) }}</span>
        </div>
        <div class="flex-box space-between mg-t8">
          <span class="fit-tc-secondary tw-n">{{ profitLossNum < 0 ? $t('预计亏损') : $t('预计盈利') }}:</span>
          <span :class="profitLossNum < 0 ? 'fit-fall' : 'fit-rise'">{{ profitLossNum }} USDT</span>
        </div>
        <el-button type="primary" :disabled="isDisabledType1" class="mg-t40" @click="confirmType1()">{{ $t('确认') }}</el-button>
      </div>
      <div v-if="type * 1 === 2" class="check-stop-mode check-stop-mode-two">
        <p class="fit-tc-secondary">{{ $t('止盈') }}</p>
        <div class="check-stop-input flex-box mg-t8 mg-b12">
          <div class="flex-1 mg-r12">
            <el-input v-model="batchDataProfitAndLoss.profit_price_trigger" :placeholder="$t('止盈-触发价格')">
            </el-input>
          </div>
          <div class="flex-1">
            <SelectProfitLoss v-model="batchDataProfitAndLoss.profit_price_type_value"  @changeProfitType="(val) => {
              batchDataProfitAndLoss.profit_price_type = val
            }" />
          </div>
          <p v-if="adjustText(batchDataProfitAndLoss.profit_price_trigger, 0)" class="pos-tips font-size-12 fit-error">{{
              adjustText(batchDataProfitAndLoss.profit_price_trigger, 0)
          }}</p>
        </div>
        <div class="tips-cont fit-tc-secondary font-size-12">
          {{ $t('当最新成交价触达') }}
          <span class="fit-rise">{{ batchDataProfitAndLoss.profit_price_trigger || '--' }}</span>
          {{ $t('时，将会触发市价止盈委托平仓当前仓位。预期盈亏为') }}
          <span :class="profitNum.replace(/,/g, '') * 1 < 0 ? 'fit-fall' : 'fit-rise'">{{ profitNum }} USDT</span>
        </div>
        <p class="fit-tc-secondary">{{ $t('止损') }}</p>
        <div class="check-stop-input flex-box mg-t8 mg-b12">
          <div class="flex-1 mg-r12">
            <el-input v-model="batchDataProfitAndLoss.loss_price_trigger" :placeholder="$t('止损-触发价格')">
            </el-input>
          </div>
          <div class="flex-1">
            <SelectProfitLoss v-model="batchDataProfitAndLoss.loss_price_type_value"  @changeProfitType="(val) => {
              batchDataProfitAndLoss.loss_price_type = val
            }" />
          </div>
          <p v-if="adjustText(batchDataProfitAndLoss.loss_price_trigger, 1)" class="pos-tips font-size-12 fit-error">{{
            adjustText(batchDataProfitAndLoss.loss_price_trigger, 1)
          }}</p>
        </div>
        <div class="tips-cont fit-tc-secondary font-size-12">
          {{ $t('当最新成交价触达') }}
          <span class="fit-fall">{{ batchDataProfitAndLoss.loss_price_trigger || '--' }}</span>
          {{ $t('时，将会触发市价止盈委托平仓当前仓位。预期盈亏为') }}
          <span :class="lossNum.replace(/,/g, '') * 1 < 0 ? 'fit-fall' : 'fit-rise'">{{ lossNum }} USDT</span>
        </div>
        <el-button type="primary" :disabled="isDisabledType2" :loading="isTrade" class="mg-t40" @click="confirmType2()">{{ $t('确认') }}</el-button>
      </div>
    </div>
  </el-dialog>
  <futures-limit-check-dialog v-if="isShowLimitDialog" :isShow="isShowLimitDialog" :curType="curType" @close="isShowLimitDialog = false" @confirmKnow="confirmKnow" />
</template>
<script lang="ts" setup>
  import BigNumber from 'bignumber.js'
  import { ElDialog, ElInput, ElButton, ElSelect, ElOption } from 'element-plus'
  import { openOrder } from '~/api/order'
  import { futureStore } from '~/stores/futureStore'
  import { commonStore } from '~/stores/commonStore'
  import { format } from '~/utils'
  import FuturesLimitCheckDialog from './FuturesLimitCheckDialog.vue'
  import BoxSlider from '~/components/common/BoxSlider.vue'
  import SelectProfitLoss from '~/components/future/orders/SelectProfitLoss.vue'
  import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
  import { useCommonData } from '~/composables/index'
  const useCommon = useCommonData()
  const { locale, t } = useI18n()
  const store = futureStore()
  const { futureInfo } = storeToRefs(store)
  const publicStore = commonStore()
  const { subAllTickerSocket, cancelAllTicker } = publicStore
  const { marketsObj, pairInfo } = storeToRefs(publicStore)
  const props = defineProps({
    isShow: {
      type: Boolean,
      default: false
    },
    coinInfo: {
      type: Object,
      default () {
        return {}
      }
    },
    data: {
      type: Object,
      default () {
        return {}
      }
    },
    isCBCUnitUSD: {
      type: Boolean,
      default: false
    },
    isCBUUnitUSDT: {
      type: Boolean,
      default: false
    }
  })
  const emit = defineEmits(['close'])
  const visible = ref(false)
  const requestAnimationFrameInterval = ref(null)
  const type = ref(1)
  const markets = ref({})
  const quantity = computed(() => {
    return Number(props.data.quantity) < 0 ? -Number(props.data.quantity) : Number(props.data.quantity)
  })
  const closableQty = computed(() => {
    return Number(props.data.closableQty) < 0 ? -Number(props.data.closableQty) : Number(props.data.closableQty)
  })
  const nowPrice = computed(() => {
    return markets.value && markets.value[props.data.symbol] && markets.value[props.data.symbol].last || 0
  })
  const nowLc = computed(() => {
    return !props.isCBUUnitUSDT ? quantity.value : useCommon.cbuConver(quantity.value, props.isCBUUnitUSDT, nowPrice.value)
  })
  const orderType = ref(1)
  const orderTypeList = ref([
    {
      label: t('市价单'),
      value: 1
    },
    {
      label: t('限价单'),
      value: 2
    }
  ])
  const batchDataProfitOrLoss = ref({
    price_trigger: '',
    price_type_value: '',
    price_type: 1,
    price: '',
    amount: '',
    amount_percent: ''
  })
  const batchDataProfitAndLoss = ref({
    profit_price_trigger: '',
    loss_price_trigger: '',
    loss_price_type_value: '',
    loss_price_type: 1,
    profit_price_type_value: '',
    profit_price_type: 1
  })
  watch(() => visible.value, (val) => {
    if (val) {
      batchDataProfitOrLoss.value.amount = nowLc.value
      batchDataProfitOrLoss.value.price = orderType.value * 1 === 1 ? t('市价') : ''
    }
  })
  watch(() => orderType.value,  (val) => {
    if (val * 1 === 1) {
      batchDataProfitOrLoss.value.price = t('市价')
    } else {
      batchDataProfitOrLoss.value.price = ''
    }
  })
  let isFocusing = false
  const focusFun = () => {
    isFocusing = true
    if (batchDataProfitOrLoss.value.amount && batchDataProfitOrLoss.value.amount.includes('%')) {
      batchDataProfitOrLoss.value.amount = ''; // 清空 amount
      batchDataProfitOrLoss.value.amount_percent = 0; // 设为 0，避免触发 watch
    }
    setTimeout(() => {
      isFocusing = false; // 稍后恢复监听
    }, 100)
  }
  watch(() => batchDataProfitOrLoss.value.amount_percent, (val) => {
    if (!isFocusing && val * 1 >= 1) {
      batchDataProfitOrLoss.value.amount = `${val}%`
    }
  })
  const nowAmount = computed(() => {
    const decimal = props.isCBUUnitUSDT ? (pairInfo.value[props.data.symbol] || {}).price_scale : (pairInfo.value[props.data.symbol] || {}).quantity_scale
    if (batchDataProfitOrLoss.value.amount_percent * 1 > 1) {
      const percent = new BigNumber(batchDataProfitOrLoss.value.amount_percent).div(100)
      const num = new BigNumber(nowLc.value).times(percent)
      return format(num, decimal, true, true) || 0
    } else {
      const num = !props.isCBUUnitUSDT ? Number(batchDataProfitOrLoss.value.amount) : new BigNumber(batchDataProfitOrLoss.value.amount.div(nowLc.value))
      return format(num, decimal, true, true) || 0
    }
  })
  const setNum = (priceTrigger, priceAmount) => {
    const num = new BigNumber((props.data.quantity * 1 < 0 ? (props.data.entryPrice - priceTrigger) : (priceTrigger - props.data.entryPrice)) * (priceAmount || 0))
    return num
  }
  const adjustText = (priceTrigger, type) => {
    if (!priceTrigger) {
      return 0
    }
    if (type === 0 && Number(props.data.quantity) > 0) {
      return Number(priceTrigger) > Number(nowPrice.value) ? '' : t('止盈触发价需大于最新成交价')
    } else if (type === 0 && Number(props.data.quantity) < 0) {
      return Number(priceTrigger) < Number(nowPrice.value) ? '' : t('止盈触发价需小于最新成交价')
    } else if (Number(props.data.quantity) > 0) {
      return Number(priceTrigger) < Number(nowPrice.value) ? '' : t('止损触发价需小于最新成交价')
    } else {
      return Number(priceTrigger) > Number(nowPrice.value) ? '' : t('止损触发价需大于最新成交价')
    }
  }
  const profitLossNum = computed(() => {
    const triggerPrice = batchDataProfitOrLoss.value.price_trigger * 1
    const currentPrice = batchDataProfitOrLoss.value.price * 1
    const lossProfitNum = setNum(orderType.value * 1 === 1 ? triggerPrice : currentPrice, nowAmount.value.replace(/,/g, ''))
    if ((orderType.value * 1 === 1 && !triggerPrice) || (orderType.value * 1 === 2 && !currentPrice)) {
      return '0'
    } else {
      return format(Number(lossProfitNum), 4, true, true)
    }
  })
  const lossNum = computed(() => {
    const triggerPrice = batchDataProfitAndLoss.value.loss_price_trigger * 1
    const num = setNum(triggerPrice, closableQty.value)
    console.log(triggerPrice, Number(num), 'dhduehduehudhehueLoss')
    if (!triggerPrice) {
      return '0'
    } else {
      return format(Number(num), 4, true, true)
    }
  })
  const profitNum = computed(() => {
    const triggerPrice = batchDataProfitAndLoss.value.profit_price_trigger * 1
    const num = setNum(triggerPrice, closableQty.value)
    console.log(triggerPrice, Number(num), 'dhduehduehudhehueProfit')
    if (!triggerPrice) {
      return '0'
    } else {
      return format(Number(num), 4, true, true)
    }
  })
  const profitNumPercent = computed(() => {
    return format(new BigNumber(profitNum.value.replace(/,/g, '')).div(props.data.posMargin).times(100), 2, true, true)
  })
  const lossNumPercent = computed(() => {
    return format(new BigNumber(lossNum.value.replace(/,/g, '')).div(props.data.posMargin).times(100), 2, true, true)
  })
  const profitLossNumPercent = computed(() => {
    return format(new BigNumber(profitLossNum.value.replace(/,/g, '')).div(props.data.posMargin).times(100), 2, true, true).replace(/,/g, '')
  })
  const productProfitLoss = computed(() => {
    const triggerPrice = batchDataProfitOrLoss.value.price_trigger * 1
    const entryPrice = props.data.entryPrice * 1
    const result = (new BigNumber(triggerPrice).minus(entryPrice)).div(entryPrice).times(100)
    if (triggerPrice) {
      return format(result, 2, true, true).replace(/,/g, '')
    } else {
      return ''
    }
  })
  const productProfit = computed(() => {
    const triggerPrice = batchDataProfitAndLoss.value.profit_price_trigger * 1
    const entryPrice = props.data.entryPrice * 1
    const result = (new BigNumber(triggerPrice).minus(entryPrice)).div(entryPrice).times(100)
    if (triggerPrice) {
      return format(result, 2, true, true)
    } else {
      return ''
    }
  })
  const productLoss = computed(() => {
    const triggerPrice = batchDataProfitAndLoss.value.loss_price_trigger * 1
    const entryPrice = props.data.entryPrice * 1
    const result = (new BigNumber(triggerPrice).minus(entryPrice)).div(entryPrice).times(100)
    if (triggerPrice) {
      return format(result, 2, true, true)
    } else {
      return ''
    }
  })
  const isDisabledType1 = computed(() => {
    if (batchDataProfitOrLoss.value.price_trigger === '' || batchDataProfitOrLoss.value.price === '' || batchDataProfitOrLoss.value.amount === '' || batchDataProfitOrLoss.value.price_trigger * 1 === props.data.entryPrice * 1) {
      return true
    } else {
      return false
    }
  })
  const isDisabledType2 = computed(() => {
    if (batchDataProfitAndLoss.value.profit_price_trigger === '' || batchDataProfitAndLoss.value.loss_price_trigger === '' || adjustText(batchDataProfitAndLoss.value.profit_price_trigger, 0) || adjustText(batchDataProfitAndLoss.value.loss_price_trigger, 1)) {
      return true
    } else {
      return false
    }
  })
  let isInternalUpdate = false;
  let lastEditedField = null; // 'trigger' 或 'typeValue'
  // 监听price_trigger等字段的变化
  watch(() => ({
    price_trigger: batchDataProfitOrLoss.value.price_trigger,
    amount: batchDataProfitOrLoss.value.amount,
    price_type: batchDataProfitOrLoss.value.price_type,
    price: batchDataProfitOrLoss.value.price
  }), (obj, oldObj) => {
    if (isInternalUpdate) return;
    // 如果是用户直接修改了price_trigger
    if ((orderType.value * 1 === 1 && obj.price_trigger !== oldObj.price_trigger) || (orderType.value * 1 === 2 && obj.price !== oldObj.price)) {
      lastEditedField = 'trigger';
      updateTypeValueFromCalculation();
    }
    // 如果是amount或price_type变化
    else if (obj.amount !== oldObj.amount || obj.price_type !== oldObj.price_type || obj.price !== oldObj.price) {
      updateTypeValueFromCalculation();
    }
  }, { deep: true });
  // 监听price_type_value的变化
  watch(() => batchDataProfitOrLoss.value.price_type_value, (newValue, oldValue) => {
    if (newValue === oldValue || isInternalUpdate) return;
    lastEditedField = 'typeValue';
    updateTriggerFromTypeValue();
  }, { deep: true });
  function updateTriggerFromTypeValue() {
    isInternalUpdate = true;
    try {
      if (!batchDataProfitOrLoss.value.price_type_value) {
        batchDataProfitOrLoss.value.price_trigger = '';
        batchDataProfitOrLoss.value.price = orderType.value * 1 === 1 ? t('市价') : '';
        return;
      }
      const entryPrice = Number(props.data.entryPrice);
      const amount = Number(nowAmount.value.replace(/,/g, ''));
      const quantity = Number(props.data.quantity);
      if (batchDataProfitOrLoss.value.price_type === 1) {
        const profitLossValue = Number(batchDataProfitOrLoss.value.price_type_value);
        const triggerPrice = quantity > 0 
          ? entryPrice + (profitLossValue / amount) 
          : entryPrice - (profitLossValue / amount);
        const result = triggerPrice <= 0 
          ? '' 
          : format(triggerPrice, (pairInfo.value[props.data.symbol] || {}).price_scale, true, true).replace(/,/g, '') || '';
        if (orderType.value * 1 === 1) {
          batchDataProfitOrLoss.value.price_trigger = result
        } else {
          batchDataProfitOrLoss.value.price = result
        }
      } 
      else if (batchDataProfitOrLoss.value.price_type === 2) {
        const posMargin = Number(props.data.posMargin);
        const percentage = Number(batchDataProfitOrLoss.value.price_type_value);
        const profitLossValue = posMargin * (percentage / 100);
        const triggerPrice = quantity > 0 
          ? entryPrice + (profitLossValue / amount) 
          : entryPrice - (profitLossValue / amount);
        const result = triggerPrice <= 0 
          ? '' 
          : format(triggerPrice, (pairInfo.value[props.data.symbol] || {}).price_scale, true, true).replace(/,/g, '') || ''
        if (orderType.value * 1 === 1) {
          batchDataProfitOrLoss.value.price_trigger = result
        } else {
          batchDataProfitOrLoss.value.price = result
        }
      } 
      else if (batchDataProfitOrLoss.value.price_type === 3) {
        const triggerPrice = entryPrice * (1 + (batchDataProfitOrLoss.value.price_type_value / 100));
        const result = format(triggerPrice, (pairInfo.value[props.data.symbol] || {}).price_scale, true, true).replace(/,/g, '');
        if (orderType.value * 1 === 1) {
          batchDataProfitOrLoss.value.price_trigger = result
        } else {
          batchDataProfitOrLoss.value.price = result
        }
      }
      if (orderType.value * 1 === 1) {
        batchDataProfitOrLoss.value.price = t('市价')
      } else {
        batchDataProfitOrLoss.value.price_trigger = nowPrice.value
      }
    } finally {
      setTimeout(() => {
        isInternalUpdate = false;
      }, 0);
    }
  }
  function updateTypeValueFromCalculation() {
    isInternalUpdate = true;
    try {
      const newValue = batchDataProfitOrLoss.value.price_type * 1 === 1 
        ? profitLossNum.value.replace(/,/g, '')
        : (batchDataProfitOrLoss.value.price_type * 1 === 2 
          ? profitLossNumPercent.value.replace(/,/g, '') 
          : productProfitLoss.value.replace(/,/g, ''));
      if (newValue !== batchDataProfitOrLoss.value.price_type_value) {
        batchDataProfitOrLoss.value.price_type_value = newValue;
      }
    } finally {
      setTimeout(() => {
        isInternalUpdate = false;
      }, 0);
    }
  }
  let isProfitUpdate = false;
  let isLossUpdate = false;

  // 止盈相关更新函数
  const updateProfitTypeValueFromCalculation = () => {
    if (isInternalUpdate) return;
    isProfitUpdate = true;
    try {
      const newValue = batchDataProfitAndLoss.value.profit_price_type * 1 === 1 
        ? profitNum.value.replace(/,/g, '') 
        : (batchDataProfitAndLoss.value.profit_price_type * 1 === 2 
          ? profitNumPercent.value.replace(/,/g, '') 
          : productProfit.value.replace(/,/g, ''));
      
      batchDataProfitAndLoss.value.profit_price_type_value = newValue;
    } finally {
      setTimeout(() => {
        isProfitUpdate = false;
      }, 0);
    }
  };

  const updateProfitTriggerFromTypeValue = () => {
    if (isInternalUpdate || isProfitUpdate) return;
    isInternalUpdate = true;
    try {
      const entryPrice = Number(props.data.entryPrice);
      const amount = Number(closableQty.value);
      const quantity = Number(props.data.quantity);
      const priceType = batchDataProfitAndLoss.value.profit_price_type;
      const posMargin = Number(props.data.posMargin);
      const percentage = batchDataProfitAndLoss.value.profit_price_type_value;
      
      let triggerPrice;
      if (priceType * 1 === 3) {
        // 基点类型
        triggerPrice = entryPrice * ((percentage / 100) + 1);
      } else {
        // 绝对值或百分比类型
        const profitLossValue = priceType * 1 === 1 
          ? Number(percentage) 
          : posMargin * (Number(percentage) / 100)
        triggerPrice = quantity > 0 
          ? entryPrice + (profitLossValue / amount) 
          : entryPrice - (profitLossValue / amount);
      }
      batchDataProfitAndLoss.value.profit_price_trigger = triggerPrice <= 0 
        ? '' 
        : format(triggerPrice, (pairInfo.value[props.data.symbol] || {}).price_scale, true, true).replace(/,/g, '') || '';
    } finally {
      setTimeout(() => {
        isInternalUpdate = false;
      }, 0);
    }
  };

  // 止损相关更新函数
  const updateLossTypeValueFromCalculation = () => {
    if (isInternalUpdate) return;
    isLossUpdate = true;
    try {
      const newValue = batchDataProfitAndLoss.value.loss_price_type * 1 === 1 
        ? lossNum.value.replace(/,/g, '') 
        : (batchDataProfitAndLoss.value.loss_price_type * 1 === 2 
          ? lossNumPercent.value.replace(/,/g, '') 
          : productLoss.value.replace(/,/g, ''));
      
      batchDataProfitAndLoss.value.loss_price_type_value = newValue;
    } finally {
      setTimeout(() => {
        isLossUpdate = false;
      }, 0);
    }
  };

  const updateLossTriggerFromTypeValue = () => {
    if (isInternalUpdate || isLossUpdate) return;
    isInternalUpdate = true;
    try {
      const entryPrice = Number(props.data.entryPrice);
      const amount = Number(closableQty.value);
      const quantity = Number(props.data.quantity);
      const priceType = batchDataProfitAndLoss.value.loss_price_type;
      const posMargin = Number(props.data.posMargin);
      const percentage = batchDataProfitAndLoss.value.loss_price_type_value;
      
      let triggerPrice;
      if (priceType * 1 === 3) {
        // 基点类型
        triggerPrice = entryPrice * ((percentage / 100) + 1 );
      } else {
        // 绝对值或百分比类型
        const profitLossValue = priceType * 1 === 1 
          ? Number(percentage) 
          : posMargin * (Number(percentage) / 100)
        triggerPrice = quantity < 0 
          ? entryPrice - (profitLossValue / amount) 
          : entryPrice + (profitLossValue / amount);
      }
      
      batchDataProfitAndLoss.value.loss_price_trigger = triggerPrice <= 0 
        ? '' 
        : format(triggerPrice, (pairInfo.value[props.data.symbol] || {}).price_scale, true, true).replace(/,/g, '') || '';
    } finally {
      setTimeout(() => {
        isInternalUpdate = false;
      }, 0);
    }
  };
  // 止盈相关监听
  watch(() => batchDataProfitAndLoss.value.profit_price_trigger, (newVal, oldVal) => {
    if (newVal === oldVal) return;
    updateProfitTypeValueFromCalculation();
  });
  watch(() => batchDataProfitAndLoss.value.profit_price_type_value, (newVal, oldVal) => {
    if (newVal === oldVal) return;
    updateProfitTriggerFromTypeValue();
  });
  // 止损相关监听
  watch(() => batchDataProfitAndLoss.value.loss_price_trigger, (newVal, oldVal) => {
    if (newVal === oldVal) return;
    updateLossTypeValueFromCalculation();
  });
  watch(() => batchDataProfitAndLoss.value.loss_price_type_value, (newVal, oldVal) => {
    if (newVal === oldVal) return;
    updateLossTriggerFromTypeValue();
  });
  watch(() => type.value, (val) => {
    if (val * 1 === 1) {
      batchDataProfitOrLoss.value.price_trigger = ''
      batchDataProfitOrLoss.value.price = ''
      orderType.value = 1
      batchDataProfitOrLoss.value.price = t('市价')
      batchDataProfitOrLoss.value.amount = nowLc.value
      batchDataProfitOrLoss.value.amount_percent = 100
    } else {
      batchDataProfitAndLoss.value.profit_price_trigger = ''
      batchDataProfitAndLoss.value.loss_price_trigger = ''
    }
  })
  const socketDateAnimation = () => {
    markets.value = marketsObj.value
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  }
  const profitSuccess = ref('')
  const lossSuccess = ref('')
  const profitError = ref('')
  const lossError = ref('')
  const isError = ref(false)
  const isSuccess = ref(false)
  const isTrade = ref(false)
  const tipsFun = () => {
    console.log(lossSuccess.value !== '' && profitSuccess.value === '' && isSuccess.value && isError.value, 'ddddddd')
    if (profitSuccess.value !== '' && lossSuccess.value !== '' && isSuccess.value) {
      useCommon.showMsg('success', t('下单成功'))
    } else if (isError.value && profitSuccess.value === '' && lossSuccess.value === '') {
      const errorTxt = `${t('止盈下单失败：')}${profitError.value}，${t('止损下单失败：')}${lossError.value}`
      useCommon.showMsg('error', errorTxt)
    } else if (profitSuccess.value !== '' && lossSuccess.value === '' && isSuccess.value && isError.value) {
      const errorTxt = `${profitSuccess.value}，${t('止损下单失败：')}${lossError.value}`
      useCommon.showMsg('error', errorTxt)
    } else if (lossSuccess.value !== '' && profitSuccess.value === '' && isSuccess.value && isError.value) {
      const errorTxt = `${t('止盈下单失败：')}${profitError.value}，${lossSuccess.value}`
      useCommon.showMsg('error', errorTxt)
    }
    emit('close')
    profitSuccess.value = lossSuccess.value = profitError.value = lossError.value = ''
  }
  const orderParmas = async(quantity, price, triggerPrice, orderParamsType) => {
    const entryPrice = props.data.entryPrice * 1
    if (entryPrice * 1 === triggerPrice * 1) {
      useCommon.showMsg('error', t('触发价格不能等于开仓均价'))
      return false
    }
    let positionType = ''
    if (type.value * 1 === 1) {
      if (((Number(props.data.quantity) > 0 && triggerPrice < entryPrice) || (Number(props.data.quantity) < 0 && triggerPrice > price)) && orderType.value * 1 === 1) { // 市价止损
        positionType = 'stop'
      } else if (((Number(props.data.quantity) > 0 && triggerPrice > entryPrice) || (Number(props.data.quantity) < 0 && triggerPrice < price)) && orderType.value * 1 === 1) { // 市价止盈
        positionType = 'take-profit'
      } else if (((Number(props.data.quantity) > 0 && triggerPrice < entryPrice) || (Number(props.data.quantity) < 0 && triggerPrice > price)) && orderType.value * 1 === 2) { // 限价止损
        positionType = 'stop-limit'
      } else if (((Number(props.data.quantity) > 0 && triggerPrice > entryPrice) || (Number(props.data.quantity) < 0 && triggerPrice < price)) && orderType.value * 1 === 2) { // 限价止盈
        positionType = 'take-profit-limit'
      }
    } else {
      if (orderParamsType * 1 === 1) { // 市价止损
        positionType = 'stop'
      } else if (orderParamsType * 1 === 0) { // 市价止盈
        positionType = 'take-profit'
      }
    }
    const params = {
      symbol: props.data.symbol,
      quantity: Number(props.data.quantity) > 0 ? quantity * -1 : quantity * 1,
      price,
      type: positionType,
      market: 'lpc',
      positionMerge: props.data.mergeMode,
      marginMethod: props.data.marginMethod,
      leverage: Number(props.data.leverage),
      close: true,
      positionId: props.data.id,
      trigger_price: triggerPrice
    }
    isTrade.value = true
    const { data, error } = await openOrder(params)
    if (data) {
      isTrade.value = false
      if (type.value * 1 === 1) {
        emit('close')
        useCommon.showMsg('success', t('下单成功'))
      } else {
        isSuccess.value = true
        if (orderParamsType * 1 === 0) { // 止盈
          profitSuccess.value = t('止盈下单成功')
        } else if (orderParamsType * 1 === 1) { // 止损
          lossSuccess.value = t('止损下单成功')
        }
      }
    } else {
      isTrade.value = false
      if (type.value * 1 === 1) {
        useCommon.showMsg('error', useCommon.err(error.code, error))
      } else {
        isError.value = true
        if (orderParamsType * 1 === 0) { // 止盈
          profitError.value = useCommon.err(error.code, error)
        } else if (orderParamsType * 1 === 1) { // 止损
          lossError.value = useCommon.err(error.code, error)
        }
      }
    }
  }
  const confirmType1 = async() => {
    const price = orderType.value * 1 === 1 ? nowPrice.value : batchDataProfitOrLoss.value.price
    const triggerPrice = batchDataProfitOrLoss.value.price_trigger
    await orderParmas(nowAmount.value.replace(/,/g, ''), price, triggerPrice)
  }
  const confirmType2 = async() => {
    await orderParmas(quantity.value, nowPrice.value, batchDataProfitAndLoss.value.profit_price_trigger, 0)
    await orderParmas(quantity.value, nowPrice.value, batchDataProfitAndLoss.value.loss_price_trigger, 1)
    setTimeout(async() => {
      await tipsFun()
    }, 0)
  }
  onMounted(() => {
    visible.value = props.isShow
    batchDataProfitOrLoss.value.amount_percent = 100
    socketDateAnimation()
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  })
  onBeforeUnmount(() => {
    cancelAllTicker()
    requestAnimationFrameInterval.value && cancelAnimationFrame(requestAnimationFrameInterval.value)
  })
</script>
<style lang="scss" scoped>
  .el-dialog{
    &.check-stop_dialog{
      .check-stop-tab{
        margin-top:-12px;
        ul{
          li{
            position:relative;
            font-size:18px;
            cursor:pointer;
            padding-bottom:8px;
            margin-right:20px;
            @include color(tc-secondary);
            &.active{
              @include color(tc-primary);
              &:after{
                content: '';
                position:absolute;
                bottom:0;
                left:50%;
                margin-left:-7px;
                width:14px;
                height:2px;
                @include bg-color(theme);
              }
            }
          }
        }
      }
      .check-stop-wrap{
        padding-top:16px;
        .side-tag{
          padding:2px 4px;
          font-size:12px;
          border-radius:2px;
          &.fit-rise{
            background-color:rgba(59, 193, 137, 0.1);
          }
          &.fit-fall{
            background-color:rgba(255, 98, 98, 0.1);
          }
        }
        .lever-block{
          font-size:14px;
          padding:4px 8px;
          border-radius:2px;
          @include bg-color(bg-quaternary);
        }
        .check-stop-input{
          padding-bottom:12px;
          position:relative;
          .pos-tips{
            position:absolute;
            bottom:-10px;
            left:0;
          }
        }
      }
    }
  }
</style>