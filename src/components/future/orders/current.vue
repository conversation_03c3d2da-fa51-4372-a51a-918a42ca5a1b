<template>
  <div class="tab-box">
    <BoxXOverflow v-if="!isOrder" class="flex-box">
      <li :class="{'active': curType * 1 === 1}" @click="curType = 1">
        {{ $t('限价') }}｜{{ $t('市价') }}
        <span v-if="limitMarketLen > 0">({{ limitMarketLen }})</span>
      </li>
      <li :class="{'active': curType * 1 === 2}" @click="curType = 2">
        {{ $t('止盈止损') }}
        <span v-if="stopProfitLen > 0">({{ stopProfitLen }})</span>
      </li>
      <li :class="{'active': curType * 1 === 3}" @click="curType = 3">
        {{ $t('计划委托') }}
        <span v-if="planLen > 0">({{ planLen }})</span>
      </li>
      <li :class="{'active': curType * 1 === 4}" @click="curType = 4">
        {{ $t('跟踪委托') }}
        <span v-if="trailingStopLen > 0">({{ trailingStopLen }})</span>
      </li>
    </BoxXOverflow>
  </div>
  <div class="ordersCont">
    <OrderstableBox v-if="isLogin" :isLoading="isLoading" :mSortList="mSortList" :headers="headersList" :list="filterCurrentList">
      <template #createTime="scope">
        <span class="fit-tc-secondary" style="text-align:left;">{{ timeFormat(scope.data.createTime, 'yyyy-MM-dd hh:mm:ss') }}</span>
      </template>
      <template #product="scope">
        <NuxtLink :to="`/${locale}/future/${scope.data.product}`" class="fit-tc-primary">
          <span>{{ scope.data.product.replace('_SWAP', '').replace('_', '') }}</span>
        </NuxtLink>
      </template>
      <template #side="scope">
        <span v-if="curType * 1 === 2"
          :class="scope.data.type === 'take-profit-limit' || scope.data.type === 'take-profit' ? 'fit-rise' : 'fit-fall'">
          {{ Number(scope.data.quantity) < 0 ? $t('平多') : $t('平空') }}/{{ $t(PROFIT_LOSS_MAP[scope.data.type]) }}
        </span>
        <span v-else :class="{'fit-rise': !scope.data.close && Number(scope.data.quantity) > 0 || scope.data.close && Number(scope.data.quantity) > 0, 'fit-fall': !scope.data.close && Number(scope.data.quantity) < 0 || scope.data.close && Number(scope.data.quantity) < 0}">
          <template v-if="!scope.data.close && Number(scope.data.quantity) < 0">{{ $t('开空') }}</template>
          <template v-if="!scope.data.close && Number(scope.data.quantity) > 0">{{ $t('开多') }}</template>
          <template v-if="scope.data.close && Number(scope.data.quantity) > 0">{{ $t('平空') }}</template>
          <template v-if="scope.data.close && Number(scope.data.quantity) < 0">{{ $t('平多') }}</template>
        </span>
      </template>
      <template #price="scope">
        <span>{{ scope.data.price ? format(scope.data.price, (pairInfo[scope.data.product] || {}).price_scale, true) : $t('市价') }}</span>
      </template>
      <template #triggerValue="scope">
        <span v-if="curType * 1 === 4">{{ format(scope.data.triggerValue / 100, 2, true) }} %</span>
        <span v-else>{{ format(scope.data.triggerValue, (pairInfo[scope.data.product] || {}).price_scale, true) }}</span>
      </template>
      <template #quantity="scope">
        <span>{{ format((Number(scope.data.quantity) < 0 ? -Number(scope.data.quantity) : Number(scope.data.quantity)), (pairInfo[scope.data.product] || {}).quantity_scale, true) }}</span>
      </template>
      <template #status="scope">
        <span class="fit-tc-secondary">{{ $t(ORDER_STATUS_MAP[scope.data.status]) }}</span>
      </template>
      <template #type="scope">
        <span :class="{'fit-rise': !scope.data.close && Number(scope.data.quantity) > 0 || scope.data.close && Number(scope.data.quantity) > 0, 'fit-fall': !scope.data.close && Number(scope.data.quantity) < 0 || scope.data.close && Number(scope.data.quantity) < 0}">{{ curType * 1 === 2 ? $t('止盈止损') : (curType * 1 === 3 ? $t('计划委托') : (curType * 1 === 4 ? $t('跟踪委托') : (scope.data.type === 'limit' ? $t('限价') : $t('市价')))) }}</span>
      </template>
      <template #orderId="scope">
        <span v-loading="isLoadingData[scope.data.orderId]" class="fit-theme cursor-pointer" @click="cancelOrder(scope.data)">{{ $t('撤单') }}</span>
      </template>
    </OrderstableBox>
    <div v-else class="no-login-cont-box">
      <NoLoginCont />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import BoxXOverflow from '~/components/common/BoxXOverflow.vue' 
  import BoxNoData from '~/components/common/BoxNoData.vue'
  import NoLoginCont from '~/components/common/NoLoginCont.vue'
  import OrderstableBox from '~/components/common/OrderstableBox.vue'
  import { timeFormat, format } from '~/utils'
  import { ORDER_STATUS_MAP, PROFIT_LOSS_MAP } from '~/config'
  import { useCommonData } from '~/composables/index'
  import { deleteDetail } from '~/api/order'
  import { commonStore } from '~/stores/commonStore'
  const store = commonStore()
  const { locale, t } = useI18n()
  const { pairInfo } = storeToRefs(store)
  const useCommon = useCommonData()
  const props = defineProps({
    isOrder: {
      type: Boolean,
      default: true
    },
    defaultCurType: {
      type: Number,
      default: 1
    },
    isLoading: {
      type: Boolean,
      default: false
    },
    currentList: {
      type: Object,
      default(){
        return []
      }
    },
    pair: {
      type: String,
      default: ''
    },
    isLogin: {
      type: Boolean,
      default: false
    }
  })
  const curType = ref(1)
  const emit = defineEmits(['getOrderList', 'checkCurType'])
  const mSortList = computed(() => {
    return [
      { text: t('合约'), key: 'product', align: 'left', wid: '',style: 'auto' },
      { text: t('状态'), key: 'status', align: 'left', wid: '',style: 'auto' },
      { text: t('方向'), key: 'side', align: 'left', wid: '',style: 'auto' },
      { text: t('委托量'), key: 'quantity', align: 'left', wid: '',style: 'auto' },
      { text: t('委托价格'), key: 'price', align: 'left', wid: '',style: 'auto', hide: curType.value * 1 === 4 },
      { text: curType.value * 1 === 4 ? t('回调幅度') : t('触发价格'), key: 'triggerValue', align: 'left', wid: '',style: 'auto', hide: curType.value * 1 === 1 || curType.value * 1 === 4 },
      { text: t('时间'), key: 'createTime', align: 'left', wid: '',style: 'auto' },
      { text: t('委托类型'), key: 'type', align: 'left', wid: '',style: 'auto' },
      { text: t('委托操作'), key: 'orderId', align: 'right', wid: '',style: 'auto' }
    ]
  })
  const headersList = computed(() => {
    return [
      { text: t('时间'), key: 'createTime', align: 'left', wid: '',style: 'auto' },
      { text: t('合约'), key: 'product', align: 'left', wid: '',style: 'auto' },
      { text: t('方向'), key: 'side', align: 'center', wid: '',style: 'auto' },
      { text: t('委托量'), key: 'quantity', align: 'center', wid: '',style: 'auto' },
      { text: t('委托价格'), key: 'price', align: 'center', wid: '',style: 'auto', hide: curType.value * 1 === 4 },
      { text: curType.value * 1 === 4 ? t('回调幅度') : t('触发价格'), key: 'triggerValue', align: 'left', wid: '',style: 'auto', hide: curType.value * 1 === 1 },
      { text: t('委托类型'), key: 'type', align: 'center', wid: '',style: 'auto' },
      { text: t('状态'), key: 'status', align: 'center', wid: '',style: 'auto' },
      { text: t('委托操作'), key: 'orderId', align: 'right', wid: '',style: 'auto' }
    ]
  })
  const isLoadingData = ref({})
  const allDataLen = ref(0)
  const limitMarketLen = ref(0)
  const stopProfitLen = ref(0)
  const planLen = ref(0)
  const trailingStopLen = ref(0)
  watch(() => props.currentList, (val) => {
    allDataLen.value = val.length || 0
    limitMarketLen.value = val.filter((item) => {
      return item.type === 'limit' || item.type === 'market'
    }).length || 0
    stopProfitLen.value = val.filter((item) => {
      return (item.type === 'stop' || item.type === 'stop-limit' || item.type === 'take-profit' || item.type === 'take-profit-limit')
    }).length || 0
    planLen.value = val.filter((item) => {
      return item.type === 'trigger' || item.type === 'trigger-limit'
    }).length || 0
    trailingStopLen.value = val.filter((item) => {
      return item.type === 'trailing-stop'
    }).length || 0
  }, {
    deep: true,
    immediate: true
  })
  watch(() => curType.value, (val) => {
    emit('checkCurType', val)
  }, {
    immediate: true
  })
  const filterCurrentList = computed(() => {
    let result = []
    if (curType.value * 1 === 2) {
      result = props.currentList.filter((item) => {
        return (item.type === 'stop' || item.type === 'stop-limit' || item.type === 'take-profit' || item.type === 'take-profit-limit') && item.flags * 1 !== 100
      })
    } else if (curType.value * 1 === 3) {
      result = props.currentList.filter((item) => {
        return item.type === 'trigger' || item.type === 'trigger-limit'
      })
    } else if (curType.value * 1 === 4) {
      result = props.currentList.filter((item) => {
        return item.type === 'trailing-stop'
      })
    } else if (curType.value * 1 === 1) {
      result = props.currentList.filter((item) => {
        return item.type === 'limit' || item.type === 'market'
      })
    } else {
      result = props.currentList
    }
    console.log(result)
    return result
  })
  const cancelOrder = async(row) => {
    isLoadingData.value[row.orderId] = true
    const { data, error } = await deleteDetail({
       id: row.orderId
    })
    if (data) {
      isLoadingData.value[row.orderId] = false
      emit('getOrderList')
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  onBeforeMount(() => {
    curType.value = props.defaultCurType
  })
</script>

<style scoped lang="scss">
  .no-login-cont-box{
    height:100%;
    display:flex;
    align-items:center;
    justify-content: center;
  }
  .tab-box{
    padding:10px 16px 0;
    li{
      font-size:14px;
      padding:6px 10px;
      border-radius:6px;
      margin-right:12px;
      border:1px solid;
      cursor:pointer;
      @include border-color(border);
      @include color(tc-secondary);
      &.active{
        @include color(tc-primary);
        @include border-color(tc-primary);
      }
    }
  }
  .ordersCont{
    height:calc(100% - 46px);
    overflow:auto;
  }
  @include mb {
    .ordersCont{
      padding:0 20px;
    }
  }
</style>