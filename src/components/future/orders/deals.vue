<template>
  <div class="ordersCont">
    <OrderstableBox :isLoading="isLoading" :mSortList="mSortList" :headers="headersList" :list="currentList">
      <template #time="scope">
        <span class="fit-tc-secondary" style="text-align:left;">{{ timeFormat(scope.data.time, 'yyyy-MM-dd hh:mm:ss') }}</span>
      </template>
      <template #product="scope">
        <NuxtLink :to="`/${locale}/future/${scope.data.product}`" class="fit-tc-primary">
          <span>{{ scope.data.product.replace('_SWAP', '').replace('_', '') }}</span>
        </Nuxtlink>
      </template>
      <template #side="scope">
        <span :class="{'fit-rise': Number(scope.data.quantity) >= 0, 'fit-fall': Number(scope.data.quantity) < 0}">{{ Number(scope.data.quantity) < 0 ? $t('卖出') : $t('买入') }}</span>
      </template>
      <template #price="scope">
        <span>{{ format(scope.data.price, (pairInfo[scope.data.product] || {}).price_scale, true) }}</span>
      </template>
      <template #profit="scope">
        <span :class="{'fit-rise': Number(scope.data.profit) >= 0, 'fit-fall': Number(scope.data.profit) < 0}">{{ format(scope.data.profit, (pairInfo[scope.data.product] || {}).price_scale, true) }}</span>
      </template>
      <template #F="scope">
        <span>{{ format((Number(scope.data.quantity) < 0 ? (Number(scope.data.price) * -Number(scope.data.quantity)) : (Number(scope.data.price) * Number(scope.data.quantity))), Number((pairInfo[scope.data.product] || {}).price_scale), true) }}</span>
      </template>
      <template #fees="scope">
        <div class="flex-box space-between" style="width:100%;">
          <div class="flex-box">
            <div class="mg-r4">
              {{ format(totalFeesValue(scope.data.fees), 4, true) || '--' }}
            </div>
            <el-tooltip
              :content="$t('手续费详情')"
              placement="top">
              <div @click="showOrderDetails(scope.data)" class="flex-box">
                <MonoOrder size="16" class="cursor-pointer fit-theme" />
              </div>
            </el-tooltip>
          </div>
        </div>
      </template>
      <template #comment="scope">
        <el-tooltip
          :content="$t('成交详情')"
          placement="top">
          <div @click="showDealDetails(scope.data)" class="flex-box">
            <MonoOrder size="16" class="cursor-pointer fit-theme" />
          </div>
        </el-tooltip>
      </template>
      <template #quantity="scope">
        <span>{{ format((Number(scope.data.quantity) < 0 ? -Number(scope.data.quantity) : Number(scope.data.quantity)), (pairInfo[scope.data.product] || {}).quantity_scale, true) }}</span>
      </template>
      <template #type="scope">
        <span :class="{'fit-rise': scope.data.side === 'buy', 'fit-fall': scope.data.side === 'sell'}">{{ scope.data.type * 1 === 1 ? $t('限价单') : $t('市价单') }}</span>
      </template>
      <template #orderId="scope">
        <span>{{ scope.data.orderId }}</span>
      </template>
    </OrderstableBox>
  </div>
  <FillsListDialog 
    v-if="isShowOrderDetail"
    :isShow="isShowOrderDetail"
    :data="curDetail"
    @close="isShowOrderDetail = false" />
  <OrderDetailDialog
    v-if="isShowDealsDetail"
    :isShow="isShowDealsDetail"
    :isDeal="true"
    :data="curDealDetail.order"
    @close="isShowDealsDetail = false"
    @goDealList="emit('goDealList')"
  />
</template>

<script lang="ts" setup>
  import { ElTooltip } from 'element-plus'
  import BoxNoData from '~/components/common/BoxNoData.vue'
  import NoLoginCont from '~/components/common/NoLoginCont.vue'
  import OrderstableBox from '~/components/common/OrderstableBox.vue'
  import MonoOrder from '~/components/common/icon-svg/MonoOrder.vue'
  import FillsListDialog from '~/components/exchange/orders/FillsListDialog.vue'
  import OrderDetailDialog from '~/components/exchange/orders/OrderDetailDialog.vue'
  import { timeFormat, format } from '~/utils'
  import { ORDER_STATUS_MAP } from '~/config'
  import { useCommonData } from '~/composables/index'
  import { deleteDetail, getFillsDetail } from '~/api/order'
  import { commonStore } from '~/stores/commonStore'
  const store = commonStore()
  const { locale, t } = useI18n()
  const { pairInfo } = storeToRefs(store)
  const useCommon = useCommonData()
  const props = defineProps({
    isLoading: {
      type: Boolean,
      default: false
    },
    currentList: {
      type: Object,
      default(){
        return []
      }
    },
    isLogin: {
      type: Boolean,
      default: false
    }
  })
  const emit = defineEmits(['getOrderList'])
  const mSortList = ref([
    { text: t('合约'), key: 'product', align: 'left', wid: '',style: 'auto' },
    { text: '&nbsp;', key: 'comment', align: 'left', wid: '', style: 'auto' },
    { text: t('方向'), key: 'side', align: 'left', wid: '',style: 'auto' },
    { text: t('成交量'), key: 'quantity', align: 'left', wid: '',style: 'auto' },
    { text: t('成交均价'), key: 'price', align: 'left', wid: '',style: 'auto' },
    { text: t('盈亏'), key: 'profit', align: 'left', wid: '',style: 'auto' },
    { text: t('手续费(USDT)'), key: 'fees', align: 'left', wid: '',style: 'auto' },
    { text: t('时间'), key: 'time', align: 'right', wid: 'flex-2',style: 'auto' },
  ])
  const headersList = ref([
    { text: t('时间'), key: 'time', align: 'left', wid: '',style: 'auto' },
    { text: t('合约'), key: 'product', align: 'left', wid: 'flex-2',style: 'auto' },
    { text: t('方向'), key: 'side', align: 'center', wid: '',style: 'auto' },
    { text: t('成交量'), key: 'quantity', align: 'center', wid: '',style: 'auto' },
    { text: t('成交均价'), key: 'price', align: 'center', wid: '',style: 'auto' },
    { text: t('盈亏'), key: 'profit', align: 'center', wid: '',style: 'auto' },
    { text: t('手续费(USDT)'), key: 'fees', align: 'left', wid: 'flex-2',style: 'auto' },
    { text: '', key: 'comment', align: 'right', wid: '', style: 'auto' }
  ])
  const totalFeesValue = (fees) => {
    let num = 0
    fees.forEach((item) => {
      num += Number(item.value) > 0 ? Number(item.value) : Number(item.amount)
    })
    return num
  }
  const cancelOrder = async(row) => {
    console.info(row.i, 'dhueuehuduheuheuuhe')
    const { data, error } = await deleteDetail({
       id: row.i
    })
    if (data) {
      emit('getOrderList')
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const isShowOrderDetail = ref(false)
  const curDetail = ref({})
  const showOrderDetails = (row) => {
    curDetail.value = row
    isShowOrderDetail.value = true
  }
  const isShowDealsDetail = ref(false)
  const curDealDetail = ref({})
  const showDealDetails = async(row) => {
    await getFillDetailFun(row)
    isShowDealsDetail.value = true
  }
  const getFillDetailFun = async(row) => {
    const { data, error } = await getFillsDetail({
      fill_id: row.fill_id
    })
    if (data) {
      curDealDetail.value = data
      console.log(curDetail.value, 'djdiejdijeidjeijie')
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
</script>

<style scoped lang="scss">
  .ordersCont{
    height:100%;
    overflow:auto;
  }
  @include mb {
    .ordersCont{
      padding:0 20px;
    }
  }
</style>