<template>
  <div class="ordersCont">
    <OrderstableBox v-if="isLogin" :isLoading="isLoading" :mSortList="mSortList" :headers="headersList" :list="currentList">
      <template #triggerValue="scope">
        <span v-if="scope.data.type === 'trailing-stop'" class="fit-tc-secondary" style="text-align:left;">--</span>
        <span v-else class="fit-tc-secondary" style="text-align:left;">{{ format(scope.data.triggerValue, (pairInfo[scope.data.product] || {}).price_scale, true) }}</span>
      </template>
      <template #triggerValueP="scope">
        <span v-if="scope.data.type === 'trailing-stop'" class="fit-tc-secondary" style="text-align:left;">{{ format(scope.data.triggerValue / 100, 2, true) }}%</span>
        <span v-else class="fit-tc-secondary" style="text-align:left;">--</span>
      </template>
      <template #createTime="scope">
        <span class="fit-tc-secondary" style="text-align:left;">{{ timeFormat(scope.data.createTime, 'yyyy-MM-dd hh:mm:ss') }}</span>
      </template>
      <template #product="scope">
        <NuxtLink :to="`/${locale}/future/${scope.data.product}`" class="fit-tc-primary">
          <span>{{ scope.data.product.replace('_SWAP', '').replace('_', '') }}</span>
        </NuxtLink>
      </template>
      <template #side="scope">
        <span v-if="(scope.data.type === 'stop' || scope.data.type === 'stop-limit' || scope.data.type === 'take-profit' || scope.data.type === 'take-profit-limit')"
          :class="scope.data.type === 'take-profit-limit' || scope.data.type === 'take-profit' ? 'fit-rise' : 'fit-fall'">
          {{ Number(scope.data.quantity) < 0 ? $t('平多') : $t('平空') }}/{{ $t(PROFIT_LOSS_MAP[scope.data.type]) }}
        </span>
        <span v-else :class="{'fit-rise': !scope.data.close && Number(scope.data.quantity) > 0 || scope.data.close && Number(scope.data.quantity) > 0, 'fit-fall': !scope.data.close && Number(scope.data.quantity) < 0 || scope.data.close && Number(scope.data.quantity) < 0}">
          <template v-if="!scope.data.close">
            <template v-if="Number(scope.data.quantity) < 0">{{ $t('开空') }}</template>
            <template v-if="Number(scope.data.quantity) > 0">{{ $t('开多') }}</template>
          </template>
          <template v-if="scope.data.close">
            <template v-if="Number(scope.data.quantity) > 0">{{ scope.data.origin * 1 === -1 ? $t('爆仓空') : (scope.data.origin * 1 === -2 ? $t('减仓空') : $t('平空')) }}</template>
            <template v-if="Number(scope.data.quantity) < 0">{{ scope.data.origin * 1 === -1 ? $t('爆仓多') : (scope.data.origin * 1 === -2 ? $t('减仓多') : $t('平多')) }}</template>
          </template>
        </span>
      </template>
      <template #price="scope">
        <span>{{ scope.data.type === 'trailing-stop' ? '--' : (scope.data.price ? format(scope.data.price, (pairInfo[scope.data.product] || {}).price_scale, true) : $t('市价')) }}</span>
      </template>
      <template #quantity="scope">
        <span>{{ format((Number(scope.data.quantity) < 0 ? -Number(scope.data.quantity) : Number(scope.data.quantity)), (pairInfo[scope.data.product] || {}).quantity_scale, true) }}</span>
      </template>
      <template #status="scope">
        <div class="flex-box space-between" style="width:100%;">
          <span class="fit-tc-secondary mg-r4">{{ $t(ORDER_STATUS_MAP[scope.data.status]) }}</span>
          <el-tooltip
            :content="$t('详情')"
            placement="top"
            >
            <div @click="showOrderDetails(scope.data)" class="flex-box">
              <MonoOrder size="16" class="cursor-pointer fit-theme" />
            </div>
          </el-tooltip>
        </div>
      </template>
      <template #statusM="scope">
        <div :class="{'pd-b12': scope.data.close}">
          <div class="flex-box space-between" :class="{'pd-b12': scope.data.close}" style="width:100%;">
            <span class="fit-tc-secondary mg-r4">{{ $t(ORDER_STATUS_MAP[scope.data.status]) }}</span>
            <el-tooltip
              :content="$t('详情')"
              placement="top"
              >
              <div @click="showOrderDetails(scope.data)" class="flex-box">
                <MonoOrder size="16" class="cursor-pointer fit-theme" />
              </div>
            </el-tooltip>
          </div>
          <div v-if="scope.data.close" class="flex-box space-between" style="position:absolute;left:20px;right:20px;">
            <span class="flex-box fit-tc-secondary">{{ $t('盈亏') }}</span>
            <p :class="{'fit-rise': Number(scope.data.profit) >= 0, 'fit-fall': Number(scope.data.profit) < 0}">{{ format(scope.data.profit, (pairInfo[scope.data.product] || {}).price_scale, true) }}</p>
          </div>
        </div>
      </template>
      <template #profit="scope">
        <span v-if="scope.data.close" :class="{'fit-rise': Number(scope.data.profit) >= 0, 'fit-fall': Number(scope.data.profit) < 0}">{{ format(scope.data.profit, (pairInfo[scope.data.product] || {}).price_scale, true) }}</span>
      </template>
      <template #type="scope">
        <span v-if="(scope.data.type === 'stop' || scope.data.type === 'stop-limit' || scope.data.type === 'take-profit' || scope.data.type === 'take-profit-limit')"
          :class="scope.data.type === 'take-profit-limit' || scope.data.type === 'take-profit' ? 'fit-rise' : 'fit-fall'"
          >
          {{ $t('止盈止损') }}
        </span>
        <span
          v-else
          :class="{
            'fit-rise': !scope.data.close && Number(scope.data.quantity) > 0 || scope.data.close && Number(scope.data.quantity) > 0,
            'fit-fall': !scope.data.close && Number(scope.data.quantity) < 0 || scope.data.close && Number(scope.data.quantity) < 0}"
          >
          {{ ( scope.data.type === 'trigger' || scope.data.type === 'trigger-limit' ? $t('计划委托') : (scope.data.type ==='trailing-stop' ? $t('跟踪委托') : (scope.data.type === 'limit' ? $t('限价') : $t('市价')))) }}</span>
      </template>
      <template #executedQty="scope">
        <span class="fit-tc-secondary">{{ format((Number(scope.data.executedQty) < 0 ? -Number(scope.data.executedQty) : scope.data.executedQty), (pairInfo[scope.data.product] || {}).quantity_scale, true) }}</span>
      </template>
      <template #executedCost="scope">
        <span>{{ format((Number(scope.data.executedQty) * 1 < 0 ? new BigNumber(-Number(scope.data.executedCost)).div(-Number(scope.data.executedQty)) : new BigNumber(Number(scope.data.executedCost)).div(Number(scope.data.executedQty))), Number((pairInfo[scope.data.product] || {}).price_scale), true) }}</span>
      </template>
    </OrderstableBox>
    <div v-else class="no-login-cont-box">
      <NoLoginCont />
    </div>
  </div>
  <OrderDetailDialog
    v-if="isShowOrderDetail"
    :isShow="isShowOrderDetail"
    :data="curDetail"
    @close="isShowOrderDetail = false"
    @goDealList="emit('goDealList')"
  />
</template>

<script lang="ts" setup>
  import { ElTooltip } from 'element-plus'
  import BigNumber from 'bignumber.js'
  import BoxNoData from '~/components/common/BoxNoData.vue'
  import NoLoginCont from '~/components/common/NoLoginCont.vue'
  import OrderstableBox from '~/components/common/OrderstableBox.vue'
  import OrderDetailDialog from '~/components/exchange/orders/OrderDetailDialog.vue'
  import MonoOrder from '~/components/common/icon-svg/MonoOrder.vue'
  import { timeFormat, format } from '~/utils'
  import { ORDER_STATUS_MAP, PROFIT_LOSS_MAP } from '~/config'
  import { useCommonData } from '~/composables/index'
  import { deleteDetail } from '~/api/order'
  import { futureStore } from '~/stores/futureStore'
  import { commonStore } from '~/stores/commonStore'
  const store = futureStore()
  const { locale, t } = useI18n()
  const publicStore = commonStore()
  const { pairInfo } = storeToRefs(publicStore)
  const useCommon = useCommonData()
  const props = defineProps({
    isLoading: {
      type: Boolean,
      default: false
    },
    currentList: {
      type: Object,
      default(){
        return []
      }
    },
    pair: {
      type: String,
      default: ''
    },
    isLogin: {
      type: Boolean,
      default: false
    }
  })
  const emit = defineEmits(['getOrderList', 'goDealList'])
  
  const mSortList = computed(() => {
    return [
      { text: t('合约'), key: 'product', align: 'left', wid: '',style: 'auto' },
      { text: t('状态'), key: 'statusM', align: 'left', wid: '',style: 'auto' },
      { text: t('方向'), key: 'side', align: 'left', wid: '',style: 'auto' },
      { text: t('委托量'), key: 'quantity', align: 'left', wid: '',style: 'auto' },
      { text: t('成交量'), key: 'executedQty', align: 'center', wid: '',style: 'auto' },
      { text: t('委托价格'), key: 'price', align: 'left', wid: '',style: 'auto' },
      { text: t('成交价格'), key: 'executedCost', align: 'center', wid: '',style: 'auto' },
      { text: t('时间'), key: 'createTime', align: 'left', wid: '',style: 'auto' },
      { text: t('委托类型'), key: 'type', align: 'left', wid: '',style: 'auto' }
    ]
  })
  const headersList = computed(() => {
    return [
      { text: t('时间'), key: 'createTime', align: 'left', wid: 'flex-2',style: 'auto' },
      { text: t('合约'), key: 'product', align: 'left', wid: '',style: 'auto' },
      { text: t('方向'), key: 'side', align: 'center', wid: '',style: 'auto' },
      { text: t('委托量'), key: 'quantity', align: 'center', wid: '',style: 'auto' },
      { text: t('成交量'), key: 'executedQty', align: 'center', wid: '',style: 'auto' },
      { text: t('委托价格'), key: 'price', align: 'center', wid: '',style: 'auto' },
      { text: t('成交价格'), key: 'executedCost', align: 'center', wid: '',style: 'auto' },
      { text: t('委托类型'), key: 'type', align: 'center', wid: '',style: 'auto' },
      { text: t('盈亏'), key: 'profit', align: 'center', wid: '',style: 'auto' },
      { text: t('状态'), key: 'status', align: 'left', wid: 'flex-2',style: 'auto' }
    ]
  })
  const isLoadingData = ref({})
  const cancelOrder = async(row) => {
    isLoadingData.value[row.orderId] = true
    const { data, error } = await deleteDetail({
       id: row.orderId
    })
    if (data) {
      isLoadingData.value[row.orderId] = false
      emit('getOrderList')
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const curDetail = ref({})
  const isShowOrderDetail = ref(false)
  const showOrderDetails = (row) => {
    curDetail.value = row
    isShowOrderDetail.value = true
  }
</script>

<style scoped lang="scss">
  .no-login-cont-box{
    height:100%;
    display:flex;
    align-items:center;
    justify-content: center;
  }
  .ordersCont{
    height:100%;
    overflow:auto;
  }
  @include mb {
    .ordersCont{
      padding:0 20px;
    }
  }
</style>