<template>
  <div v-if="!isMobile && isLoadingS">
    <el-table v-if="filterOrders.length > 0" :data="filterOrders" class="stopLoss-table">
      <el-table-column prop="pi" :label="$t('合约')" align="left" width="220">
        <template #default="scope">
          <span :class="scope.row.type === 'take-profit-limit' || scope.row.type === 'take-profit' ? 'fit-rise' : 'fit-fall'">
            {{ Number(scope.row.quantity) < 0 ? $t('平多') : $t('平空') }}/{{ $t(PROFIT_LOSS_MAP[scope.row.type]) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="tp" :label="$t('触发价格')" align="right" width="128">
        <template #default="scope">
          <span class="font-size-14 fit-tc-primary">{{ scope.row.triggerValue }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="pr" :label="$t('委托价格')" align="right">
        <template #default="scope">
          <span class="font-size-14 fit-tc-primary">{{ scope.row.price ? format(scope.row.price, (pairInfo[scope.row.product] || {}).price_scale, true) : $t('市价') }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="q" :label="$t('委托量')" align="right">
        <template #default="scope">
          <span
            class="font-size-14 fit-tc-primary"
          >{{ Number(scope.row.quantity) < 0 ? -Number(scope.row.quantity) : Number(scope.row.quantity) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('预估盈亏')" align="right">
        <template #default="scope">
          <span :class="getItemProfitNumber(scope.row) > 0 ? 'fit-rise' : 'fit-fall'" class="ts-14 tw-5">
            {{ getItemProfitNumber(scope.row) > 0 ? '+' : '' }}{{ format(getItemProfitNumber(scope.row), (pairInfo[scope.row.product] || {}).price_scale,  true) }} {{ getSymbol(scope.row.product) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" :label="$t('创建时间')" align="right">
        <template #default="scope">
          <span class="font-size-14 fit-tc-secondary">{{ timeFormat(scope.row.createTime, 'yyyy-MM-dd hh:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="s" :label="$t('状态')" width="120" align="right">
        <template #default="scope">
          <span class="fit-tc-secondary">{{ $t(ORDER_STATUS_MAP[scope.row.status]) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="right" :label="$t('操作')" width="120">
        <template #default="scope">
          <div class="font-size-14">
            <span v-loading="isLoadingData[scope.row.orderId]" class="fit-theme cursor-pointer" @click="cancelOrder(scope.row)">{{ $t('撤单') }}</span>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <div v-if="isMobile && isLoadingS && filterOrders.length > 0">
    <div v-for="(item, index) in filterOrders" :key="index" class="profit-loss-cont-m">
      <div class="symbol-cont flex-box space-between">
        <div class="text-left">
          <p class="fit-tc-primary font-size-14">{{ item.product.replace('_SWAP', '').replace('_', '') }}</p>
          <span
            class="font-size-14"
            :class="item.type === 'take-profit-limit' || item.type === 'take-profit' ? 'fit-rise' : 'fit-fall'"
            >
            {{ Number(item.quantity) < 0 ? $t('平多') : $t('平空') }}/{{ PROFIT_LOSS_MAP[item.type] }}
          </span>
        </div>
        <div class="text-right">
          <div class="fit-theme font-size-14 cursor-pointer" @click="cancelOrder(item)">{{ $t('撤单') }}</div>
          <span class="font-size-14 fit-tc-secondary">{{ timeFormat(item.createTime, 'yyyy-MM-dd hh:mm:ss') }}</span>
        </div>
      </div>
      <div class="symbol-info-box flex-box space-between">
        <div class="text-left">
          <p class="fit-tc-secondary">{{ $t('触发价格') }}({{ item.product.split('_')[1] }})</p>
          <span>{{ item.triggerValue }}</span>
        </div>
        <div class="text-right">
          <p class="fit-tc-secondary">{{ $t('预估盈亏') }}</p>
          <span 
            :class="getItemProfitNumber(item) > 0 ? 'fit-rise' : 'fit-fall'"
            class="ts-14 tw-5">
            {{ getItemProfitNumber(item) > 0 ? '+' : '' }}{{ format(getItemProfitNumber(item), (pairInfo[item.product] || {}).price_scale,  true) }} {{ getSymbol(item.product) }}
          </span>
        </div>
      </div>
      <div class="symbol-info-box flex-box space-between">
        <div class="text-left">
          <p class="fit-tc-secondary">{{ $t('委托价格') }}({{ item.product.split('_')[1] }})</p>
          <span class="font-size-14 fit-tc-primary">{{ item.price ? format(item.price, (pairInfo[item.product] || {}).price_scale, true) : $t('市价') }}</span>
        </div>
        <div class="text-right">
          <p class="fit-tc-secondary">{{ $t('委托量') }}({{ item.product.split('_')[0] }})</p>
          <span
            class="font-size-14 fit-tc-primary"
          >{{ Number(item.quantity) < 0 ? -Number(item.quantity) : Number(item.quantity) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import BigNumber from 'bignumber.js'
  import { timeFormat, format } from '~/utils'
  import { ElTable, ElTableColumn } from 'element-plus'
  import { commonStore } from '~/stores/commonStore'
  import { deleteDetail }  from '~/api/order'
  import { ORDER_STATUS_MAP, PROFIT_LOSS_MAP } from '~/config'
  const publicStore = commonStore()
  const { isChangeFutureOrder, pairInfo } = storeToRefs(publicStore)
  const { locale, t } = useI18n()
  const props = defineProps({
    scopeRow: {
      type: Object,
      default(){
        return {}
      }
    },
    orders: {
      type: Array,
      default(){
        return []
      }
    }
  })
  const emit = defineEmits(['updateOrderLength'])
  const isLoadingData = ref({})
  const filterOrders = computed(() => {
    return props.orders.filter((arr) => {
      return props.scopeRow.id === arr.positionId
    })
  })
  const requestAnimationFrameInterval = ref(null)
  const getItemProfitNumber = (row) => {
    const price = row.price ? row.price : row.triggerValue
    const orderPrice = props.scopeRow.entryPrice
    const orderAmount = Number(row.quantity) < 0 ? -Number(row.quantity) : Number(row.quantity)
    const num = (new BigNumber(Number(row.quantity) > 0 ? (orderPrice - price) : (price - orderPrice)).multipliedBy(orderAmount).toNumber()).toFixed(4)
    return num
  }
  const getSymbol = (pair) => {
    if (!pair) {
      return '--'
    }
    return pair.split('_')[1]
  }
  const cancelOrder = async(row) => {
    isLoadingData.value[row.orderId] = true
    const { data, error } = await deleteDetail({
       id: row.orderId
    })
    if (data) {
      isLoadingData.value[row.orderId] = false
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  watch(() => filterOrders.value, (val) => {
    emit('updateOrderLength', val.length)
  })
  const isLoadingS = ref(false)
  const isMobile = ref(false)
  const screenWidth = ref(0)
  const updateScreenWidth = () => {
    screenWidth.value = window.innerWidth
    isMobile.value = screenWidth.value <= 768
  }
  onMounted(() => {
    isLoadingS.value = true
    updateScreenWidth()
  })
</script>
<style lang="scss">
.el-table {
  &.stopLoss-table {
    tr {
      &.hover-row {
        td {
          background-color: transparent !important;
        }
      }
      &:hover {
        td {
          background: transparent !important;
        }
      }
      &:last-child {
        td {
          border: 0;
          border-bottom: 1px solid;
          @include border-color(border);
        }
      }
    }
    th {
      padding: 5px 0 !important;
      &:first-child {
        .cell {
          padding-left: 12px !important;
        }
      }
      &:last-child {
        .cell {
          padding-right: 12px !important;
        }
      }
      &.is-leaf {
        border: none;
      }
      .filter-side {
        &:hover {
          .icon-my-filter {
            @include color(theme, 1);
          }
        }
      }
    }
    td {
      border-bottom: 0;
      @include border-color(border);
      &:first-child {
        .cell {
          padding-left: 12px !important;
        }
      }
      &:last-child {
        .cell {
          padding-right: 12px !important;
        }
      }
    }
    .cell {
      padding-top: 0px !important;
      padding-bottom: 0px !important;
      padding-left: 0px !important;
      padding-right: 0px !important;
    }
  }
}
</style>
<style lang="scss" scoped>
  @include mb {
    .profit-loss-cont-m{
      padding:12px 0;
      border-bottom:1px solid;
      @include color(border);
      &:last-child{
        border:0;
      }
      .symbol-cont{
        padding-bottom:2px;
      }
      .symbol-info-box{
        padding-top: 8px;
        p{
          font-size:12px;
        }
        span{
          font-size:14px;
          @include color(tc-primary);
        }
      }
    }
  }
</style>