<template>
  <div v-loading="filterDeals.length === 0" class="exchnage-deals-wrap">
    <div class="deals-row fit-tc-primary head">
      <div class="deals-row__item left">{{ $t('时间') }}</div>
      <div class="deals-row__item right">{{ $t('价格') }}(USDT)</div>
      <div class="deals-row__item right">{{ $t('数量') }}({{ useCommon.getFuturesSymbol(pair, futuresType === 'ipc' ? isCBCUnitUSD : isCBUUnitUSDT) }})
      </div>
    </div>
    <el-scrollbar v-if="filterDeals.length > 0" wrap-class="deals-area" tag="ul">
      <li v-for="(item, index) in filterDeals" :key="index">
        <span class="left">{{ timeFormat(item.t, 'hh:mm:ss') }}</span>
        <span class="right" :class="Number(item.s) < 0 ? 'fit-fall' : 'fit-rise' ">{{ format(item.p, priceScale, true) }}</span>
        <span class="right" :class="Number(item.s) < 0 ? 'fit-fall' : 'fit-rise' ">
          <template v-if="futuresType === 'ipc'">
            {{ format((useCommon.cbuConver(Number(item.q) < 0 ? -Number(item.q) : item.q, isCBUUnitUSDT, item.p)), isCBUUnitUSDT ? priceScale : quantityScale, true) }}
          </template>
          <template v-if="futuresType === 'lpc'">
            {{ format((useCommon.cbuConver(Number(item.q) < 0 ? -Number(item.q) : item.q, isCBUUnitUSDT, item.p)), isCBUUnitUSDT ? priceScale : quantityScale, true) }}
          </template>
        </span>
      </li>
    </el-scrollbar>
    <div v-if="filterDeals.length === 0" style="height:100%;">
      <BoxNoData :text="$t('暂无数据')" />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { format } from '~/utils'
  import BigNumber from 'bignumber.js'
  import BoxNoData from '~/components/common/BoxNoData.vue'
  import { timeFormat } from '~/utils'
  import { ElScrollbar } from 'element-plus'
  import { useCommonData } from '~/composables/index'
  const useCommon = useCommonData()
  const props = defineProps({
    deals: {
      type: Object,
      default () {
        return []
      }
    },
    pair: {
      type: String,
      default: ''
    },
    isCBCUnitUSD: {
      type: Boolean,
      default: false
    },
    isCBUUnitUSDT: {
      type: Boolean,
      default: false
    },
    futuresType: {
      type: String,
      default: ''
    },
    priceScale: {
      default: 0,
      type: [Number, String]
    },
    quantityScale: {
      type: [Number, String],
      default: 0
    }
  })
  const filterDeals = ref([])
  watch(() => props.deals, (val) => {
    if (val.length > 0) {
      filterDeals.value = val.reverse()
    }
  })
  const transfer = (amount) => {
    const value = props.futuresType === 'ipc' ? val.value : 1
    const decimal = props.futuresType === 'ipc' ? 0 : props.priceScale
    return new BigNumber(amount).dividedBy(value).toFixed(decimal, 1)
  }
</script>
<style lang="scss">
@import url('@/assets/style/exchange/deals.scss');
</style>
