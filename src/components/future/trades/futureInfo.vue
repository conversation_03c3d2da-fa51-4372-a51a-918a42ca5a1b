<template>
  <div class="pd-lr24">
    <div class="font-size-16 tw-5 mg-b12 pd-t12 fit-tc-primary">{{ pair.replace('_', '').replace('_SWAP', '') }} {{ $t('永续') }}</div>
    <div class="flex-box space-between align-start mg-b4">
      <div class="fit-tc-secondary font-size-14 tw-4" style="width:160px; white-space:nowrap;">{{ $t('结算货币') }}</div>
      <div class="fit-tc-primary font-size-14 tw-4 text-right">{{ futuresType === 'lpc' ? pair.split('_')[1] : pair.split('_')[0] }}</div>
    </div>
    <div class="flex-box space-between align-start mg-b4">
      <div class="fit-tc-secondary font-size-14 tw-4" style="width:180px; white-space:nowrap;">{{ $t('单笔最小订单量') }}</div>
      <div class="fit-tc-primary font-size-14 tw-4 text-right">{{ format((pairInfo[pair] || {}).min_order_size, (pairInfo[pair] || {}).quantity_scale, true) || 0 }} {{ pair.split('_')[0] }}</div>
    </div>
    <div class="flex-box space-between align-start mg-b4">
      <div class="fit-tc-secondary font-size-14 tw-4" style="width:180px; white-space:nowrap;">{{ $t('单笔最大订单量') }}</div>
      <div class="fit-tc-primary font-size-14 tw-4 text-right">{{ format((pairInfo[pair] || {}).max_order_size, (pairInfo[pair] || {}).quantity_scale, true) || 0 }} {{ pair.split('_')[0] }}</div>
    </div>
    <div class="flex-box space-between align-start mg-b4">
      <div class="fit-tc-secondary font-size-14 tw-4" style="width:160px; white-space:nowrap;">{{ $t('交易对最大委托单数') }}</div>
      <div class="fit-tc-primary font-size-14 tw-4 text-right">{{ format((pairInfo[pair] || {}).limit_order_number, (pairInfo[pair] || {}).price_scale, true) }}</div>
    </div>
    <div class="flex-box space-between align-start mg-b4">
      <div class="fit-tc-secondary font-size-14 tw-4" style="width:160px; white-space:nowrap;">{{ $t('最小变动价位') }}</div>
      <div class="fit-tc-primary font-size-14 tw-4 text-right">{{ format(minPriceChange, (pairInfo[pair] || {}).price_scale, true) || 0 }}</div>
    </div>
    <div class="flex-box space-between align-start mg-b4">
      <div class="fit-tc-secondary font-size-14 tw-4" style="width:160px; white-space:nowrap;">{{ $t('杠杆倍数') }}</div>
      <div class="fit-tc-primary font-size-14 tw-4 text-right">1 ~ {{ (pairInfo[pair] || {}).leverage }}</div>
    </div>
    <div class="flex-box space-between align-start mg-b4">
      <div class="fit-tc-secondary font-size-14 tw-4" style="width:160px; white-space:nowrap;">{{ $t('资金费率时间') }}</div>
      <div class="fit-tc-primary font-size-14 tw-4 text-right">{{ $t('每天08:00 16:00 24:00(UTC+8)') }}</div>
    </div>
    <div class="flex-box space-between align-start mg-b4">
      <div class="fit-tc-secondary font-size-14 tw-4" style="width:160px; white-space:nowrap;">{{ $t('标记价格') }}</div>
      <div class="fit-tc-primary font-size-14 tw-4 text-right">{{ format((futureInfo[pair] || {}).markPrice, (pairInfo[pair] || {}).price_scale, true) || '--' }}</div>
    </div>
    <div class="flex-box space-between align-start mg-b4">
      <div class="fit-tc-secondary font-size-14 tw-4" style="width:160px; white-space:nowrap;">{{ $t('持仓量') }}</div>
      <div class="fit-tc-primary font-size-14 tw-4 text-right">{{ format((ticker || {}).amount, (pairInfo[pair] || {}).quantity_scale, true) }}</div>
    </div>
    <div class="flex-box space-between align-start mg-b4">
      <div class="fit-tc-secondary font-size-14 tw-4" style="width:160px; white-space:nowrap;">{{ $t('24小时成交量') }}</div>
      <div class="fit-tc-primary font-size-14 tw-4 text-right">{{ format((Number((ticker || {}).volume) < 0 ? -Number((ticker || {}).volume) : Number((ticker || {}).volume)), (pairInfo[pair] || {}).quantity_scale, true) || '--' }} {{ futuresType === 'lpc' ? pair.split('_')[0] : pair.split('_')[1] }}</div>
    </div>
    <!-- <div class="flex-box space-between align-start mg-b12">
      <div class="fit-tc-secondary font-size-14 tw-4" style="width:160px;">{{ $t('风险准备金') }}</div>
      <div class="fit-tc-primary font-size-14 tw-4 text-right">11</div>
    </div> -->
    <div class="border-bottom-line__second mg-b12"></div>
    <div class="fit-tc-secondary font-size-14 tw-4 mg-b8">{{ $t('平仓收益') }}</div>
    <div v-if="futuresType === 'lpc'">
      <div class="fit-tc-primary font-size-14 tw-4 mg-b4">{{ $t('多仓: 仓位 * 平仓价位 - 仓位 * 开仓价位') }}</div>
      <div class="fit-tc-primary font-size-14 tw-4 mg-b20">{{ $t('空仓: 仓位 * 开仓价位 - 仓位 * 平仓价位') }}</div>
    </div>
    <div v-else>
      <div class="fit-tc-primary font-size-14 tw-4 mg-b4">{{ $t('多仓: 仓位 / 开仓均价 – 仓位 / 平仓均价') }}</div>
      <div class="fit-tc-primary font-size-14 tw-4 mg-b20">{{ $t('空仓: 仓位 / 平仓均价 - 仓位 / 开仓均价') }}</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { futureStore } from '~/stores/futureStore'
  import { commonStore } from '~/stores/commonStore'
  import { format } from '~/utils'
  const store = futureStore()
  const { futureInfo } = storeToRefs(store)
  const publicStore = commonStore()
  const { pairInfo } = storeToRefs(publicStore)
  const props = defineProps({
    pair: {
      type: String,
      default: ''
    },
    ticker: {
      type: Object,
      default () {
        return {}
      }
    }
  })
  const minPriceChange = computed(() => {
    const result = ((pairInfo.value[props.pair] || {}).price_scale * (pairInfo.value[props.pair] || {}).quantity_increment)
    return generateDecimal(result)
  })
  const generateDecimal = (precision) => {
    return Math.pow(10, -precision);
  }
  const futuresType = ref('lpc')
</script>