<template> 
  <ElPopover popper-class="download-popover-cont" :width="230" popper-append-to-body trigger="hover">
    <template #reference>
      <div class="head-icon flex-box space-center mg-r16">
        <MonoDownload type="primary" size="16" />
      </div>
    </template>
    <div class="download-cont flex-box space-center">
      <div class="pd-t16 pd-b16 text-center">
        <p class="ts-14 fit-tc-primary download-text pd-b12">{{ $t('扫码下载') }}</p>
        <BoxQrcode :size="166" :value="`https://www.ktx.com/${locale}/download`" />
        <NuxtLink :to="`/${locale}/download`" class="download-more-btn">{{ $t('更多客户端') }}</NuxtLink>
      </div>
    </div>
  </ElPopover>
</template>
<script setup lang="ts">
  import { ElPopover } from 'element-plus'
  const { locale, t } = useI18n()
</script>
<style lang="scss">
.download-text{
  text-align:center;
  padding-bottom:12px;
}
.download-cont{
  canvas{
    width:166px;
    height:166px;
  }
}
.download-more-btn{
  display:inline-block;
  border:1px solid;
  margin:0 auto;
  margin-top:12px;
  border-radius:24px;
  @include color(tc-primary);
  padding:4px 12px;
  @include border-color(border);
  
}
</style>
