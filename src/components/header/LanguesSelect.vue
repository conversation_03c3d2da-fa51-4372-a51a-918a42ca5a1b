<template>
  <!-- 多语言选择器 -->
  <ElPopover popper-class="language-popover-cont" :width="460" popper-append-to-body>
    <template #reference>
      <div class="head-icon flex-box space-center mg-r16" @click="setColorMode">
        <MonoLanguage type="primary" size="16" />
      </div>
    </template>
    <div class="language-cont flex-box align-start">
      <ul class="flex-1">
        <li>{{ $t('语言') }}</li>
        <li v-for="(item, index) in LOCALES" :key="item.code" :class="{'active': locale === item.code}" @click="switchLang(item)">{{ item.label }}</li>
      </ul>
      <ul class="flex-1">
        <li>{{ $t('汇率') }}</li>
        <li v-for="(item, index) in RATES" :key="item.rate" :class="{'active': exchangeRate.rate === item.rate}" @click="switchRate(item)">{{ item.rate }}</li>
      </ul>
    </div>
  </ElPopover>
</template>
<script lang="ts" setup>
  import { watch } from 'vue'
  import { ElPopover } from 'element-plus'
  import { LOCALES, RATES } from '~/config'
  import { cookies } from '~/utils/cookies'
  import { commonStore } from '~/stores/commonStore'
  const store = commonStore()
  const { switchExchangeRate, getMessageError } = store
  const { exchangeRate } = storeToRefs(store)
  const {locale, setLocale} = useI18n()
  const switchLang = (item) => {
    const html = document.documentElement || document.body
    html.setAttribute('lang', locale.value)
    localStorage.setItem('lang', locale.value)
    getMessageError(item.code)
    setLocale(item.code)
  }
  const switchRate = (rate) => {
    switchExchangeRate(rate)
    cookies.set('ktx-exchangeRates', rate, Infinity)
    switchExchangeRate(exchangeRate.value)
  }
  onBeforeMount(() => {
    cookies.remove('exchangeRates')
    const cookieExchangerate = cookies.get('ktx-exchangeRates')
    console.log(!cookieExchangerate || JSON.stringify(cookieExchangerate) === '{}', exchangeRate.value.rate, 'djdjeidjiejdiejdiejidejie')
    if (!cookieExchangerate || JSON.stringify(cookieExchangerate) === '{}') {
      switchRate(exchangeRate.value)
      switchExchangeRate(exchangeRate.value)
    } else {
      exchangeRate.value = cookieExchangerate
      switchExchangeRate(cookieExchangerate)
    }
    const html = document.documentElement || document.body
    html.setAttribute('lang', locale.value)
    localStorage.setItem('lang', locale.value)
  })
</script>
<style lang="scss">
  .el-popover{
    &.language-popover-cont{
      padding:12px 0;
      .language-cont{
        ul{
          border-right:1px solid;
          @include border-color(border);
          &:last-child{
            border:0;
          }
          li{
            text-align:center;
            height:46px;
            line-height:46px;
            cursor:pointer;
            @include color(tc-primary);
            &.active{
              @include color(theme);
            }
            &:first-child{
              @include color(tc-secondary);
              @include pc-hover {
                &:hover{
                  background: none !important;
                  cursor:default;
                }
              }
            }
            @include pc-hover {
              &:hover{
                @include bg-color(bg-quaternary);
              }
            }
          }
        }
      }
    }
  }
</style>