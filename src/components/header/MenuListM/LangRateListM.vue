<template>
  <div class="menu-list-mobile-cont animate__animated" :class="{'animate__fadeOutRight': !isShow, 'animate__fadeInRight': isShow}">
    <div class="menu-list-mobile-head flex-box">
      <div class="back-btn" @click="emit('close')">
        <MonoRightArrowShort size="16" />
      </div>
    </div>
    <div class="menu-list-normal">
      <div class="normal-title">{{ $t(title) }}</div>
      <ul>
        <li
          v-for="(item, index) in list"
          :key="index"
          :class="{'active': activeValue  === item[value]}"
          @click="changeFun(item)">
            {{ item[label] }}
        </li>
      </ul>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { commonStore } from '~/stores/commonStore'
  const store = commonStore()
  const { getMessageError } = store
  const props = defineProps({
    isShow: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    activeValue: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    list: {
      type: Object,
      default () {
        return []
      }
    },
    label: {
      type: String,
      default: 'label'
    },
    value: {
      type: String,
      default: 'value'
    }
  })
  const emit = defineEmits(['close'])
  const changeFun = (item) => {
    if (item.code) {
      getMessageError(item.code)
    }
    emit('change', {
      type: props.type,
      obj: item
    })
    emit('close')
  }
</script>
<style lang="scss">
@import url('@/assets/style/header/menuListM.scss');
</style>
