<template>
  <div class="menu-list-mobile-cont animate__animated" :class="{'animate__fadeOutRight': !isShow, 'animate__fadeInRight': isShow}">
    <div class="menu-list-mobile-head">
      <div class="menu-list-mobile-head-wrap">
        <div class="search-input-text flex-box">
          <MonoSearch class="icon-search" size="16" />
          <ElInput ref="inputTxtRef" v-model="searchText" type="text" clearable class="searchInputM"/>
          <ElButton class="cancel-btn" @click="closeFun">{{ $t('取消') }}</ElButton>
        </div>
      </div>
    </div>
    <div class="menu-list-mobile-search">
      <div class="menu-list-mobile-search-wrap">
        <div v-if="historyList.length > 0" class="search-history-list">
          <div class="history-title flex-box space-between">
            {{ $t('历史记录') }}
            <MonoDelete size="16" />  
          </div>
          <ul class="history-list">
            <li v-for="(historyItem, index) in historyList">
              <a>{{ historyItem.pair.replace('_', '/') }}</a>
            </li>
          </ul>
        </div>
        <div class="search-list-cont">
          <div class="search-list-wrap">
            <div v-if="spotAllList.length > 0" class="cont-wrap">
              <h2>{{ $t('现货') }}</h2>
              <ul>
                <li v-for="item in spotAllList.slice(0, visibleSpotCount)" class="flex-box space-between" @click="toTrade(item.pair)">
                  <div class="li-left">
                    <i class="icon"></i>
                    <div class="name">
                      <h3>{{ item.pair.replace('_', '/') }}</h3>
                      <p>{{ item.general_name }}</p>
                    </div>
                  </div>
                  <div class="li-right">
                    <h3 :class="className(markets[item.pair] ? markets[item.pair].change : item.change)">{{ format((markets[item.pair] ? markets[item.pair].change : item.change) * 100, 2, true) }}%</h3>
                    <p>{{ format((markets[item.pair] ? markets[item.pair].last : item.last), (pairInfo[item.pair] || {}).price_scale, true) }}</p>
                  </div>
                </li>
                <div class="more-bx flex-box" @click="showMoreSpot">
                  <em class="mg-r4">
                    {{ spotAllList.length > visibleSpotCount ? $t('查看更多') : $t('收起') }} 
                    <template v-if="hasSpotAllList > 0">({{ hasSpotAllList }})</template>
                  </em>
                  <MonoDownArrowShort :size="16" :class="{'icon-up': hasSpotAllList === 0}" />
                </div>
              </ul>
            </div>
            <div v-if="futureAllList.length > 0" class="cont-wrap">
              <h2>{{ $t('合约') }}</h2>
              <ul>
                <li v-for="item in futureAllList.slice(0, visibleFutureCount)" class="flex-box space-between" @click="toTrade(item.pair)">
                  <div class="li-left">
                    <i class="icon"></i>
                    <div class="name">
                      <h3>{{ item.pair.replace('_', '').replace('_SWAP', '') }}</h3>
                      <p>{{ $t('永续') }}</p>
                    </div>
                  </div>
                  <div class="li-right">
                    <h3 :class="className(markets[item.pair] ? markets[item.pair].change : item.change)">{{ format((markets[item.pair] ? markets[item.pair].change : item.change) * 100, 2, true) }}%</h3>
                    <p>{{ format((markets[item.pair] ? markets[item.pair].last : item.last), (pairInfo[item.pair] || {}).price_scale, true) }}</p>
                  </div>
                </li>
                <div class="more-bx flex-box" @click="showMoreFuture">
                  <em class="mg-r4">
                    {{ futureAllList.length > visibleFutureCount ? $t('查看更多') : $t('收起') }}
                    <template v-if="hasFutureAllList > 0">({{ hasFutureAllList }})</template>
                  </em>
                  <MonoDownArrowShort :size="16" :class="{'icon-up': hasFutureAllList === 0}" />
                </div>
              </ul>
            </div>
            <div style="height:500px;">
              <BoxNoData v-if="spotAllList.length === 0 && futureAllList.length === 0" :text="$t('暂无数据')" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ElInput, ElButton } from 'element-plus'
import { getPairHistory, getAllPairs } from '~/api/public'
import BoxNoData from '~/components/common/BoxNoData.vue'
import { commonStore } from '~/stores/commonStore'
import MonoDownArrowShort from '~/components/common/icon-svg/MonoDownArrowShort.vue'
const store = commonStore()
const { subAllTickerSocket, cancelAllTicker, pairInfo } = store
const { marketsObj } = storeToRefs(store)
const props = defineProps({
  isShow: {
    type: Boolean,
    default: false
  }
})
const router = useRouter()
const { locale, t } = useI18n()
const searchText = ref('')
const emit = defineEmits(['close'])
const inputTxtRef = ref(null)
const visibleSpotCount = ref(3) // 初始显示的现货项目数量
const visibleFutureCount = ref(3) // 初始显示的合约项目数量
const closeFun = () => {
  searchText.value = ''
  nextTick(() => {
    inputTxtRef.value.blur()
  })
  emit('close')
}
watch(() => props.isShow, (val) => {
    if (val) {
      nextTick(() => {
        inputTxtRef.value.focus()
      })
    }
  }, {
  immediate: true
})
const historyList = ref([])
const spotList = ref([])
const futureList = ref([])
const spotAllList = computed(() => {
  if (searchText.value === '') {
    return spotList.value
  } else {
    return spotList.value.filter((item) => {
      return item.pair.replace('_', '/').includes(searchText.value.toUpperCase())
    })
  }
})
const futureAllList = computed(() => {
  if (searchText.value === '') {
    return futureList.value
  } else {
    return futureList.value.filter((item) => {
      return item.pair.replace('_', '/').includes(searchText.value.toUpperCase())
    })
  }
})
const hasSpotAllList = computed(() => {
  return spotAllList.value.length - visibleSpotCount.value < 0 ? 0 : spotAllList.value.length - visibleSpotCount.value
})
const hasFutureAllList = computed(() => {
  return futureAllList.value.length - visibleFutureCount.value < 0 ? 0 : futureAllList.value.length - visibleFutureCount.value
})
const showMoreSpot = () => {
  if (spotAllList.value.length > visibleSpotCount.value) {
    visibleSpotCount.value += 10 // 每次点击增加5个项目
  } else {
    visibleSpotCount.value = 3
  }
}
const showMoreFuture = () => {
  if (futureAllList.value.length > visibleFutureCount.value) {
    visibleFutureCount.value += 10 // 每次点击增加5个项目
  } else {
    visibleFutureCount.value = 3
  }
}
const className = (c) => {
  if (c * 1 < 0) {
    return 'fit-fall'
  } else {
    return 'fit-rise'
  }
}
const toTrade = (pair) => {
  if (pair.includes('_SWAP')) {
    router.push(`/${locale.value}/future/${pair}`)
  } else {
    router.push(`/${locale.value}/exchange/${pair}`)
  }
}
const getHistoryList = async() => {
  const { data } = await getPairHistory()
  if (data) {
    historyList.value = data
  }
}
const getAllList = async() => {
  const { data } = await getAllPairs({
    uniq: 1
  })
    if (data) {
      spotList.value = data.spot
      futureList.value = data.contract
    }
}
const requestAnimationFrameInterval = ref(null)
const markets = ref({})
const socketDateAnimation = () => {
  markets.value = marketsObj.value
  requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
}
onBeforeMount(() => {
  socketDateAnimation()
  subAllTickerSocket()
  getHistoryList()
  getAllList()
  requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
})
onBeforeUnmount(() => {
  cancelAllTicker()
  requestAnimationFrameInterval.value && cancelAnimationFrame(requestAnimationFrameInterval.value)
})
</script>
<style lang="scss">
@import url('@/assets/style/header/menuListM.scss');
</style>
