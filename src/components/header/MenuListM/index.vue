<template>
  <div class="menu-list-mobile-cont animate__animated" :class="{'animate__fadeOutRight': !isShow, 'animate__fadeInRight': isShow}">
    <div class="menu-list-mobile-close flex-box space-center" @click="closeFun">
      <MonoClose size="16" />
    </div>
    <div v-if="menuType === 'menu'" class="menu-list-mobile-head">
      <div class="menu-list-mobile-head-wrap">
        <div class="search-input-text flex-box">
          <MonoSearch class="icon-search" size="16" />
          <div class="input-text flex-1" @click="openSearch">{{ $t('搜索') }}</div>
        </div>
      </div>
    </div>
    <div class="menu-list-mobile-cont" :class="{'isMenuUser': menuType === 'user'}">
      <div class="menu-list-mobile-wrap">
        <div class="userLogin">
          <div v-if="menuType === 'user'" class="user-info-name flex-box flex-column align-start">
            <div class="flex-box">
              <NuxtLink class="fit-tc-primary" :to="`/${locale}/my/dashboard`" @click="closeFun">
                {{ isVisibleUserInfo ? user.name_visible : user.name }}
              </NuxtLink>
              <div v-if="user.user_type * 1 === 100" class="user-gray-box">{{ $t('子账户') }}</div>
            </div>
            <div v-if="user.user_type * 1 !== 100" class="flex-box">
              <NuxtLink :to="`/${locale}/my/kyc/result`" @click="closeFun">
                <div v-if="user.is_real_name * 1 === 0" class="warning-text mg-t12">{{ $t('未认证') }}</div>
                <div v-if="user.is_real_name * 1 === 1" class="waiting-text mg-t12">{{ $t('审核中') }}</div>
                <div v-if="user.is_real_name * 1 === 2" class="warning-text mg-t12">{{ $t('认证失败') }}</div>
                <div v-if="user.is_real_name * 1 === 3" class="success-text mg-t12">{{ $t('已认证') }}</div>
              </NuxtLink>
              <NuxtLink :to="`/${locale}/rate-list`" style="display:block;">
                <div class="user-level-box">
                  {{ userLevel }}
                </div>
              </NuxtLink>
            </div>
          </div>
          <div v-if="!isLogin" class="login-register-btn flex-box">
            <ElButton class="flex-1 login-btn" @click="useCommon.openLogin()">{{ $t('登录') }}</ElButton>
            <ElButton type="primary" class="flex-1 register-btn" @click="useCommon.openRegister()">{{ $t('注册') }}</ElButton>
          </div>
        </div>
        <div class="menu-list-cont-box">
          <ul>
            <li v-if="menuType === 'user' && user.user_type * 1 === 100 && user.is_switch_login * 1 === 1">
              <NuxtLink class="flex-box space-between fit-tc-primary" @click="checkUserFun">
                <div class="li-left flex-box">
                  <div class="list-icon-m qiehuan"></div>
                  <p>{{ $t('切换账户') }}</p>
                </div>
              </NuxtLink>
            </li>
            <li v-if="menuType === 'user'">
              <NuxtLink :to="`/${locale}/my/assets/deposit`" class="flex-box space-between fit-tc-primary" @click="closeFun">
                <div class="li-left flex-box">
                  <div class="list-icon-m chongzhi"></div>
                  <p>{{ $t('充值') }}</p>
                </div>
              </NuxtLink>
            </li>
            <li v-if="menuType === 'menu'">
              <NuxtLink :to="`/${locale}`" class="flex-box space-between fit-tc-primary" @click="closeFun">
                <div class="li-left flex-box">
                  <div class="list-icon-m hangqing"></div>
                  <p>{{ $t('行情') }}</p>
                </div>
              </NuxtLink>
            </li>
            <li v-if="menuType === 'menu'">
              <NuxtLink :to="`/${locale}/exchange/BTC_USDT`" class="flex-box space-between fit-tc-primary" @click="closeFun">
                <div class="li-left flex-box">
                  <div class="list-icon-m exchange"></div>
                  <p>{{ $t('币币交易') }}</p>
                </div>
              </NuxtLink>
            </li>
            <li v-if="menuType === 'menu'">
              <NuxtLink :to="`/${locale}/future/BTC_USDT_SWAP`" class="flex-box space-between fit-tc-primary" @click="closeFun">
                <div class="li-left flex-box">
                  <div class="list-icon-m future"></div>
                  <p>{{ $t('合约交易') }}</p>
                </div>
              </NuxtLink>
            </li>
            <li v-if="menuType === 'menu'">
              <NuxtLink :to="`/${locale}/launchpool`" class="flex-box space-between fit-tc-primary" @click="closeFun">
                <div class="li-left flex-box">
                  <div class="list-icon-m launchpool"></div>
                  <p>{{ $t('LaunchPool') }}</p>
                </div>
              </NuxtLink>
            </li>
            <!-- <li v-if="menuType === 'menu'">
              <NuxtLink :to="`/${locale}/coupon/list`" class="flex-box space-between fit-tc-primary" @click="closeFun">
                <div class="li-left flex-box">
                  <div class="list-icon-m launchpool"></div>
                  <p>{{ $t('福利中心') }}</p>
                </div>
              </NuxtLink>
            </li> -->
            <li v-if="menuType === 'user'">
              <NuxtLink :to="`/${locale}/my/dashboard`" class="flex-box space-between fit-tc-primary" @click="closeFun">
                <div class="li-left flex-box">
                  <div class="list-icon-m zonglan"></div>
                  <p>{{ $t('总览') }}</p>
                </div>
              </NuxtLink>
            </li>
            <li v-if="menuType === 'user'">
              <NuxtLink :to="`/${locale}/my/assets/wallet`" class="flex-box space-between fit-tc-primary" @click="closeFun">
                <div class="li-left flex-box">
                  <div class="list-icon-m zichan"></div>
                  <p>{{ $t('资产') }}</p>
                </div>
              </NuxtLink>
            </li>
            <li v-if="menuType === 'user'">
              <NuxtLink :to="`/${locale}/my/orders/spot`" class="flex-box space-between fit-tc-primary" @click="closeFun">
                <div class="li-left flex-box">
                  <div class="list-icon-m orders"></div>
                  <p>{{ $t('订单') }}</p>
                </div>
              </NuxtLink>
            </li>
            <li v-if="menuType === 'user' && user.user_type * 1 !== 100" class="flex-box space-between">
              <NuxtLink :to="`/${locale}/my/subAccount`" class="flex-box space-between fit-tc-primary flex-1" @click="closeFun">
                <div class="li-left flex-box">
                  <div class="list-icon-m zizhanghu"></div>
                  <p>{{ $t('子账户') }}</p>
                </div>
              </NuxtLink>
              <MonoSubtract size="16" @click.stop="checkUserFun" />
            </li>
            <li v-if="menuType === 'user' && user.user_type * 1 !== 100">
              <NuxtLink :to="`/${locale}/my/account/security`" class="flex-box space-between fit-tc-primary" @click="closeFun">
                <div class="li-left flex-box">
                  <div class="list-icon-m shezhi"></div>
                  <p>{{ $t('设置') }}</p>
                </div>
              </NuxtLink>
            </li>
            <li v-if="menuType === 'user' && user.user_type * 1 !== 100">
              <NuxtLink :to="`/${locale}/my/account/api`" class="flex-box space-between fit-tc-primary" @click="closeFun">
                <div class="li-left flex-box">
                  <div class="list-icon-m api"></div>
                  <p>{{ $t('API管理') }}</p>
                </div>
              </NuxtLink>
            </li>
            <li v-if="menuType === 'user' && user.user_type * 1 !== 100">
              <NuxtLink :to="`/${locale}/invite`" class="flex-box space-between fit-tc-primary" @click="closeFun">
                <div class="li-left flex-box">
                  <div class="list-icon-m yaoqing"></div>
                  <p>{{ $t('邀请返佣') }}</p>
                </div>
              </NuxtLink>
            </li>
            <li v-if="menuType === 'user' && user.user_type * 1 !== 100">
              <NuxtLink :to="`/${locale}/nft/my/assets/nft-list`" class="flex-box space-between fit-tc-primary" @click="closeFun">
                <div class="li-left flex-box">
                  <div class="list-icon-m launchpool"></div>
                  <p>{{ $t('我的NFT') }}</p>
                </div>
              </NuxtLink>
            </li>
            <div v-if="menuType === 'menu'" class="box-line"></div>
            <li v-if="menuType === 'menu'" class="flex-box space-between" @click="isShowSetMenu = !isShowSetMenu">
              <div class="li-left flex-box">
                <div class="list-icon-m shezhi"></div>
                <p>{{ $t('设置') }}</p>
              </div>
              <div class="li-right">
                <MonoRightArrowShort size="14" class="right-arrow-box" :class="{'showMenu': isShowSetMenu}" />
              </div>
            </li>
            <li v-if="isShowSetMenu" class="flex-box space-between subli">
              <div class="li-left flex-box">
                <div class="list-icon-m"></div>
                <p>{{ $t('黑夜模式') }}</p>
              </div>
              <div class="li-right">
                <div class="theme-list flex-box">
                  <div class="theme-item" :class="{'active': colorMode.preference === 'light'}" @click="colorMode.preference = 'light'">
                    <MonoSun size="16" />
                  </div>
                  <div class="theme-item" :class="{'active': colorMode.preference === 'dark'}" @click="colorMode.preference = 'dark'">
                    <MonoMoon size="16" />
                  </div>
                </div>
              </div>
            </li>
            <li v-if="isShowSetMenu" class="flex-box space-between subli" @click="showListFun('lang')">
              <div class="li-left flex-box">
                <div class="list-icon-m"></div>
                <p>{{ $t('语言') }}</p>
              </div>
              <div class="li-right flex-box">
                <div class="info-text">{{ langName }}</div>
                <MonoRightArrowShort size="14" />
              </div>
            </li>
            <li v-if="isShowSetMenu" class="flex-box space-between subli" @click="showListFun('rate')">
              <div class="li-left flex-box">
                <div class="list-icon-m"></div>
                <p>{{ $t('汇率') }}</p>
              </div>
              <div class="li-right flex-box">
                <div class="info-text">{{ exchangeRate.rate }}</div>
                <MonoRightArrowShort size="14" />
              </div>
            </li>
            <li v-if="menuType === 'menu'" class="flex-box space-between">
              <a :href="`${useCommon.zendeskUrl(locale)}/categories/4662266924574`" class="flex-box space-between fit-tc-primary" @click="closeFun">
                <div class="li-left flex-box">
                  <div class="list-icon-m help"></div>
                  <p>{{ $t('帮助中心') }}</p>
                </div>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div v-if="menuType === 'user'" class="menu-list-mobile-foot">
      <div class="menu-list-mobile-foot-wrap" @click="useCommon.loginout()">{{ $t('退出') }}</div>
    </div>
  </div>
  <SearchListM v-if="isShowSearchDom" :isShow="isShowSearch" @close="closeSearch" />
  <LangRateListM
    v-if="isShowNormalDom"
    :activeValue="current.active"
    :isShow="isShowNormal"
    :type="current.type"
    :title="current.title"
    :list="current.list"
    :label="current.label"
    :value="current.value"
    @close="closeLangRateDialog"
    @change="changeListFun"
  />
</template>
<script setup lang="ts">
import { cookies } from '~/utils/cookies'
import { LOCALES, RATES } from '~/config'
import { ElInput } from 'element-plus'
import SearchListM from '~/components/header/MenuListM/SearchListM.vue'
import LangRateListM from '~/components/header/MenuListM/LangRateListM.vue'
import MonoSubtract from '~/components/common/icon-svg/MonoSubtract.vue'
import { useCommonData } from '~/composables/index'
import { useUserStore } from '~/stores/useUserStore'
import { commonStore } from '~/stores/commonStore'
const commonStoreT = commonStore()
const { switchExchangeRate } = commonStoreT
const { exchangeRate } = storeToRefs(commonStoreT)
const store = useUserStore()
const { isLogin } = storeToRefs(store)
const {locale, setLocale} = useI18n()
const colorMode = useColorMode()
const useCommon = useCommonData()
const props = defineProps({
  isShow: {
    type: Boolean,
    default: false
  },
  menuType: {
    type: String,
    default: ''
  },
  user: {
    type: Object,
    default () {
      return {}
    }
  },
  isVisibleUserInfo: {
    type: Boolean,
    default: false
  }
})
const isShowSearch = ref(false)
const isShowSearchDom = ref(false)
const isShowNormal = ref(false)
const isShowNormalDom = ref(false)
const isShowSetMenu = ref(true)
const current = ref({
  type: '',
  title: '',
  list: [],
  value: '',
  label: '',
  active: ''
})
const langActive = ref('')
const rateActive = ref('')
const langName = computed(() => {
  let lang = ''
  LOCALES.forEach((item) => {
    if (item.code === locale.value) {
      lang = item.label
    }
  })
  return lang
})
const emit = defineEmits(['close', 'checkUserList'])
const checkUserFun = () => {
  emit('checkUserList')
  emit('close')
}
const userLevel = computed(() => {
  const levelType = useCommon.userLevelMethod(Math.min(props.user.user_level * 1, props.user.contract_level * 1)).levelType
  return levelType === 'special' ? t('特殊费率') : levelType
})
const closeLangRateDialog = () => {
  isShowNormal.value = false
  setTimeout(() => {
    isShowNormalDom.value = false
  }, 300)
}
const showListFun = (type) => {
  isShowNormal.value = true
  isShowNormalDom.value = true
  if (type === 'lang') {
    current.value = {
      type: 'lang',
      title: '语言',
      list: LOCALES,
      value: 'code',
      label: 'label',
      active: locale.value
    }
  } else if (type === 'rate') {
    current.value = {
      type: 'rate',
      title: '汇率',
      list: RATES,
      value: 'rate',
      label: 'rate',
      active: exchangeRate.value.rate
    }
  }
}
const changeListFun = (item) => {
  if (item.type === 'lang') {
    setLocale(item.obj.code)
  } else if (item.type === 'rate') {
    exchangeRate.value = item.obj
    cookies.set('ktx-exchangeRates', item.obj)
    switchExchangeRate(item.obj)
  }
}
const closeSearch = () => {
  isShowSearch.value = false
  setTimeout(() => {
    isShowSearchDom.value = false
  }, 300)
}
const openSearch = () => {
  isShowSearchDom.value = true
  isShowSearch.value = true
}
const closeFun = () => {
  emit('close')
}
onBeforeMount(() => {
  isShowSetMenu.value = props.menuType === 'menu'
  if (JSON.stringify(cookies.get('ktx-exchangeRates')) === '{}' || cookies.get('ktx-exchangeRates') === undefined) {
    switchExchangeRate(exchangeRate.value)
  } else {
    exchangeRate.value = cookies.get('ktx-exchangeRates')
    switchExchangeRate(exchangeRate.value)
  }
})
</script>
<style lang="scss">
@import url('@/assets/style/header/menuListM.scss');
</style>