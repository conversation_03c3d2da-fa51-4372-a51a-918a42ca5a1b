<template>
  <div class="madEx-search-container">
    <div class="madEx-search-wrapper">
      <ElPopover :visible="visible" popper-class="search-popover-cont" :width="460" @mouseleave="handleContainerLeave" @mouseenter="handleListEnter">
        <template #reference>
          <div class="search-input" @mouseenter="handleInputEnter" @mouseleave="handleInputLeave">
            <ElInput ref="searchTextRef" type="text" v-model="searchText" :placeholder="$t('搜索')" clearable @focus="handleInputFocus">
              <template #prepend>
                <MonoSearch size="16" />
              </template>
            </ElInput>
          </div>
        </template>
        <div class="search-list-cont" @mouseenter="handleListEnter" @mouseleave="handleListLeave">
          <div v-if="searchText === ''" class="hot-search-list">
            <div class="hot-title-box">{{ $t('热门搜索') }}</div>
            <ul v-for="(item, index) in hotEndList" class="hot-list-cont" @click="toTrade(item.pair)">
              <li>
                <dl class="flex-box space-between">
                  <dt class="flex-box">
                    <BoxCoinIcon :icon="item.icon_url" class="icon-box" />
                    <span>{{ item.pair.includes('_SWAP') ? item.pair.replace('_SWAP', '').replace('_', '') : item.pair.replace('_', '/') }}</span>
                  </dt>
                  <dd>
                    <p :class="className(markets[item.pair] ? markets[item.pair].change : item.change)">{{ format((markets[item.pair] ? markets[item.pair].change : item.change) * 100, 2, true) }}%</p>
                    <p>{{ format((markets[item.pair] ? markets[item.pair].last : item.last), (pairInfo[item.pair] ||{}).price_scale, true) }}</p>
                  </dd>
                </dl>
              </li>
            </ul>
          </div>
          <div v-if="searchText !== ''" class="search-list-wrap">
            <div v-if="spotAllList.length > 0" class="cont-wrap">
              <h2>{{ $t('现货') }}</h2>
              <ul>
                <li v-for="item in spotAllList.slice(0, visibleSpotCount)" class="flex-box space-between cursor-pointer" @click="toTrade(item.pair)">
                  <div class="li-left">
                    <i class="icon"></i>
                    <div class="name">
                      <h3>{{ item.pair.replace('_', '/') }}</h3>
                      <p>{{ item.general_name }}</p>
                    </div>
                  </div>
                  <div class="li-right">
                    <h3 :class="className(markets[item.pair] ? markets[item.pair].change : item.change)">{{ format((markets[item.pair] ? markets[item.pair].change : item.change) * 100, 2, true)}}%</h3>
                    <p>{{ format((markets[item.pair] ? markets[item.pair].last : item.last), (pairInfo[item.pair] || {}).price_scale, true) }}</p>
                  </div>
                </li>
                <div class="more-bx flex-box" @click="showMoreSpot">
                  <em class="mg-r4">
                    {{ spotAllList.length > visibleSpotCount ? $t('查看更多') : $t('收起') }} 
                    <template v-if="hasSpotAllList > 0">({{ hasSpotAllList }})</template>
                  </em>
                  <MonoDownArrowShort :size="16" :class="{'icon-up': hasSpotAllList === 0}" />
                </div>
              </ul>
            </div>
            <div v-if="futureAllList.length > 0" class="cont-wrap">
              <h2>{{ $t('合约') }}</h2>
              <ul>
                <li v-for="item in futureAllList.slice(0, visibleFutureCount)" class="flex-box space-between cursor-pointer" @click="toTrade(item.pair)">
                  <div class="li-left">
                    <i class="icon"></i>
                    <div v-if="item.pair.includes('_SWAP')" class="name">
                      <h3>{{ item.pair.replace('_', '').replace('_SWAP', '') }}</h3>
                      <p>{{ $t('永续') }}</p>
                    </div>
                  </div>
                  <div class="li-right">
                    <h3 :class="className(markets[item.pair] ? markets[item.pair].change : item.change)">{{ format((markets[item.pair] ? markets[item.pair].change : item.change) * 100, 2, true) }}%</h3>
                    <p>{{ format((markets[item.pair] ? markets[item.pair].last : item.last), (pairInfo[item.pair] || {}).price_scale, true) }}</p>
                  </div>
                </li>
                <div class="more-bx flex-box" @click="showMoreFuture">
                  <em class="mg-r4">
                    {{ futureAllList.length > visibleFutureCount ? $t('查看更多') : $t('收起') }}
                    <template v-if="hasFutureAllList > 0">({{ hasFutureAllList }})</template>
                  </em>
                  <MonoDownArrowShort :size="16" :class="{'icon-up': hasFutureAllList === 0}" />
                </div>
              </ul>
            </div>
            <div style="height:600px;" v-if="spotAllList.length === 0 && futureAllList.length === 0">
              <BoxNoData :text="$t('暂无数据')" />
            </div>
          </div>
        </div>
      </ElPopover>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ElInput, ElPopover } from 'element-plus'
  import { getAllPairs, hotPairList } from '~/api/public'
  import { format } from '~/utils'
  import { commonStore } from '~/stores/commonStore'
  import { imgDmain } from '~/config'
  import MonoDownArrowShort from '~/components/common/icon-svg/MonoDownArrowShort.vue'
  const router = useRouter()
  const { locale, t } = useI18n()
  const store = commonStore()
  const { subAllTickerSocket, cancelAllTicker, pairInfo } = store
  const { marketsObj } = storeToRefs(store)
  const visible = ref(false)
  const isHoveringInput = ref(false)
  const isHoveringList = ref(false)
  const closeTimer = ref<NodeJS.Timeout | null>(null)
  const searchText = ref<string|number>('')
  const spotList = ref([])
  const futureList = ref([])
  const allList = ref([])
  const hotMap = ref({})
  const visibleSpotCount = ref(3) // 初始显示的现货项目数量
  const visibleFutureCount = ref(3) // 初始显示的合约项目数量
  const searchTextRef = ref(null)
  const hotEndList = computed(() => {
    // 获取 hotMap 的所有键（按顺序）
    const orderedHotKeys = Object.keys(hotMap.value);
    
    // 创建一个映射表，记录每个 pair 在 hotMap 中的顺序索引
    const keyOrderMap = new Map();
    orderedHotKeys.forEach((key, index) => {
      keyOrderMap.set(key, index);
    });
    console.log(hotMap, 'hotMap')
    // 先过滤出存在于 hotMap 中的条目
    const filteredList = allList.value.filter(item => hotMap.value[item.pair]);

    // 按照 hotMap 的键顺序排序（保持 allList 中的原始数据）
    return filteredList.sort((a, b) => {
      // 获取两个 pair 在 hotMap 中的顺序位置
      const indexA = keyOrderMap.get(a.pair);
      const indexB = keyOrderMap.get(b.pair);
      
      // 根据索引差值排序
      return indexA - indexB;
    });
  });
  const spotAllList = computed(() => {
    return spotList.value.filter((item) => {
      return item.pair.replace('_', '/').includes(searchText.value.toUpperCase())
    })
  })
  const futureAllList = computed(() => {
    return futureList.value.filter((item) => {
      return item.pair.replace('_', '/').includes(searchText.value.toUpperCase())
    })
  })
  const hasSpotAllList = computed(() => {
    return spotAllList.value.length - visibleSpotCount.value < 0 ? 0 : spotAllList.value.length - visibleSpotCount.value
  })
  const hasFutureAllList = computed(() => {
    return futureAllList.value.length - visibleFutureCount.value < 0 ? 0 : futureAllList.value.length - visibleFutureCount.value
  })
  const leaveContFun = () => {
    visible.value = false
    searchTextRef.value.blur()
  }
  const handleInputFocus = () => {
    visible.value = true
  }
  const handleInputEnter = () => {
    isHoveringInput.value = true
    if (closeTimer.value) {
      clearTimeout(closeTimer.value)
      closeTimer.value = null
    }
  }
  const handleListEnter = () => {
    isHoveringList.value = true
    if (closeTimer.value) {
      clearTimeout(closeTimer.value)
      closeTimer.value = null
    }
  }

  const handleListLeave = () => {
    isHoveringList.value = false
    console.log(isHoveringInput.value, isHoveringList.value, 'djdiejdiejijeijdieji')
    tryClosePopover()
  }

  const handleContainerLeave = () => {
    if (!isHoveringInput.value && !isHoveringList.value) {
      tryClosePopover()
    }
  }
  const tryClosePopover = () => {
    console.log(isHoveringInput.value, isHoveringList.value, 'djdiejdiejijeijdieji')
    if (closeTimer.value) {
      clearTimeout(closeTimer.value)
    }
    closeTimer.value = setTimeout(() => {
      if (!isHoveringInput.value && !isHoveringList.value) {
        visible.value = false
        searchTextRef.value?.blur()
      }
    }, 200) // 200ms延迟关闭，避免鼠标快速移动时误关闭
  }
  const handleInputLeave = () => {
    isHoveringInput.value = false
    tryClosePopover()
  }
const showMoreSpot = () => {
  if (spotAllList.value.length > visibleSpotCount.value) {
    visibleSpotCount.value += 10 // 每次点击增加5个项目
  } else {
    visibleSpotCount.value = 3
  }
}
const showMoreFuture = () => {
  if (futureAllList.value.length > visibleFutureCount.value) {
    visibleFutureCount.value += 10 // 每次点击增加5个项目
  } else {
    visibleFutureCount.value = 3
  }
}
  const className = (c) => {
    if (c * 1 < 0) {
      return 'fit-fall'
    } else {
      return 'fit-rise'
    }
  }
  const getAllList = async() => {
    const { data } = await getAllPairs({
      uniq: 1
    })
    if (data) {
      spotList.value = data.spot
      futureList.value = data.contract
      allList.value = data.spot.concat(data.contract)
    }
  }
  const toTrade = (pair) => {
    if (pair.includes('_SWAP')) {
      router.push(`/${locale.value}/future/${pair}`)
    } else {
      router.push(`/${locale.value}/exchange/${pair}`)
    }
  }
  const getHotList = async() => {
    const { data } = await hotPairList()
    if (data) {
      hotMap.value = {}
      data.forEach((item) => {
        hotMap.value[item.pair] = true
      })
    }
  }
  const requestAnimationFrameInterval = ref(null)
  const markets = ref({})
  const socketDateAnimation = () => {
    markets.value = marketsObj.value
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  }
  onMounted(() => {
    socketDateAnimation()
    subAllTickerSocket()
    getAllList()
    getHotList()
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  })
  onBeforeUnmount(() => {
    cancelAllTicker()
    requestAnimationFrameInterval.value && cancelAnimationFrame(requestAnimationFrameInterval.value)
  })
</script>
<style lang="scss">
.madEx-search-container{
  .madEx-search-wrapper{
    .search-input{
      width:220px;
      height:36px;
      overflow:hidden;
      .el-input-group--prepend{
        border-radius:30px;
        .el-input__wrapper{
          border-radius:30px !important;
          @include bg-color(bg-secondary);
        }
      }
      .el-input__wrapper{
        border-radius:30px;
      }
      .el-input__inner{
        height:32px !important;
        border-radius:30px;
      }
    }
  }
}
.el-popover{
  &.search-popover-cont{
    .hot-search-list{
      .hot-title-box{
        padding:24px;
        font-size:16px;
        @include color(tc-primary);
      }
      .hot-list-cont{
        li{
          padding: 8px 24px;
          cursor:pointer;
          @include pc-hover {
            &:hover{
              @include bg-color(bg-quaternary);
            }
          }
          dl{
            dt{
              .icon-box{
                width:32px;
                height:auto;
                font-size:32px;
                margin-right:12px;
              }
              span{
                font-size:16px;
                @include color(tc-primary);
              }
            }
            dd{
              p{
                font-size:14px;
                text-align:right;
                padding:2px 0;
                @include color(tc-secondary);
                &.fit-rise{
                  @include color(rise);
                }
                &.fit-fall{
                  @include color(fall);
                }
              }
            }
          }
        }
      }
    }
    .search-list-cont{
      height:600px;
      overflow:auto;
      .search-list-wrap{
        h2{
          padding:12px 24px;
          font-size:16px;
          font-weight:normal;
          @include color(tc-secondary);
        }
        .cont-wrap{
          padding:12px 0;
          border-bottom:1px solid;
          @include border-color(border);
          &:last-child{
            border-bottom:0;
          }
        }
        ul{
          li{
            padding:12px 24px;
            font-size:14px;
            @include pc-hover {
              &:hover{
                @include bg-color(bg-quaternary);
              }
            }
            p{
              font-size:12px;
              @include color(tc-secondary);
            }
          }
        }
        .more-bx{
          font-size:14px;
          padding:8px 24px;
          cursor:pointer;
          @include color(theme);
          em{
            font-style:normal;
          }
          svg{
            &.icon-up{
              transform: rotate(180deg);
            }
          }
        }
      }
    }
  }
}
</style>
