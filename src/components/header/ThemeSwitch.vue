<template>
  <div v-if="colorMode.preference === 'light'" class="head-icon flex-box space-center" @click="setColorMode">
    <MonoSun size="16" />
  </div>
  <div v-if="colorMode.preference === 'dark'" class="head-icon flex-box space-center" @click="setColorMode">
    <MonoMoon size="16" />
  </div>
</template>
<script lang="ts" setup>
  import { ElButton } from 'element-plus'
  import { cookies } from '~/utils/cookies'
  const colorMode = useColorMode()
  const setColorMode = async() => {
    colorMode.preference = colorMode.preference === 'dark' ? 'light' : 'dark'
    const html = document.documentElement || document.body
    html.setAttribute('data-theme', colorMode.preference)
    html.setAttribute('class', colorMode.preference)
    localStorage.setItem('nuxt-color-mode', colorMode.preference)
    await nextTick()
  }
</script>