<template>
    <div class="user-login-head-cont">
      <div v-if="!isLogin" class="login-register-btn">
        <ElButton class="special-btn" @click="useCommon.openLogin()">{{ $t('登录') }}</ElButton>
        <ElButton type="primary" class="normal-btn" @click="useCommon.openRegister()">{{ $t('注册') }}</ElButton>
      </div>
      <div v-if="isLogin" class="logined-user-cont flex-box">
        <ElButton type="primary" class="normal-btn mg-r16" @click="goCharge">{{ $t('充值') }}</ElButton>
        <ElPopover v-if="sonInfoList.length > 0 && user.is_switch_login * 1 === 1 && isMainPage" popper-class="user-popover-cont" :width="230" popper-append-to-body trigger="hover">
          <template #reference>
            <ElButton type="primary" class="normal-btn mg-r16">{{ $t('切换账户') }}</ElButton>
          </template>
          <div class="switch-accont-wrap">
            <h2>{{ $t('主账户') }}</h2>
            <ul>
              <li :class="{'active': user.user_id === mainUserInfo.user_id}" @click="checkMainAccount(mainUserInfo.user_id)">
                {{ mainUserInfo.name }}
                <MonoRigthChecked size="16" class="right-cont fit-theme" />
              </li>
            </ul>
            <h2 v-if="sqSonList.length > 0">{{ $t('授权子账户') }}</h2>
            <ul v-if="sqSonList.length > 0">
              <li v-for="(item, index) in sqSonList" :key="index" :class="{'active': user.user_id === item.user_id}" @click="checkMainAccount(item.user_id)">
                <p class="white-text pd-b4">{{ item.name }}</p>
                <p class="fit-tc-secondary">{{ item.desc }}</p>
                <MonoRigthChecked size="16" class="right-cont fit-theme" />
              </li>
            </ul>
            <h2 v-if="kjSonList.length > 0">{{ $t('快捷子账户') }}</h2>
            <ul v-if="kjSonList.length > 0">
              <li v-for="(item, index) in kjSonList" :key="index" :class="{'active': user.user_id === item.user_id}" @click="checkMainAccount(item.user_id)">
                <p class="fit-tc-secondary">{{ item.desc }}</p>
                <MonoRigthChecked size="16" class="right-cont fit-theme" />
              </li>
            </ul>
          </div>
        </ElPopover>
        <ElPopover popper-class="user-popover-cont" :width="230" popper-append-to-body trigger="hover">
          <template #reference>
            <div class="head-icon flex-box space-center">
              <MonoUserInfo type="primary" size="16" />
            </div>
          </template>
          <div class="user-cont">
            <div class="user-head-top flex-box flex-column space-start align-start">
              <div class="flex-box">
                <NuxtLink class="fit-tc-primary" :to="`/${locale}/my/dashboard`">
                  {{ isVisibleUserInfo ? user.name_visible : user.name }}
                </NuxtLink>
                <div v-if="user.user_type * 1 === 100" class="user-gray-box">{{ $t('子账户') }}</div>
              </div>
              <div v-if="user.user_type * 1 !== 100" class="flex-box">
                <NuxtLink :to="`/${locale}/my/kyc/result`" style="display:block;">
                  <div v-if="user.is_real_name * 1 === 0" class="warning-text mg-t12">{{ $t('未认证') }}</div>
                  <div v-if="user.is_real_name * 1 === 1" class="waiting-text mg-t12">{{ $t('审核中') }}</div>
                  <div v-if="user.is_real_name * 1 === 2" class="warning-text mg-t12">{{ $t('认证失败') }}</div>
                  <div v-if="user.is_real_name * 1 === 3" class="success-text mg-t12">{{ $t('已认证') }}</div>
                </NuxtLink>
                <NuxtLink :to="`/${locale}/rate-list`" style="display:block;">
                  <div class="user-level-box">
                    {{ userLevel }}
                  </div>
                </NuxtLink>
              </div>
            </div>
            <!-- <NuxtLink :to="`/${locale}/my/account/device`" class="user-device-info">
              <p class="font-size-14 fit-tc-primary">{{ user.ip_province }}</p>
              <p class="font-size-14 fit-tc-primary">{{ user.login_ip_address }}</p>
            </NuxtLink> -->
            <ul>
              <li v-if="user.user_type * 1 === 100 && user.is_switch_login * 1 === 1" @click="showAccountList = true">
                <NuxtLink class="flex-box fit-tc-primary">
                  <div class="user-icon zizhanghuqie"></div>
                  <p>{{ $t('切换账户') }}</p>
                </NuxtLink>
              </li>
              <li>
                <NuxtLink :to="`/${locale}/my/dashboard`" class="flex-box fit-tc-primary">
                  <div class="user-icon zonglan"></div>
                  <p>{{ $t('总览') }}</p>
                </NuxtLink>
              </li>
              <li>
                <NuxtLink :to="`/${locale}/my/assets/wallet`" class="flex-box fit-tc-primary">
                  <div class="user-icon zichan"></div>
                  <p>{{ $t('资产') }}</p>
                </NuxtLink>
              </li>
              <li>
                <NuxtLink :to="`/${locale}/my/orders/spot`" class="flex-box fit-tc-primary">
                  <div class="user-icon dingdan"></div>
                  <p>{{ $t('订单') }}</p>
                </NuxtLink>
              </li>
              <li v-if="user.user_type * 1 !== 100" class="flex-box space-between">
                <NuxtLink :to="`/${locale}/my/subAccount`" class="flex-box fit-tc-primary flex-1">
                  <div class="user-icon zizhanghu"></div>
                  <p>{{ $t('子账户') }}</p>
                </NuxtLink>
                <MonoSubtract v-if="sonInfoList.length > 0" size="16" @click.stop="showAccountList = true" />
              </li>
              <li v-if="user.user_type * 1 !== 100">
                <NuxtLink :to="`/${locale}/my/account/security`" class="flex-box fit-tc-primary">
                  <div class="user-icon shezhi"></div>
                  <p>{{ $t('设置') }}</p>
                </NuxtLink>
              </li>
              <li v-if="user.user_type * 1 !== 100">
                <NuxtLink :to="`/${locale}/my/account/api`" class="flex-box fit-tc-primary">
                  <div class="user-icon api"></div>
                  <p>{{ $t('API管理') }}</p>
                </NuxtLink>
              </li>
              <li v-if="user.user_type * 1 !== 100">
                <NuxtLink :to="`/${locale}/invite`" class="flex-box fit-tc-primary">
                  <div class="user-icon yaoqing"></div>
                  <p>{{ $t('邀请返佣') }}</p>
                </NuxtLink>
              </li>
              <li v-if="user.user_type * 1 !== 100">
                <NuxtLink :to="`/${locale}/nft/my/assets/nft-list`" class="flex-box fit-tc-primary">
                  <div class="user-icon launchpool"></div>
                  <p>{{ $t('我的NFT') }}</p>
                </NuxtLink>
              </li>
            </ul>
            <div class="user-foot-bottom" @click="useCommon.loginout()">
              {{ $t('退出') }}
            </div>
          </div>
        </ElPopover>
      </div>
      <SubAccountList v-if="showAccountList" :isShow="showAccountList" @close="showAccountList = false" />
    </div>
</template>
<script lang="ts" setup>
import { ElButton, ElPopover, ElDialog } from 'element-plus'
import { useCommonData } from '~/composables/index'
import { useUserStore } from '~/stores/useUserStore'
import { getSwitchAccountList, switchAccount } from '~/api/user'
import MonoSubtract from '~/components/common/icon-svg/MonoSubtract.vue'
import SubAccountList from '~/components/common/SubAccountList.vue'
import MonoRigthChecked from '~/components/common/icon-svg/MonoRigthChecked.vue'
const useCommon = useCommonData()
const { locale, t } = useI18n()
const router = useRouter()
const props = defineProps({
  isLogin: {
    type: Boolean,
    default: false
  },
  user: {
    type: Object,
    default () {
      return {}
    }
  }
})
const store = useUserStore()
const { getUserInfoAction, getsubAccountList } = store
const { isVisibleUserInfo, mainUserInfo, sonInfoList } = storeToRefs(store)
const sqSonList = computed(() => {
  return sonInfoList.value.filter((item) => {
    return item.type * 1 === 1
  })
})
const kjSonList = computed(() => {
  return sonInfoList.value.filter((item) => {
    return item.type * 1 === 2
  })
})
const curPage = computed(() => {
  const pathAry = router.currentRoute.value.path.split('/')
  pathAry.splice(1, 1)
  if (pathAry.join('/').endsWith('/')) {
    return pathAry.join('/').replace(/\/$/g, '')
  }
  return pathAry.join('/')
})
const isMainPage = computed(() => {
  return curPage.value === '' || curPage.value.includes('/exchange/') || (curPage.value.includes('/future/') && !curPage.value.includes('/my/orders/future/current') && !curPage.value.includes('/my/orders/future/history') && !curPage.value.includes('/my/orders/future/deals')) || curPage.value.includes('/launchpool')
})
const goCharge = () => {
  router.push(`/${locale.value}/my/assets/deposit`)
}
const showAccountList = ref(false)
const userLevel = computed(() => {
  const levelType = useCommon.userLevelMethod(Math.min(props.user.user_level * 1, props.user.contract_level * 1)).levelType
  const nowLevel = useCommon.userLevelMethod(Math.min(props.user.user_level * 1, props.user.contract_level * 1)).nowLevel
  return levelType === 'special' ? t('特殊费率') : levelType + ' ' + nowLevel
})
const checkMainAccount = async(userId) => {
  const { data, error } = await switchAccount({
    user_id: userId
  })
  if (data) {
    await getUserInfoAction()
    useCommon.showMsg('success', t('切换账户成功！'))
  } else {
    useCommon.showMsg('error', useCommon.err(error.code, error))
  }
}
onBeforeMount(() => {
  getsubAccountList()
})
</script>
<style lang="scss">
.normal-btn{
  margin-left:0 !important;
}
.user-login-head-cont{
  padding-right:16px;
  position:relative;
  &:after{
    // content: '';
    // position:absolute;
    // top:50%;
    // right:0;
    // margin-top:-8px;
    // width:1px;
    // height:16px;
    // @include bg-color(border);
  }
  .el-button{
    border-radius:6px;
    &.special-btn{
      @include bg-color(bg-primary);
      @include border-color(bg-primary);
    }
  }
}
.el-popover{
  &.user-popover-cont{
    .user-cont{
      .user-head-top{
        padding:16px;
        font-size:16px;
        @include color(tc-primary);
        .user-gray-box{
          font-size:12px;
          margin-left:12px;
          padding:4px 8px;
          border-radius:4px;
          @include bg-color(bg-quaternary);
          @include color(tc-secondary);
        }
        .user-level-box{
          margin-left:12px;
          margin-top:12px;
          font-size:12px;
          padding:4px 8px;
          border-radius:4px;
          background:rgba(240, 185, 11, 0.1);
          @include color(theme);
        }
      }
      .warning-text{
        font-size:12px;
        padding:4px 8px;
        border-radius:2px;
        background: rgba(219, 99, 114, 0.1);
        cursor:pointer;
        @include color(warn);
      }
      .waiting-text{
        font-size:12px;
        padding:4px 8px;
        border-radius:2px;
        cursor:pointer;
        @include color(tc-secondary);
        @include bg-color(bg-quaternary);
      }
      .success-text{
        font-size:12px;
        padding:4px 8px;
        border-radius:2px;
        background: rgba(59, 193, 137, 0.1);
        cursor:pointer;
        @include color(rise);
      }
      .user-device-info{
        padding:8px 24px;
        margin:0 -12px;
        border-top:1px solid;
        border-bottom:1px solid;
        @include border-color(border);
      }
      ul{
        li{
          font-size:14px;
          padding:12px 24px;
          margin:0 -12px;
          cursor:pointer;
          @include color(tc-primary);
          @include pc-hover {
            &:hover{
              @include bg-color(bg-quaternary);
              @include color(theme);
            }
          }
          .user-icon{
            width:24px;
            height:24px;
            margin-right:12px;
            &.shiming{
              background-size: 100% auto;
              @include get-img('@/assets/images/common/icon_shiming-light.png', '@/assets/images/common/icon_shiming-dark.png');
            }
            &.zhanghu{
              background-size: 100% auto;
               @include get-img('@/assets/images/common/icon_zhanghu-light.png', '@/assets/images/common/icon_zhanghu-light.png');
            }
            &.api{
              background-size: 100% auto;
               @include get-img('@/assets/images/common/icon_api-light.png', '@/assets/images/common/icon_api-dark.png');
            }
            &.launchpool{
              background-size: 100% auto;
              @include get-img('@/assets/images/common/Launchpool-m-light.png', '@/assets/images/common/Launchpool-m-dark.png');
            }
            &.zonglan{
              background-size: 100% auto;
              @include get-img('@/assets/images/common/icon_zonglan-light.png', '@/assets/images/common/icon_zonglan-dark.png');
            }
            &.zichan{
              background-size: 100% auto;
              @include get-img('@/assets/images/common/icon_zichan-light.png', '@/assets/images/common/icon_zichan-dark.png');
            }
            &.dingdan{
              background-size: 100% auto;
              @include get-img('@/assets/images/common/icon_dingdan-light.png', '@/assets/images/common/icon_dingdan-dark.png');
            }
            &.shezhi{
              background-size: 100% auto;
              @include get-img('@/assets/images/common/icon_shezhi-light.png', '@/assets/images/common/icon_shezhi-dark.png');
            }
            &.zizhanghu{
              background-size: 100% auto;
              @include get-img('@/assets/images/common/icon_zizhanghu-light.png', '@/assets/images/common/icon_zizhanghu-dark.png');
            }
            &.zizhanghuqie{
              background-size: 100% auto;
              @include get-img('@/assets/images/common/icon_zizhanghuqie-light.png', '@/assets/images/common/icon_zizhanghuqie-dark.png');
            }
            &.yaoqing{
              background-size: 100% auto;
              @include get-img('@/assets/images/common/icon_yaoqing-light.png', '@/assets/images/common/icon_yaoqing-dark.png');
            }
          }
        }
      }
      .user-foot-bottom{
        border-top:1px solid;
        padding:12px 24px;
        margin:0 -12px;
        text-align:center;
        font-size:14px;
        cursor:pointer;
        @include border-color(border);
        @include color(tc-primary);
        @include pc-hover {
          &:hover{
            @include color(theme);
          }
        }
      }
    }
  }
}
.switch-accont-wrap{
  margin:0 -12px;
  max-height:calc(100vh - 100px);
  overflow-y:auto;
  h2{
    font-size:14px;
    padding:8px 20px;
    @include color(tc-primary);
  }
  ul{
    li{
      cursor:pointer;
      padding:12px 20px;
      font-size:14px;
      position:relative;
      @include color(tc-primary);
      .white-text{
        white-space:nowrap;
        overflow:hidden;
        text-overflow: ellipsis;
      }
      .right-cont{
        display:none;
      }
      &.active{
        @include bg-color(bg-quaternary);
        @include color(theme);
        .fit-tc-secondary{
          @include color(theme);
        }
        .right-cont{
          display:block;
          position:absolute;
          top:50%;
          right:10px;
          margin-top:-8px;
        }
      }
      &:hover{
        @include bg-color(bg-quaternary);
      }
    }
  }
}
</style>
