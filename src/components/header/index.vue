<template>
  <header v-if="!isApp()" class="madEx-header flex-box space-between">
    <div class="madEx-header-left flex-box space-between flex-1 mg-r16">
      <div class="flex-box flex-1">
        <NuxtLink :to="`/${locale}`" class="madEx-logo">
          <img src="~/assets/images/common/logo.png" />
        </NuxtLink>
        <MenuList v-if="!isMobile" :modeType="modeType" />
      </div>
      <SearchList v-if="!isMobile" />
    </div>
    <div v-if="!isMobile" class="madEx-header-right flex-box">
      <UserLogin :isLogin="isLogin" :user="userInfo" />
      <div class="box-line-cont mg-r16"></div>
      <DownLoad  class="mg-r16" />
      <LanguesSelect class="mg-r16"/>
      <ThemeSwitch />
    </div>
    <div v-if="isLoading && isMobile" class="madEx-header-right flex-box">
      <div class="user-login-cont mg-r16">
        <ElButton v-if="!isLogin" type="primary" class="normal-btn" @click="useCommon.openLogin()">{{ $t('登录') }}</ElButton>
        <div v-if="isLogin" class="head-icon flex-box space-center mg-r16">
          <MonoUserInfo size="16" @click="showMenu('user')" />
        </div>
      </div>
      <div class="head-icon flex-box space-center mg-r16">
        <MonoSearch size="16" @click="openSearch" />
      </div>
      <div class="head-icon flex-box space-center">
        <MonoMenuIcon size="16" @click="showMenu('menu')" />
      </div>
    </div>
    <SearchListM v-if="isShowSearchDom" :isShow="isShowSearch" @close="closeSearch" />
    <MenuListM v-if="isDeleteMenuDom" :isShow="isShowMenu" :user="userInfo" :menuType="curClickType" :isVisibleUserInfo="isVisibleUserInfo" @close="closeMenuFun" @checkUserList="showAccountList = true" />
    <SubAccountList v-if="showAccountList" :isShow="showAccountList" @close="showAccountList = false" />
  </header>
</template>
<script lang="ts" setup>
  import { isApp } from '~/utils'
  import MenuList from '~/components/header/MenuList.vue'
  import SearchList from '~/components/header/SearchList.vue'
  import UserLogin from '~/components/header/UserLogin.vue'
  import DownLoad from '~/components/header/DownLoad.vue'
  import ThemeSwitch from '~/components/header/ThemeSwitch.vue'
  import LanguesSelect from '~/components/header/LanguesSelect.vue'
  import MenuListM from '~/components/header/MenuListM/index.vue'
  import SearchListM from '~/components/header/MenuListM/SearchListM.vue'
  import SubAccountList from '~/components/common/SubAccountList.vue'
  import { useCommonData } from '~/composables/index'
  import { useUserStore } from '~/stores/useUserStore'
  import { commonStore } from '~/stores/commonStore'
  const props = defineProps({
    modeType: {
      type: String,
      default: ''
    }
  })
  const publicStore = commonStore()
  const { getPairDetail } = publicStore
  const store = useUserStore()
  const { getUserInfoAction, LoginStaus } = store
  const { isLogin, isUserInfoLoading, userInfo, isVisibleUserInfo } = storeToRefs(store)
  const useCommon = useCommonData()
  const { locale } = useI18n()
  const screenWidth = ref(0)
  const isMobile = ref(true)
  const isLoading = ref(false)
  const isShowMenu = ref(false)
  const isDeleteMenuDom = ref(false)
  const showAccountList = ref(false)
  const updateScreenWidth = () => {
    screenWidth.value = window.innerWidth
    isMobile.value = screenWidth.value <= 1024
  }
  const closeMenuFun = () => {
    isShowMenu.value = false
    setTimeout(() => {
      isDeleteMenuDom.value = false
    }, 300)
  }
  const curClickType = ref('')
  const showMenu = (type) => {
    curClickType.value = type
    console.log(curClickType.value, 'dhdhduehduheudheuheuheu')
    isShowMenu.value = true
    isDeleteMenuDom.value = true
  }
  const isShowSearchDom = ref(false)
  const isShowSearch = ref(false)
  const openSearch = () => {
    isShowSearchDom.value = true
    isShowSearch.value = true
  }
  const closeSearch = () => {
    isShowSearch.value = false
    setTimeout(() => {
      isShowSearchDom.value = false
    }, 300)
  }
  onMounted(async() => {
    getPairDetail()
    if (!isUserInfoLoading.value) {
      const isLoginCookie = cookies.get('isLogin')
      if (isLoginCookie) {
        try {
          if (isLoginCookie && isLoginCookie !== 'false') {
            LoginStaus(true)
          } else {
            LoginStaus(false)
          }
        } catch (err) {
          console.info('login auth error:', err)
        }
      }
      await getUserInfoAction()
    }
     isLoading.value = true
    updateScreenWidth()
    window.addEventListener('resize', updateScreenWidth)
  })
</script>
<style lang="scss">
.madEx-header{
  height:74px;
  overflow:hidden;
  padding:0 20px 0 8px;
  border-bottom:1px solid;
  @include border-color(border);
  .madEx-header-left{
    .madEx-logo{
      display:block;
      padding: 12px;
      height:auto;
      margin-right:21px;
      position:relative;
      img{
        display:block;
        width:80px;
        height:auto;
      }
      &:after{
        position:absolute;
        content:'';
        width:1px;
        height:15px;
        display:block;
        top:50%;
        right:-16px;
        margin-top:-4px;
        @include bg-color(border);
      }
    }
  }
  .head-icon{
    width:32px;
    height:32px;
    border-radius:50%;
    cursor:pointer;
    @include bg-color(bg-secondary);
    @include color(tc-primary);
    @include pc-hover{
      &:hover{
        @include color(theme);
      }
    }
  }
  .box-line-cont{
    width:1px;
    height:14px;
    @include bg-color(border);
  }
}
@include md {
  .madEx-header{
    height:64px;
    .madEx-header-left{
      .madEx-logo{
        margin-right:0;
        &:after{
          display:none;
        }
      }
    }
    .user-login-cont{
      .head-icon{
        position:relative;
        &:after{
          display:block;
          content: '';
          position:absolute;
          top:50%;
          right:-12px;
          margin-top:-6px;
          width:1px;
          height:12px;
          @include bg-color(border);
        }
      }
    }
    .head-icon{
      width:28px;
      height:28px;
    }
  }
}
@include mb {
  .madEx-header{
    height:64px;
    .mg-r16{
      margin-right:12px !important;
    }
    .madEx-header-left{
      .madEx-logo{
        margin-right:0;
        &:after{
          display:none;
        }
      }
    }
    .user-login-cont{
      .head-icon{
        position:relative;
        &:after{
          display:block;
          content: '';
          position:absolute;
          top:50%;
          right:-12px;
          margin-top:-6px;
          width:1px;
          height:12px;
          @include bg-color(border);
        }
      }
    }
    .head-icon{
      width:28px;
      height:28px;
      font-size:16px;
      svg{
        font-size:16px !important;
      }
    }
  }
}
</style>
