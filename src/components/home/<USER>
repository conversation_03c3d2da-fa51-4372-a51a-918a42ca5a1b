<template>
  <div v-if="landingPairs.length > 0" class="landing-pair-wrapper">
    <div class="new-title">{{ $t('即将上线') }}</div>
    <Swiper
      v-if="landingPairs.length > 0"
      :modules="[SwiperPagination, Autoplay]"
      :breakpoints="breakpoints"
      :pagination="{ clickable: true}"
      class="landing-pair-swiper"
      >
      <SwiperSlide v-for="(item, ind) in landingPairs" :key="ind + 'landingPairs1'" class="flex-box space-center landing-pair-slider">
        <countDownItem :item="item" :landing-pairs="landingPairs" @changeDateList="getLandingPairs()" />
      </SwiperSlide>
    </Swiper>
  </div>
</template>
<script lang="ts" setup>
  import { Swiper, SwiperSlide } from 'swiper/vue'
  import { getLandingPairsApi } from '~/api/public.ts'
  import countDownItem from './CountDownItem'
  import { Autoplay } from 'swiper/modules'
  const landingPairs = ref([])
  const breakpoints = computed(() => {
    return { 
      768: {  //当屏幕宽度大于等于768 
        slidesPerView: landingPairs.value.length >= 2 ? 2 : landingPairs.value.length,
        spaceBetween: 20
      },
      1064: {  //当屏幕宽度大于等于1280
        slidesPerView: landingPairs.value.length >= 3 ? 3 : landingPairs.value.length,
        spaceBetween: 12
      }
    }
  })
  const autoConfig = reactive({
    delay: 1500, // 间隔时间
    disableOnInteraction: false, // 设置为false，用户交互（滑动）后自动播放不会被禁用，每次交互后都会重新启动
    reverseDirection: false, // 是否反方向轮播
    stopOnLastSlide: false, // 执行到最后
    pauseOnMouseEnter:true // 鼠标输入时暂停
  })
  const getLandingPairs = async() => {
    const { data, error } = await getLandingPairsApi()
    if (data) {
      landingPairs.value = data
    }
  }
  onBeforeMount(() => {
    getLandingPairs()
  })
</script>
<style lang="scss">
  .landing-pair-wrapper{
    border-radius:16px;
    padding:16px 16px 0;
    .new-title{
      padding: 0 16px 20px;
      font-size: 18px;
      width: 140px;
      box-sizing: border-box;
      @include color(tc-primary);
    }
    .landing-pair-slider{
      @include bg-color(bg-secondary);
      border-radius:12px;
      padding:20px 0;
    }
    .landing-pair-swiper{
      padding-bottom:30px;
    }
    .swiper-pagination{
      .swiper-pagination-bullet{
        width:8px;
        height:4px;
        margin:0 5px;
        border-radius:8px;
        opacity:1;
        @include bg-color(bg-secondary);
        &.swiper-pagination-bullet-active{
          width:8px;
          height:4px;
          border-radius:8px;
          @include bg-color(theme);
          &:after{
            display:none;
          }
        }
      }
    }
  }
</style>