<template>
	<div class="markets-wrap-container pd-t20 pd-b40">
		<div class="markets-wrapper flex-box align-start">
			<MarketsTableList class="flex-3" :is-mobile="isMobile" :is-loading="isLoading" :is-min="isMin" :markets="marketsObj || {}" />
			<MarketsRankList class="flex-1" :is-mobile="isMobile" :is-loading="isLoading" :markets="marketsObj || {}" />
    </div>
	</div>
</template>
<script setup lang="ts">
import MarketsRankList from './Markets/RankList.vue'
import MarketsTableList from './Markets/TableList.vue'
import { commonStore } from '~/stores/commonStore'
const store = commonStore()
const { marketsObj } = storeToRefs(store)
defineProps({
  isMobile: {
    type: Boolean,
    default: false
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  isMin: {
    type: Boolean,
    default: false
  }
})
</script>
<style lang="scss" scoped>
.markets-wrap-container{
	padding:0 20px;
	.markets-wrapper{
		width:100%;
	}
}
@include md1280 {
  .markets-wrap-container{
    .markets-wrapper{
      &.flex-box{
        flex-direction: column-reverse;
        .flex-3{
          width:100%;
        }
      }
    }
	}
}
@include md{
	.markets-wrap-container{
    .markets-wrapper{
      &.flex-box{
        flex-direction: column-reverse;
        .flex-3{
          width:100%;
        }
      }
    }
	}
}
@include mb{
	.markets-wrap-container{
    &.pd-t20{
      padding-top:0 !important;
    }
    .markets-wrapper{
      &.flex-box{
        flex-direction: column-reverse;
        .flex-3{
          width:100%;
        }
      }
    }
	}
}
</style>
