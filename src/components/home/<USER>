<template>
  <div class="banner-swiper-cont">
    <Swiper
      v-if="bannerList.length > 0"
      class="home-banner-swiper pd-b32"
      ref="mySwiperRef"
      :modules="[SwiperNavigation, SwiperPagination, Autoplay]"
      :slides-per-view="1" 
      :space-between="40" 
      navigation
      :breakpoints="breakpoints" 
      :pagination="{ clickable: true }"
      :speed="800"
      :loop="shouldLoop"
      :autoplay="autoConfig"
      @swiper="onSwiper"
    >
      <SwiperSlide v-for="(item, index) in bannerList" :key="item.id" :virtualIndex="index">
        <div class="schema-card">
          <a :href="`${(item[swiper] || item['en-ww'] || item['zh-cn'] || {}).url}`" target="_blank">
            <img :src="`${imgDmain}${(item[swiper] || item['en-ww'] || item['zh-cn'] || {}).imgUrl}`">
          </a>
        </div>
      </SwiperSlide>
    </Swiper>
  </div>
</template>

<script setup lang="ts">
import { imgDmain } from '~/config'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Autoplay } from 'swiper/modules'

const { locale } = useI18n()
const props = defineProps({
  bannerList: {
    type: Array,
    default: () => []
  }
})

// 响应式窗口宽度
const windowWidth = ref()
// 监听窗口大小变化
const handleResize = () => {
  windowWidth.value = window.innerWidth
}
onMounted(() => {
  handleResize()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 计算是否应该启用loop
const shouldLoop = computed(() => {
  // 如果屏幕宽度小于1280，或者bannerList长度大于等于6，则启用loop
  return windowWidth.value < 1280 || props.bannerList.length >= 6
})

const autoConfig = reactive({
  delay: 1500,
  disableOnInteraction: false,
  reverseDirection: false,
  stopOnLastSlide: false,
  pauseOnMouseEnter: true
})

const swiperObj = ref({
  zh: 'zh-cn',
  en: 'en-ww',
  ja: 'ja-jp',
  ko: 'ko-kr',
  'zh-Hant': 'zh-Hant'
})

const swiper = computed(() => {
  return swiperObj.value[locale.value]
})

const breakpoints = ref({
  768: {
    slidesPerView: 2,
    spaceBetween: 20
  },
  1064: {
    slidesPerView: 3,
    spaceBetween: 12
  }
})

let useSwiper: any = null

const onSwiper = (swiper: any) => {
  useSwiper = swiper
}

const changePage = (num: number) => {
  if (num > 0) {
    useSwiper.slideNext()
  } else if (num < 0) {
    useSwiper.slidePrev()
  }
}
</script>
<style lang="scss">
.schema-card{
  overflow:hidden;
  a{
    display:block;
  }
  img{
    border-radius:16px;
    width:100%;
    height:auto;
  }
}
.banner-swiper-cont{
  padding:20px 20px 0;
}
.home-banner-swiper{
  .swiper-pagination{
    display:flex;
    align-items:center;
    justify-content: center;
    bottom:0px;
    &.swiper-pagination-lock{
      display:none;
      &.swiper-pagination-clickable{
        display:flex;
      }
    }
    .swiper-pagination-bullet{
      display:block;
      width:12px;
      height:12px;
      border-radius:50%;
      opacity: 1;
      margin:0 12px;
      @include bg-color(bg-secondary);
      &.swiper-pagination-bullet-active{
        position:relative;
        width:18px;
        height:18px;
        border-radius:50%;
        @include bg-color(theme);
        &:after{
          content: '';
          display:block;
          width:6px;
          height:6px;
          position:absolute;
          top:50%;
          left:50%;
          margin-top:-3px;
          margin-left:-3px;
          border-radius:50%;
          @include bg-color(bg-primary);
        }
      }
    }
  }
  .swiper-button-next, .swiper-button-prev{
    width:46px;
    height:46px;
    border-radius:50%;
    top: var(--swiper-navigation-top-offset, 40%);
    @include bg-color(bg-primary);
    &:after{
      font-size:14px;
      font-weight:bold;
      @include color(tc-primary);
    }
  }
}
@include md{
  .home-banner-swiper{
    .swiper-pagination{
      .swiper-pagination-bullet{
        width:10px;
        height:10px;
        margin:0 8px;
        &.swiper-pagination-bullet-active{
          width:16px;
          height:16px;
           &:after{
            width:4px;
            height:4px;
            margin-top:-2px;
            margin-left:-2px;
          }
        }
      }
    }
    .swiper-button-next, .swiper-button-prev{
      width:36px;
      height:36px;
      &:after{
        font-size:14px;
      }
    }
  }
}
@include mb {
  .schema-card{
    border-radius:0;
    display:flex;
    align-items:center;
    justify-content: center;
    a{
      display:flex;
      align-items:center;
      justify-content: center;
    }
    img{
      width:100%;
      border-radius:0;
    }
  }
  .banner-swiper-cont{
    padding:0;
  }
  .home-banner-swiper{
    padding-bottom:0;
    .swiper-pagination{
      bottom:14px;
      .swiper-pagination-bullet{
        width:8px;
        height:4px;
        margin:0 5px;
        border-radius:8px;
        &.swiper-pagination-bullet-active{
          width:8px;
          height:4px;
          border-radius:8px;
           &:after{
            display:none;
          }
        }
      }
    }
    .swiper-button-next, .swiper-button-prev{
      display:none;
    }
  }
}
</style>