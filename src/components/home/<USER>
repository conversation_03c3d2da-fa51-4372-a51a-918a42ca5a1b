<template>
  <div v-if="leftTimeMap[item.pair]" class="cursor-pointer flex-box new-token-bg space-center" @click="toTrade(item)">
    <BoxCoinIcon :icon="item.icon_url" class="font-size-32 mg-r16" />
    <div class="flex-box space-center">
      <div class="flex-box">
        <div class="flex-box align-baseline">
          <div class="font-size-18 pd-r4 fit-tc-primary">{{ item.pair.includes('_SWAP') ? item.pair.replace('_SWAP', '').replace('_', '') : item.pair.replace('_', '/') }}</div>
        </div>
        <div v-if="leftTimeMap[item.pair]" class="time-area flex-box">
          {{ leftTimeMap[item.pair].h }}
          <span class="split mg-lr8">:</span>
          {{ leftTimeMap[item.pair].m }}
          <span class="split mg-lr8">:</span>
          {{ leftTimeMap[item.pair].s }}
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { getTimeLeft } from '~/utils'
  import MonoRightArrow from '~/components/common/icon-svg/MonoRightArrow.vue'
  const router = useRouter()
  const { locale, t } = useI18n()
  const props = defineProps({
    item: {
      default: () => {
        return {}
      },
      type: Object
    },
    landingPairs: {
      default: () => {
        return []
      },
      type: Array
    }
  })
  const emit = defineEmits(['changeDateList'])
  const interval = ref('')
  const leftTimeMap = ref({})
  const countDown = () => {
    interval.value && clearInterval(interval.value)
    interval.value = setInterval(() => {
      props.landingPairs.forEach(item => {
        const time = new Date().getTime()
        const leftTime = item.time_left - (time - (item.local_time || item.system_time))
        const leftTimeMapP = getTimeLeft(leftTime)
        if (leftTime <= 500) {
          emit('changeDateList')
        }
        leftTimeMap.value[item.pair] = leftTimeMapP || {}
      })
      // this.$forceUpdate()
    }, 1000)
  }
  const toTrade = (item) => {
    if (item.pair.includes('_SWAP')) {
      router.push(`/${locale.value}/future/${item.pair}`)
    } else {
      router.push(`/${locale.value}/exchange/${item.pair}`)
    }
  }
  onMounted(() => {
    countDown()
  })
  onBeforeUnmount(() => {
    interval.value && clearInterval(interval.value)
  })
</script>
<style lang="scss" scoped>
  .time-area {
    margin-left: 24px;
    padding-left: 24px;
    font-size: 18px;
    line-height: 24px;
    @include color(tc-primary);
    position:relative;
    &:before{
      display:block;
      content: '';
      width:1px;
      height:14px;
      position:absolute;
      top:50%;
      left:0;
      margin-top:-7px;
      @include bg-color(tc-primary);
    }
    .split {
      font-size: 16px;
      line-height: 24px;
      opacity: 0.6;
    }
  }
</style>