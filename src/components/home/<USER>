<template>
  <div v-if="isShowDownload" class="download-cont">
    <div class="download-wrap flex-box space-between">
      <div class="download-close-btn" @click="isShowDownload = false">
        <MonoClose size="18" class="fit-tc-secondary" />
      </div>
      <dl class="flex-box">
        <dt>
          <img src="~@/assets/images/home/<USER>" />
        </dt>
        <dd>
          <h2>{{ $t('KTX应用程序') }}</h2>
          <p>{{ $t('安全,极速,尽在掌中') }}</p>
        </dd>
      </dl>
      <el-button type="primary" @click="goDownloadFun()">{{ $t('下载APP') }}</el-button>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ElButton } from 'element-plus'
  const { locale, t } = useI18n()
  const router = useRouter()
  const isShowDownload = ref(true)
  const goDownloadFun = () => {
    router.push(`/${locale.value}/download`)
  }
</script>
<style lang="scss" scoped>
  .download-cont{
    display:none;
  }
  @include mb{
    .download-cont{
      display:block;
      position:fixed;
      bottom:0;
      left:0;
      right:0;
      padding:8px 16px;
      z-index:2000;
      .download-wrap{
        border:1px solid;
        border-radius:12px;
        padding:16px;
        position:relative;
        @include border-color(border);
        @include bg-color(bg-dialog);
        .download-close-btn{
          position:absolute;
          right:0;
          top:0;
          padding:2px 6px;
          cursor:pointer;
        }
        dl{
          dt{
            width:40px;
            height:40px;
            margin-right:8px;
          }
          dd{
            h2{
              font-size:16px;
              @include color(tc-primary);
            }
            p{
              font-size:12px;
              padding-top:4px;
              @include color(tc-secondary);
            }
          }
        }
      }
    }
  }
</style>