<template>
  <div class="rank-list-cont">
    <BoxLoading v-if="isLoading" />
    <div class="rank-list-wrap isPc">
      <ul>
        <li class="item-title flex-box space-between">
          <div class="flex-box">
            <MonoFire size="24" color="#FF6262" class="mg-r16" />
            {{ $t('成交榜') }}
          </div>
          <!-- <div class="more-icon">
            <MonoRightArrowShort size="16" />
          </div> -->
        </li>
        <li v-for="(item, dealKey) in dealList" :key="dealKey" class="item-li flex-box space-between" @click="toTrade(item)">
          <div class="li-left flex-box">
            <BoxCoinIcon :icon="item.icon_url" size="24" class="mg-r16" />
            <div class="left-text">
              <p class="fit-tc-primary">{{ item.pair.replace('_', '/') }}</p>
              <p class="fit-tc-secondary">{{ item.general_name }}</p>
            </div>
          </div>
          <div class="li-right">
            <div class="right-text">
              <p class="fit-tc-primary">{{ format((markets[item.pair] ? markets[item.pair].last : item.last), (pairInfo[item.pair] || {}).price_scale, true) }}</p>
              <p :class="className(markets[item.pair] ? markets[item.pair].change : item.change)">{{ (markets[item.pair] ? markets[item.pair].change : item.change) * 1 > 0 ? '+' : '' }}{{ format(((markets[item.pair] ? markets[item.pair].change : item.change) * 100), 2, true) }}%</p>
            </div>
          </div>
        </li>
      </ul>
      <ul>
        <li class="item-title flex-box space-between">
          <div class="flex-box">
            <MonoKlineRotate size="24" color="#3BC189" class="mg-r16" />
            {{ $t('涨幅榜') }}
          </div>
          <!-- <div class="more-icon">
            <MonoRightArrowShort size="16" />
          </div> -->
        </li>
        <li v-for="(item, upKey) in upList" :key="upKey" class="item-li flex-box space-between" @click="toTrade(item)">
          <div class="li-left flex-box">
            <BoxCoinIcon :icon="item.icon_url" size="24" class="mg-r16" />
            <div class="left-text">
              <p class="fit-tc-primary">{{ item.pair.replace('_', '/') }}</p>
              <p class="fit-tc-secondary">{{ item.general_name }}</p>
            </div>
          </div>
          <div class="li-right">
            <div class="right-text">
              <p class="fit-tc-primary">{{ format((markets[item.pair] ? markets[item.pair].last : item.last), (pairInfo[item.pair] || {}).price_scale, true) }}</p>
              <p :class="className(markets[item.pair] ? markets[item.pair].change : item.change)">{{ (markets[item.pair] ? markets[item.pair].change : item.change) * 1 > 0 ? '+' : '' }}{{ format(((markets[item.pair] ? markets[item.pair].change : item.change) * 100), 2, true) }}%</p>
            </div>
          </div>
        </li>
      </ul>
      <ul>
        <li class="item-title flex-box space-between">
          <div class="flex-box">
            <MonoKlineRotate size="24" color="#FF6262" class="mg-r16" />
            {{ $t('跌幅榜') }}
          </div>
          <!-- <div class="more-icon">
            <MonoRightArrowShort size="16" />
          </div> -->
        </li>
        <li v-for="(item, downKey) in downList" :key="downKey" class="item-li flex-box space-between" @click="toTrade(item)">
          <div class="li-left flex-box">
            <BoxCoinIcon :icon="item.icon_url" size="24" class="mg-r16" />
            <div class="left-text">
              <p class="fit-tc-primary">{{ item.pair.replace('_', '/') }}</p>
              <p class="fit-tc-secondary">{{ item.general_name }}</p>
            </div>
          </div>
          <div class="li-right">
            <div class="right-text">
              <p class="fit-tc-primary">{{ format((markets[item.pair] ? markets[item.pair].last : item.last), (pairInfo[item.pair] || {}).price_scale, true) }}</p>
              <p :class="className(markets[item.pair] ? markets[item.pair].change : item.change)">{{ (markets[item.pair] ? markets[item.pair].change : item.change) * 1 > 0 ? '+' : '' }}{{ format(((markets[item.pair] ? markets[item.pair].change : item.change) * 100), 2, true) }}%</p>
            </div>
          </div>
        </li>
      </ul>
    </div>
    <Swiper
        class="rank-list-wrap isMobile"
        ref="mySwiperRef"
        :modules="[SwiperPagination]"
        :slides-per-view="1" 
        :space-between="40" 
        navigation
        :breakpoints="breakpoints" 
        :pagination="{ clickable: true}"
      >
      <SwiperSlide>
        <ul>
          <li class="item-title flex-box space-between">
            <div class="flex-box">
              <MonoFire size="24" color="#FF6262" class="mg-r16" />
              {{ $t('成交榜') }}
            </div>
            <!-- <div class="more-icon">
              <MonoRightArrowShort size="16" />
            </div> -->
          </li>
          <li v-for="(item, dealKey) in dealList" :key="dealKey" class="item-li flex-box space-between" @click="toTrade(item)">
            <div class="li-left flex-box">
              <BoxCoinIcon :icon="item.icon_url" size="24" class="mg-r16" />
              <div class="left-text">
                <p class="fit-tc-primary">{{ item.pair.replace('_', '/') }}</p>
                <p class="fit-tc-secondary">{{ item.general_name }}</p>
              </div>
            </div>
            <div class="li-right">
              <div class="right-text">
                <p class="fit-tc-primary">{{ format((markets[item.pair] ? markets[item.pair].last : item.last), (pairInfo[item.pair] || {}).price_scale, true) }}</p>
                <p :class="className(markets[item.pair] ? markets[item.pair].change : item.change)">{{ (markets[item.pair] ? markets[item.pair].change : item.change) * 1 > 0 ? '+' : '' }}{{ format(((markets[item.pair] ? markets[item.pair].change : item.change) * 100), 2, true) }}%</p>
              </div>
            </div>
          </li>
        </ul>
      </SwiperSlide>
      <SwiperSlide>
        <ul>
          <li class="item-title flex-box space-between">
            <div class="flex-box">
              <MonoKlineRotate size="24" color="#3BC189" class="mg-r16" />
              {{ $t('涨幅榜') }}
            </div>
            <!-- <div class="more-icon">
              <MonoRightArrowShort size="16" />
            </div> -->
          </li>
          <li v-for="(item, upKey) in upList" :key="upKey" class="item-li flex-box space-between" @click="toTrade(item)">
            <div class="li-left flex-box">
              <BoxCoinIcon :icon="item.icon_url" size="24" class="mg-r16" />
              <div class="left-text">
                <p class="fit-tc-primary">{{ item.pair.replace('_', '/') }}</p>
                <p class="fit-tc-secondary">{{ item.general_name }}</p>
              </div>
            </div>
            <div class="li-right">
              <div class="right-text">
                <p class="fit-tc-primary">{{ format((markets[item.pair] ? markets[item.pair].last : item.last), (pairInfo[item.pair] || {}).price_scale, true) }}</p>
                <p :class="className(markets[item.pair] ? markets[item.pair].change : item.change)">{{ (markets[item.pair] ? markets[item.pair].change : item.change) * 1 > 0 ? '+' : '' }}{{ format(((markets[item.pair] ? markets[item.pair].change : item.change) * 100), 2, true) }}%</p>
              </div>
            </div>
          </li>
        </ul>
      </SwiperSlide>
      <SwiperSlide>
        <ul>
          <li class="item-title flex-box space-between">
            <div class="flex-box">
              <MonoKlineRotate size="24" color="#FF6262" class="mg-r16" />
              {{ $t('跌幅榜') }}
            </div>
            <!-- <div class="more-icon">
              <MonoRightArrowShort size="16" />
            </div> -->
          </li>
          <li v-for="(item, downKey) in downList" :key="downKey" class="item-li flex-box space-between" @click="toTrade(item)">
            <div class="li-left flex-box">
              <BoxCoinIcon :icon="item.icon_url" size="24" class="mg-r16" />
              <div class="left-text">
                <p class="fit-tc-primary">{{ item.pair.replace('_', '/') }}</p>
                <p class="fit-tc-secondary">{{ item.general_name }}</p>
              </div>
            </div>
            <div class="li-right">
              <div class="right-text">
                <p class="fit-tc-primary">{{ format((markets[item.pair] ? markets[item.pair].last : item.last), (pairInfo[item.pair] || {}).price_scale, true) }}</p>
                <p :class="className(markets[item.pair] ? markets[item.pair].change : item.change)">{{ (markets[item.pair] ? markets[item.pair].change : item.change) * 1 > 0 ? '+' : '' }}{{ format(((markets[item.pair] ? markets[item.pair].change : item.change) * 100), 2, true) }}%</p>
              </div>
            </div>
          </li>
        </ul>
      </SwiperSlide>
    </Swiper>
  </div>
</template>
<script setup lang="ts">
  import MonoFire from '~/components/common/icon-svg/MonoFire.vue'
  import MonoKlineRotate from '~/components/common/icon-svg/MonoKlineRotate.vue'
  import { useCommonData } from '~/composables/index'
  import { format } from '~/utils'
  import { commonStore } from '~/stores/commonStore'
  import { MarketsStore } from '~/stores/marketsStore'
  const store = commonStore()
  const { pairInfo } = storeToRefs(store)
  const marketStore = MarketsStore()
  const { getRankList } = marketStore
  const { rankObj } = storeToRefs(marketStore)
  const router = useRouter()
  const { locale, t } = useI18n()
  const useCommon = useCommonData()
  const breakpoints = ref(null)
  const props = defineProps({
    markets: {
      type: Object,
      default () {
        return {}
      }
    }
  })
  breakpoints.value = { 
    768: {  //当屏幕宽度大于等于768 
      slidesPerView: 2,
      spaceBetween: 20
    }
  }
  const dealList = computed(() => {
    return rankObj.value[0] || []
  })
  const upList = computed(() => {
    return rankObj.value[1] || []
  })
  const downList = computed(() => {
    return rankObj.value[2] || []
  })
  const isLoading = computed(() => {
    return JSON.stringify(rankObj.value) === '{}'
  })
  const className = (c) => {
    if (c * 1 < 0) {
      return 'fit-fall'
    } else {
      return 'fit-rise'
    }
  }
  const toTrade = (item) => {
    router.push(`/${locale.value}/exchange/${item.pair}`)
  }
  onBeforeMount(async () => {
    await getRankList()
    isLoading.value = false
  })
</script>
<style lang="scss">
  .rank-list-cont{
    position:relative;
    border:1px solid;
    @include border-color(border);
    .rank-list-wrap{
      &.isPc{
        display:block;
      }
      &.isMobile{
        display:none;
      }
      .swiper-button-prev, .swiper-button-next{
        display:none;
      }
      ul{
        padding:12px 0;
        border-bottom:1px solid;
        @include border-color(border);
        &:last-child{
          border-bottom:0;
        }
        .item-title{
          height:48px;
          padding:0 20px;
          font-size:16px;
          @include color(tc-primary);
          .more-icon{
            @include color(tc-secondary);
          }
        }
        .item-li{
          padding:12px 20px;
          cursor:pointer;
          @include pc-hover{
            &:hover{
              @include bg-color(bg-quaternary);
            }
          }
          &:active{
            @include bg-color(bg-quaternary);
          }
          .left-text{
            p{
              &:first-child{
                font-size:14px;
              }
              &:last-child{
                font-size:12px;
              }
            }
          }
          .right-text{
            text-align:right;
            p{
              &:first-child{
                font-size:16px;
              }
              &:last-child{
                font-size:14px;
                font-weight:600;
              }
            }
          }
        }
      }
    }
  }
  @include md1280{
    .rank-list-cont{
      width:100%;
      border:0;
      margin-bottom:12px;
      &.flex-1{
        flex:none;
      }
      .rank-list-wrap{
        padding-bottom:8px;
        &.isPc{
          display:flex;
        }
        ul{
          padding:12px 0;
          border:1px solid;
          flex:1;
          border-radius:20px;
          @include border-color(border);
          &:nth-child(2){
            margin:0 16px;
          }
          &:last-child{
            border:1px solid;
            @include border-color(border);
          }
          .item-title{
            height:48px;
            padding:0 20px;
            font-size:16px;
            @include color(tc-primary);
            .more-icon{
              @include color(tc-secondary);
            }
          }
          .item-li{
            padding:12px 20px;
            cursor:pointer;
            @include pc-hover{
              &:hover{
                @include bg-color(bg-quaternary);
              }
            }
            &:active{
              @include bg-color(bg-quaternary);
            }
            .left-text{
              p{
                &:first-child{
                  font-size:14px;
                }
                &:last-child{
                  font-size:12px;
                }
              }
            }
            .right-text{
              text-align:right;
              p{
                &:first-child{
                  font-size:16px;
                }
                &:last-child{
                  font-size:12px;
                }
              }
            }
          }
        }
      }
    }
  }
  @include md {
    .rank-list-cont{
      width:100%;
      margin-bottom:12px;
      border:0;
      &.flex-1{
        flex:none;
      }
      .rank-list-wrap{
        padding-bottom:40px;
        &.isPc{
          display:none;
        }
        &.isMobile{
          display:block;
        }
        ul{
          border:1px solid;
          border-radius:20px;
          @include border-color(border);
          &:last-child{
            border-bottom:1px solid;
            @include border-color(border);
          }
          .item-title{
            height:40px;
            padding:0 24px;
            font-size:14px;
            .more-icon{
              font-size:12px;
            }
          }
          .item-li{
            padding:8px 24px;
            .right-text{
              text-align:right;
              p{
                &:first-child{
                  font-size:14px;
                }
              }
            }
          }
        }
        .swiper-pagination{
          .swiper-pagination-bullet{
            width:8px;
            height:4px;
            margin:0 5px;
            border-radius:8px;
            opacity:1;
            @include bg-color(bg-secondary);
            &.swiper-pagination-bullet-active{
              width:8px;
              height:4px;
              border-radius:8px;
              @include bg-color(theme);
              &:after{
                display:none;
              }
            }
          }
        }
      }
    }
  }
  @include mb {
    .rank-list-cont{
      width:100%;
      margin-bottom:0;
      border:0;
      &.flex-1{
        flex:none;
      }
      .rank-list-wrap{
        padding-bottom:28px;
        &.isPc{
          display:none;
        }
        &.isMobile{
          display:block;
        }
        ul{
          border:1px solid;
          border-radius:20px;
          @include border-color(border);
          &:last-child{
            border-bottom:1px solid;
            @include border-color(border);
          }
          .item-title{
            height:40px;
            padding:0 24px;
            font-size:14px;
            .more-icon{
              font-size:12px;
            }
          }
          .item-li{
            padding:8px 24px;
            .right-text{
              text-align:right;
              p{
                &:first-child{
                  font-size:14px;
                }
              }
            }
          }
        }
        .swiper-pagination{
          .swiper-pagination-bullet{
            width:8px;
            height:4px;
            margin:0 5px;
            border-radius:8px;
            opacity:1;
            @include bg-color(bg-secondary);
            &.swiper-pagination-bullet-active{
              width:8px;
              height:4px;
              border-radius:8px;
              @include bg-color(theme);
              &:after{
                display:none;
              }
            }
          }
        }
      }
    }
  }
</style>
