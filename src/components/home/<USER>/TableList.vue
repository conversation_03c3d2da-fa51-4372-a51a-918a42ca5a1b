<template>
<div class="markets-table-cont">
  <div class="markets-table-nav">
    <el-tabs
      v-model="editableTabsValue"
      @tab-change="changeTab">
      <el-tab-pane key="collect" name="collect">
        <template #label>
          <div class="flex-box">
            <MonoCollected size="16" class="nav-icon"/>
            <span>{{ $t('自选') }}</span>
          </div>
        </template>
      </el-tab-pane>
      <el-tab-pane
        v-for="(item, index) in firstTab"
        :key="item.id"
        :name="item.id">
        <template #label>
          <div class="flex-box">
            <img :src="`${imgDmain}${item.icon_url}`" class="nav-icon" />
            <span>{{ JSON.parse(item.name)[locale] ? JSON.parse(item.name)[locale] : JSON.parse(item.name)['en'] }}</span>
          </div>
        </template>
      </el-tab-pane>
    </el-tabs>
  </div>
  <div v-if="editableTabsValue === 'collect'" class="sub-list-nav">
    <ul class="flex-box">
      <li :class="{'active': subActive === '' }" @click="clickSubTab('')">{{ $t('全部') }}</li>
      <li :class="{'active': subActive === 'spot' }" @click="clickSubTab('spot')">{{ $t('现货') }}</li>
      <li :class="{'active': subActive === 'future' }" @click="clickSubTab('future')">{{ $t('合约') }}</li>
    </ul>
  </div>
  <div v-if="editableTabsValue !== 'collect' && secondTab.length > 0" class="sub-list-nav">
    <el-tabs
      v-model="secondActive"
      @tab-change="handleTabClick"
      >
      <el-tab-pane
        v-for="(item, secondIndex) in secondTab"
        :key="secondIndex"
        :label="item.route_name"
        :name="item.id"
      ></el-tab-pane>
    </el-tabs>
  </div>
  <!--<div v-if="editableTabsValue !== 'collect' && secondTab.length > 0" class="sub-list-nav">
    <ul class="flex-box">
      <li v-for="(item, secondIndex) in secondTab" :key="secondIndex" :class="{'active': secondActive === item.id}" @click="changeTab(item.id, 2)">{{ item.route_name }}</li>
    </ul>
  </div>-->
  <div class="markets-table-wrapList">
    <BoxLoading v-show="isLoadingList" />
    <el-scrollbar v-show="!isLoadingList" ref="tableContainerRef" class="table-container mg-t4" :height="isMobile ? 800 : ''" @scroll="handleScroll">
      <ElTable
          :data="endTableList"
          style="width: 100%"
          row-class-name="cursor-pointer"
          class="marketsTable"
          @row-click="toTrade"
        >
        <template v-if="!isLoadingList && endTableList.length === 0" #empty>
          <div style="height:600px;">
            <BoxNoData :text="$t('暂无数据')" />
          </div>
        </template>
        <ElTableColumn prop="symbol" :label="$t('名称')" :min-width="!isMobile ? 190 : 140">
          <template #default="scope">
            <div class="flex-box symbolName pd-t8 pd-b8">
              <div class="collet-icon" @click.stop="selectCollect(scope.row)">
                <MonoCollected v-if="favoriteMap[scope.row.pair]" size="16" class="fit-theme" />
                <MonoCollect v-else size="16" />
              </div>
              <BoxCoinIcon :icon="scope.row.icon_url" class="iconImg" />
              <div v-if="scope.row.pair.includes('_SWAP')" class="symbolTxt">
                <h3>{{ scope.row.pair.replace('_', '').replace('_SWAP', '') }}</h3>
                <p>{{ $t('永续') }}</p>
              </div>
              <div v-else class="symbolTxt">
                <h3>{{ scope.row.pair.replace('_', '/') }}</h3>
                <p>{{ scope.row.general_name }}</p>
              </div>
            </div>
          </template>
        </ElTableColumn>
        <ElTableColumn v-if="!isMobile" prop="last" align="right" :label="$t('最新价')" min-width="120">
          <template #default="scope">
            <div class="flex-box space-end">
              <span class="flex-box space-end" style="width:90px;">{{ format((markets[scope.row.pair] ? markets[scope.row.pair].last : scope.row.last), (pairInfo[scope.row.pair] || {}).price_scale, true) || '--' }}</span>
            </div>
          </template>
        </ElTableColumn>
        <ElTableColumn v-if="!isMobile" prop="last" align="right" label="&nbsp;" min-width="90">
          <template #default="scope">
            <div class="flex-box">
              <canvas :id="`chart-${scope.row.pair}`" slot-scope="scope" width="96" height="40"></canvas>
            </div>
          </template>
        </ElTableColumn>
        <ElTableColumn v-if="!isMobile" prop="change" align="right" :label="$t('24h涨跌幅')" min-width="120">
          <template #default="scope">
            <div class="flex-box space-end">
              <span :class="className(markets[scope.row.pair] ? markets[scope.row.pair].change : scope.row.change)">{{ (markets[scope.row.pair] ? markets[scope.row.pair].change : scope.row.change) >= 0 ? '+' : '' }} {{ format(((markets[scope.row.pair] ? markets[scope.row.pair].change : scope.row.change) * 100), 2, true) || '--' }}%</span>
              <MonoDownArrowMin size="14" class="mg-l8 change-arrow" :class="className(markets[scope.row.pair] ? markets[scope.row.pair].change : scope.row.change)" />
            </div>
          </template>
        </ElTableColumn>
        <ElTableColumn v-if="!isMobile" prop="high" align="right" :label="$t('24h最高价')" min-width="110">
          <template #default="scope">
            {{ format((markets[scope.row.pair] ? markets[scope.row.pair].high : scope.row.high), (pairInfo[scope.row.pair] || {}).price_scale, true) || '--' }}
          </template>
        </ElTableColumn>
        <ElTableColumn v-if="!isMobile" prop="low" align="right" :label="$t('24h最低价')" min-width="110">
          <template #default="scope">
            {{ format((markets[scope.row.pair] ? markets[scope.row.pair].low : scope.row.low), (pairInfo[scope.row.pair] || {}).price_scale, true) || '--' }}
          </template>
        </ElTableColumn>
        <ElTableColumn v-if="!isMobile" prop="volume" align="right" :label="$t('24H成交量')" min-width="120">
          <template #default="scope">
            {{ format((Number(markets[scope.row.pair] ? markets[scope.row.pair].amount : scope.row.amount) < 0 ? -Number(markets[scope.row.pair] ? markets[scope.row.pair].amount : scope.row.amount) : Number(markets[scope.row.pair] ? markets[scope.row.pair].amount : scope.row.amount)), (pairInfo[scope.row.pair] || {}).quantity_scale, true) || '--' }}
          </template>
        </ElTableColumn>
        <ElTableColumn v-if="isMobile" :label="$t('价格/24H涨跌')" align="right">
          <template #header>
            <div class="flex-box align-end flex-column">
              <p>{{ $t('价格') }}</p>
              <p>{{ $t('24H涨跌') }}</p>
            </div>
          </template>
          <template #default="scope">
            <div class="flex-box align-end flex-column">
              <p>{{ format((markets[scope.row.pair] ? markets[scope.row.pair].last : scope.row.last), (pairInfo[scope.row.pair] || {}).price_scale, true) || '--' }}</p>
              <p :class="className(markets[scope.row.pair] ? markets[scope.row.pair].change : scope.row.change)">{{ markets[scope.row.pair] ? format(markets[scope.row.pair].change * 100, 2, true) : format(scope.row.change * 100, 2, true) || '--' }}%</p>
            </div>
          </template>
        </ElTableColumn>
        <ElTableColumn v-if="isMobile" prop="volume" :label="$t('市值/成交量')" align="right">
          <template #header>
            <div class="flex-box align-end flex-column">
              <p>{{ $t('成交量') }}</p>
              <p>{{ $t('成交额') }}</p>
            </div>
          </template>
          <template #default="scope">
            <p>{{ format((Number(markets[scope.row.pair] ? markets[scope.row.pair].volume : scope.row.volume) < 0 ? -Number(markets[scope.row.pair] ? markets[scope.row.pair].volume : scope.row.volume) : Number(markets[scope.row.pair] ? markets[scope.row.pair].volume : scope.row.volume)), (pairInfo[scope.row.pair] || {}).quantity_scale, true)  || '--' }}</p>
            <p>{{ format((markets[scope.row.pair] ? markets[scope.row.pair].amount : scope.row.amount), (pairInfo[scope.row.pair] || {}).quantity_scale, true) || '--' }}</p>
          </template>
        </ElTableColumn>
        <ElTableColumn v-if="!isMin" align="right" min-width="148" :label="$t('操作')">
          <template #header>
            <div class="pd-r12">
              {{ $t('操作') }}
            </div>
          </template>
          <template #default="scope">
            <div class="flex-box space-end pd-r12">
              <NuxtLink class="fit-theme cursor-pointer mg-r12" @click.stop="goDetail(scope.row)">{{ $t('详情') }}</NuxtLink>
              <ElButton type="primary" @click.stop="toTrade(scope.row)">{{ $t('交易') }}</ElButton>
            </div>
          </template>
        </ElTableColumn>
      </ElTable>
      <div v-if="endTableList.length > 0 && isMobile && editableTabsValue !== 'collect'" class="fit-tc-secondary text-center font-size-14">{{ moreTxt }}</div>
    </el-scrollbar>
    <ElPagination
      v-if="editableTabsValue !== 'collect' && tableTotal > 20"
      class="flex-box space-center market-pagination mg-t24"
      background
      layout="prev, pager, next"
      center
      :current-page="pager.page"
      :page-size="pager.size"
      :total="tableTotal"
      @current-change="handleCurrentChange"
      >
    </ElPagination>
  </div>
</div>
</template>
<script setup lang="ts">
import { ElTable, ElTableColumn, ElButton, ElPagination, ElScrollbar, ElTabs, ElTabPane } from 'element-plus'
import { format } from '~/utils'
import { imgDmain } from '~/config'
import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
import MonoKlineCoin from '~/components/common/icon-svg/MonoKlineCoin.vue'
import MonoKlineFang from '~/components/common/icon-svg/MonoKlineFang.vue'
import MonoNewIcon from '~/components/common/icon-svg/MonoNewIcon.vue'
import MonoViewAll from '~/components/common/icon-svg/MonoViewAll.vue'
import { getPairAreas, getPairsByArea, getAllPairs, getFavoriteList, favoriteAddOrDel } from '~/api/public'
import { useCommonData } from '~/composables/index'
import { commonStore } from '~/stores/commonStore'
import { MarketsStore } from '~/stores/marketsStore'
const marketStore = MarketsStore()
const { getAreaList, renderKline, setSecondActive } = marketStore
const { editableTabs, editableTabsValue, secondActive, tableData, tableTotal } = storeToRefs(marketStore)
const store = commonStore()
const { pairInfo } = storeToRefs(store)
const useCommon = useCommonData()
const { locale, t } = useI18n()
const router = useRouter()
const props = defineProps({
  isMobile: {
    type: Boolean,
    default: false
  },
  markets: {
    type: Object,
    default () {
      return {}
    }
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  isMin: {
    type: Boolean,
    default: false
  }
})
const tableContainerRef = ref(null)
const subActive = ref('')
const className = (c) => {
  if (c * 1 < 0) {
    return 'fit-fall'
  } else {
    return 'fit-rise'
  }
}
const moreTxt = ref('')
const pager = ref({
  page: 1,
  size: 20
})
const isLoadingList = ref(false)
watch(
  () => tableData.value.length, // 直接监听长度变化
  (length) => {
    isLoadingList.value = length === 0
  },
  { immediate: true } // 初始化时立即执行一次
)
const getAreaByList = async(id) => {
  if (pager.value.page * 1 === 1 || !props.isMobile) {
    isLoadingList.value = true
  }
  const { data, error } = await getPairsByArea({
    id,
    page: pager.value.page,
    size: pager.value.size,
    need_kline: 1
  })
  if (data) {
    // 假设tableData.value是外部管理的状态，这里直接更新
    if (props.isMobile) {
      tableTotal.value = data.count
      if (pager.value.page * 1 === 1) {
        tableData.value = data.rows
      } else {
        tableData.value = [...tableData.value, ...data.rows]
      }
      if (tableTotal.value > pager.value.page * pager.value.size) {
        moreTxt.value = t('加载中...')
      } else {
        moreTxt.value = t('-没有更多了哦-')
      }
      isLoadingList.value = false
      return false
    }
    isLoadingList.value = true
    tableData.value = data.rows
    isLoadingList.value = false
    tableTotal.value = data.count
    const keys = ['time', 'open', 'high', 'low', 'close', 'dealAmount', 'volume', 'id', 'exchangeAmount']
    tableData.value.forEach((item) => {
      const klineData = item.klineData || []
      let mappedItems = []
      item.kline.forEach((ite) => {
        const finalItem = {}
        ite.forEach((v, i) => {
          finalItem[keys[i]] = Number(v) < 0 ? -Number(v) : Number(v)
        })
        mappedItems.push(finalItem)
      })
      // 将映射后的数据添加到klineData
      klineData.push(...mappedItems)
      // 更新item的klineData
      item.klineData = klineData
      // console.info(klineData, 'klineData')
    })
    if (!props.isMobile) {
     setTimeout(() => {
       renderKline(props.markets)
     }, 10)
    }
  } else {
    isLoadingList.value = false
    useCommon.showMsg('error', useCommon.err(error.code, error))
  }
}
const loading = ref(false)
const handleScroll = async() => {
  if (loading.value) return;
  const scrollContainer = tableContainerRef.value?.$el.querySelector('.el-scrollbar__wrap');
  if (!scrollContainer) return;
  const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
  if (tableTotal.value > pager.value.page * pager.value.size  && scrollHeight - (scrollTop + clientHeight) < 50) {
    pager.value.page += 1;
    await getAreaByList(editableTabsValue.value);
  }
}
const clickSubTab = (item) => {
  subActive.value = item
}
const favoriteMap = ref({})
const selectCollect = async(item) => { // 取消或增加收藏
  const { data, error } = await favoriteAddOrDel({
    add_str: favoriteMap.value[item.pair] ? undefined : item.pair,
    del_str: favoriteMap.value[item.pair] ? item.pair : undefined
  })
  if (data) {
    useCommon.showMsg('success', favoriteMap.value[item.pair] ? t('取消收藏') : t('收藏成功'))
    await getCollectList()
  } else if (error.code * 1 === 2013) {
    useCommon.openLogin()
  } else {
    useCommon.showMsg('error', useCommon.err(error.code, error))
  }
}
// 获取collectList
const getCollectList = async() => {
  const { data, error} = await getFavoriteList()
  if (data) {
    favoriteMap.value = {}
    data.forEach((item) => {
      favoriteMap.value[item.pair] = true
    })
  }
}
const goDetail = (row) => {
  router.push(`/${locale.value}/detail/${row.product.split('_')[0]}`)
}
const firstTab = computed(() => {
  if (editableTabs.value.length > 0) {
    return editableTabs.value.filter((item) => {
      return item.level * 1 === 1
    })
  }
  return []
})
const secondTab = computed(() => {
  if (editableTabsValue.value === 'collect') {
    return [
      { id: '', route_name: t('全部') },
      { id: 'spot', route_name: t('现货') },
      { id: 'future', route_name: t('合约') }
    ]
  }
  if (editableTabs.value.length > 0) {
    return editableTabs.value.filter((item) => {
      return item.level * 1 === 2 && item.father_area_id === editableTabsValue.value
    })
  }
  console.log(editableTabs.value, 'editableTabs')
  return []
})
const spotList = ref([])
const futureList = ref([])
const allList = computed(() => {
  if (subActive.value === 'spot') {
    return spotList.value
  } else if (subActive.value === 'future') {
    return futureList.value
  } else {
    return spotList.value.concat(futureList.value)
  }
})
const endTableList = computed(() => {
  let endArr = []
  if (editableTabsValue.value === 'collect') {
    // 获取 favoriteMap 的所有键（pair 名称）
    const favoritePairs = Object.keys(favoriteMap.value)
    endArr = allList.value
      .filter(item => favoriteMap.value[item.pair])
      // 按照 favoritePairs 数组中的顺序排序
      .sort((a, b) => favoritePairs.indexOf(a.pair) - favoritePairs.indexOf(b.pair))
  } else {
    endArr = tableData.value
  }
  return endArr
})

const getAllList = async() => {
  isLoadingList.value = true
  const { data } = await getAllPairs()
  if (data) {
    isLoadingList.value = false
    spotList.value = data.spot
    futureList.value = data.contract
  }
}
const handleTabClick = (name) => {
  changeTab(name, 2)
}
// 点击导航
const changeTab = async(name, type) => {
  console.log(name, 'hdhdheuhueuhe')
  if (name === 'collect') {
    setSecondActive('')
    getAllList()
  } else {
    tableData.value = []
    pager.value.page = 1
    if (type * 1 === 2) {
      setSecondActive(name)
      await getAreaByList(name)
    } else {
      if (secondTab.value.length > 0) {
        setSecondActive(secondTab.value[0].id)
      }
      await getAreaByList(secondTab.value.length > 0 ? secondTab.value[0].id : name)
    }
  }
}
const handleSizeChange = () => {
}
const handleCurrentChange = async(val: number) => {
  tableData.value = []
  pager.value.page = val
  await getAreaByList(editableTabsValue.value)
}
const toTrade = (item) => {
  if (item.pair.includes('_SWAP')) {
    router.push(`/${locale.value}/future/${item.pair}`)
  } else {
    router.push(`/${locale.value}/exchange/${item.pair}`)
  }
}
watch(() => props.isMobile, async(val) => {
  pager.value.page = 1
  getCollectList()
  getAreaList(locale.value, props.markets)
})
onMounted(() => {
  // 监听滚动事件
  nextTick(() => {
    const scrollContainer = tableContainerRef.value?.$el.querySelector('.el-scrollbar__wrap');
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll);
    }
  });
})
onBeforeMount(() => {
  getCollectList()
  getAreaList(locale.value, props.markets)
})
onUnmounted(() => {
  // 移除滚动事件监听
  const scrollContainer = tableContainerRef.value?.$el.querySelector('.el-scrollbar__wrap');
  if (scrollContainer) {
    scrollContainer.removeEventListener('scroll', handleScroll);
  }
});
</script>
<style lang="scss">
  .el-table{
    &.marketsTable{
      .cell{
        padding-left:4px !important;
        padding-right:4px !important;
      }
      .change-arrow{
        &.fit-rise{
          transform:rotate(180deg);
        }
      }
    }
  }
  .table-container {
    /* 设置合适的高度，以便出现滚动条 */
    // height: 120px;
    height:auto;
    overflow-y: auto; /* 启用垂直滚动 */
  }
  .markets-table-cont{
    .markets-table-nav{
      border-top:1px solid;
      @include border-color(border);
      .el-tabs{
        .el-tabs__header{
          margin-bottom:0px;
          .el-tabs__nav-wrap{
            &:after{
              height:0px;
              z-index:9;
              @include bg-color(border);
            }
            .el-tabs__active-bar{
              display:none;
            }
            .el-tabs__item{
              border:1px solid;
              border-radius:30px;
              padding:0 20px;
              margin-right:8px;
              margin-top:24px;
              font-size:14px;
              @include color(tc-secondary);
              @include border-color(border);
              .nav-icon{
                font-size:16px;
                margin-right:8px;
                @include color(theme);
                width:16px;
                height:16px;
              }
              @include pc-hover {
                &:hover{
                  @include color(tc-primary);
                  @include border-color(theme);
                }
              }
              &.is-active{
                @include color(tc-primary);
                @include border-color(theme);
              }
            }
          }
        }
      }
    }
    .sub-list-nav{
      padding:4px 0 0;
      margin-bottom:-10px;
      .el-tabs__header{
        margin:4px 0;
        .el-tabs__nav-wrap{
         &.is-scrollable{
            padding:0 20px 0 0;
          }
          &:after{
            display:none;
          }
          .el-tabs__nav-prev{
            z-index:9;
            left:-4px;
            @include bg-color(bg-dialog);
            &.is-disabled{
              display:none;
            }
          }
          .el-tabs__nav{
            .el-tabs__active-bar{
              display:none;
            }
            .el-tabs__item{
              margin:8px 0;
              height:auto;
              padding:2px 10px;
              margin-right:10px;
              @include color(tc-secondary);
              &.is-active{
                border-radius:8px;
                border:1px solid;
                @include border-color(theme);
                @include color(theme);
              }
            }
          }
        }
      }
      ul{
        padding:12px 0;
        li{
          padding:2px 10px;
          cursor:pointer;
          font-size:14px;
          position:relative;
          border-radius:8px;
          margin-right:10px;
          @include color(tc-secondary);
          &.active{
            border:1px solid;
            @include color(theme);
            @include border-color(theme);
          }
        }
      }
    }
    .markets-table-wrapList{
      margin-top:8px;
      position:relative;
      min-height:500px;
      .el-table__header{
        tr{
          th{
            border-bottom:0;
            padding:16px 0;
            font-size:14px;
            @include color(tc-secondary);
            .cell{
              padding:0;
            }
          }
        }
      }
      .el-table__body-wrapper{
        tr{
          border-bottom:0;
          td{
            border-bottom:0;
          }
        }
      }
      .symbolName{
        .collet-icon{
          margin-right:12px;
          cursor:pointer;
          font-size:14px;
          @include color(tc-secondary);
        }
        .iconImg{
          width:30px;
          height:30px;
          margin-right:12px;
        }
        .symbolTxt{
          h3{
            font-size:16px;
            font-weight:600;
            @include color(tc-primary);
          }
          p{
            font-size:12px;
            @include color(tc-secondary);
          }
        }
      }
    }
  }
  @include mb {
    .markets-table-cont{
      margin-top:-10px;
      .markets-table-nav{
        border-top:0;
        margin:0 -20px;
        .el-tabs{
          .el-tabs__nav-scroll{
            padding:0 12px;
          }
          .el-tabs__header{
            margin-bottom:0px;
            .el-tabs__nav-wrap{
              &:after{
                height:1px;
              }
              .el-tabs__active-bar{
                display:none;
              }
              .el-tabs__item{
                border:0;
                padding:0 8px;
                margin-right:0;
                margin-bottom:0;
                margin-top:0;
                font-size:14px;
                height:44px;
                line-height:44px;
                margin-bottom:8px;
                &.is-active{
                  &:after{
                    content: '';
                    display:block;
                    position:absolute;
                    bottom:0;
                    left:50%;
                    width:10px;
                    height:3px;
                    margin-left:-5px;
                    border-radius:5px;
                    @include bg-color(theme);
                  }
                }
                .nav-icon{
                  display:none;
                }
                @include pc-hover {
                  &:hover{
                    border:0;
                  }
                }
                &.is-active, &:focus{
                  border:0;
                }
              }
            }
          }
        }
      }
      .markets-table-wrapList{
        margin:0 -20px;
        .el-table{
          .el-table__header{
            tr{
              th{
                padding:12px 2px;
              }
            }
          }
          .el-table__body-wrapper{
            tr{
              border-bottom:0;
              td{
                padding:12px 2px;
                border-bottom:0;
              }
            }
          }
        }
        .symbolName{
          .collet-icon{
            svg{
              font-size:16px !important;
            }
          }
          .iconImg{
            width:30px;
            height:30px;
            margin-right:12px;
          }
          .symbolTxt{
            h3{
              font-size:14px;
            }
            p{
              font-size:12px;
            }
          }
        }
        .market-pagination{
          display:none;
        }
      }
    }
  }
</style>
