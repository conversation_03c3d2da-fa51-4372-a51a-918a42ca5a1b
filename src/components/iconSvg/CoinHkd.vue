<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px;`"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M0 12C-3.32088e-08 13.5759 0.310389 15.1363 0.913446 16.5922C1.5165 18.0481 2.40042 19.371 3.51472 20.4853C4.62902 21.5996 5.95189 22.4835 7.4078 23.0866C8.86371 23.6896 10.4241 24 12 24C13.5759 24 15.1363 23.6896 16.5922 23.0866C18.0481 22.4835 19.371 21.5996 20.4853 20.4853C21.5996 19.371 22.4835 18.0481 23.0866 16.5922C23.6896 15.1363 24 13.5759 24 12C24 8.8174 22.7357 5.76516 20.4853 3.51472C18.2348 1.26428 15.1826 0 12 0C8.8174 0 5.76516 1.26428 3.51472 3.51472C1.26428 5.76516 0 8.8174 0 12Z"
      fill="#D93145"
    />
    <path
      d="M3.975 16.225V7.29641H5.94559V10.9119H7.3217V7.29641H9.3033V16.225H7.3217V12.3229H5.94559V16.225H3.975Z"
      fill="white"
    />
    <path
      d="M10.2828 16.225V7.29641H12.2534V10.945L13.7506 7.29641H15.6772L14.0259 11.3308L15.7542 16.225H13.7506L12.4626 12.2788L12.2534 12.6315V16.225H10.2828Z"
      fill="white"
    />
    <path
      d="M17.7114 17.4375V16.3463C16.9555 16.3095 16.5367 16.067 16.155 15.6187C15.7807 15.1631 15.5789 14.465 15.5496 13.5244L17.2339 13.2708C17.2413 13.653 17.2743 13.9616 17.333 14.1968C17.3917 14.4319 17.4761 14.601 17.5862 14.7038C17.7036 14.8067 17.8431 14.8582 18.0045 14.8582C18.21 14.8582 18.3421 14.7883 18.4009 14.6487C18.4669 14.5091 18.4999 14.3621 18.4999 14.2078C18.4999 13.8404 18.4119 13.5317 18.2357 13.2819C18.0669 13.0247 17.8321 12.7675 17.5312 12.5103L16.7495 11.8379C16.5293 11.6395 16.3275 11.4374 16.144 11.2316C15.9679 11.0185 15.8248 10.776 15.7147 10.5041C15.6046 10.2248 15.5496 9.89415 15.5496 9.51202C15.5496 8.8139 15.7477 8.26643 16.144 7.8696C16.5477 7.47278 16.9875 7.21877 17.3873 7.1972V6.15002H18.3238V7.18618C18.7495 7.20823 19.0907 7.30743 19.3476 7.4838C19.6045 7.65282 19.7953 7.86593 19.9201 8.12313C20.0522 8.37299 20.1403 8.64121 20.1843 8.92781C20.2357 9.20706 20.265 9.46793 20.2724 9.71044L18.566 9.91987C18.5586 9.65532 18.5403 9.42384 18.5109 9.22543C18.4889 9.02701 18.4376 8.87637 18.3568 8.77349C18.2834 8.66326 18.1623 8.61182 17.9935 8.61916C17.81 8.61916 17.6743 8.69633 17.5862 8.85065C17.4981 9.00497 17.4541 9.15929 17.4541 9.31361C17.4541 9.66634 17.5422 9.95294 17.7183 10.1734C17.9018 10.3865 18.0999 10.5886 18.3128 10.7797L19.0614 11.43C19.3109 11.6578 19.5384 11.9003 19.7439 12.1575C19.9568 12.4147 20.1256 12.7013 20.2503 13.0173C20.3751 13.3333 20.4375 13.6971 20.4375 14.1086C20.4375 14.4981 20.3568 14.8545 20.1953 15.1778C20.0338 15.5012 19.8027 15.7657 19.5017 15.9715C19.2008 16.1699 18.9912 16.2911 18.5729 16.3352V17.4375H17.7114Z"
      fill="white"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'CoinHkd',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
