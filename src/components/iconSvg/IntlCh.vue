<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px;`"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M12 24C18.6274 24 24 18.6274 24 12C24 5.37258 18.6274 0 12 0C5.37258 0 0 5.37258 0 12C0 18.6274 5.37258 24 12 24Z"
      fill="#F1361D"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M11.1117 4.90547L9.84688 5.13261L10.7332 4.15114L10.1801 2.98962L11.3274 3.51849L12.2504 2.57349L12.0731 3.88182L13.1967 4.45929L11.9399 4.73901L11.7113 6.04092L11.1117 4.90547ZM14.2699 8.31965L13.4645 9.31998L13.4888 7.99842L12.3084 7.48429L13.5217 7.13157L13.5976 5.8135L14.3232 6.91706L15.5504 6.61658L14.7856 7.65134L15.4681 8.7837L14.2699 8.31965ZM11.0135 16.8819L11.7835 15.8541L12.9972 16.2761L12.2755 15.1683L13.0037 14.1075L11.7877 14.4505L11.024 13.3729L10.9942 14.6929L9.794 15.0877L10.9916 15.5603L11.0135 16.8819ZM13.8723 12.5277L12.7191 13.0943L13.3003 11.9069L12.448 10.9426L13.6968 11.1351L14.3233 9.97259L14.514 11.279L15.7534 11.5248L14.6224 12.1397L14.7619 13.4541L13.8723 12.5277ZM2.62729 13.576L5.625 11.439L8.6227 13.576L7.51665 10.0647L10.4754 7.87404L6.7941 7.8409L5.625 4.35003L4.45589 7.8409L0.774612 7.87404L3.73335 10.0647L2.62729 13.576Z"
      fill="#FFDC42"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'IntlCh',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
