<template>
  <svg
    :style="`width: 1em; height: 1em; font-size: ${size}px; ${color ? 'color:' + color + ';' : ''};`"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M11 1H13V5H11V1Z" fill="currentColor" />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M18 12C18 15.3137 15.3137 18 12 18C8.6863 18 6 15.3137 6 12C6 8.6863 8.6863 6 12 6C15.3137 6 18 8.6863 18 12ZM12 8C9.79087 8 8 9.79087 8 12C8 14.2091 9.79087 16 12 16C14.2091 16 16 14.2091 16 12C16 9.79087 14.2091 8 12 8Z"
      fill="currentColor"
    />
    <path d="M13 23V19H11V23H13Z" fill="currentColor" />
    <path
      d="M19.0711 3.51472L20.4853 4.92893L17.6569 7.75736L16.2426 6.34315L19.0711 3.51472Z"
      fill="currentColor"
    />
    <path
      d="M7.75736 17.6569L6.34315 16.2426L3.51472 19.0711L4.92893 20.4853L7.75736 17.6569Z"
      fill="currentColor"
    />
    <path d="M23 11V13H19V11H23Z" fill="currentColor" />
    <path
      d="M4.99999 13V11H0.999992L0.999992 13H4.99999Z"
      fill="currentColor"
    />
    <path
      d="M20.4853 19.0711L19.0711 20.4853L16.2426 17.6569L17.6568 16.2426L20.4853 19.0711Z"
      fill="currentColor"
    />
    <path
      d="M6.34315 7.75736L7.75736 6.34314L4.92894 3.51472L3.51472 4.92893L6.34315 7.75736Z"
      fill="currentColor"
    />
  </svg>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
defineProps({
  size: {
    default: 24,
    type: [Number, String],
  },
  color: {
    default: '',
    type: String,
  },
})
defineOptions({
  name: 'MonoSun',
})
</script>
<style lang="scss">
.bge-color-svg {
  --icon-theme: #fff833;
  --icon-gray: #9baec2;
  --icon-gay-dark: #526375;

  .dark & {
    --icon-theme: #ffff38;
    --icon-gray: #708397;
    --icon-gay-dark: #32404d;
  }
}

.bge-fill-icon-theme {
  fill: var(--icon-theme);
}

.bge-fill-icon-gray {
  fill: var(--icon-gray);
}

.bge-fill-gay-dark {
  fill: var(--icon-gay-dark);
}

.bge-stroke-icon-theme {
  stroke: var(--icon-theme);
}

.bge-stroke-icon-gray {
  stroke: var(--icon-gray);
}

.bge-stroke-gay-dark {
  stroke: var(--icon-gay-dark);
}
</style>
