<template>
  <el-dialog v-model="visible" :title="$t('添加邀请链接')" width="480px" class="invite-dialog" @close="emit('closeAddInvite')">
    <div class="invite-container">
      <div v-if="user.is_crm_user * 1 === 0"
       class="invite-title">
        <div class="font-size-14 fit-tc-primary">
          {{ $t('您的基础返佣比例:') }}
          <span class="mg-l8 tw-5">{{ rateInfo.back1Rate }}%</span>
        </div>
      </div>
      <div class="invite-cont-box pd-t24">
        <template v-if="user.is_crm_user * 1 === 0">
          <h3 class="font-size-14 fit-tc-primmary mg-b4 tw-4">{{ $t('设置分享给到好友返现比例') }}</h3>
          <div class="cont-box flex-box">
            <div class="cont-left flex-1">
              <span class="font-size-14 fit-tc-secondary mg-b4">{{ $t('您的返佣比例') }}</span>
              <div class="font-size-16 fit-tc-primary">{{ new BigNumber(rateInfo.back1Rate).minus(curItemNum)}}%</div>
            </div>
            <div class="cont-right flex-1">
              <span class="font-size-14 fit-tc-secondary mg-b4">{{ $t('好友返现比例') }}</span>
              <div class="font-size-16 fit-tc-primary">{{ curItemNum }}%</div>
            </div>
          </div>
          <div class="font-size-14">{{ $t('好友的返现比例') }}</div>
          <div class="tab-button flex-box">
            <ul class="flex-box">
              <li v-for="(item, index) in percentList" :key="index" class="flex-1" :class="{'active': curItemNum === item}" @click="curItemNum = item">
                {{ item }}%
              </li>
            </ul>
          </div>
        </template>
        <div class="input-box">
          <span class="font-size-14 fit-tc-primary mg-b4 flex-box">{{ $t('备注') }}</span>
          <el-input v-model="bzText" />
        </div>
        <div class="pd-t24 pd-b40">
          <el-checkbox v-model="isDefault" class="flex-box">{{ $t('设为默认邀请链接') }}</el-checkbox>
        </div>
        <div class="btn-box">
          <el-button type="primary" :disabled="isDisabled" @click="addInviteLinkFun()">{{ $t('生成邀请链接') }}</el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
  import BigNumber from 'bignumber.js'
  import { ElDialog, ElButton } from 'element-plus'
  import { useCommonData } from '~/composables/index'
  import { genInvitationLink } from '~/api/tt.ts'
  const useCommon = useCommonData()
  const props = defineProps({
    isShow: {
      type: Boolean,
      default: false
    },
    level: {
      type: Number,
      default: -1
    },
    user: {
      type: Object,
      default () {
        return {}
      }
    },
    rateInfo: {
      type: Object,
      default () {
        return {}
      }
    }
  })
  const emit = defineEmits(['closeAddInvite', 'updateData'])
  const splitAmount = (total) => {
    const ratioMap = {
      20: [0, 5, 10, 15],
      25: [0, 10, 15, 20],
      30: [0, 10, 20, 25],
      40: [0, 10, 20, 30],
      50: [0, 15, 30, 40]
    }
    const ratios = ratioMap[total]; // 默认比例
    return ratios;
  }
  const percentList = computed(() => {
    return splitAmount(props.rateInfo.back1Rate)
  })
  const bzText = ref('')
  const isDefault = ref(false)
  const curItemNum = ref(0)
  const visible = ref(false)
  const isDisabled = computed(() => {
    if (bzText.value !== '') {
      return false
    } else {
      return true
    }
  })
  watch(() => visible.value, (val) => {
    if (!val) {
      curItemNum.value = 0
      bzText.value = ''
      isDefault.value = false
    }
  })
  const addInviteLinkFun = async() => {
    const { data, error } = await genInvitationLink({
      desc: bzText.value,
      is_default: isDefault.value ? 1 : 0,
      lv1_other_part: props.user.is_crm_user * 1 === 0 ? curItemNum.value / 100 : 0
    })
    if (data) {
      emit('closeAddInvite')
      emit('updateData', isDefault.value)
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  onMounted(() => {
    visible.value = props.isShow
  })
</script>
<style lang="scss">
.el-dialog{
  &.invite-dialog{
    .invite-container {
      .invite-title {
      }
      .invite-cont-box {
        .cont-box {
          @include bg-color(bg-secondary);
          border-radius: 8px;
          padding: 16px 24px;
          margin-bottom: 16px;
          span {
            display: block;
          }
        }
        .tab-button {
          padding-bottom: 24px;
          padding-top:16px;
          ul{
            width:100%;
            li{
              font-size:14px;
              margin-right:8px;
              border-radius:6px;
              height:32px;
              line-height:32px;
              @include bg-color(bg-quaternary);
              text-align:center;
              cursor:pointer;
              &:last-child{
                margin-right:0;
              }
              &.active{
                @include bg-color(theme);
                @include color(tc-primary);
              }
            }
          }
          .el-button {
            width: 100%;
            padding: 8px 12px;
            border: 0;
            @include bg-color(bg-secondary);
            &:hover {
              @include bg-color(bg-quaternary);
              @include bg-color(tc-button);
            }
            &.is-disabled {
              @include bg-color(bg-secondary);
              @include color(tc-tertiary);
              opacity: 1;
            }
          }
        }
        .input-box {
          span {
            display: block;
          }
        }
        .btn-box {
          .el-button {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>