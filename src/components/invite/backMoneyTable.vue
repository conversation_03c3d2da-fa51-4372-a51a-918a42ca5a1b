<template>
  <OrderstableBox :isLoading="isLoading" :mSortList="mSortList" :headers="headersList" :list="currentList">
    <template #back_amount="scope">
      <div class="flex-box">
        <span>{{ scope.data.back_amount }} {{ scope.data.symbol }}</span>
      </div>
    </template>
    <template #createdAt="scope">
      {{ timeFormat(scope.data.createdAt, 'yyyy-MM-dd hh:mm:ss') }}
    </template>
  </OrderstableBox>
</template>
<script lang="ts" setup>
  import OrderstableBox from '~/components/common/OrderstableBox.vue'
  import MonoEdit from '~/components/common/icon-svg/MonoEdit.vue'
  import MonoCopy from '~/components/common/icon-svg/MonoCopy.vue'
  import { timeFormat } from '~/utils'
  import { getinvitationLinkHis } from '~/api/tt'
  const { locale, t } = useI18n()
  const headersList = ref([
    { text: t('返现金额'), key: 'back_amount', align: 'left', wid: '',style: 'auto' },
    { text: t('返现时间'), key: 'createdAt', align: 'right', wid: '',style: 'auto' }
  ])
  const mSortList = ref([
    { text: t('返现金额'), key: 'back_amount', align: 'left', wid: '',style: 'auto' },
    { text: t('返现时间'), key: 'createdAt', align: 'right', wid: '',style: 'auto' }
  ])
  const currentList = ref([])
  const isLoading = ref(true)
  const pages = ref({
    page: 1,
    size: 100
  })
  const getList = async() => {
    const { data } = await getinvitationLinkHis({
      page: pages.value.page,
      size: pages.value.size,
      is_inv: 2
    })
    if (data) {
      currentList.value = data.rows
      isLoading.value = false
    } else {
      isLoading.value = false
    }
  }
  onMounted(() => {
    getList()
  })
</script>