<template>
  <OrderstableBox :isLoading="isLoading" :mSortList="mSortList" :headers="headersList" :list="currentList">
    <template #createdAt="scope">
      {{ timeFormat(scope.data.createdAt, 'yyyy-MM-dd hh:mm:ss') }}
    </template>
    <template #back_amount="scope">
      {{ scope.data.back_amount }} {{ scope.data.symbol }}
    </template>
  </OrderstableBox>
</template>
<script lang="ts" setup>
  import OrderstableBox from '~/components/common/OrderstableBox.vue'
  import MonoEdit from '~/components/common/icon-svg/MonoEdit.vue'
  import MonoCopy from '~/components/common/icon-svg/MonoCopy.vue'
  import { timeFormat } from '~/utils'
  import { getinvitationLinkHis } from '~/api/tt'
  const { locale, t } = useI18n()
  const headersList = ref([
    { text: t('被邀请人账号'), key: 'user_id', align: 'left', wid: '',style: '' },
    { text: t('返佣时间'), key: 'createdAt', align: 'left', wid: '',style: 'auto' },
    { text: t('奖励类型'), key: 'comment', align: 'left', wid: '',style: '' },
    { text: t('数量'), key: 'back_amount', align: 'right', wid: '',style: 'auto' },
  ])
  const mSortList = ref([
    { text: t('被邀请人账号'), key: 'user_id', align: 'left', wid: '',style: 'auto' },
    { text: t('返佣时间'), key: 'createdAt', align: 'left', wid: '',style: 'auto' },
    { text: t('奖励类型'), key: 'comment', align: 'left', wid: '',style: '' },
    { text: t('数量'), key: 'back_amount', align: 'right', wid: '',style: 'auto' },
  ])
  const checked = ref({})
  const currentList = ref([])
  const isLoading = ref(true)
  const pages = ref({
    page: 1,
    size: 100
  })
  const getList = async() => {
    const { data } = await getinvitationLinkHis({
      page: pages.value.page,
      size: pages.value.size,
      is_inv: 0
    })
    if (data) {
      currentList.value = data.rows
      isLoading.value = false
    } else {
      isLoading.value = false
    }
  }
  onMounted(() => {
    getList()
  })
</script>