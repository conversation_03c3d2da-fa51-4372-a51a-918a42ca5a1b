<template>
  <OrderstableBox :isLoading="isLoading" :mSortList="mSortList" :headers="headersList" :list="currentList">
    <template #is_default="scope">
      <MonoRigthChecked v-if="checked[scope.data.id]" size="16" class="fit-theme" />
      <span v-if="scope.data.is_default === 0" class="text-info-tag fit-theme cursor-pointer" @click="editDefault(scope.data)">{{ $t('设为默认') }}</span>
    </template>
    <template #desc="scope">
      <div class="flex-box">
        <span>{{ scope.data.desc ? scope.data.desc : $t('邀请链接') }}</span>
        <MonoEdit size="14" class="fit-theme cursor-pointer mg-l4" @click="emit('editRemark', scope.data)" />
      </div>
    </template>
    <template #code="scope">
      <div class="flex-box">
        <span>{{ scope.data.code }}</span>
        <MonoCopy size="14" class="fit-theme cursor-pointer mg-l4" @click="useCommon.copy(scope.data.code, $t('复制成功！'))" />
      </div>
    </template>
    <template #lv1_self_part="scope">
      <span>{{ format(scope.data.lv1_self_part * 100, 2, true, true) }}%</span>
    </template>
    <template #lv1_other_part="scope">
      <span>{{ format(scope.data.lv1_other_part * 100, 2, true, true) }}%</span>
    </template>
    <template #register_count="scope">
      <span>{{ scope.data.register_count }}</span>
    </template>
    <template #header-register_count="scope">
      <div class="flex-box">
        <span>{{ $t('有效邀请人数') }}</span>
        <el-tooltip placement="top">
          <MonoWarn size="16" class="cursor-pointer fit-tc-secondary mg-l4" />
          <template #content>
            <div class="font-size-12" style="max-width:400px;">
              <div>{{ $t('好友充值≥100USDT，或交易达到200USDT') }}</div>
            </div>
          </template>
        </el-tooltip>
      </div>
    </template>
    <template #all_register_count="scope">
      <span>{{ scope.data.all_register_count }}</span>
    </template>
    <template #id="scope">
      <div class="flex-box tag-box">
        <span class="text-info-tag mg-l40 fit-theme cursor-pointer" @click="useCommon.copy(`https:${useCommon.domain.value}/${locale}/login/register?invite_code=${scope.data.code}`, $t('复制成功！'))">{{ $t('复制链接') }}</span>
      </div>
    </template>
  </OrderstableBox>
</template>
<script lang="ts" setup>
  import { ElMessageBox, ElTooltip } from 'element-plus'
  import MonoRigthChecked from '~/components/common/icon-svg/MonoRigthChecked.vue'
  import OrderstableBox from '~/components/common/OrderstableBox.vue'
  import MonoWarn from '~/components/common/icon-svg/MonoWarn.vue'
  import MonoEdit from '~/components/common/icon-svg/MonoEdit.vue'
  import MonoCopy from '~/components/common/icon-svg/MonoCopy.vue'
  import { useCommonData } from '~/composables/index'
  import { format } from '~/utils'
  import { editInvitationLinkAPI } from '~/api/tt.ts'
  const useCommon = useCommonData()
  const { locale, t } = useI18n()
  const props = defineProps({
    isLogin: {
      type: Boolean,
      default: false
    },
    currentList: {
      type: Array,
      default () {
        return []
      }
    },
    user: {
      type: Object,
      default () {
        return {}
      }
    },
    checked: {
      type: Object,
      default () {
        return {}
      }
    }
  })
  const emit = defineEmits(['editRemark', 'getInviteList'])
  const isLoading = ref(false)
  const headersList = computed(() => {
    if (props.user.is_crm_user * 1 === 0) {
      return [
        { text: t('默认'), key: 'is_default', align: 'left', wid: 'flex-1',style: 'auto' },
        { text: t('备注'), key: 'desc', align: 'left', wid: '',style: 'auto' },
        { text: t('邀请码'), key: 'code', align: 'left', wid: '',style: 'auto' },
        { text: t('我的返佣比例'), key: 'lv1_self_part', align: 'left', wid: '',style: 'auto' },
        { text: t('好友返现比例'), key: 'lv1_other_part', align: 'left', wid: '',style: 'auto' },
        { text: t('好友数'), key: 'all_register_count', align: 'left', wid: '',style: 'auto' },
        { text: t('有效邀请人数'), key: 'register_count', align: 'left', wid: '',style: 'auto', headerAuto: true },
        { text: t('操作'), key: 'id', align: 'right', wid: 'flex-1',style: 'auto' }
      ]
    } else {
      return [
        { text: t('默认'), key: 'is_default', align: 'left', wid: 'flex-1',style: 'auto' },
        { text: t('备注'), key: 'desc', align: 'left', wid: '',style: 'auto' },
        { text: t('邀请码'), key: 'code', align: 'left', wid: '',style: 'auto' },
        { text: t('好友数'), key: 'all_register_count', align: 'left', wid: '',style: 'auto' },
        { text: t('有效邀请人数'), key: 'register_count', align: 'left', wid: '',style: 'auto', headerAuto: true },
        { text: t('操作'), key: 'id', align: 'right', wid: 'flex-1',style: 'auto' }
      ]
    }
  })
  const mSortList = computed(() => {
    if (props.user.is_crm_user * 1 === 0) {
      return [
        { text: t('备注'), key: 'desc', align: 'left', wid: '',style: 'auto' },
        { text: t('操作'), key: 'id', align: 'right', wid: '',style: 'auto' },
        { text: t('邀请码'), key: 'code', align: 'left', wid: '',style: 'auto' },
        { text: t('我的返佣比例'), key: 'lv1_self_part', align: 'left', wid: '',style: 'auto' },
        { text: t('好友返现比例'), key: 'lv1_other_part', align: 'left', wid: '',style: 'auto' },
        { text: t('好友数'), key: 'all_register_count', align: 'left', wid: '',style: 'auto' },
        { text: t('有效邀请人数'), key: 'register_count', align: 'left', wid: '',style: 'auto' }
      ]
    } else {
      return [
        { text: t('备注'), key: 'desc', align: 'left', wid: '',style: 'auto' },
        { text: t('操作'), key: 'id', align: 'right', wid: '',style: 'auto' },
        { text: t('邀请码'), key: 'code', align: 'left', wid: '',style: 'auto' },
        { text: t('好友数'), key: 'all_register_count', align: 'left', wid: '',style: 'auto' },
        { text: t('有效邀请人数'), key: 'register_count', align: 'left', wid: '',style: 'auto' }
      ]
    }
  })
  const editDefault = (item) => {
    ElMessageBox.confirm(t('您确定要设置为默认吗？'), t('提示'), {
      distinguishCancelAndClose: true,
      confirmButtonText: t('确认'),
      cancelButtonText: t('取消'),
      beforeClose: async(action, instance, done) => {
        if (action === 'confirm') {
          instance.confirmButtonLoading = true
          const { data, error } = await editInvitationLinkAPI({
            id: item.id,
            is_default: 1
          })
          if (data) {
            useCommon.showMsg('success', t('修改成功'))
            emit('getInviteList')
            instance.confirmButtonLoading = false
            done()
          } else {
            useCommon.showMsg('error', useCommon.err(error.code, error))
            instance.confirmButtonLoading = false
          }
        } else {
          done()
        }
      }
    })
  }
</script>
<style lang="scss">
.el-checkbox{
  &.default-checked{
    &.is-disabled{
      &.is-checked{
        .el-checkbox__inner{
          @include bg-color(theme);
          @include border-color(theme);
          &:after{
            @include border-color(tc-button);
          }
        }
      }
      .el-checkbox__inner{
        @include bg-color(bg-primary);
      }
    }
  }
}

@include md{
  .flex-box{
    &.tag-box{
      display:block;
    }
  }
  .text-info-tag{
    display:block;
    &.mg-l40{
      margin-left:0 !important;
    }
  }
}
</style>
