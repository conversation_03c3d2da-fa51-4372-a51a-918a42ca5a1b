<template>
  <OrderstableBox :isLoading="isLoading" :mSortList="mSortList" :headers="headersList" :list="currentList">
    <template #register_time="scope">
      {{ timeFormat(scope.data.register_time, 'yyyy-MM-dd hh:mm:ss') }}
    </template>
    <template #user_id="scope">
      {{ scope.data.user_id }}
    </template>
    <template #status="scope">
      {{ scope.data.status === 1 ? $t('已激活') : $t('未完成任务') }}
    </template>
  </OrderstableBox>
</template>
<script lang="ts" setup>
  import OrderstableBox from '~/components/common/OrderstableBox.vue'
  import MonoEdit from '~/components/common/icon-svg/MonoEdit.vue'
  import MonoCopy from '~/components/common/icon-svg/MonoCopy.vue'
  import { timeFormat } from '~/utils'
  import { getinvitationLinkHis } from '~/api/tt'
  const { locale, t } = useI18n()
  const isLoading = ref(true)
  const headersList = ref([
    { text: t('邀请人账号'), key: 'user_id', align: 'left', wid: '',style: '' },
    { text: t('邀请时间'), key: 'register_time', align: 'left', wid: '',style: 'auto' },
    { text: t('状态'), key: 'status', align: 'right', wid: '',style: 'auto' },
  ])
  const mSortList = ref([
    { text: t('邀请人账号'), key: 'user_id', align: 'left', wid: '',style: '' },
    { text: t('邀请时间'), key: 'register_time', align: 'left', wid: '',style: 'auto' },
    { text: t('状态'), key: 'status', align: 'right', wid: '',style: 'auto' },
  ])
  const currentList = ref([])
  const pages = ref({
    page: 1,
    size: 100
  })
  const getList = async() => {
    const { data } = await getinvitationLinkHis({
      page: pages.value.page,
      size: pages.value.size,
      is_inv: 1
    })
    if (data) {
      currentList.value = data.rows
      isLoading.value = false
    } else {
      isLoading.value = false
    }
  }
  onMounted(() => {
    getList()
  })
</script>