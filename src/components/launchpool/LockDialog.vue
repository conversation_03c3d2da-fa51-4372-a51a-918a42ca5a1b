<template>
  <el-dialog class="launchpool-check-dialog" v-model="visible" :title="$t('选择您锁仓的NFT')" width="800px" @close="emit('close')">
    <LockDetail :detail="detail" @request="emit('request')" />
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElDialog } from 'element-plus'
  import LockDetail from '~/components/launchpool/LockDetail.vue'
  const props = defineProps({
    detail: {
      type: Object,
      default () {
        return {}
      }
    },
    isShow: {
      type: Boolean,
      default: false
    }
  })
  const emit = defineEmits(['close', 'request'])
  const visible = ref(false)
  const searchText = ref('')
  onMounted(() => {
    visible.value = props.isShow
  })
</script>