<template>
  <div v-loading="isLoading">
    <div class="record-title flex-box space-between pd-b24">
      <div>
        <p class="fit-tc-secondary pd-b8">{{ $t('已领取：') }}<span class="fit-tc-primary">{{ prizeValue }} {{ projectSymbol }}</span></p>
        <p class="fit-tc-secondary">{{ $t('Tips:领取的空投会放入您的钱包账户。') }}</p>
      </div>
      <el-button type="primary" :loading="isGetLoading" style="width:auto!important;" :disabled="projectAirdropStatus * 1 === 0" @click="getLpPrize()">{{ $t('一键领取') }}</el-button>
    </div>
    <div class="record-cont" @scroll="onScroll" ref="scrollContainer">
      <el-table :data="recordList">
        <template #empty>
          <BoxNoData :text="$t('暂无数据')" />
        </template>
        <el-table-column :label="$t('币种')" prop="airdrop_symbol" align="left">
        </el-table-column>
        <el-table-column :label="$t('领取数量')" prop="airdrop_amount" align="center">
        </el-table-column>
        <el-table-column :label="$t('领取时间')" prop="prize_time" align="right">
          <template #default="scope">
            <span class="fit-tc-secondary">
              {{ scope.row.prize_time ? timeFormat(scope.row.prize_time, 'yyyy-MM-dd hh:mm:ss') : $t('待领取') }}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <div v-if="recordList.length > 0" class="text-center font-size-12 fit-tc-secondary pd-t24">
        <div class="fit-tc-secondary">{{ moreTxt }}</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ElTable, ElButton } from 'element-plus'
  import { getLpPrizeListAPI, getLpPrizeGeAPI } from '~/api/tt.ts'
  import BoxNoData from '~/components/common/BoxNoData.vue'
  import { timeFormat } from '~/utils'
  import { useCommonData } from '~/composables/index'
  const useCommon = useCommonData()
  const { locale, t } = useI18n()
  const props = defineProps({
    projectId: {
      type: [String, Number],
      default: ''
    },
    projectAirdropStatus: {
      type: String,
      default: ''
    },
    projectSymbol: {
      type: String,
      default: ''
    }
  })
  const emit = defineEmits(['LpPrizeResquest'])
  const pager = ref({
    page: 1,
    size: 20
  })
  const moreTxt = ref('')
  const pageTotal = ref(0)
  const isGetLoading = ref(false)
  const recordList = ref([])
  const prizeValue = ref(0)
  const isLoading = ref(false)

  const getLpPrize = async () => {
    isGetLoading.value = true
    const { data, error } = await getLpPrizeGeAPI({
      airdrop_id: props.projectId
    })
    if (data) {
      recordList.value = []
      await getRecordList()
      useCommon.showMsg('success', t('领取成功！'))
      emit('LpPrizeResquest')
      isGetLoading.value = false
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
      isGetLoading.value = false
    }
  }

  const getRecordList = async () => {
    isLoading.value = true
    const { data } = await getLpPrizeListAPI({
      ...pager.value,
      airdrop_id: props.projectId
    })
    if (data) {
      isLoading.value = false
      prizeValue.value = data.prize_amount
      recordList.value = [...recordList.value, ...data.rows]
      pageTotal.value = data.count
      if (pageTotal.value > pager.value.page * pager.value.size) {
        moreTxt.value = t('加载中...')
      } else {
        moreTxt.value = t('-没有更多了哦-')
      }
    }
  }

  const onScroll = (event: Event) => {
    const container = event.target as HTMLElement
    // 判断是否滚动到接近底部
    if (container.scrollHeight - container.scrollTop <= container.clientHeight + 50) {
      if (pager.value.page * pager.value.size < pageTotal.value && !isLoading.value) {
        pager.value.page++
        getRecordList()
      }
    }
  }

  onMounted(() => {
    getRecordList()
  })
</script>
<style lang="scss" scoped>
  .record-cont {
    height: 400px; /* 可以根据需要调整 */
    overflow-y: auto;
  }
</style>