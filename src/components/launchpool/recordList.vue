<template>
  <el-dialog v-model="visible" :title="`#${projectName}#${$t('领取记录')}`" @close="emit('close')">
    <RecordDetail :projectId="projectId" :projectAirdropStatus="projectAirdropStatus" :projectSymbol="projectSymbol" @LpPrizeResquest="emit('LpPrizeResquest')" />
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElDialog, ElTable } from 'element-plus'
  import RecordDetail from '~/components/launchpool/RecordDetail.vue'
  const props = defineProps({
    isShow: {
      type: Boolean,
      default: false
    },
    projectName: {
      type: String,
      default: ''
    },
    projectSymbol: {
      type: String,
      default: ''
    },
    projectId: {
      type: [String, Number],
      default: ''
    },
    projectAirdropStatus: {
      type: String,
      default: ''
    }
  })
  const emit = defineEmits(['close'])
  const visible = ref(false)
  onMounted(() => {
    visible.value = props.isShow
  })
</script>
<style lang="scss" scoped>
  .record-cont{
    margin:0 -14px;
    padding-top:24px;
  }
</style>