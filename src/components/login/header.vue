<template>
  <div class="login-header flex-box">
    <NuxtLink :to="`/${locale}`" class="logo-wrap">
      <img src="~/assets/images/common/logo.png" />
    </NuxtLink>
  </div>
</template>
<script setup lang="ts">
const { locale } = useI18n()
</script>
<style lang="scss">
.login-header{
  padding:0 20px;
  height:74px;
  border-bottom:1px solid;
  @include bg-color(bg-primary);
  @include border-color(border);
  .logo-wrap{
    width:80px;
    height:24px;
    display:block;
    img{
      width:100%;
      height:auto;
    }
  }
}
@include mb{
  .login-header{
    display:none;
  }
}
</style>
