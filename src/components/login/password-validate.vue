<template>
  <div v-if="password !== ''" class="password-tips flex-box flex-wrap">
    <span class="fit-tc-primary">{{ $t('您的密码必须包含') }}：</span>
    <div class="tips-lab flex-box" :class="{'fit-rise': hasLength && password !== '', 'fit-fall': !hasLength && password !== ''}">
      <MonoRigthChecked v-if="hasLength" size="14" class="mg-r4" />
      <MonoClose v-else size="14" class="mg-r4" />
      {{ $t('8-20字符') }}
    </div>
    <div class="tips-lab flex-box" :class="{'fit-rise': hasLowercase && password !== '', 'fit-fall': !hasLowercase && password !== ''}">
      <MonoRigthChecked v-if="hasLowercase" size="14" class="mg-r4" />
      <MonoClose v-else size="14" class="mg-r4" />
      {{ $t('1个小写字母') }}
    </div>
    <div class="tips-lab flex-box" :class="{'fit-rise': hasUppercase && password !== '', 'fit-fall': !hasUppercase && password !== ''}">
      <MonoRigthChecked v-if="hasUppercase" size="14" class="mg-r4" />
      <MonoClose v-else size="14" class="mg-r4" />
      {{ $t('1个大写字母') }}
    </div>
    <div class="tips-lab flex-box" :class="{'fit-rise': hasNumber && password !== '', 'fit-fall': !hasNumber && password !== ''}">
      <MonoRigthChecked v-if="hasNumber" size="14" class="mg-r4" />
      <MonoClose v-else size="14" class="mg-r4" />
      {{ $t('1个数字') }}
    </div>
    <div class="tips-lab flex-box" :class="{'fit-rise': hasSpecialChar && password !== '', 'fit-fall': !hasSpecialChar && password !== ''}">
      <MonoRigthChecked v-if="hasSpecialChar" size="14" class="mg-r4" />
      <MonoClose v-else size="14" class="mg-r4" />
      {{ $t('1个特殊字符') }}
    </div>
  </div>
</template>
<script lang="ts" setup>
  import MonoRigthChecked from '~/components/common/icon-svg/MonoRigthChecked.vue'
  const props = defineProps({
    password: {
      type: String,
      default: ''
    }
  })
  const emit = defineEmits(['changeStatus'])
  // 预编译正则表达式
  const lowercaseRegex = /[a-z]/
  const uppercaseRegex = /[A-Z]/
  const numberRegex = /[0-9]/
  const specialCharRegex = /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/
  const hasLength = computed(() => {
    return props.password.length >= 8 && props.password.length <= 20
  })
  const hasLowercase = computed(() => lowercaseRegex.test(props.password))
  const hasUppercase = computed(() => uppercaseRegex.test(props.password))
  const hasNumber = computed(() => numberRegex.test(props.password))
  const hasSpecialChar = computed(() => specialCharRegex.test(props.password))
  // 更简洁的watch方式
  watch(() => props.password, () => {
    const isValid = hasLength.value && hasLowercase.value && hasUppercase.value && hasNumber.value && hasSpecialChar.value
    emit('changeStatus', isValid)
  }, { immediate: true })
</script>
<style lang="scss" scoped>
  .password-tips{
    padding-top:4px;
    font-size:12px;
    line-height:24px;
    .tips-lab{
      @include color(tc-tertiary);
      margin-right:12px;
      &.fit-rise{
        @include color(rise);
      }
      &.fit-fall{
        @include color(fall);
      }
    }
  }
</style>