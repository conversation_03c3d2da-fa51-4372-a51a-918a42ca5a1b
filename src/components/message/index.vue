<template>
  <div v-if="isShowMessage && messageList.length > 0" class="message-cont">
    <div class="message-wrap flex-box">
      <div class="message-title flex-box">
        <MonoNotice size="20" />
        <span class="mg-l4">{{ $t('系统公告') }}</span>
      </div>
      <ul class="flex-box flex-1">
        <li v-for="(item, index) in messageList" :key="item.id" class="flex-1">
          <a :href="(item.context[locale] || {}).url || (item.context['en'] || {}).url">
            {{ (item.context[locale] || {}).title || (item.context['en'] || {}).title }}
          </a>
        </li>
      </ul>
      <div class="more-txt">
        <MonoCloseBg size="16" class="mg-r8 fit-tc-secondary" @click="closeMessage" />
        <a :href="`${useCommon.zendeskUrl(locale)}/sections/4662314935326`" target="_blank" class="fit-tc-secondary"><MonoRightArrowShort size="16"/></a>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { cookies } from '~/utils'
import MonoCloseBg from '~/components/common/icon-svg/MonoCloseBg.vue'
import MonoNotice from '~/components/common/icon-svg/MonoNotice.vue'
import UAParser from 'ua-parser-js'
import md5 from 'blueimp-md5'
import MonoRightArrowShort from '~/components/common/icon-svg/MonoRightArrowShort.vue'
import { getNoteListApi } from '~/api/public'
import { useCommonData } from '~/composables/index'
const { locale, t } = useI18n()
const useCommon = useCommonData()
const messageList = ref([])
const isShowMessage = ref(false)
const closeMessage = () => {
  isShowMessage.value = false
  cookies.set('showMessage', 0)
}
onBeforeMount(async() => {
  if (cookies.get('KTX_device_id')) {
      localStorage.setItem('KTX_device_id', cookies.get('KTX_device_id'))
    }
  if (cookies.get('showMessage') === undefined || cookies.get('showMessage') * 1 === 1) {
    isShowMessage.value = true
    cookies.set('showMessage', 1)
  } else if (cookies.get('showMessage') * 1 === 0) {
    isShowMessage.value = false
    cookies.set('showMessage', 0)
  }
  const Ua = new UAParser(window.navigator.userAgent)
  const browser = Ua.getBrowser() || {}
  const os = Ua.getOS() || {}
  const device = Ua.getDevice()
  const deviceName = device.model ? device.model : `${os.name} ${browser.name}`
  const deviceId = cookies.get('KTX_device_id') || localStorage.getItem('KTX_device_id') || md5(`${Date.now()}-${Math.floor(Math.random() * 10000000000).toString(16)}-${os.name}-${browser.name}`)
  const { data } = await getNoteListApi({
    device_id: deviceId,
    notice_type: 1,
    page: 1,
    size: 3
  })
  if (data) {
    data.rows.forEach((item) => {
      let obj = {}
      JSON.parse(item.content).forEach((ite) => {
        obj[ite.lang] = ite
      })
      item.context = obj
    })
    messageList.value = data.rows.slice(0, 4)
  }
})
</script>
<style lang="scss">
.message-cont {
  padding:4px 20px;
  @include bg-color(bg-secondary);
  .message-wrap{
    .message-title{
      padding:8px;
      font-size:14px;
      margin-right:8px;
      white-space:nowrap;
      border-radius:4px;
      @include bg-color(bg-primary);
      @include color(tc-secondary);
    }
    ul{
      li{
        white-space:nowrap;
        overflow:hidden;
        text-overflow:ellipsis;
        margin-right:16px;
        cursor:pointer;
        font-size:14px;
        @include color(tc-primary);
        a{
          @include color(tc-primary);
          @include pc-hover {
            &:hover{
              @include color(theme);
            }
          }
        }
      }
    }
    .more-txt{
      cursor:pointer;
      @include color(tc-secondary);
      @include pc-hover {
        &:hover{
          @include color(theme);
        }
      }
    }
  }
}
@include pc {
  .message-cont {
    .message-wrap{
      ul{
        li{
          // width:260px;
          // &:last-child{
          //   display:none;
          // }
        }
      }
    }
  }
}
@include md{
  .message-cont {
    .message-wrap{
      ul{
        li{
          // width:200px;
          &:last-child{
            display:none;
          }
        }
      }
    }
  }
}
@include mb{
  .message-cont {
    padding:4px 10px;
    .message-title{
      font-size:12px !important;
      svg{
        font-size:12px !important;
      }
      span{
        display:none;
      }
    }
    .message-wrap{
      ul{
        overflow:hidden;
        li{
          font-size:12px;
          // width:200px;
          &:last-child, &:nth-child(2), &:nth-child(3), &:nth-child(4){
            display:none;
          }
          white-space:nowrap;
          overflow:hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
</style>