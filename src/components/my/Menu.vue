<template>
  <div class="my-menu-container">
    <div class="sidebar-h5-banner flex-box space-between" @click="isShowMenu = !isShowMenu; emit('showMenu', isShowMenu)">
      <div class="sidebar-title">
        {{ $t(activeName) }}
      </div>
      <MonoDownArrowShort :class="{'arrow-up': isShowMenu}" />
    </div>
    <div class="my-menu-wrapper" :class="{'showMenu': isShowMenu}">
      <div v-for="(item, index) in menuListParams" :key="index" class="slidebar-wrap">
        <div class="sidebar-menu-item flex-box space-between" :class="{ 'active': item.isActive }" @click="handleMenuItemClick(item, index)">
          <div class="sidebar-text flex-box">
            <div :class="`my-icon ${item.iconName}`"></div>
            {{ $t(item.name) }}
          </div>
          <MonoDownArrowShort :class="{ 'showSubMenu': item.isOpen }" v-if="item.children.length > 0" :size="14" />
        </div>
        <ul v-if="item.children && item.isOpen">
          <li v-for="(ite, ind) in item.children" :key="ind" :class="{ 'active': ite.isActive }">
            <nuxt-link :to="`/${locale}${ite.url}`">
              {{ $t(ite.name) }}
            </nuxt-link>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import MonoDownArrowShort from '~/components/common/icon-svg/MonoDownArrowShort.vue'
  const { locale } = useI18n()
  const router = useRouter()
  const props = defineProps({
    menuList: {
      type: Object,
      default () {
        return []
      }
    },
    userInfo: {
      type: Object,
      default () {
        return {}
      }
    }
  })
  const menuListParams = ref([])
  const activeName = ref('')
  const emit = defineEmits(['showMenu'])
  const isShowMenu = ref(false)
  const curUrl = computed(() => {
    return router.currentRoute.value.fullPath
  })
  watch(() => curUrl.value, () => {
    isShowMenu.value = false
    emit('showMenu', isShowMenu.value)
    updateActiveMenuItem()
  })
  const handleMenuItemClick = (item, index) => {
    if (item.children && item.children.length > 0) {
      menuListParams.value[index].isOpen = !item.isOpen
    } else {
      router.push(`/${locale.value}${item.url}`)
    }
  }
  const updateActiveMenuItem = () => {
    menuListParams.value = props.menuList
    menuListParams.value.forEach((item) => {
      item.isActive = false
      if (curUrl.value === `/${locale.value}${item.url}`) {
        activeName.value = item.name
        item.isActive = true
      } else {
        if (item.children && item.children.length > 0) {
          item.children.forEach((child, index) => {
            child.isActive = false
            if (curUrl.value === `/${locale.value}${child.url}` || (child.subUrl && child.subUrl.includes(curUrl.value.replace(`/${locale.value}`, '')))) {
              activeName.value = child.name
              item.isOpen = true
              child.isActive = true
            }
          })
        }
      }
    })
  }
  watch(() => props.menuList, () => {
    if (JSON.stringify(props.userInfo) !== '{}') {
      updateActiveMenuItem()
    }
  }, {
    immediate: true
  })
</script>
<style lang="scss" scoped>
  .my-menu-container{
    .sidebar-h5-banner{
      display:none;
      cursor:pointer;
    }
    .my-menu-wrapper{
      width:100%;
      display:block;
      .sidebar-menu-item{
        height:48px;
        margin-bottom:10px;
        cursor:pointer;
        padding:0 24px;
        &.active{
          border-top-right-radius:8px;
          border-bottom-right-radius:8px;
          @include bg-color(bg-quaternary);
          .sidebar-text{
            @include color(tc-primary);
          }
        }
        @include pc-hover {
          &:hover{
            border-top-right-radius:8px;
            border-bottom-right-radius:8px;
            @include bg-color(bg-quaternary);
          }
        }
        .sidebar-text{
          font-size:16px;
          @include color(tc-secondary);
          .my-icon{
            width:24px;
            height:24px;
            margin-right:8px;
            &.zonglan{
              background-size:100% auto;
              @include get-img('@/assets/images/common/icon_zonglan-light.png', '@/assets/images/common/icon_zonglan-dark.png');
            }
            &.yaoqing{
              background-size:100% auto;
              @include get-img('@/assets/images/common/icon_yaoqing-light.png', '@/assets/images/common/icon_yaoqing-dark.png');
            }
            &.zichan{
              background-size:100% auto;
              @include get-img('@/assets/images/common/icon_zichan-light.png', '@/assets/images/common/icon_zichan-dark.png');
            }
            &.zhanghu{
              background-size:100% auto;
              @include get-img('@/assets/images/common/icon_zizhanghu-light.png', '@/assets/images/common/icon_zizhanghu-dark.png');
            }
            &.dingdan{
              background-size:100% auto;
              @include get-img('@/assets/images/common/icon_dingdan-light.png', '@/assets/images/common/icon_dingdan-dark.png');
            }
            &.shezhi{
              background-size:100% auto;
              @include get-img('@/assets/images/common/icon_shezhi-light.png', '@/assets/images/common/icon_shezhi-dark.png');
            }
          }
        }
        svg{
          @include color(tc-primary);
          &.showSubMenu{
            transform:rotate(180deg);
          }
        }
      }
      ul{
        li{
          height:48px;
          line-height:48px;
          padding-left:56px;
          margin-bottom:10px;
          cursor:pointer;
          font-size:14px;
          @include color(tc-secondary);
          a{
            display:block;
            @include color(tc-secondary);
          }
          &.active{
            border-top-right-radius:8px;
            border-bottom-right-radius:8px;
            @include color(tc-primary);
            @include bg-color(bg-quaternary);
            a{
              @include color(tc-primary);
            }
          }
          @include pc-hover {
            &:hover{
              border-top-right-radius:8px;
              border-bottom-right-radius:8px;
              @include bg-color(bg-quaternary);
              a{
                @include color(tc-primary);
              }
            }
          }
        }
      }
    }
  }
  @include mb{
    .my-menu-container{
      position:relative;
      padding:0 20px;
      @include bg-color(bg-primary);
      .sidebar-h5-banner{
        display:flex;
        height:48px;
        border-bottom:1px solid;
        @include border-color(border);
        .sidebar-title{
          @include color(tc-primary);
        }
        svg{
          font-size:16px !important;
          @include color(tc-primary);
          &.arrow-up{
            transform: rotate(180deg);
          }
        }
      }
      .my-menu-wrapper{
        height:calc(100vh - 154px);
        overflow-y: auto;
        display:none;
        &.showMenu{
          display:block;
        }
        .sidebar-menu-item{
          width:100%;
          padding:0;
          &.active{
            border-radius:0px;
            @include bg-color(bg-primary);
            .sidebar-text{
              @include color(tc-primary);
            }
          }
          @include pc-hover {
            &:hover{
              border-radius:0px;
              @include bg-color(bg-primary);
            }
          }
          svg{
            font-size:16px !important;
          }
        }
        ul{
          li{
            padding-left:32px;
            &.active{
              border-radius:0;
              @include bg-color(bg-primary);
            }
            @include pc-hover {
              &:hover{
                border-radius:0;
                @include bg-color(bg-primary);
              }
            }
          }
        }
      }
    }
  }
</style>