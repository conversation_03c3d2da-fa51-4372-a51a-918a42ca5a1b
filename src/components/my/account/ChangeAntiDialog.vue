<template>
  <el-dialog v-model="isShowDialog" :title="$t('邮件防钓鱼码')" width="480" @close="emit('close')">
    <el-form ref="changeFormRef" :model="changeForm" :rules="changeFormRules">
      <el-form-item :label="$t('设置防钓鱼码')" prop="email_verify">
        <el-input v-model="changeForm.email_verify" type="text" clearable :placeholder="$t('输入4-20个字符，支持汉字、字母、数字或上述组合')" />
        <p class="fit-tc-secondary cursor-pointer">{{ $t('* 防钓鱼码是系统为您开放可以设置的一项安全功能，您可以设置一组独特的汉字、字母或数字，用以鉴别邮件是否来源于官方。') }}</p>
      </el-form-item>
      <el-form-item :label="$t('谷歌验证码')" prop="code">
        <GoogleCodeInputMin v-model="changeForm.code" :defaultFocus="false" @focus="emit('focus', changeForm.code)" />
      </el-form-item>
    </el-form>
    <el-button type="primary" :disabled="isDisabled" style="margin-top:10px;" @click="confirm">{{ $t('确认') }}</el-button>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElDialog, ElForm, ElFormItem, ElInput } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import { isValidPwd } from '~/utils/index'
  import GoogleCodeInputMin from '~/components/common/GoogleCodeInput.vue'
  import { useCommonData } from '~/composables/index'
  import { useUserStore } from '~/stores/useUserStore'
  import { setEmailVerify } from '~/api/user'
  const useUser = useUserStore()
  const useCommon = useCommonData()
  const  { t } = useI18n()
  const props = defineProps({
    visibleDialog: {
      type: Boolean,
      default: false
    },
    emailVerify: {
      type: String,
      default: ''
    }
  })
  const emit = defineEmits(['close', 'success'])
  const isShowDialog = ref(false)
  interface ChangeForm{
    email_verify: String,
    code: String
  }
  const changeFormRef = ref<FormInstance>()
  const changeForm = reactive<ChangeForm>({
    email_verify: '',
    code: ''
  })
  const valideEmailVerify = (rule, value, callback) => {
    if (value.length < 4 || value.length > 20) {
      return callback(new Error(t('请输入正确的防钓鱼码')))
    } else {
      return callback()
    }
  }
  const changeFormRules = reactive<FormRules<ChangeForm>>({
    email_verify: [
      { required: true, message: t('请输入防钓鱼码'), trigger: ['blur', 'change'] },
      { validator: valideEmailVerify, trigger: 'change' }
    ],
    code: [
      { required: true, message: t('请输入谷歌验证码'), trigger: ['blur', 'change'] }
    ]
  })
  const isDisabled = computed(() => {
    if (changeForm.email_verify !== '' && changeForm.code !== '' && changeForm.code.length === 6) {
      return false
    } else {
      return true
    }
    return true
  })
  const confirm = async() => {
    const { data, error } = await setEmailVerify(changeForm)
    if (data) {
      await useUser.getUserInfoAction()
      useCommon.showMsg('success', props.emailVerify === '' ? t('防钓鱼码设置成功') : t('防钓鱼码修改成功'))
      emit('close')
    } else {
      changeForm.code = ''
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  onMounted(() => {
    isShowDialog.value = props.visibleDialog
    changeForm.email_verify = props.emailVerify
  })
</script>
<style lang="scss" scoped>
.warn-tips{
  padding-bottom:20px;
}
</style>