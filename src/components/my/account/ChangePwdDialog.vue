<template>
  <el-dialog v-model="isShowDialog" :title="$t('修改登录密码')" width="480" @close="emit('close')">
    <p class="fit-warn warn-tips">{{ $t('*修改登录密码后，24小时内禁止提现操作！') }}</p>
    <el-form ref="changeFormRef" :model="changeForm" :rules="changeFormRules">
      <el-form-item :label="$t('输入原登录密码')" prop="oldPwd">
        <el-input v-model="changeForm.oldPwd" :type="showPwd ? 'text' : 'password'" :placeholder="$t('输入原登录密码')">
          <template #suffix>
            <div v-if="changeForm.oldPwd !== ''" class="flex-box space-end eye-icon" @click="showPwd = !showPwd">
              <MonoEyeClose v-if="!showPwd" />
              <MonoEyeOpen v-if="showPwd" />
            </div>
          </template>
        </el-input>
        <p class="fit-theme cursor-pointer" @click="forgetFun">{{ $t('忘记密码') }}</p>
      </el-form-item>
      <el-form-item :label="$t('设置新登录密码')" prop="newPwd">
        <el-input v-model="changeForm.newPwd" :type="showPwd1 ? 'text' : 'password'" :placeholder="$t('输入新登录密码')">
          <template #suffix>
            <div v-if="changeForm.newPwd !== ''" class="flex-box space-end eye-icon" @click="showPwd1 = !showPwd1">
              <MonoEyeClose v-if="!showPwd1" />
              <MonoEyeOpen v-if="showPwd1" />
            </div>
          </template>
        </el-input>
        <PasswordValidate :password="changeForm.newPwd" @changeStatus="passwordvalidate" />
      </el-form-item>
      <el-form-item :label="$t('再次输入新登录密码')" prop="confirmPwd">
        <el-input v-model="changeForm.confirmPwd" :type="showPwd2 ? 'text' : 'password'" :placeholder="$t('再次输入新登录密码')">
          <template #suffix>
            <div v-if="changeForm.confirmPwd !== ''" class="flex-box space-end eye-icon" @click="showPwd2 = !showPwd2">
              <MonoEyeClose v-if="!showPwd2" />
              <MonoEyeOpen v-if="showPwd2" />
            </div>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item v-if="user.is_bind_totp * 1 === 1" :label="$t('谷歌验证')" prop="totp_code">
        <GoogleCodeInputMin v-model="changeForm.totp_code" :defaultFocus="false" />
      </el-form-item>
    </el-form>
    <el-button type="primary" :disabled="isDisabled" style="margin-top:10px;" @click="confirm">{{ $t('确认') }}</el-button>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElDialog, ElForm, ElFormItem, ElInput } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import { isValidPwd } from '~/utils/index'
  import { useCommonData } from '~/composables/index'
  import GoogleCodeInputMin from '~/components/common/GoogleCodeInput.vue'
  import PasswordValidate from '~/components/login/password-validate.vue'
  import MonoEyeOpen from '~/components/common/icon-svg/MonoEyeOpen.vue'
  import MonoEyeClose from '~/components/common/icon-svg/MonoEyeClose.vue'
  import { changePwd } from '~/api/user'
  const useCommon = useCommonData()
  const  { locale, t } = useI18n()
  const router = useRouter()
  const props = defineProps({
    visibleDialog: {
      type: Boolean,
      default: false
    },
    user: {
      type: Object,
      default () {
        return {}
      }
    },
    params: {
      type: Object,
      default () {
        return {}
      }
    }
  })
  const emit = defineEmits(['close'])
  const isShowDialog = ref(false)
  const showPwd = ref(false)
  const showPwd1 = ref(false)
  const showPwd2 = ref(false)
  interface ChangeForm{
    oldPwd: String,
    newPwd: String,
    confirmPwd: String,
    totp_code: String
  }
  const changeFormRef = ref<FormInstance>()
  const changeForm = reactive<ChangeForm>({
    oldPwd: '',
    newPwd: '',
    confirmPwd: '',
    totp_code: ''
  })
  const valideOldPwd = (rule, value, callback) => {
    if (!value) {
      return callback(new Error(t('请输入原密码')))
    } else {
      return callback()
    }
  }
  const validePwd = (rule, value, callback) => {
    /* eslint no-lonely-if: "error" */
    if (changeForm.confirmPwd) {
      if (!value) {
        return callback(new Error(t('请输入确认密码')))
      } else if (value !== changeForm.confirmPwd) {
        return callback(new Error(t('两次输入的密码不一致')))
      } else {
        return callback()
      }
    } else if (changeForm.confirmPwd === '') {
      if (!value) {
        return callback(new Error(t('请输入密码')))
      } else {
        return callback()
      }
    }
  }
  const valideAgainPwd = (rule, value, callback) => {
    if (changeForm.newPwd) {
      if (!value) {
        return callback(new Error(t('请输入确认密码')))
      } else if (value !== changeForm.newPwd) {
        return callback(new Error(t('两次输入的密码不一致')))
      } else {
        return callback()
      }
    } else if (changeForm.newPwd === '') {
      return callback(new Error(t('请输入密码')))
    }
  }
  const changeFormRules = reactive<FormRules<ChangeForm>>({
    oldPwd: [
      { required: true, message: t('请输入原登录密码'), trigger: ['blur', 'change'] },
      { validator: valideOldPwd, trigger: ['blur', 'change'] }
    ],
    newPwd: [
      { required: true, message: t('请输入新登录密码'), trigger: ['blur', 'change'] },
      { validator: validePwd, trigger: ['blur', 'change'] }
    ],
    confirmPwd: [
      { required: true, message: t('请输入确认密码'), trigger: ['blur', 'change'] },
      { validator: valideAgainPwd, trigger: ['blur', 'change'] }
    ],
    totp_code: [
      { required: true, message: t('请输入谷歌'), trigger: ['blur', 'change'] }
    ]
  })
  const passwordValid = ref(false)
  const passwordvalidate = (val) => {
    passwordValid.value = val
  }
  const isDisabled = computed(() => {
    if (changeForm.oldPwd !== '' && changeForm.newPwd !== '' && passwordValid.value && changeForm.confirmPwd !== '' && isValidPwd(changeForm.oldPwd) && isValidPwd(changeForm.newPwd) && isValidPwd(changeForm.confirmPwd) && changeForm.newPwd === changeForm.confirmPwd && ((props.user.is_bind_totp * 1 === 1 && changeForm.totp_code.length === 6) || (props.user.is_bind_totp * 1 !== 1 && changeForm.totp_code.length < 6))) {
      return false
    } else {
      return true
    }
    return true
  })
  const forgetFun = async() => {
    router.push(`/${locale.value}/login/reset`)
  }
  const confirm = async() => {
    console.info({
      old_pwd: changeForm.oldPwd,
      new_pwd: changeForm.newPwd,
      ...props.params
    })
    const { data, error } = await changePwd({
      old_pwd: changeForm.oldPwd,
      new_pwd: changeForm.newPwd,
      totp_code: changeForm.totp_code,
      ...props.params
    })
    if (data) {
      await useCommon.loginout()
      useCommon.showMsg('success', t('您的密码已修改，请重新登录'))
      emit('close')
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  onMounted(() => {
    isShowDialog.value = props.visibleDialog
  })
</script>
<style lang="scss" scoped>
.warn-tips{
  padding-bottom:20px;
}
</style>