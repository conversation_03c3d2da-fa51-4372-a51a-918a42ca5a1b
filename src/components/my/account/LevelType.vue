<template>
  <div class="level-type-cont">
    <em class="level-new flex-box fit-theme mg-l8" style="font-size:20px;" :class="{'pro': userLevel.levelType === 'Pro' || (userLevel.levelType === 'Lv' && activeVip.contract_trade_amount >= 1000 && type === 'contract'), 'normal': userLevel.levelType === 'Lv' && type === 'spot', 'special': userLevel.levelType === 'special'}">
      {{ userLevel.levelType === 'special' ? `${$t('特殊费率')}` : ((userLevel.levelType === 'Lv' && Ltype === 'contract') ? (activeVip.contract_trade_amount >= 1000 ? `Pro ${userLevel.nowLevel}` : `Pro 0`) : $t(userLevel.levelType + ' ' + userLevel.nowLevel)) }}
    </em>
  </div>
</template>
<script lang="ts" setup>
  import BigNumber from 'bignumber.js'
  import MonoInfo from '~/components/common/icon-svg/MonoInfo.vue'
  import { ElTooltip } from 'element-plus'
  import { useUserStore } from '~/stores/useUserStore'
  import { useCommonData } from '~/composables/index'
  const props = defineProps({
    activeVip: {
      type: Object,
      default () {
        return {}
      }
    },
    Ltype: {
      type: String,
      default: ''
    }
  })
  const useCommon = useCommonData()
  const store = useUserStore()
  const { userInfo } = storeToRefs(store)
  const userLevel = computed(() => {
    return props.Ltype === 'spot' ? useCommon.userLevelMethod(userInfo.value.user_level) : useCommon.userLevelMethod(userInfo.value.contract_level)
  })
</script>
<style lang="scss">
dl,
dt,
dd,
p,
h1,
h2,
h3,
h4,
ul,
li {
  padding: 0;
  margin: 0;
  font-weight: 500;
}
em {
  font-style: normal;
}
.info-box{
  .level-type-cont {
    .level-new{
      font-size:24px !important;
    }
  }
}
.level-type-cont {
  &.type-level{
    p{
      display:none;
    }
  }
  &.info-level{
    em{
      display:none;
    }
  }
  .level-new {
    padding: 0 4px 0 0;
    height: 24px;
    border-radius: 14px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    box-sizing: border-box;
    font-style: normal;
    font-size: 14px;
    font-weight: 500;
    &.normal {
      @include color(theme);
      border: 0;
      .ld-main {
        display: block;
        width: 20px;
        height: 20px;
        // background: url("~@/assets/images/account/normal-icon.png") no-repeat center;
        // background-size: 100% auto;
      }
    }
    &.pro {
      @include color(theme);
      .ld-main {
        display: block;
        width: 20px;
        height: 20px;
        // background: url("~@/assets/images/account/pro-icon.png") no-repeat center;
        // background-size: 100% auto;
      }
    }
    &.special {
      @include color(theme);
      &::after{
        display: none;
      }
      .ld-main {
        display: none;
      }
    }
    &.big_gold {
      @include color(theme);
      .ld-main {
        display: block;
        width: 24px;
        height: 24px;
        // background: url("~@/assets/images/account/vip-icon-blue.png") no-repeat center;
        // background-size: 100% auto;
        margin-right: 0;
      }
    }
    &.big_diamond {
      @include color(theme);
      .ld-main {
        display: block;
        width: 24px;
        height: 24px;
        // background: url("~@/assets/images/account/vip-icon-blue.png") no-repeat center;
        // background-size: 100% auto;
        margin-right: 0;
      }
    }
    &.big_black_gold {
      @include color(theme);
      .ld-main {
        display: block;
        width: 24px;
        height: 24px;
        // background: url("~@/assets/images/account/vip-icon-blue.png") no-repeat center;
        // background-size: 100% auto;
        margin-right: 0;
      }
    }
  }
}
@include mb {
  .info-box{
    justify-content: center;
    .level-type-cont {
      .level-new{
        font-size:14px !important;
      }
    }
  }
  .level-type-cont {
    text-align:center;
    &.type-level{
      p{
        display:none;
      }
    }
    &.info-level{
      em{
        display:none;
      }
    }
    .level-new {
      &.mg-r16{
        margin-right:0 !important;
      }
      &.normal {
        @include color(theme);
        border: 0;
        .ld-main {
          display: block;
          width: 20px;
          height: 20px;
          // background: url("~@/assets/images/account/normal-icon.png") no-repeat center;
          // background-size: 100% auto;
        }
      }
      &.pro {
        @include color(theme);
        .ld-main {
          display: block;
          width: 20px;
          height: 20px;
          // background: url("~@/assets/images/account/pro-icon.png") no-repeat center;
          // background-size: 100% auto;
        }
      }
      &.special {
        @include color(theme);
        &::after{
          display: none;
        }
        .ld-main {
          display: none;
        }
      }
      &.big_gold {
        @include color(theme);
        .ld-main {
          display: block;
          width: 24px;
          height: 24px;
          // background: url("~@/assets/images/account/vip-icon-blue.png") no-repeat center;
          // background-size: 100% auto;
          margin-right: 0;
        }
      }
      &.big_diamond {
        @include color(theme);
        .ld-main {
          display: block;
          width: 24px;
          height: 24px;
          // background: url("~@/assets/images/account/vip-icon-blue.png") no-repeat center;
          // background-size: 100% auto;
          margin-right: 0;
        }
      }
      &.big_black_gold {
        @include color(theme);
        .ld-main {
          display: block;
          width: 24px;
          height: 24px;
          // background: url("~@/assets/images/account/vip-icon-blue.png") no-repeat center;
          // background-size: 100% auto;
          margin-right: 0;
        }
      }
    }
  }
}
</style>