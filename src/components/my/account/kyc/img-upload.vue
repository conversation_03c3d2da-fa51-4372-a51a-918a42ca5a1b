<template>
  <el-upload
    :show-file-list="false"
    :http-request="upload"
    action
    :class="{'id-card': classType, 'id-card-en': classTypeEn, 'id-card-passport': classTypePassport, 'id-card-driver': classTypeDriver, 'id-card-en': classTypeEn, 'card-before': className === 'card-before', 'card-back': className === 'card-back', 'card-selfie': className === 'card-selfie'}"
  >
    <div v-if="status === 1" class="loading-box">
      <span>{{ $t('上传中...') }}</span>
    </div>
    <div v-else-if="status === 2" class="img-box">
      <img :src="successBase64" />
    </div>
    <div v-else-if="status === 3" class="error-box">
      <span>{{ $t('上传失败, 请重新上传') }}</span>
    </div>
    <p v-if="successBase64 === ''" class="add-btn">{{ textBtn }}</p>
  </el-upload>
</template>
<script lang="ts" setup>
  import { ElUpload } from 'element-plus'
  import { UploadFile } from '~/utils/UploadFile'
  import { getDomainF } from '~/utils'
  const props = defineProps({
    textBtn: {
      type: String,
      default: ''
    },
    className: {
      type: String,
      default: ''
    },
    classType: {
      type: Boolean,
      default: false
    },
    classTypeEn: {
      type: Boolean,
      default: false
    },
    classTypeDriver: {
      type: Boolean,
      default: false
    },
    classTypePassport: {
      type: Boolean,
      default: false
    },
    modelValue: {
      type: String,
      default: ''
    }
  })
  const emit = defineEmits(['update:modelValue'])
  const status = ref(0) // 未上传:0; 上传中:1; 上传成功:2; 上传失败:3;
  const successBase64 = ref('')
  const upload = (fileData) => {
    status.value = 1
    const upload = new UploadFile({
      purpose: 'kyc'
    }, fileData, getDomainF().includes('tonetou') ? `https://ma-tapi${getDomainF()}/v1/upload/uploadfront` : `https://api${getDomainF()}/v1/upload/uploadfront`)
    upload.upload((res, name) => {
      const photoAfter = name ? name.split('.').pop() : 'png'
      const before = res.startsWith
      const path = `${before}madEx_kyc_${Date.now()}.${photoAfter}`
      console.info(path, res, 'FileReader')
      return path
    }, true).then((res) => {
      console.info(res, 'FileReader')
      status.value = 2
      emit('update:modelValue', res)
      const reader = new FileReader()
      reader.readAsDataURL(fileData.file)
      reader.onload = (e) => {
        successBase64.value = e.target.result
      }
    }).catch((err, imgUrl) => {
      console.info(err, imgUrl, 'FileReader')
      status.value = 3
      setTimeout(() => {
        status.value = 0
      }, 5000)
      console.info(err)
    })
  }
  onMounted(() => {
    if (props.modelValue) {
      status.value = 2
    }
  })
</script>
<style>
</style>