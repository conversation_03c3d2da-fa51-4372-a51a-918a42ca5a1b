<template>
  <div class="list-select-container">
    <div class="list-select-wrapper">
      <el-popover ref="popoverRef" trigger="click" popper-class="select-list-popover" width="460" @visible-change="handleVisibleChange">
        <template #reference>
          <div class="select-input">
            <div class="selected-input-box flex-box space-between" :class="{'active': isShowDownSelect}" @click="focusSelect">
              <div class="selected-left flex-box space-between flex-1">
                <template v-if="isShowSelected">
                  <div class="flex-box">
                    <span>{{ curItem.address }}</span>
                  </div>
                </template>
                <div v-else class="placeholder-txt">{{ $t('选择地址') }}</div>
              </div>
              <div class="selected-right">
                <MonoDownArrowMin :size="12" />
              </div>
            </div>
          </div>
        </template>
        <div class="list-select-body small">
          <div class="select-body-wrap">
            <div class="select-coin-list-box">
              <ul v-if="listfilter.length > 0">
                <li v-for="(item, index) in listfilter" :class="{'disabled': item.addr_type * 1 !== 1}" class="noflex" :key="index" @click="changeSymbol(item)">
                  <div class="list-p flex-box space-between">
                    <div class="left flex-box">
                      <span v-if="item.name">{{ item.name }}</span>
                      <span class="flex-box verify-icon-text" v-if="item.acl * 1 === 1">{{ $t('免验证') }}</span>
                    </div>
                    <div v-if="item.addr_type * 1 === 1" class="white-address-tag">{{ $t('白名单地址') }}</div>
                    <!-- <div class="right flex-box flex-column align-end">
                      <div class="fit-tc-secondary font-size-14">{{ item.chain_type }}</div>
                    </div> -->
                  </div>
                  <div class="flex-box space-between">
                    <div>
                      <p>{{ item.address }}</p>
                      <p v-if="item.address_tag">{{ item.address_tag }}</p>
                    </div>
                  </div>
                  <div class="flex-box">
                    <div class="fit-tc-secondary font-size-14">{{ item.chain_type }}</div>
                  </div>
                </li>
              </ul>
              <div v-if="listfilter.length === 0" style="height:200px;">
                <BoxNoData :text="$t('暂无数据')" />
              </div>
            </div>
            <div class="bottom-cont-box flex-box space-center">
              <div class="add-item" @click="addWhiteItemFun()">
                {{ $t('添加地址') }}
              </div>
            </div>
          </div>
        </div>
      </el-popover>
    </div>
  </div>
  <WhitelistAddDialog v-if="isShowAddWhiteItem" :curSymbol="symbol" :dialogVisible="isShowAddWhiteItem" :isOpenWhite="isOpenWhite" @close="isShowAddWhiteItem = false" @success="getWhiteList()" />
</template>
<script lang="ts" setup>
  import { ElPopover, ElInput } from 'element-plus'
  import BoxNoData from '~/components/common/BoxNoData.vue'
  import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
  import MonoManage from '~/components/common/icon-svg/MonoManage.vue'
  import WhitelistAddDialog from '~/components/my/assets/addresslist/AddDialog.vue'
  import { getWithdrawAddrApi } from '~/api/tf'
  const { locale } = useI18n()
  const props = defineProps({
    modelValue: {
      type: String,
      default: ''
    },
    symbol: {
      type: String,
      default: ''
    },
    isOpenWhite: {
      type: Boolean,
      default: false
    }
  })
  const emit = defineEmits(['update:modelValue', 'changeWebsite'])
  const curItem = ref({})
  const visible = ref<Boolean>(false)
  const isShowSelected = ref<Boolean>(false)
  const popoverRef = ref(null)
  const isShowDownSelect = ref(false)
  const list = ref<Array>([])
  const changeSymbol = (item) => {
    if (item.addr_type * 1 !== 1) {
      return false
    }
    emit('update:modelValue', item.url)
    isShowSelected.value = true
    curItem.value = item
    emit('changeWebsite', item)
    if (popoverRef.value) {
      nextTick(() => {
        popoverRef.value.hide()
      })
    }
  }
  watch(() => props.symbol, (val) => {
    if (val !== '') {
      curItem.value = ''
      isShowSelected.value = false
      emit('update:modelValue', '')
    }
  })
  const handleVisibleChange = (visible) => {
    console.info(visible, 'dhdhudeuhdueu')
    isShowDownSelect.value = visible
  }
  const isShowAddWhiteItem = ref(false)
  const addWhiteItemFun = () => {
    console.log(props.symbol, 'djdjeiijdijeij')
    isShowAddWhiteItem.value = true
  }
  const listfilter = computed(() => {
    return list.value.filter((v) => {
      return v.general_symbol === props.symbol && v.addr_type * 1 === 1
    })
  })
  const getWhiteList = async() => {
    const { data } = await getWithdrawAddrApi({
      page: 1,
      size:1000
    })
    if (data) {
      list.value = data.rows
    }
  }
  onMounted(() => {
    getWhiteList()
  })
</script>
<style lang="scss">
@import url('@/assets/style/orders/selectList.scss');
</style>