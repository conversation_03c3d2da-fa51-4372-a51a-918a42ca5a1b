<template>
  <div class="list-select-container" :style="{'width': width}">
    <div class="list-select-wrapper" :style="{'width': width}">
      <el-popover ref="popoverRef" trigger="click" popper-class="select-list-popover" :width="460" @visible-change="handleVisibleChange">
        <template #reference>
          <div class="select-input" :style="{'width': width}">
            <div class="selected-input-box flex-box space-between" :style="{'width': width}" :class="{'withdrawal': isWithdrawal && isShowSelected, active: isShowDownSelect}" @click="focusSelect">
              <div class="selected-left flex-box space-between flex-1">
                <template v-if="isShowSelected">
                  <div>
                    <div class="flex-box">
                      <!-- <span>{{ curItem.symbol }}</span> -->
                      <div class="fit-tc-primary font-size-16">{{ curItem.chain_type }}</div>
                    </div>
                    <!-- <p v-if="isWithdrawal"><b>{{ $t('网络确认') }}</b> {{ curItem.safe_confirm_count }} {{ $t('次') }}</p> -->
                  </div>
                  <div v-if="isWithdrawal" class="mg-r8">
                    <p style="text-align:right;"><em>{{ $t('最小提现数量：') }}</em>{{ curItem.withdraw_min }} {{ curItem.general_name }}</p>
                    <p style="text-align:right;"><em>{{ $t('手续费：') }}</em>{{ curItem.withdraw_fee }} {{ curItem.general_name }}</p>
                  </div>
                </template>
                <div v-else class="placeholder-txt">{{ $t('选择网络') }}</div>
              </div>
              <div class="selected-right">
                <MonoDownArrowMin :size="12" />
              </div>
            </div>
          </div>
        </template>
        <div class="list-select-body small">
          <div class="select-body-wrap">
            <div class="select-coin-list-box">
              <div class="select-title flex-box space-between">
                {{ $t('选择网络') }}
              </div>
              <ul>
                <li v-for="(item, index) in list" :key="index" class="flex-box space-between" @click="changeSymbol(item)">
                  <div class="fit-tc-primary">
                    <!-- <div class="fit-tc-primary">{{ item.symbol }}</div> -->
                    {{ item.chain_type }}
                  </div>
                  <div class="flex-box flex-column align-end">
                    <div v-if="!isWithdrawal" class="fit-tc-secondary font-size-12">{{ $t('最小充值数量：') }}{{ item.deposit_min }} {{ item.general_name }}</div>
                    <div v-if="isWithdrawal" class="fit-tc-secondary font-size-12">{{ $t('最小提现数量：') }}{{ item.withdraw_min }} {{ item.general_name }}</div>
                    <div v-if="isWithdrawal" class="fit-tc-secondary font-size-12">{{ $t('手续费：') }}{{ item.withdraw_fee }} {{ item.general_name }}</div>
                    <!-- <div class="fit-tc-secondary font-size-12">{{ $t('网络确认') }} {{ item.safe_confirm_count }} {{ $t('次') }}</div> -->
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </el-popover>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ElPopover, ElInput } from 'element-plus'
  import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
  import { getListByGeneralNameApi } from '~/api/public'
  const props = defineProps({
    value: {
      type: String,
      default: 'symbol'
    },
    width: {
      type: String,
      default: ''
    },
    modelValue: {
      type: String,
      default: ''
    },
    isWithdrawal: {
      type: Boolean,
      default: false
    },
    symbol: {
      type: String,
      default: ''
    },
    list: {
      type: Array,
      default () {
        return []
      }
    }
  })
  const emit = defineEmits(['update:modelValue', 'changeWebsite'])
  const curItem = ref({})
  const visible = ref<Boolean>(false)
  const isShowSelected = ref<Boolean>(false)
  const popoverRef = ref(null)
  const isShowDownSelect = ref(false)
  watch(() => props.symbol, (val) => {
    console.info(val, 'props.symbol')
    if (val !== '') {
      curItem.value = ''
      isShowSelected.value = false
      emit('update:modelValue', '')
    }
  },{ immediate: true })
  const changeSymbol = (item) => {
    emit('changeWebsite', item)
    emit('update:modelValue', item[props.value])
    isShowSelected.value = true
    curItem.value = item
    if (popoverRef.value) {
      nextTick(() => {
        popoverRef.value.hide()
      })
    }
  }
  watch(() => props.modelValue, (val) => {
    if (val === '') {
      isShowSelected.value = false
      curItem.value = {}
    }
  })
  const handleVisibleChange = (visible) => {
    console.info(visible, 'dhdhudeuhdueu')
    isShowDownSelect.value = visible
  }
</script>
<style lang="scss">
@import url('@/assets/style/orders/selectList.scss');
</style>