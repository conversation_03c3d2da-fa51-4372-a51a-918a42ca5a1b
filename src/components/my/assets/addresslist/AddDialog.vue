<template>
  <el-dialog :title="$t('添加地址')" v-model="dialogTableVisible" width="480" @close="emit('close')">
    <el-form ref="whiteFormRef" :model="whiteForm" :rules="whiteRules">
      <el-form-item :label="$t('地址备注')" prop="addr_remark">
        <el-input v-model="whiteForm.addr_remark" type="text" :placeholder="$t('长度50个字符，支持中英文和数字')" clearable />
      </el-form-item>
      <el-form-item :label="$t('币种')" prop="coin_symbol">
        <CoinListSelect v-model="whiteForm.coin_symbol" width="100%" />
      </el-form-item>
      <el-form-item :label="$t('选择转账网络')" prop="website">
        <WebsiteSelect v-model="whiteForm.website" :symbol="whiteForm.coin_symbol" width="100%" value="id" @changeWebsite="changeWebsite" :list="websiteList" />
      </el-form-item>
      <el-form-item v-if="websiteItme.issupport_memo * 1 === 1" :label="$t('MEMO')" prop="memo">
        <el-input v-model="whiteForm.memo" type="text" :placeholder="$t('填写MEMO')" clearable />
      </el-form-item>
      <el-form-item :label="$t('提币地址')" prop="addr">
        <el-input v-model="whiteForm.addr" type="text" :placeholder="$t('粘贴提币地址')" clearable />
      </el-form-item>
      <el-form-item>
        <el-checkbox v-model="enableAcl">{{ $t('开启提币免验证') }}</el-checkbox>
        <p class="font-size-14 fit-tc-secondary">{{ $t('* 开启提币免验证后，下次使用该地址提币将免安全验证。') }}</p>
        <el-checkbox v-if="isOpenWhite" v-model="addressType">{{ $t('将地址添加到白名单') }}</el-checkbox>
      </el-form-item>
    </el-form>
    <el-button :disabled="isDisabled" type="primary" @click="isShowConfirm = true">{{ $t('确认') }}</el-button>
  </el-dialog>
  <withdrawalConfirm
      v-if="isShowConfirm"
      :dialogVisible="isShowConfirm"
      @close="isShowConfirm = false"
      @confirm="confirm"
    />
</template>
<script lang="ts" setup>
  import { ElDialog, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElCheckbox } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import CoinListSelect from '~/components/common/CoinListSelect.vue'
  import WebsiteSelect from '~/components/my/assets/WebsiteSelect.vue'
  import GoogleCodeInputMin from '~/components/common/GoogleCodeInput.vue'
  import withdrawalConfirm from '~/components/my/assets/withdrawalConfirm.vue'
  import DropdownSelect from '~/components/common/DropdownSelect'
  import { addWithdrawAddrApi } from '~/api/tf'
  import { getListByGeneralNameApi } from '~/api/public'
  import { commonStore } from '~/stores/commonStore'
  import { useCommonData } from '~/composables/index'
  const useCommon = useCommonData()
  const store = commonStore()
  const { getCoinList } = store
  const { coinList } = storeToRefs(store)
  const { locale, t } = useI18n()
  const props = defineProps({
    dialogVisible: {
      type: Boolean,
      default: false
    },
    isOpenWhite: {
      type: Boolean,
      default: false
    },
    curSymbol: {
      type: String,
      default: ''
    }
  })
  const emit = defineEmits(['focus', 'close', 'success'])
  const dialogTableVisible = ref(false)
  interface WhiteForm{
    addr_remark: String,
    coin_symbol: String,
    addr: String,
    memo: String,
    website: String,
    enable_acl: String,
    address_type: String
  }
  const whiteFormRef = ref<FormInstance>()
  const whiteForm = reactive<WhiteForm>({
    addr_remark: '',
    coin_symbol: '',
    addr: '',
    memo: '',
    website: '',
    enable_acl: '',
    addr_type: 2
  })
  const enableAcl = ref(false)
  const addressType = ref(false)
  const isShowConfirm = ref(false)
  watch(() => enableAcl.value, (val) => {
    whiteForm.enable_acl = val ? 1 : ''
  })
  watch(() => addressType.value, (val) => {
    whiteForm.addr_type = val ? 1 : 2
  })
  const valideaddrRemark = (rule, value, callback) => {
    if (value.length > 50 && locale.value === 'en') {
      return callback(new Error(t('备注不能超过50个字符')))
    } else if (value.length > 25 && locale.value === 'zh') {
      return callback(new Error(t('备注不能超过25个字符')))
    } else {
      return callback()
    }
  }
  const whiteRules = reactive<FormRules<WhiteForm>>({
    addr_remark: [
      { required: true, message: t('请输入地址备注'), trigger: 'blur' },
      { validator: valideaddrRemark, trigger: 'change' }
    ],
    addr: [
      { required: true, message: t('请输入提币地址'), trigger: 'blur' }
    ],
    memo: [
      { required: true, message: t('请输入MEMO'), trigger: 'blur' }
    ],
    website: [
      { required: true, message: t('请选择转账网络'), trigger: 'blur' }
    ]
    // code: [
    //   { required: true, message: t('请输入谷歌验证码'), trigger: 'blur' }
    // ]
  })
  const searchInput = ref('')
  //  && whiteForm.code.length === 6
  const isDisabled = computed(() => {
    if ( (websiteItme.value.issupport_memo * 1 === 0 && whiteForm.addr_remark !== '' && whiteForm.coin_symbol !== '' && whiteForm.addr !== '' && whiteForm.website !== '') || (websiteItme.value.issupport_memo * 1 === 1 && whiteForm.memo !== '' && whiteForm.addr_remark !== '' && whiteForm.coin_symbol !== '' && whiteForm.addr !== '' && whiteForm.website !== '')) {
      return false
    } else {
      return true
    }
  })
  const websiteItme = ref({})
  const websiteList = ref([])
  const changeWebsite = (item) => {
    websiteItme.value = item
  }
  watch(() => whiteForm.coin_symbol, () => {
    whiteForm.website = ''
    getSymboleInfo()
  })
  const getSymboleInfo = async() => {
    const { data } = await getListByGeneralNameApi({
      general_name: whiteForm.coin_symbol
    })
    if (data) {
      websiteList.value = data
      websiteItme.value = data[0]
    }
  }
  const confirm = async(params) => {
    const { data, error } = await addWithdrawAddrApi({
      coin_id: whiteForm.website,
      address: whiteForm.addr,
      address_tag: whiteForm.memo,
      name: whiteForm.addr_remark,
      addr_type: whiteForm.addr_type,
      enable_acl: whiteForm.enable_acl,
      email_code: params.emailCode,
      totp_code: params.code
    })
    if (data) {
      isShowConfirm.value = false
      emit('close')
      emit('success')
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  onMounted (() => {
    getCoinList()
    dialogTableVisible.value = props.dialogVisible
    console.log(props.curSymbol, 'dhjdeuhduheuhuheuheu')
    whiteForm.coin_symbol = props.curSymbol
  })
</script>
<style lang="scss">
.orders-select{
  &.select-symbol-box{
    margin-right:0;
    margin-top:0;
    .select-box{
      height:44px;
      border-radius:4px;
    }
  }
}
</style>