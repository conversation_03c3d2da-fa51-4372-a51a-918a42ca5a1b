<template>
  <div class="dialog-container-m">
    <div class="dialog-wrapper-m animate__animated" :class="{'animate__fadeOutDown': !isShowSlectParamsCont, 'animate__fadeInUp': isShowSlectParamsCont}">
      <div class="wrapper-pd-box">
        <div class="dialog-title-m flex-box space-between">
          {{ $t('筛选') }}
          <MonoClose @click="emit('closeDialog')" />
        </div>
        <div class="dialog-body-m">
          <div class="label-item-cont">
            <div class="label-title">{{ $t('关键字搜索') }}</div>
            <div class="label-cont-box">
              <el-input v-model="search.addr_remark" type="text" :placeholder="$t('输入地址备注、币种、转账网络')" />
            </div>
          </div>
          <div class="label-item-cont">
            <div class="label-title">{{ $t('地址类型') }}</div>
            <div class="label-cont-box">
              <DropdownSelect popperClass="w100" v-model="search.address_type" :list="typeList" :labelName="$t('地址类型')" wid="100%" />
            </div>
          </div>
          <div class="label-item-cont">
            <div class="label-title">{{ $t('币种') }}</div>
            <div class="label-cont-box flex-box">
              <div class="flex-1">
                <DropdownSelect isSearch popperClass="w100" v-model="search.coin_symbol" :list="coinList" :labelName="$t('币种')" wid="100%" @change="emit('change')">
                  <slot></slot>
                </DropdownSelect>
              </div>
            </div>
          </div>
        </div>
        <div class="dialog-footer-m flex-box">
          <div class="flex-1 mg-r12">
            <el-button class="reset-btn" @click="initDate">{{ $t('重置') }}</el-button>
          </div>
          <div class="flex-1">
            <el-button type="primary" @click="confirmFun">{{ $t('确认') }}</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import DropdownSelect from '~/components/common/DropdownSelect'
  const props = defineProps({
    isBill: {
      type: Boolean,
      default: false
    },
    isShowSlectParamsCont: {
      type: Boolean,
      default: false
    },
    coinList: {
      type: Boolean,
      default () {
        return []
      }
    },
    typeList: {
      type: Boolean,
      default () {
        return []
      }
    }
  })
  const emit = defineEmits(['confirmParams', 'cloaseDialog', 'change'])
  const search = ref({
    address_type: '',
    coin_symbol: '',
    addr_remark: ''
  })
  const initDate = () => {
    search.value = {
      address_type: '',
      coin_symbol: '',
      addr_remark: ''
    }
  }
  const confirmFun = () => {
    emit('confirmParams', search.value)
    emit('cloaseDialog')
  }
</script>
<style lang="scss" scoped>
@import '@/assets/style/global-dialog-m.scss';
</style>