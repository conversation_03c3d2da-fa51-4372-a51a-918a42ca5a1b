<template>
  <el-dialog v-model="visible" :title="$t('充币地址')" width="480" @close="emit('close')">
    <div class="deposit-tips font-size-14 fit-tc-secondary">{{ $t('最多可新增10个充值地址') }}</div>
    <div v-loading="isLoading" class="deposit-list">
      <ul>
        <li v-for="(item, index) in depositList" :class="{'active': defaultAddress.id === item.id}" @click="changeAddress(item)">
          <h3 class="cursor-pointer" @click.stop="bzEditFun(item, item.index)">
            <div class="flex-box">
              {{ item.alias ? item.alias : $t('地址x',{ num: item.index }) }}
              <MonoEdit size="16" class="edit-icon mg-l4" />
            </div>
          </h3>
          <p>{{ item.address }}</p>
          <div class="checked-icon">
            <MonoRigthChecked size="20" class="fit-theme" />
          </div>
        </li>
      </ul>
    </div>
    <el-button type="primary" :disabled="depositList.length === 10" :loading="isBtnLoading" class="mg-t24" @click="addAddressFun()">{{ $t('新增地址') }}</el-button>
  </el-dialog>
  <el-dialog v-model="isShow" :title="$t('修改备注')" width="400px" class="bz-dialog" @close="isShow = false; bzText = ''; editLoading = false">
    <div class="invite-container">
      <div class="invite-cont-box">
        <div class="input-box">
          <el-input v-model="bzText" />
        </div>
        <div class="btn-box mg-t24">
          <el-button type="primary" :loading="editLoading" :disabled="isBzDisabled" @click="editBzFun">{{ $t('确定') }}</el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { getDepositListApi, getDepositAddressApi, editAddressApi } from '~/api/tf'
  import MonoEdit from '~/components/common/icon-svg/MonoEdit.vue'
  import MonoRigthChecked from '~/components/common/icon-svg/MonoRigthChecked.vue'
  import { ElDialog } from 'element-plus'
  import { useCommonData } from '~/composables/index'
  const useCommon = useCommonData()
  const { local, t } = useI18n()
  const props = defineProps({
    isShowDialog: {
      type: Boolean,
      default: false
    },
    symbol: {
      type: String,
      default: ''
    },
    defaultAddress: {
      type: String,
      default() {
        return {}
      }
    }
  })
  const emit = defineEmits(['close', 'changeAddress'])
  const visible = ref(false)
  const depositList = ref([])
  const isLoading = ref(false)
  const isBtnLoading = ref(false)
  const isShow = ref(false)
  const curItem = ref({})
  const bzText = ref('')
  const editLoading = ref(false)
  const isBzDisabled = computed(() => {
    return bzText.value === ''
  })
  const getDepositList = async() => {
    isLoading.value = true
    const { data } = await getDepositListApi({
      coin_symbol: props.symbol
    })
    if (data) {
      depositList.value = data
      isLoading.value = false
    }
    isLoading.value = false
  }
  const addAddressFun = async() => {
    if (depositList.value.length === 10) {
      return false
    }
    isBtnLoading.value = true
    const { data, error } = await getDepositAddressApi({
      coin_symbol: props.symbol,
      force: 1
    })
    if (data) {
      useCommon.showMsg('success', t('新增成功'))
      await getDepositList()
      isBtnLoading.value = false
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
      isBtnLoading.value = false
    }
  }
  const changeAddress = async(item) => {
    const { data, error } = await editAddressApi({
      id: item.id,
      is_default: 1
    })
    if (data) {
      emit('changeAddress', item)
      emit('close')
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const bzEditFun = (item, idx) => {
    isShow.value = true
    curItem.value = item
    bzText.value = item.alias ? item.alias : t('地址x',{ num: idx })
  }
  const editBzFun = async() => {
    editLoading.value = true
    const { data, error } = await editAddressApi({
      id: curItem.value.id,
      alias: bzText.value
    })
    if (data) {
      getDepositList()
      isShow.value = false
      editLoading.value = false
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
      editLoading.value = false
    }
  }
  onMounted(() => {
    getDepositList()
    visible.value = props.isShowDialog
  })
</script>
<style lang="scss" scoped>
  .deposit-list{
    padding-top:4px;
    height:280px;
    margin:0 -24px;
    overflow-y:auto;
    ul{
      li{
        padding:12px 24px;
        margin-top:8px;
        cursor:pointer;
        position:relative;
        &.active{
          .checked-icon{
            display:block;
          }
        }
        .checked-icon{
          display:none;
          position:absolute;
          right:24px;
          top:50%;
          margin-top:-10px;
        }
        &:hover{
          @include bg-color(bg-quaternary);
        }
        h3{
          display:inline-block;
          font-size:14px;
          @include color(tc-secondary);
          &:hover{
            .edit-icon{
              @include color(theme);
            }
          }
          .edit-icon{
            @include color(tc-secondary);
            
          }
        }
        p{
          font-size:14px;
          padding-top:8px;
          @include color(tc-primary);
        }
      }
    }
  }
  @include mb{
    .deposit-list{
      margin:0 -16px;
      ul{
        li{
          padding:12px 16px;
          cursor:pointer;
          .checked-icon{
            position:absolute;
            right:16px;
            top:50%;
            margin-top:-10px;
          }
        }
      }
    }
  }
</style>