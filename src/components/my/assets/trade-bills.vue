<template>
  <div class="wallet-table-cont">
    <div class="wallet-table-wrap">
      <div class="table-header flex-box space-between">
        <div class="head-left">{{ $t('账户变动记录') }}</div>
        <div class="head-right flex-box">
          <NuxtLink :to="`/${locale}/my/orders/bills?type=trade`" class="fit-theme">
            {{ $t('查看更多') }}
            <MonoRightArrowShort size="14" />
          </NuxtLink>
        </div>
      </div>
      <div :style="isLoading || currentList.length === 0 ?'height:500px;' : 'height:auto'">
        <OrderstableBox :isLoading="isLoading" :mSortList="mSortList" :headers="headersList" :list="currentList">
          <template #time="scope">
            <span class="fit-tc-secondary">{{ timeFormat(scope.data.time) }}</span>
          </template>
          <template #asset="scope">
            <span>{{ scope.data.asset }}</span>
          </template>
          <template #amount="scope">
            <span :class="scope.data.amount * 1 < 0 ? 'fit-fall' : 'fit-rise'">{{ scope.data.amount * 1 < 0 ? '-' : '' }}{{ useCommon.hideAssets(format(scope.data.amount * 1 < 0 ? -Number(scope.data.amount) : scope.data.amount, 10, true, true)) }} {{ scope.data.asset }}</span>
          </template>
          <template #balance="scope">
            <span>{{ useCommon.hideAssets(format(scope.data.balance, 10 , true, true)) }} {{ scope.data.asset }}</span>
          </template>
          <template #type="scope">
            <span v-if="TRADE_BILLS_MAP[scope.data.type]">{{ $t(`${TRADE_BILLS_MAP[scope.data.type ? scope.data.type : '--']}`) }}</span>
            <span v-else>{{ scope.data.type }}</span>
          </template>
        </OrderstableBox>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import DropdownSelect from '~/components/common/DropdownSelect.vue'
import OrderstableBox from '~/components/common/OrderstableBox.vue'
import { timeFormat, format } from '~/utils'
import { tarderBillsChange } from '~/api/tf'
import { TRADE_BILLS_MAP } from '~/config'
import { useCommonData } from '~/composables/index'
const useCommon = useCommonData()
const props = defineProps({
  curCoin: {
    type: String,
    default: ''
  }
})
const { locale, t } = useI18n()
const headersList = ref([
  { text: t('时间'), key: 'time', align: 'left', wid: 'flex-2', style: 'auto' },
  { text: t('币种'), key: 'asset', align: 'left', wid: '',style: 'auto' },
  { text: t('数量'), key: 'amount', align: 'right', wid: 'flex-2', style: 'auto' },
  { text: t('剩余'), key: 'balance', align: 'right', wid: 'flex-2', style: 'auto' },
  { text: t('类型'), key: 'type', align: 'right', wid: '', style: 'auto' }
])
const mSortList = ref([
  { text: t('币种'), key: 'asset', align: 'left', wid: '',style: 'auto' },
  { text: t('时间'), key: 'time', align: 'left', wid: 'flex-2', style: 'auto' },
  { text: t('类型'), key: 'type', align: 'right', wid: '', style: 'auto' },
  { text: t('数量'), key: 'amount', align: 'right', wid: '', style: 'auto' },
  { text: t('剩余'), key: 'balance', align: 'right', wid: '', style: 'auto' }
])
const currentList = ref([])
const isLoading = ref(true)
const getBill = async() => {
  const { data } = await tarderBillsChange()
  if (data) {
    isLoading.value = false
    currentList.value = props.curCoin ? data.filter((item) => {
      return item.asset === props.curCoin
    }).reverse() : data.reverse()
  }
}
const type = ref('')
const fliterTypeList = ref([
  {
    label: t('全部'),
    value: ''
  }
])
onMounted(() => {
  getBill()
})
</script>