<template>
  <div class="wallet-table-cont">
    <div class="wallet-table-wrap">
      <div class="table-header flex-box space-between">
        <div class="head-left">{{ $t('账户变动记录') }}</div>
        <div class="head-right flex-box">
          <NuxtLink :to="`/${locale}/my/orders/bills?type=wallet`" class="fit-theme">
            {{ $t('查看更多') }}
            <MonoRightArrowShort size="14" />
          </NuxtLink>
        </div>
      </div>
      <div :style="isLoading || currentList.length === 0 ?'height:500px;' : 'height:auto'">
        <OrderstableBox :isLoading="isLoading" :mSortList="mSortList" :headers="headersList" :list="currentList">
          <template #updatedAt="scope">
            <span class="fit-tc-secondary">{{ timeFormat(scope.data.updatedAt) }}</span>
          </template>
          <template #symbol="scope">
            <span>{{ scope.data.symbol }}</span>
          </template>
          <template #change="scope">
            <span :class="scope.data.change * 1 < 0 ? 'fit-fall' : 'fit-rise'">{{ scope.data.change * 1 < 0 ? '-' : '' }}{{ useCommon.hideAssets(format(scope.data.change * 1 < 0 ? -Number(scope.data.change) : scope.data.change, 10, true, true)) }} {{ scope.data.symbol }}</span>
          </template>
          <template #result="scope">
            <span style="white-space:nowrap;">{{ useCommon.hideAssets(format(scope.data.result, 10, true, true)) }} {{ scope.data.symbol }}</span>
          </template>
          <template #type="scope">
            <span>{{ tabMap[1][scope.data.type] || $t(scope.data.comment) }}</span>
          </template>
        </OrderstableBox>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import DropdownSelect from '~/components/common/DropdownSelect.vue'
import OrderstableBox from '~/components/common/OrderstableBox.vue'
import { timeFormat, format } from '~/utils'
import { MainBillsChange, getTreeNewApi } from '~/api/tf'
import { useCommonData } from '~/composables/index'
const useCommon = useCommonData()
const props = defineProps({
  curCoin: {
    type: String,
    default: ''
  }
})
const { locale, t } = useI18n()
const headersList = ref([
  { text: t('时间'), key: 'updatedAt', align: 'left', wid: 'flex-2', style: 'auto' },
  { text: t('币种'), key: 'symbol', align: 'left', wid: '',style: 'auto' },
  { text: t('数量'), key: 'change', align: 'right', wid: 'flex-2', style: 'auto' },
  { text: t('剩余'), key: 'result', align: 'right', wid: 'flex-2', style: 'auto' },
  { text: t('类型'), key: 'type', align: 'right', wid: '', style: 'auto' }
])
const mSortList = ref([
  { text: t('币种'), key: 'symbol', align: 'left', wid: '',style: 'auto' },
  { text: t('时间'), key: 'updatedAt', align: 'left', wid: 'flex-2', style: 'auto' },
  { text: t('类型'), key: 'type', align: 'right', wid: '', style: 'auto' },
  { text: t('数量'), key: 'change', align: 'right', wid: 'flex-2', style: 'auto' },
  { text: t('剩余'), key: 'result', align: 'right', wid: 'flex-2', style: 'auto' }
])
const currentList = ref([])
const isLoading = ref(true)
const getBill = async() => {
  const { data } = await MainBillsChange({
    page: 1,
    size: 100
  })
  if (data) {
    isLoading.value = false
    currentList.value = props.curCoin ? data.list.filter((item) => {
      return item.symbol === props.curCoin
    }) : data.list
  }
}
const type = ref('')
const fliterTypeList = ref([
  {
    label: t('全部'),
    value: ''
  }
])
const tabMap = ref({})
const deepMap = (arr) => {
  const tabMapP = {}
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i]
    tabMapP[item.id] = recursion(item.children)
  }
  tabMap.value = tabMapP
}
const recursion = (arr, obj = {}) => {
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i]
    if (item.bill_type >= 0) {
      obj[item.bill_type] = item.bill_info_comment ? item.bill_info_comment : item.bill_info_alias
    }
    if (item.children && item.children.length) {
      recursion(item.children, obj)
    }
  }
  return obj
}
const getTreeNew = async() => {
  const { data, error } = await getTreeNewApi({
    lang: locale.value
  })
  if (data) {
    deepMap(data)
  }
}
onMounted(() => {
  getTreeNew()
  getBill()
})
</script>