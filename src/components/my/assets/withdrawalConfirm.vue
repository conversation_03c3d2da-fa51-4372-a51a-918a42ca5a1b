<template>
  <el-dialog v-model="isShowDialog" :title="$t('安全验证')" width="480" @close="emit('close')">
    <el-steps direction="vertical" :active="activeStep">
      <!-- <el-step>
        <template #description>
          <div class="step-content">
            <div class="step-label-text">{{ $t('登录密码') }}</div>
            <el-input v-model="formData.loginPassword" :type="showPwd ? 'text' : 'password'" :placeholder="$t('请输入登录密码')">
              <template #suffix>
                <div v-if="formData.loginPassword !== ''" class="flex-box space-end eye-icon" @click="showPwd = !showPwd">
                  <MonoEyeClose v-if="!showPwd" />
                  <MonoEyeOpen v-if="showPwd" />
                </div>
              </template>
            </el-input>
          </div>
        </template>
      </el-step> -->
      <el-step>
        <template #description>
          <div class="step-content">
            <div class="step-label-text">{{ $t('邮箱验证码') }}</div>
            <el-input type="text" maxlength="6" v-model="formData.emailCode" :placeholder="$t('请在此输入您接收到的邮箱验证码')">
              <template #append>
                <div class="yz-container">
                  <span class="small-btn" :class="{'disabled':isPending}" @click="sendCodeFun">{{ isPending ? `${left}S ${$t('重新发送')}` : $t('发送验证码') }}</span>
                </div>
              </template>
            </el-input>
          </div>
        </template>
      </el-step>
      <el-step>
        <template #description>
          <div class="step-content">
            <div class="step-label-text">{{ $t('谷歌验证码') }}</div>
            <GoogleCodeInputMin v-model="formData.code" :disabled="activeStep !== 1" :defaultFocus="false" @focus="emit('focus', formData.code)" />
          </div>
        </template>
      </el-step>
    </el-steps>
    <el-button type="primary" :disabled="isDisabled" @click="confirmFun">{{ $t('确认') }}</el-button>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElDialog, ElSteps, ElStep, ElInput } from 'element-plus'
  import GoogleCodeInputMin from '~/components/common/GoogleCodeInput.vue'
  import { useCommonData } from '~/composables/index'
  import Verify from '~/utils/verify'
  import { emailConfirmAsk } from '~/api/user.ts'
  import MonoEyeOpen from '~/components/common/icon-svg/MonoEyeOpen.vue'
  import MonoEyeClose from '~/components/common/icon-svg/MonoEyeClose.vue'
  const { locale } = useI18n()
  const useCommon = useCommonData()
  const props = defineProps({
    dialogVisible: {
      type: Boolean,
      default: false
    }
  })
  const emit = defineEmits(['focus', 'close'])
  const isShowDialog = ref(false)
  const showPwd = ref(false)
  const formData = ref({
    loginPassword: '',
    emailCode: '',
    code: ''
  })
  // const activeStep = computed(() => {
  //   let number = 0
  //   if (formData.value.loginPassword !== '' && (formData.value.emailCode === '' ||  formData.value.emailCode.length < 6) ) {
  //     number = 1
  //   } else if (formData.value.loginPassword !== '' && formData.value.emailCode.length === 6) {
  //     number = 2
  //   }
  //   console.info(number, 'numbernumber')
  //   return number
  // })
  const activeStep = computed(() => {
    let number = 0
    if (formData.value.emailCode.length === 6) {
      number = 1
    }
    console.info(number, 'numbernumber')
    return number
  })
  const isPending = ref(false)
  const left = ref(60)
  const setIntervalFun = () => { // 验证码倒计时
    let timer = setInterval(() => {
      if (left.value > 0) {
        left.value--
      } else {
        left.value = 60
        isPending.value = false
        clearInterval(timer)
        timer = null
      }
    }, 1000)
  }
  const verifyEmailCode = ref(null)
  const sendCodeFun = () => {
    if (isPending.value) {
      return false
    }
    verifyEmailCode.value.verify()
  }
  const getCodeVerify = async(err, res) => {
    if (err) {
      return
    }
    if (res.type === 'success' || res.type === 'resend') {
      const params = {
        ...res.param,
        lang: locale.value
      }
      const { data, error } = await emailConfirmAsk(params)
      if (data) {
        isPending.value = true
        setIntervalFun()
      } else {
        useCommon.showMsg('error', useCommon.err(error.code, error))
      }
    }
  }
  const isDisabled = computed(() => {
    // formData.value.loginPassword !== '' && 
    if (formData.value.emailCode.length === 6 && formData.value.code.length === 6) {
      return false
    } else {
      return true
    }
  })
  const confirmFun = () => {
    emit('confirm', formData.value)
  }
  onMounted(() => {
    isShowDialog.value = props.dialogVisible
    verifyEmailCode.value = new Verify(getCodeVerify)
  })
</script>
<style lang="scss" scoped>
.step-label-text{
  font-size:14px;
  padding-bottom:8px;
  margin-top:-8px;
  @include color(tc-primary);
}
.yz-container{
  cursor:pointer;
  @include color(theme);
  &.disabled{
    opacity:0.5;
    cursor:not-allowed;
  }
}
</style>