<template>
  <div class="ordersCont">
    <OrderstableBox :isLoading="isLoading" :mSortList="mSortList" :headers="headersList" :list="currentList">
      <template #createdAt="scope">
        <span class="fit-tc-secondary" style="text-align:left;">{{ timeFormat(scope.data.createdAt, 'yyyy-MM-dd hh:mm:ss') }}</span>
      </template>
      <template #symbol="scope">
        <span>{{ scope.data.symbol === 'KtxQ1' ? t('体验金') : (scope.data.symbol === 'KtxQ2' ? t('抵扣金') : scope.data.symbol) }}</span>
      </template>
      <template #change_amount="scope">
        <span :class="{'fit-rise': scope.data.change_amount * 1 >= 0, 'fit-fall': scope.data.change_amount * 1 < 0}">{{ format(scope.data.change_amount, 10, true, true) }}</span>
      </template>
      <template #F="scope">
        <span>{{ Number(scope.data.quantity) < 0 ? (Number(scope.data.price) * -Number(scope.data.quantity)) : (Number(scope.data.price) * Number(scope.data.quantity)) }}</span>
      </template>
      <template #result="scope">
        <span>{{ format(scope.data.result, 10, true, true) }}</span>
      </template>
      <template #comment="scope">
        <span class="fit-tc-secondary">{{ tabMap[tabIndex][scope.data.bill_type] || $t(scope.data.comment) }}</span>
      </template>
    </OrderstableBox>
  </div>
</template>

<script lang="ts" setup>
  import BoxNoData from '~/components/common/BoxNoData.vue'
  import NoLoginCont from '~/components/common/NoLoginCont.vue'
  import OrderstableBox from '~/components/common/OrderstableBox.vue'
  import { timeFormat, format } from '~/utils'
  import { ORDER_STATUS_MAP } from '~/config'
  import { useCommonData } from '~/composables/index'
  import { deleteDetail } from '~/api/order'
  const useCommon = useCommonData()
  const { locale, t } = useI18n()
  const props = defineProps({
    isLoading: {
      type: Boolean,
      default: false
    },
    currentList: {
      type: Object,
      default(){
        return []
      }
    },
    pair: {
      type: String,
      default: ''
    },
    isLogin: {
      type: Boolean,
      default: false
    },
    tabMap: {
      type: Object,
      default () {
        return {}
      }
    },
    tabIndex: {
      type: [String, Number],
      default: ''
    }
  })
  const emit = defineEmits(['getOrderList'])
  const mSortList = ref([
    { text: t('时间'), key: 'createdAt', align: 'left', wid: '',style: 'auto' },
    { text: t('币种'), key: 'symbol', align: 'left', wid: 'flex-2',style: 'auto' },
    { text: t('数量'), key: 'change_amount', align: 'left', wid: 'flex-2',style: 'auto' },
    { text: t('账户余额'), key: 'result', align: 'left', wid: 'flex-2',style: 'auto' },
    { text: t('类型'), key: 'comment', align: 'right', wid: '',style: 'auto' }
  ])
  const headersList = ref([
    { text: t('时间'), key: 'createdAt', align: 'left', wid: '',style: 'auto' },
    { text: t('币种'), key: 'symbol', align: 'left', wid: 'flex-2',style: 'auto' },
    { text: t('数量'), key: 'change_amount', align: 'left', wid: 'flex-2',style: 'auto' },
    { text: t('账户余额'), key: 'result', align: 'left', wid: 'flex-2',style: 'auto' },
    { text: t('类型'), key: 'comment', align: 'right', wid: 'flex-2',style: 'auto' }
  ])
  const cancelOrder = async(row) => {
    console.info(row.i, 'dhueuehuduheuheuuhe')
    const { data, error } = await deleteDetail({
       id: row.i
    })
    if (data) {
      emit('getOrderList')
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
</script>

<style scoped lang="scss">
  .ordersCont{
    height:100%;
    overflow:auto;
  }
  @include mb {
    .ordersCont{
      padding:0 20px;
    }
  }
</style>