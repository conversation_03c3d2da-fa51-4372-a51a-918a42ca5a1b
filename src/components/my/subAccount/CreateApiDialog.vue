<template>
  <el-dialog v-model="isShowDialog" :title="$t('创建API')" width="480" @close="emit('close')">
    <el-form ref="addFormRef" :model="addForm" :rules="addFormRules">
      <el-form-item :label="$t('设置API的备注名称')" prop="name">
        <el-input v-model="addForm.name" :placeholder="$t('请输入备注名称')" />
        <p>{{ $t('* 设置API名称后便于您快速查找相应API信息') }}</p>
      </el-form-item>
      <el-radio-group v-model="ipType" class="batch-radio">
        <el-radio :value="0">
          {{ $t('授权信任的 IP 地址') }}
        </el-radio>
        <el-radio :value="1">
          {{ $t('不进行 IP 限制') }}
        </el-radio>
      </el-radio-group>
      <el-form-item v-if="ipType * 1 === 0" prop="ids">
        <div class="mg-t8">
          <el-input v-model="addForm.ids" :placeholder="$t('请在此输入IP 地址')" />
          <p>{{ $t('在框中输入信任的IP地址，如果有多个IP地址，请用逗号分隔') }}</p>
        </div>
      </el-form-item>
      <el-form-item :label="$t('谷歌验证码')" prop="code">
        <GoogleCodeInput v-model="addForm.code" :defaultFocus="false" @focus="emit('focus', addForm.code)" />
      </el-form-item>
    </el-form>
    <el-button :disabled="isDisabled" type="primary" @click="confirm">{{ $t('确认') }}</el-button>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElDialog, ElForm, ElFormItem, ElInput, ElRadioGroup, ElRadio } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import GoogleCodeInput from '~/components/common/GoogleCodeInput.vue'
  const { locale, t } = useI18n()
  const props = defineProps({
    dialogVisible: {
      type: Boolean,
      default: false
    },
    isMain: {
      type: Boolean,
      default: false
    }
  })
  interface AddForm{
    name: String,
    ids: String,
    code: String
  }
  const addFormRef = ref<FormInstance>()
  const addForm = reactive<AddForm>({
    name: '',
    ids: '',
    code: ''
  })
  const ipType = ref(0)
  const validatorIds = (rule, value, callback) => {
    // IPv4正则（允许0-255）
    const regIPv4 = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    // IPv6正则（支持完整和缩写格式）
    const regIPv6 = /^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::([0-9a-fA-F]{1,4}:){0,6}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:))$/;
    // 处理输入：替换中文逗号，分割并去除空格
    const ips = value.replace(/，/g, ',').split(',').map(ip => ip.trim());
    // 检查每个IP是否匹配IPv4或IPv6
    const allValid = ips.every(ip => regIPv4.test(ip) || regIPv6.test(ip));
    console.log(allValid, 'allValid111111111')
    if (!allValid) {
      return callback(new Error(t('IP地址输入有误')));
    } else {
      return callback();
    }
  }
  const addFormRules = reactive<FormRules<AddForm>>({
    name: [
      { required: true, message: t('请输入备注名称'), trigger: ['blur', 'change'] }
    ],
    code: [
      { required: true, message: t('请输入谷歌验证码'), trigger: ['blur', 'change'] }
    ],
    ids: [
      { required: true, message: t('请输入IP地址'), trigger: ['blur', 'change'] },
      { validator: validatorIds, trigger: 'change' }
    ]
  })
  const isDisabled = computed(() => {
    if (addForm.name !== '' && addForm.code !== '' && addForm.code.length === 6 && ((ipType.value * 1 === 0 && addForm.ids !== '') || (ipType.value * 1 === 1 && addForm.ids === ''))) {
      return false
    }
    return true
  })
  const emit = defineEmits(['close', 'confirm'])
  const isShowDialog = ref(false)
  const confirm = () => {
    emit('confirm', addForm)
  }
  onMounted(() => {
    isShowDialog.value = props.dialogVisible
  })
</script>
<style lang="scss" scoped>
  .el-dialog{
    p{
      font-size:14px;
      @include color(tc-secondary);
    }
  }
</style>