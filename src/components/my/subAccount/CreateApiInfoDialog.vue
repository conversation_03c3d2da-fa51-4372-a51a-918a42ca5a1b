<template>
  <el-dialog v-model="visible" width="480" @close="emit('close')">
    <div class="fit-tc-primary">
      <div class="ts-14 tw-5">
      Access Key
    </div>
    <div class="flex-box space-between align-start font-size-14">
      {{ createdData.api_key }}
      <MonoCopy :size="18" class="cursor-pointer" @click="useCommon.copy(createdData.api_key, `Access Key ${$t('复制成功！')}`)" />
    </div>
    <div class="font-size-14 tw-5 mg-t24">
      Secret Key
    </div>
    <div class="flex-box space-between align-start font-size-14">
      {{ createdData.api_secret }}
      <MonoCopy  :size="18" class="cursor-pointer" @click="useCommon.copy(createdData.api_secret, `Secret Key ${$t('复制成功！')}`)" />
    </div>
    <div class="font-size-12 fit-warn">
      {{ $t('(仅显示 1 次, 遗失后不可找回, 请务必妥善保存)') }}
    </div>
    <div class="mg-t24 font-size-12 tw-5 fit-tc-secondary">
      {{ $t('提示') }}
    </div>
    <div class="font-size-12 fit-tc-secondary">
      {{ $t('请不要泄露您的 Secret Key, 以免造成资产损失') }}
    </div>
    <div class="font-size-12 fit-tc-secondary">
      {{ $t('如您忘记了 Secret Key, 请回收该密钥对并申请新的密钥对') }}
    </div>
      <el-button type="primary" class="mg-t24" @click="emit('close')">{{ $t('确认') }}</el-button>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElDialog, ElButton } from 'element-plus'
  import MonoCopy from '~/components/common/icon-svg/MonoCopy.vue'
  import { useCommonData } from '~/composables/index'
  const useCommon = useCommonData()
  const emit = defineEmits(['close'])
  const props = defineProps({
    dialogVisible: {
      type: Boolean,
      default: false
    },
    createdData: {
      type: Object,
      default () {
        return {}
      }
    }
  })
  const visible = ref(false)
  onMounted(() => {
    visible.value = props.dialogVisible
  })
</script>
<style lang="scss">
</style>