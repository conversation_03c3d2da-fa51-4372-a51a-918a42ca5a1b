<template>
  <el-dialog class="sub-account-dialog" v-model="visible" :title="isAuthorize ? $t('授权子账号信息') : $t('新建快捷子账号')" width="480px" @close="emit('close')">
    <CreateView :isAuthorize="isAuthorize" :len="len" :currentItem="currentItem" @close="emit('close')" @request="emit('request')" />
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElDialog } from 'element-plus'
  import CreateView from '~/components/my/subAccount/createView.vue'
  const props = defineProps({
    isShow: {
      type: Boolean,
      default: false
    },
    isAuthorize: {
      type: Boolean,
      default: false
    },
    len: {
      type: Number,
      default: 0
    },
    currentItem: {
      type: Object,
      default () {
        return {}
      }
    }
  })
  const emit = defineEmits(['close'])
  const visible = ref(false)
  onMounted(() => {
    visible.value = props.isShow
  })
</script>
<style lang="scss">
  .el-dialog{
    &.sub-account-dialog{
      padding:0 !important;
      .son-tips, .son-tips-ul{
        margin-top:-12px;
        font-size:14px;
        padding-bottom:20px;
        @include color(tc-secondary);
        li{
          padding-left:16px;
          padding-bottom:8px;
          position:relative;
          &:after{
            content:'';
            display:block;
            position:absolute;
            top:8px;
            left:2px;
            width:4px;
            height:4px;
            border-radius:50%;
            @include bg-color(tc-secondary);
          }
        }
      }
      .text-info{
        width:100%;
        height:44px;
        line-height:44px;
        border-radius:4px;
        border:1px solid;
        font-size:14px;
        padding:0 12px;
        @include color(tc-primary);
        @include border-color(border);
      }
      .info-tips{
        font-size:14px;
        @include color(tc-secondary);
        span{
          margin-right:4px;
          cursor:pointer;
          text-decoration: underline;
        }
      }
    }
  }
  @include mb{
    .el-dialog{
      &.sub-account-dialog{
        width:100% !important;
        position:fixed;
        top:0;
        left:0;
        right:0;
        bottom:0;
        margin:0 !important;
        padding:0 !important;
        .el-dialog__header{
          padding:24px 16px;
        }
        .son-tips, .son-tips-ul{
          margin-top:0;
        }
        .text-info{
          width:100%;
          height:44px;
          line-height:44px;
          border-radius:4px;
          border:1px solid;
          font-size:14px;
          padding:0 12px;
          @include color(tc-primary);
          @include border-color(border);
        }
        .info-tips{
          font-size:14px;
          @include color(tc-secondary);
          span{
            margin-right:4px;
            cursor:pointer;
            text-decoration: underline;
          }
        }
        .btn-box{
          position:fixed;
          bottom:0;
          left:0;
          right:0;
          padding:12px 16px;
          @include bg-color(bg-primary);
        }
      }
    }
  }
</style>