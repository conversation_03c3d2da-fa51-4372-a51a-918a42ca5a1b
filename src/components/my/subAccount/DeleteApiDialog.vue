<template>
  <el-dialog v-model="isShowDialog" :title="$t('删除API信息')" width="480" @close="emit('close')">
    <div class="delete-api-cont">
      <h3>{{ $t('确认要删除该API信息？') }}</h3>
      <p>Access Key</p>
      <p>{{ $t('显示API Key 信息') }}</p>
      <p>{{ data.api_key }}</p>
      <div class="warn">
        <p>{{ $t('安全提示：') }}</p>
        <p>{{ $t('请不要随意删除您的 Secret Key, 以免造成资产损失') }}</p>
      </div>
      <el-form>
        <el-form-item :label="$t('谷歌验证码')">
          <GoogleCodeInputMin v-model="code" :defaultFocus="false" @focus="emit('focus', code)" />
        </el-form-item>
      </el-form>
      <el-button type="primary" :disabled="isDisabled" @click="confirm">{{ $t('确定') }}</el-button>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElDialog, ElForm, ElFormItem } from 'element-plus'
  import GoogleCodeInputMin from '~/components/common/GoogleCodeInput.vue'
  const props = defineProps({
    dialogVisible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default () {
        return {}
      }
    },
    params: {
      type: Object,
      default () {
        return {}
      }
    }
  })
  const emit = defineEmits(['close', 'confirm'])
  const isShowDialog = ref(false)
  const code = ref('')
  const isDisabled = computed(() => {
    if (code.value !== '' && code.value.length === 6) {
      return false
    } else {
      return true
    }
  })
  const confirm = () => {
    emit('confirm', code.value)
  }
  onMounted(() => {
    isShowDialog.value = props.dialogVisible
  })
</script>
<style lang="scss" scoped>
.delete-api-cont{
  h3{
    font-size:16px;
    padding-bottom:18px;
    font-weight:500;
    @include color(tc-primary);
  }
  p{
    font-size:14px;
    @include color(tc-secondary);
  }
  .warn{
    padding-top:28px;
    padding-bottom:28px;
    p{
      @include color(warn);
    }
  }
}
</style>