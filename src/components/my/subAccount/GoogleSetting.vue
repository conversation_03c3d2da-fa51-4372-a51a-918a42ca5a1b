<template>
  <el-dialog v-model="isShowDialog" class="google-setting" :title="$t('绑定谷歌验证')" width="480" :show-close="showClose" @close="emit('close')" :close-on-click-modal="false" :close-on-press-escape="false" >
    <div class="google-download-box">
      <div class="download-title">{{ $t('下载安装') }}</div>
      <div class="flex-box download-btn">
        <a href="https://itunes.apple.com/cn/app/google-authenticator/id388497605?mt=8" target="_blank">
          <span class="icon apple"></span>
          <div class="text-box">
            <p>Apple store</p>
            {{ $t('下载安装') }}
          </div>
        </a>
        <a href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2" target="_blank">
          <span class="icon google"></span>
          <div class="text-box">
            <p>Google play</p>
            {{ $t('下载安装') }}
          </div>
        </a>
      </div>
    </div>
    <div v-loading="googleKeyLoading" class="google-bind-cont">
      <h3>1.{{ $t('打开 App 点击 “ + ” 然后扫描或者手动输入验证码') }}</h3>
      <div class="bind-info-cont">
        <dl class="flex-box flex-column">
          <dt>
            <BoxQrcode :size="90" :value="googleData.uri" />
          </dt>
          <dd class="flex-1 flex-box mg-t12">
            <div class="code-box">
              {{ googleData.secret }}
              <MonoCopy @click="useCommon.copy(googleData.secret, $t('密钥复制成功！'))" />
            </div>
          </dd>
        </dl>
      </div>
      <h3>2.{{ $t('请输入在您Google Authenticator中生成的6位验证码，完成绑定') }}</h3>
      <div class="mg-t8 mg-b20">
        <GoogleCodeInputMin v-model="code" :defaultFocus="false" @focus="emit('focus', code)" />
      </div>
    </div>
    <div class="flex-box btn-box">
      <el-button v-if="!showClose" class="flex-1" @click="emit('cancelDialog')">{{ $t('取消') }}</el-button>
      <el-button class="flex-1" type="primary" @click="confirmFun()">{{ $t('提交') }}</el-button>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElDialog } from 'element-plus'
  import { useCommonData } from '~/composables/index'
  import MonoCopy from '~/components/common/icon-svg/MonoCopy.vue'
  import GoogleCodeInputMin from '~/components/common/GoogleCodeInput.vue'
  import { activeSonGoolgKey } from '~/api/user'
  const { locale, t } = useI18n()
  const useCommon = useCommonData()
  const props = defineProps({
    dialogVisible: {
      type: Boolean,
      default: false
    },
    googleKeyLoading: {
      type: Boolean,
      default: false
    },
    showClose: {
      type: Boolean,
      default: true
    },
    currentItem: {
      type: Object,
      default(){
        return {}
      }
    },
    googleData: {
      type: Object,
      default(){
        return {}
      }
    }
  })
  const emit = defineEmits(['close', 'confirm', 'cancelDialog'])
  const isShowDialog = ref(false)
  const code = ref('')
  const confirmFun = async() => {
    if (!props.showClose) {
      emit('confirm', code.value)
    } else {
      const { data, error } = await activeSonGoolgKey({
        son_id: props.currentItem.user_id,
        code: code.value
      })
      if (data) {
        useCommon.showMsg('success', t('谷歌绑定成功'))
        emit('confirm')
        emit('close')
      } else {
        useCommon.showMsg('error', useCommon.err(error.code, error))
      }
    }
  }
  onMounted(() => {
    isShowDialog.value = props.dialogVisible
  })
</script>
<style lang="scss" scoped>
  .google-download-box{
    margin-top:-20px;
    .download-title{
      font-size:14px;
      padding:12px 0;
      @include color(tc-secondary);
    }
    .download-btn{
      a{
        display:block;
        flex:1;
        border-radius:4px;
        margin-left:24px;
        display:flex;
        align-items:center;
        justify-content: center;
        padding:12px 10px;
        @include bg-color(bg-tertiary);
        @include color(tc-primary);
        &:first-child{
          margin:0;
        }
        .icon{
          display:block;
          width:24px;
          height:24px;
          margin-right:10px;
          &.apple{
            background: url('@/assets/images/my/appStore-img.png') no-repeat;
            background-size: 100% auto;
          }
          &.google{
            background: url('@/assets/images/my/google-img.png') no-repeat;
            background-size: 100% auto;
          }
        }
      }
    }
  }
  .google-bind-cont{
    h3{
      font-size:14px;
      padding:12px 0;
      @include color(tc-secondary);
    }
    .bind-info-cont{
      dl{
        display:flex;
        width:100%;
        dt{
          padding:10px;
          border:1px solid;
          border-radius:6px;
          @include border-color(border);
        }
        dd{
          .code-box{
            font-size:14px;
            padding: 10px;
            border-radius:8px;
            @include bg-color(bg-tertiary);
          }
          svg{
            font-size: 14px !important;
            cursor:pointer;
            margin-left:8px;
          }
        }
      }
    }
  }
  @include mb{
    .google-download-box{
    margin-top:-20px;
    .download-title{
      font-size:14px;
      padding:12px 0;
      @include color(tc-secondary);
    }
    .download-btn{
      &.flex-box{
        display:block;
        a{
          margin-left:0;
          margin-top:12px;
          .text-box{
            display:flex;
            p{
              margin-right:4px;
            }
          }
        }
      }
    }
  }
  }
</style>
<style lang="scss">
  @include mb {
    .el-dialog{
      &.google-setting{
        width:100% !important;
        position:fixed;
        top:0;
        left:0;
        right:0;
        bottom:0;
        margin:0 !important;
        padding:0 !important;
      }
    }
  }
</style>