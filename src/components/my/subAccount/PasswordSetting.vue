<template>
  <el-dialog v-model="isShowDialog" :title="$t('重置登录密码')" @close="emit('close')" width="480">
    <el-form ref="setFormRef" :model="setForm" :rules="setFormRules">
      <el-form-item :label="$t('子账户')">
        {{ currentItem.name }}
      </el-form-item>
      <el-form-item :label="$t('新登录密码')" prop="pwd">
        <p class="fit-tc-secondary font-size-14 pd-b4">{{ $t('8-20个字符，需包含大小写字母、数字和特殊字符') }}</p>
        <el-input v-model="setForm.pwd" :type="isShowEye ? 'text' : 'password'">
          <template #suffix>
            <div class="flex-box space-end eye-icon" @click="isShowEye = !isShowEye">
              <MonoEyeClose v-if="!isShowEye" />
              <MonoEyeOpen v-if="isShowEye" />
            </div>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item :label="$t('确认密码')" prop="pwd_c">
        <el-input v-model="setForm.pwd_c" :type="isShowEye1 ? 'text' : 'password'">
          <template #suffix>
            <div class="flex-box space-end eye-icon" @click="isShowEye1 = !isShowEye1">
              <MonoEyeClose v-if="!isShowEye1" />
              <MonoEyeOpen v-if="isShowEye1" />
            </div>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item :label="$t('谷歌验证码')" prop="code">
        <GoogleCodeInput v-model="setForm.code" :defaultFocus="false" @focus="emit('focus', setForm.code)" />
      </el-form-item>
    </el-form>
    <el-button :disabled="isDisabled" type="primary" class="mg-t12" @click="confirmFun">{{ $t('确认') }}</el-button>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElDialog, ElForm, ElFormItem, ElInput } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import GoogleCodeInput from '~/components/common/GoogleCodeInput.vue'
  import MonoEyeOpen from '~/components/common/icon-svg/MonoEyeOpen.vue'
  import MonoEyeClose from '~/components/common/icon-svg/MonoEyeClose.vue'
  import { isValidSubAccountPwd } from '~/utils/index'
  import { useCommonData } from '~/composables/index'
  import { sonChangeLoginPwd } from '~/api/user'
  const useCommon = useCommonData()
  const { locale, t } = useI18n()
  const props = defineProps({
    dialogVisible: {
      type: Boolean,
      default: false
    },
    currentItem: {
      type: Object,
      default(){
        return {}
      }
    }
  })
  const emit = defineEmits(['close', 'confirm'])
  const isShowDialog = ref(false)
  interface SetForm{
    pwd: String,
    pwd_c: String,
    code: String
  }
  const setFormRef = ref<FormInstance>()
  const setForm = reactive<SetForm>({
    pwd: '',
    pwd_c: '',
    code: ''
  })
  const validatorPwd = (rule, value, callback) => {
    /* eslint no-lonely-if: "error" */
    if (setForm.pwd_c) {
      if (!value) {
        return callback(new Error(t('请输入确认密码')))
      } else if (value !== setForm.pwd_c) {
        return callback(new Error(t('两次输入的密码不一致')))
      } else {
        return callback()
      }
    } else if (setForm.pwd_c === '') {
      if (!value) {
        return callback(new Error(t('请输入密码')))
      } else if (!isValidSubAccountPwd(value)) {
        return callback(new Error(t('请输入正确的密码格式')))
      } else {
        return callback()
      }
    }
  }
  const validatorPwdConfirm = (rule, value, callback) => {
    if (setForm.pwd) {
      if (!value) {
        return callback(new Error(t('请输入确认密码')))
      } else if (value !== setForm.pwd) {
        return callback(new Error(t('两次输入的密码不一致')))
      } else {
        return callback()
      }
    } else if (setForm.pwd === '') {
      if (!value) {
        return callback(new Error(t('请输入密码')))
      } else if (!isValidSubAccountPwd(value)) {
        return callback(new Error(t('请输入正确的密码格式')))
      } else {
        return callback()
      }
    }
  }
  const setFormRules = reactive<FormRules<SetForm>>({
    pwd: [
      { required: true, message: t('请输入新密码'), trigger: ['blur', 'change'] },
      { validator: validatorPwd, trigger: ['blur', 'change'] }
    ],
    pwd_c: [
      { required: true, message: t('请输入确认密码'), trigger: ['blur', 'change'] },
      { validator: validatorPwdConfirm, trigger: ['blur', 'change'] }
    ],
    code: [
      { required: true, message: t('请输入谷歌验证码'), trigger: ['blur', 'change'] }
    ]
  })
  const isShowEye = ref(false)
  const isShowEye1 = ref(false)
  const isDisabled = computed(() => {
    if (setForm.pwd !== '' && setForm.pwd_c !== '' && setForm.code !== '' && isValidSubAccountPwd(setForm.pwd) && isValidPwd(setForm.pwd_c) && setForm.pwd === setForm.pwd_c && setForm.code.length === 6) {
      return false
    }
    return true
  })
  const confirmFun = async() => {
    const { data, error } = await sonChangeLoginPwd({
      son_id: props.currentItem.user_id,
      ... setForm
    })
    if ( data) {
      useCommon.showMsg('success', t('密码重置成功！'))
      emit('confirm')
      emit('close')
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  onMounted(() => {
    isShowDialog.value = props.dialogVisible
  })
</script>
<style lang="scss" scoped>
</style>