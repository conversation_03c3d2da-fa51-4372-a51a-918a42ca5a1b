<template>
  <el-dialog v-model="visible" :title="`${currentItem.name || currentItem.desc || currentItem.user_id} ${$t('资产详情')}`" class="sub-assets-dialog" width="480px" @close="emit('close')">
    <div class="assets-cont">
      <ul class="assets-tab flex-box">
        <li :class="{'active': curTab === 1}" @click="curTab = 1">{{ $t('钱包账户') }}</li>
        <li :class="{'active': curTab === 2}" @click="curTab = 2">{{ $t('交易账户') }}</li>
      </ul>
      <div v-if="JSON.stringify(allAsset) !== '{}'" class="assets-wrap">
        <div v-if="curTab === 1" class="assets-info flex-box space-between">
          <div class="info-title flex-box">
            <span class="font-size-16">{{ $t('钱包账户') }}</span>
            <MonoEyeOpen v-if="!isHideAssets" @click="setHideAssets" />
            <MonoEyeClose v-if="isHideAssets" @click="setHideAssets" />
          </div>
          <div class="flex-box flex-column align-end">
            <div class="info-text">
              <span>{{ useCommon.hideAssets(curAssetItem.main.eqbtc) }}</span>
              <em>BTC</em>
            </div>
            <div class="info-small-text"> ≈{{ useCommon.hideAssets(curAssetItem.main.equsdt) }} USDT</div>
          </div>
        </div>
        <div v-if="curTab === 2" class="assets-info flex-box space-between">
          <div class="info-title flex-box">
            <span class="font-size-16">{{ $t('交易账户') }}</span>
            <MonoEyeOpen v-if="!isHideAssets" @click="setHideAssets" />
            <MonoEyeClose v-if="isHideAssets" @click="setHideAssets" />
          </div>
          <div class="flex-box flex-column align-end">
            <div class="info-text">
              <span>{{ useCommon.hideAssets(curAssetItem.trade.eqbtc) }}</span>
              <em>BTC</em>
            </div>
            <div class="info-small-text"> ≈{{ useCommon.hideAssets(curAssetItem.trade.equsdt) }} USDT</div>
          </div>
        </div>
        <div class="table-body">
          <el-table :data="currentList" class="table-normal">
            <template #empty>
              <div style="height:300px;">
                <BoxNoData :text="$t('暂无数据')" />
              </div>
            </template>
            <el-table-column :label="$t('名称')" prop="coin_symbol">
              <template #default="scope">
                <div class="symbol-box flex-box">
                  <BoxCoinIcon :icon="scope.row.icon_url" class="font-size-24 iconImg mg-r12" />
                  <div class="symbolTxt">
                    <h3>{{ scope.row.asset }}</h3>
                    <p>{{ scope.row.symbolName }}</p>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column align="right" :label="$t('数量')" prop="amount">
              <template #default="scope">
                <p>{{ useCommon.hideAssets(scope.row.total) }}</p>
                <p>≈{{ useCommon.hideAssets(scope.row.equsdt) }}USDT</p>
              </template>
            </el-table-column>
            <el-table-column align="right" v-if="!isMobile" :label="$t('可用')" prop="balance">
              <template #default="scope">
                {{ useCommon.hideAssets(scope.row.balance) }}
              </template>
            </el-table-column>
            <el-table-column align="right" v-if="!isMobile" :label="$t('冻结')" prop="holds">
              <template #default="scope">
                {{ useCommon.hideAssets(scope.row.holds) }}
              </template>
            </el-table-column>
            <el-table-column align="right" v-if="isMobile" :label="`${$t('可用')}/${$t('冻结')}`">
              <template #default="scope">
                <p>{{ useCommon.hideAssets(scope.row.balance) }}</p>
                <p>{{ useCommon.hideAssets(scope.row.holds) }}</p>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElDialog, ElButton, ElTable, ElTableColumn } from 'element-plus'
  import { useCommonData } from '~/composables/index'
  import { commonStore } from '~/stores/commonStore'
  import MonoEyeOpen from '~/components/common/icon-svg/MonoEyeOpen.vue'
  import MonoEyeClose from '~/components/common/icon-svg/MonoEyeClose.vue'
  const store = commonStore()
  const useCommon = useCommonData()
  const { setHideAssets } = store
  const { isHideAssets } = storeToRefs(store)
  const props = defineProps({
    isShow: {
      type: Boolean,
      default: false
    },
    currentItem: {
      type: Object,
      default () {
        return {}
      }
    },
    curAssetItem: {
      type: Object,
      default () {
        return {}
      }
    }
  })
  const isMobile = ref(false)
  const screenWidth = ref(0)
  const emit = defineEmits(['close'])
  const visible = ref(false)
  const curTab = ref(1)
  const currentList = computed(() => {
    console.info(props.curAssetItem, 'dhdhdueudheuh')
    if (curTab.value === 1) {
      return Object.values(props.curAssetItem.mainAssets) || []
    } else {
      return Object.values(props.curAssetItem.tradeAssets) || []
    }
  })
  const updateScreenWidth = () => {
    screenWidth.value = window.innerWidth
    isMobile.value = screenWidth.value <= 768
  }
  onMounted(async() => {
    updateScreenWidth()
    visible.value = props.isShow
  })
</script>
<style lang="scss">
.el-dialog{
  &.sub-assets-dialog{
    .assets-cont{
      .assets-tab{
        height:40px;
        border-bottom:1px solid;
        @include border-color(border);
        li{
          cursor:pointer;
          flex:1;
          margin-right:16px;
          font-size:14px;
          height:40px;
          line-height:40px;
          text-align:center;
          @include color(tc-secondary);
          &.active{
            position:relative;
            @include color(tc-primary);
            &:after{
              content: '';
              display:block;
              position:absolute;
              bottom:0;
              left:50%;
              margin-left:-7px;
              height:2px;
              width:14px;
              @include bg-color(theme);
            }
          }
        }
      }
      .assets-wrap{
        padding: 20px 0;
        .assets-info{
          font-size:14px;
          @include color(tc-primary);
          .info-title{
            svg{
              cursor:pointer;
              font-size:14px !important;
              margin-left:8px;
              @include color(tc-secondary);
            }
          }
        }
      }
      .table-body{
        margin-top:20px;
        margin-left:-12px;
        margin-right:-12px;
        border-top:1px solid;
        @include border-color(border);
        .el-table{
           @include bg-color(bg-secondary);
        }
        .el-table th.el-table__cell, .el-table td.el-table__cell{
          @include bg-color(bg-secondary);
        }
      }
    }
  }
}
</style>