<template>
  <OrderstableBox :isLoading="isLoading" :mSortList="mSortList" :headers="headersList" :list="currentList">
    <template #createdAt="scope">
      {{ timeFormat(scope.data.createdAt, 'yyyy-MM-dd hh:mm:ss') }}
    </template>
    <template #symbol="scope">
      <div class="flex-box space-start" style="text-align:left">
        {{ scope.data.name }}({{ scope.data.symbol }})
      </div>
    </template>
    <template #chain_type="scope">
      {{ scope.data.chain_type }}
    </template>
    <template #tx_id="scope">
      <div class="flex-box space-between" style="width:100%;">
        <el-tooltip placement="top">
          <template #content>
            <div style="width:350px">{{ scope.data.tx_id }}</div>
          </template>
          <a :href="scope.data.scan_url" target="_blank" class="cursor-pointer fit-tc-primary" style="text-decoration: underline;">
            {{ codeLinkFormat(scope.data.tx_id) }}
          </a>
        </el-tooltip>
        <div class="flex-box">
          <a :href="scope.data.scan_url" target="_blank" class="fit-tc-primary">
            <MonoLinkUrl size="16" style="cursor:pointer;" class="fit-tc-primary mg-r12 mg-l8 mg-t4" />
          </a>
          <MonoCopy size="16" style="cursor:pointer;" class="fit-tc-primary" @click="useCommon.copy(scope.data.tx_id, $t('复制成功！'))" />
        </div>
      </div>
    </template>
    <template #status="scope">
      <span class="fit-rise">{{ $t('成功') }}</span>
    </template>
  </OrderstableBox>
</template>
<script lang="ts" setup>
  import { ElTooltip } from 'element-plus'
  import { timeFormat } from '~/utils'
  import OrderstableBox from '~/components/common/OrderstableBox.vue'
  import MonoCopy from '~/components/common/icon-svg/MonoCopy.vue'
  import MonoLinkUrl from '~/components/common/icon-svg/MonoLinkUrl.vue'
  import { getNftDepositList } from '~/api/tf.ts'
  import { useCommonData } from '~/composables/index'
  const useCommon = useCommonData()
  const { locale, t } = useI18n()
  const isLoading = ref(true)
  const mSortList = ref([
    { text: t('时间'), key: 'createdAt', align: 'left', wid: 'flex-2', style: 'auto' },
    { text: t('状态'), key: 'status', align: '', wid: '', style: 'auto' },
    { text: t('名称'), key: 'symbol', align: '', wid: '', style: 'auto' },
    { text: t('网络'), key: 'chain_type', align: '', wid: '', style: 'auto' },
    { text: t('交易ID'), key: 'tx_id', align: '', wid: 'flex-2', style: 'auto' }
  ])
  const headersList = ref([
    { text: t('名称'), key: 'symbol', align: 'left', wid: '', style: 'auto' },
    { text: t('网络'), key: 'chain_type', align: '', wid: '', style: 'auto' },
    { text: t('交易ID'), key: 'tx_id', align: 'left', wid: 'flex-2', style: 'auto' },
    { text: t('状态'), key: 'status', align: '', wid: '', style: 'auto' },
    { text: t('时间'), key: 'createdAt', align: 'right', wid: '', style: 'auto' },
  ])
  const currentList = ref([])
  const pager = ref({
    page: 1,
    size: 100
  })
  const total = ref(0)
  const getDepositList = async() => {
    isLoading.value = true
    const { data } = await getNftDepositList(pager.value)
    if (data) {
      total.value = data.count
      currentList.value = data.rows
    }
    isLoading.value = false
  }
  const codeLinkFormat = (str) => {
    return `${str.slice(0, 12)}......${str.slice(-12)}`
  }
  onBeforeMount(() => {
    getDepositList()
  })
</script>
<style lang="scss" scoped>
</style>