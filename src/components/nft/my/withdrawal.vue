<template>
  <div class="nft-deposit-withdrawal-cont">
    <div class="nft-deposit-withdrawal-wrap">
      <div class="nft-deposit-withdrawal-title">
        {{ $t('提现NFT') }}
      </div>
      <div class="nft-deposit-withdrawal-box">
        <el-steps direction="vertical" :active="activeStep">
          <el-step :title="$t('提现地址')">
            <template #description>
              <el-input>
                <template #append>
                  <span class="fit-theme cursor-pointer">{{ $t('粘贴') }}</span>
                </template>
              </el-input>
            </template>
          </el-step>
          <el-step :title="$t('提现NFT')">
            <template #description>
              <div v-if="nftList.length > 0" class="nft-check-box-container heiAuto">
                <el-checkbox-group v-model="checkedList" class="flex-box space-start" @change="handleChange">
                  <div v-for="(item, index) in nftList" :key="index" class="check-item">
                    <div class="check-item-pd">
                      <div class="check-item-title text-center">
                        <p>{{ item.name }}</p>
                        <p>#{{ item.sequence }}#</p>
                      </div>
                      <div class="check-item-img">
                        <img :src="item.icon_url" />
                      </div>
                      <el-checkbox :value="item.id" :key="item.id" :disabled="item.status * 1 === 2">{{ $t('选择') }}</el-checkbox>
                    </div>
                  </div>
                </el-checkbox-group>
              </div>
              <div class="box-left-h5">
                <p class="fit-tc-primary font-size-14 pd-t8">
                  <span class="fit-tc-secondary">{{ $t('提现手续费：') }}</span>
                  89729
                </p>
                <p class="font-size-14 fit-warn pd-t8">{{ $t('*请确保您绑定的钱包中有足够的资金来支付燃料费。') }}</p>
                <el-button type="primary" class="tx-btn" @click="isShowConfirm = true">{{ $t('提现') }}</el-button>
              </div>
            </template>
          </el-step>
        </el-steps>
      </div>
      <div class="nft-deposit-withdrawal-title">
        {{ $t('最近提现记录') }}
        <NuxtLink :to="`/${locale}/nft/my/assets/withdrawal-list`">{{ $t('查看更多') }}</NuxtLink>
      </div>
      <div class="nft-deposit-withdrawal-list">
        <WithdrawalList />
      </div>
    </div>
    <el-dialog v-model="isShowInfo" :title="$t('提现NFT确认')" width="480" :close-on-click-modal="false" @close="isShowInfo = false">
      <div class="withdrawal-detail">
        <dl class="flex-box space-between font-size-14 mg-b8">
          <dt class="fit-tc-secondary">{{ $t('网络') }}</dt>
          <dd class="fit-tc-primary">SOL</dd>
        </dl>
        <dl class="flex-box space-between font-size-14 mg-b8">
          <dt class="fit-tc-secondary">{{ $t('提现NFT集合名称') }}</dt>
          <dd class="fit-tc-primary">SOL</dd>
        </dl>
        <dl class="flex-box space-between font-size-14 mg-b8">
          <dt class="fit-tc-secondary">{{ $t('目标地址') }}</dt>
          <dd class="fit-tc-primary">SOL</dd>
        </dl>
        <dl class="flex-box space-between font-size-14 mg-b8">
          <dt class="fit-tc-secondary">{{ $t('提现手续费') }}</dt>
          <dd class="fit-tc-primary">SOL</dd>
        </dl>
        <el-button type="primary" class="mg-t36">{{ $t('确认') }}</el-button>
      </div>
    </el-dialog>
    <withdrawalConfirm
      v-if="isShowConfirm"
      :dialogVisible="isShowConfirm"
      @close="isShowConfirm = false"
      @confirm="transferoutFun"
    />
  </div>
  <BoxScript id="gt" ref="gt" append-to-body src="https://res.ktx.com/common/js/gt4.js" />
</template>
<script lang="ts" setup>
  import { ElSteps, ElStep, ElButton, ElInput, ElDialog } from 'element-plus'
  import WithdrawalList from '~/components/nft/my/withdrawal-list'
  import CoinListSelect from '~/components/common/CoinListSelect.vue'
  import MonoCopy from '~/components/common/icon-svg/MonoCopy.vue'
  import withdrawalConfirm from '~/components/my/assets/withdrawalConfirm.vue'
  import { getNftAddrApi } from '~/api/tf'
  import { useCommonData } from '~/composables/index'
  const { locale, t } = useI18n()
  const useCommon = useCommonData()
  const search = ref({
    symbol: ''
  })
  const nftAddressInfo = ref({})
  const activeStep = computed(() => {
    let number = 0
    if (search.value.symbol) {
      number = 1
    }
    return number
  })
  const nftList = ref([
    {
      name: 'tetste',
      sequence: 'd2234',
      icon_url: '',
      id: 1,
      status: '1'
    },
    {
      name: 'tetste',
      sequence: 'd2234',
      icon_url: '',
      id: 1,
      status: '1'
    },
    {
      name: 'tetste',
      sequence: 'd2234',
      icon_url: '',
      id: 1,
      status: '1'
    },
    {
      name: 'tetste',
      sequence: 'd2234',
      icon_url: '',
      id: 1,
      status: '1'
    },
    {
      name: 'tetste',
      sequence: 'd2234',
      icon_url: '',
      id: 1,
      status: '1'
    },
    {
      name: 'tetste',
      sequence: 'd2234',
      icon_url: '',
      id: 1,
      status: '1'
    },
    {
      name: 'tetste',
      sequence: 'd2234',
      icon_url: '',
      id: 1,
      status: '1'
    }
  ])
  const isShowConfirm = ref(false)
  const isShowInfo = ref(false)
  watch(() => search.value.symbol, (val) => {
    getNftAddress()
  })
  const getNftAddress = async() => {
    const { data } = await getNftAddrApi({
      nft_id: search.value.symbol
    })
    if (data) {
      nftAddressInfo.value = data
    }
  }
</script>
<style lang="scss" scoped>
  @import '@/assets/style/nft/assets.scss';
  @import '@/assets/style/nft/deposit-withdrawal.scss';
</style>