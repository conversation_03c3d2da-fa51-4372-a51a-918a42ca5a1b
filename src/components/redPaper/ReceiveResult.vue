<template>
  <div class="red-paper-result">
    <div class="result-header">
      <div class="flex-box space-between">
        <div class="receive-logo">
          <NuxtLink :to="`/${locale}`" class="logo-wrap">
            <img src="~/assets/images/common/logo.png" />
          </NuxtLink>
        </div>
        <div class="copyTxt" @click="useCommon.copy('', $t('复制成功'))">{{ $t('复制地址分享') }}</div>
      </div>
      <div class="mg-t32 font-size-20 tw-b color-y text-center flex-box space-center">
        <span class="receive-icon mg-r4">
          <BoxCoinIcon
            :icon="(redPaperDetail.myredpacket || {}).icon_url || (packetDetail.redpacketout || {}).icon_url"
            class="icon-content">
          </BoxCoinIcon>
        </span>
        {{ packetDetail.redpacketout.nickname }}
      </div>
      <div class="font-size-14 color-y blessing text-center mg-t4">
        {{ packetDetail.redpacketout.desc || '' }}
      </div>
      <div
        v-if="redPaperDetail.isEnd"
        class="mg-t12 text-center font-size-20 color-w tw-6">
        {{ $t('来晚一步, 红包都被领走了') }}
      </div>
      <div>
        <div class="pd-lr8 pd-tb8 discount-assets">
          <p class="color-y font-size-14 text-center">
            <span class="font-size-24 tw-6" style="line-height:1.33">{{ format((redPaperDetail.myredpacket || {}).amount, 4, true) }}</span> {{ (redPaperDetail.myredpacket || {}).coin_symbol || (packetDetail.redpacketout || {}).coin_symbol }}
          </p>
        </div>
        <div class="mg-t4 font-size-12 color-y text-center">
          {{!redPaperDetail.isNew ? $t('红包已到账, 请去“我的钱包”查看') : $t('红包已发放，请注册后并下载APP去“我的钱包”查看')}}
        </div>
      </div>
    </div>
    <div class="result-content">
      <div v-if="!redPaperDetail.isNew" class="result-list">
        <div class="list-title">
          {{ $t('领取') }} {{ packetDetail.redpacketout.out_number || '--' }} / {{ packetDetail.redpacketout.number || '--' }} {{ $t('个') }}
        </div>
        <ul>
          <li v-for="(item, key) in packetDetail.redpacketin"
            :key="key"
            class="red-paper_item flex-box space-between"
          >
            <div>
              <div class="fit-tc-primary font-size-18">
                {{ item.account }}
              </div>
              <div class="fit-tc-secondary font-size-14">
                {{ timeFormat(item.create_time, 'yyyy-MM-dd hh:mm:ss') }}
              </div>
            </div>
            <div class="fit-tc-primary font-size-18">
               {{ format(item.amount, 4, true) }} {{ item.coin_symbol }}
            </div>
          </li>
        </ul>
      </div>
      <div v-else>
        <div class="color-db2c49 text-center font-size-14 mg-b24">
          {{ $t('您当前账户尚未注册 Madex邮箱') }}
          <br />
          {{ $t('立即注册即可激活红包') }}
        </div>
        <div class="color-5c7287 font-size-12 text-center">
          {{ $t('当前邮箱') }}
        </div>
        <div class="font-size-16 tw-b color-18232e text-center mg-b8">
          {{ formatAccount }}
        </div>
        <div class="register-cont">
          <el-form ref="registerFormRef" :model="registerForm" :rules="registerRules" >
            <el-form-item prop="password" :label="$t('密码')">
              <el-input
                v-model="registerForm.password"
                :type="showPwd ? 'text' : 'password'"
                :placeholder="$t('输入密码')"
                autocomplete="off"
                @keyup.enter.native="onSubmit(registerFormRef)"
              >
                <template #suffix>
                  <div v-if="registerForm.password !== ''" class="flex-box space-end eye-icon" @click="showPwd = !showPwd">
                    <MonoEyeClose v-if="!showPwd" />
                    <MonoEyeOpen v-if="showPwd" />
                  </div>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item prop="passwordAgain" :label="$t('确认密码')">
              <el-input 
                v-model="registerForm.passwordAgain"
                :type="showPwd1 ? 'text' : 'password'"
                :placeholder="$t('输入密码')"
                autocomplete="off"
              >
                <template #suffix>
                  <div v-if="registerForm.passwordAgain !== ''" class="flex-box space-end eye-icon" @click="showPwd1 = !showPwd1">
                    <MonoEyeClose v-if="!showPwd1" />
                    <MonoEyeOpen v-if="showPwd1" />
                  </div>
                </template>
              </el-input>
            </el-form-item>
            <el-checkbox v-model="registerForm.xyChecked">
              <p class="ts-12 info-text">
                <em class="fit-tc-primary">{{ $t('我已知晓并同意网站名称的') }}</em>
                <span class="v-span">
                  <a :href="`${useCommon.zendeskUrl(locale)}/articles/4662556551966`" target="_blank" class="fit-new-theme">{{ $t('隐私政策') }}</a>
                  <em class="fit-tc-primary">{{ $t('和') }}</em>
                  <a :href="`${useCommon.zendeskUrl(locale)}/articles/4733818896670`" target="_blank" class="fit-new-theme">{{ $t('用户协议') }}</a>
                </span>
              </p>
            </el-checkbox>
          </el-form>
          <el-button type="primary" class="btn-box" :loading="isLoading" @click="submitRegister(registerFormRef)">{{ $t('确认') }}</el-button>
        </div>
      </div>
    </div>
  </div>
  <ElDialog v-model="isShowSuccess" width="480" class="success-register-dialog" @close="goApp()">
    <div class="success-box">
      <div class="success-logo"></div>
      <div class="succsss-text">{{ $t('恭喜您注册成功！') }}</div>
      <p class="fit-tc-secondary font-size-14 mg-b12">{{ $t('快去 Madex App 查看您的红包吧') }}</p>
      <el-button type="primary" @click="goApp()">{{ $t('立即下载 APP') }}</el-button>
    </div>
  </ElDialog>
  <VerifyCodeDialog
    v-if="dialogVisible"
    :dialogVisible="dialogVisible"
    :isEmail="true"
    :isRegister="true"
    :params="{ email: account, lang: locale, pwd: registerForm.password, invite_code: inviteCode }"
    @request="getCodeSuccess"
    @handleClose="dialogVisible = false"
  />
  <BoxScript id="gt" ref="gt" append-to-body src="https://res.ktx.com/common/js/gt4.js" />
</template>
<script lang="ts" setup>
  import { ElForm, ElFormItem, ElInput, ElCheckbox, ElButton, ElDialog } from 'element-plus'
  import { timeFormat, format, cookies } from '~/utils'
  import { getPacketDetailAPI, cashPacketAPI } from '~/api/tt'
  import { addUserByEmail } from '~/api/user'
  import UAParser from 'ua-parser-js'
  import md5 from 'blueimp-md5'
  import { useCommonData } from '~/composables/index'
  import MonoEyeOpen from '~/components/common/icon-svg/MonoEyeOpen.vue'
  import MonoEyeClose from '~/components/common/icon-svg/MonoEyeClose.vue'
  import Verify from '~/utils/verify'
  import VerifyCodeDialog from '~/components/common/VerifyCodeDialog.vue'
  const { locale, t } = useI18n()
  const router = useRouter()
  const useCommon = useCommonData()
  const props = defineProps({
    random: {
      type: String,
      default: ''
    },
    account: {
      type: String,
      default: ''
    },
    inviteCode: {
      type: String,
      default: ''
    }
  })
  const showPwd = ref(false)
  const isLoading = ref(false)
  const showPwd1 = ref(false)
  const isShowSuccess = ref(false)
  const dialogVisible = ref(false)
  const validatorPwd = (rule, value, callback) => {
    if (!isValidPwd(value)) {
      callback(new Error(t('请输入正确的密码格式')))
    } else {
      callback()
    }
  }
  const valideAgainPwd = (rule, value, callback) => {
    if (registerForm.password) {
      if (!value) {
        return callback(new Error(t('请输入确认密码')))
      } else if (value !== registerForm.password) {
        return callback(new Error(t('两次输入的密码不一致')))
      } else {
        return callback()
      }
    } else if (registerForm.password === '') {
      if (!value) {
        return callback(new Error(t('请输入密码')))
      } else if (!isValidPwd(value)) {
        return callback(new Error(t('请输入正确的密码格式')))
      } else {
        return callback()
      }
    }
  }
  interface RegisterForm{
    password: string,
    passwordAgain: string,
    xyChecked: string
  }
  const registerFormRef = ref<FormInstance>()
  const registerForm = reactive<RegisterForm>({
    password: '',
    passwordAgain: '',
    xyChecked: ''
  })
  const registerRules = reactive<FormRules<RegisterForm>>({
    password: [
      { required: true, message: t('请输入密码'), trigger: 'change' },
      { validator: validatorPwd, trigger: 'change' }
    ],
    passwordAgain: [
      { required: true, message: t('请再次输入密码'), trigger: 'change' },
      { validator: valideAgainPwd, trigger: 'change' }
    ]
  })
  const packetDetail = ref({
    redpacketin: [],
    redpacketout: {}
  })
  const formatAccount = computed(() => {
    const result = `${props.account.slice(0, 2)}***${props.account.slice(-2)}`
    return result
  })
  const cashPacket = async () => {
    return cashPacketAPI({
      random: props.random,
      account: props.account
    })
  }
  const redPaperDetail = ref(JSON.parse(localStorage.getItem('redPaperDetail') || '{}'))
  const getPackerDetail = async() => {
    const { data, error } = await getPacketDetailAPI({
      random: props.random
    })
    if (data) {
      packetDetail.value = data
    }
  }
  const verifyCode = ref(null)
  const sendCodeFun = (err, res) => {
    if (err) {
      isLoading.value = false
      return
    }
    if (res.type === 'success' || res.type === 'resend') {
      isLoading.value = false
      dialogVisible.value = true
    }
  }
  const submitDataFun = async() => {
    verifyCode.value = new Verify(sendCodeFun)
    nextTick(() => {
      verifyCode.value.verify()
    })
  }
  const goApp = () => {
    window.location.href = 'https://pre-ssr.tonetou.com/'
  }
  const verifyForm = async() => {
    if (!registerForm.xyChecked) {
      useCommon.showMsg('error', t('请勾选用户协议和隐私策略'))
    } else {
      isLoading.value = true
      await submitDataFun()
    }
  }
  const submitRegister = async(formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
      if (valid) {
        verifyForm()
      } else {
        console.info('error submit!', fields)
      }
    })
  }
  const getCodeSuccess = (params) => {
    register({
      type: 'success',
      code: params.code || params.phone_code || params.email_code
    })
  }
  const register = async(res) => {
    if (cookies.get('KTX_device_id')) {
      localStorage.setItem('KTX_device_id', cookies.get('KTX_device_id'))
    }
    const Ua = new UAParser(window.navigator.userAgent)
    const browser = Ua.getBrowser() || {}
    const os = Ua.getOS() || {}
    const device = Ua.getDevice()
    const deviceName = device.model ? device.model : `${os.name} ${browser.name}`
    const deviceId = cookies.get('KTX_device_id') || localStorage.getItem('KTX_device_id') || md5(`${Date.now()}-${Math.floor(Math.random() * ***********).toString(16)}-${os.name}-${browser.name}`)
    const type = router.currentRoute.value.query.userFrom || router.currentRoute.value.query.user_from || localStorage.getItem('userFrom') || 'web-pc'
    const params = {
      lang: locale.value,
      email: props.account,
      invite_code: props.inviteCode,
      code: res.code,
      device_id: deviceId,
      device_name1: deviceName || '--',
      platform: device.model || os.name,
      type
    }
    const { data, error } = await addUserByEmail(params)
    if (data) {
      dialogVisible.value = false
      isShowSuccess.value = true
      await cashPacket()
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  onMounted(() => {
    getPackerDetail()
  })
</script>
<style lang="scss">
  .color-y{
    color:#ffeac2;
  }
  .color-w{
    color:#fff;
  }
  .el-dialog{
    &.success-register-dialog{
      .success-box{
        padding:40px 32px;
        text-align:center;
        .success-logo{
          width:200px;
          height:200px;
          margin:0 auto;
          background:url('@/assets/images/login/register-success.png') no-repeat center;
          background-size:100% auto;
        }
        .succsss-text{
          padding:24px 0;
          font-size:28px;
          text-align:center;
          @include color(tc-primary);
        }
        .el-button{
          width:100%;
        }
      }
    }
  }
  .red-paper-result{
    .receive-icon {
      box-shadow: 0 4px 12px 0 rgba(102, 0, 17, 0.2);
      border-radius: 50%;
      border: solid 2px #ffeac2;
      background:#fff;
      display: flex;
      .icon-content {
        width: 24px;
        height: 24px;
        margin: 0!important;
      }
    }
    .result-header{
      height:280px;
      width:100%;
      background:url('@/assets/images/redPaper/result-head-bg.png') repeat top;
      background-size:auto 100%;
      .receive-logo{
        padding: 20px;
        .logo-wrap{
          display:block;
          width:80px;
          height:auto;
        }
      }
      .copyTxt{
        cursor:pointer;
        font-size:14px;
        padding: 5px 10px;
        border-top-left-radius:20px;
        border-bottom-left-radius:20px;
        color: #fff;
        @include bg-color(theme);
      }
    }
    .result-content{
      padding:20px;
      .info-text{
        color:#414655;
        em{
          font-style:inherit;
        }
        a{
          @include color(theme);
        }
      }
      .btn-box{
        width:100%;
        height:44px;
        margin-top:20px;
      }
      .result-list{
        .list-title{
          font-size:16px;
          padding-bottom:20px;
          color:#414655;
        }
        ul{
          li{
            &.red-paper_item{
              padding:10px 0;
              border-top:1px solid #eeeeee;
            }
          }
        }
      }
    }
  }
</style>