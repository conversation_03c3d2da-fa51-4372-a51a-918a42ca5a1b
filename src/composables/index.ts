import BigNumber from 'bignumber.js'
import { cookies, format, getDomainFirst, getDomain } from '~/utils/index'
import { ElMessage } from 'element-plus'
import { commonStore } from '~/stores/commonStore'
import { loginOut } from '~/api/user'
import { isApp } from '~/utils'
export const useCommonData = () => {
  const showMsg = (type = 'error', message = '') => {
    const offset = window.innerHeight / 2 - 30
    return ElMessage({
      message,
      type,
      iconClass: type === 'info' ? 'ts-32 el-message__icon icon-Subtract' : undefined,
      offset,
      duration: 3000
    })
  }
  const domain = computed(() => {
    if (!process.client) {
      return '//www.trade-planets.com'
    }
    return `//${getDomainFirst()}${getDomain()}${location.port ? (':' + location.port) : ''}` //! 线上测试环境后续改成www
  })
  const zendeskUrl = (lang) => {
    const zendeskObj = {
      'zh': 'zh-my',
      'en': 'en-001',
      'ja': 'ja',
      'ko': 'ko',
      'zh-Hant': 'zh-tw'
    }
    return `https://support.ktx.com/hc/${zendeskObj[lang] || 'en-001'}`
  }
  const err = (code: any, params: any) => {
    if (process.client) {
      const nuxtApp = useNuxtApp()
      const lang = nuxtApp.$i18n.locale.value
      let message = (commonStore().errorMessages[lang] || {})[code] || (commonStore().errorMessages['en'] || {})[code] || params.msg || `Error: ${code}`
      for (const key in params) {
        message = message.replace(`{${key}}`, params[key])
      }
      return message
    }
  }
  const copy = (data: any, tips: string) => {
    // 方法1：使用 textarea（保留换行符）
    const textarea = document.createElement('textarea');
    textarea.value = data; // 直接传入带 \n 的内容
    textarea.style.position = 'fixed'; // 避免页面滚动
    document.body.appendChild(textarea);
    textarea.select();
  
    try {
      document.execCommand('copy'); // 执行复制
      useCommonData().showMsg('success', tips);
    } catch (err) {
      console.error('复制失败:', err);
      useCommonData().showMsg('error', '复制失败');
    } finally {
      document.body.removeChild(textarea);
    }
  };
  const convert = (value = '', currencySymbol = 'USDT', isAssets = false, isSymbol = true, currency = '') => {
    if (value === '') {
      return '--'
    }
    value = Number(value)
    if (process.client && currencySymbol) {
      const rates = (commonStore().currencyRate || {}).rates
      const { rate, symbol } = commonStore().exchangeRate
      if (!currency) {
        currency = rate
      }
      let decimal = 2
      if (rates === undefined) {
        return '--'
      }
      const currencySymbolRate = rates[currencySymbol.replace('USDT', 'USD')]
      const currencyRate = rates[currency]
      const cnyValue = new BigNumber(value).dividedBy(currencySymbolRate).valueOf()
      const currencyValue = new BigNumber(cnyValue).multipliedBy(currencyRate).valueOf()
      if (Number(currencyValue) < 1) {
        decimal = 4
      } else if (currencyValue >= 100000) {
        decimal = 0
      }
      return format(currencyValue, decimal, true, isAssets) + ' ' + rate
    }
  }
  const jsAppBridge = (type, params, callback = () => {}) => { // 跳转APP
    let _params = {
      type: type
    }
    if (typeof params !== 'function') {
      _params = {
        ..._params,
        ...params
      }
    }
    return window.setupWebViewJavascriptBridge(function (bridge) {
      bridge.callHandler('appPushPersonalPostAction', _params, callback)
    })
  }
  const hideAssets = (num: any) => {
    if (process.client) {
      return commonStore().isHideAssets ? '***' : num
    } else {
      return num
    }
  }
  const userLevelMethod = (level) => {
    if (level < 1) {
      return {
        levelType: 'VIP 0',
        nowLevel: '',
        level
      }
    } else if (level < 9) {
      return {
        levelType: 'VIP',
        nowLevel: level,
        level
      }
    } else if (level >= 50) {
      return {
        levelType: 'special',
        nowLevel: 0,
        level
      }
    }
    return {
      levelType: 'VIP',
      nowLevel: level + 1,
      level
    }
    // if (level < 10) {
    //   return {
    //     levelType: 'Lv',
    //     nowLevel: level + 1,
    //     level
    //   }
    // } else if (level >= 10 && level < 20) {
    //   return {
    //     levelType: 'Pro',
    //     nowLevel: level - 10 + 1,
    //     level
    //   }
    // } else if (level >= 20 && level < 50) {
    //   return {
    //     levelType: 'Big',
    //     nowLevel: level - 20,
    //     level
    //   }
    // } else if (level >= 50) {
    //   return {
    //     levelType: 'special',
    //     nowLevel: 0,
    //     level
    //   }
    // }
    // return {
    //   levelType: 'Lv',
    //   nowLevel: level + 1,
    //   level
    // }
  }
  const getFuturesSymbol = (pair, isCurrency) => {
    if (pair.includes('_USD_')) {
      return isCurrency ? 'USD' : pair.replace('_SWAP', '').replace('_USD', '')
    }
    return isCurrency ? 'USDT' : pair.split('_')[0]
  }
  const cbuConver = (num, isUSDT, price) => {
    if (num === '--' || isNaN(Number(num))) {
      return '--'
    }
    return isUSDT ? new BigNumber(num).multipliedBy(price) : num
  }
  const cbcConver = (num, isUSDT, price) => {
    if (num === '--' || isNaN(Number(num))) {
      return '--'
    }
    return isUSDT ? num : new BigNumber(num).dividedBy(price)
  }
  const formatDecimal = (num, decimal) => {
    num = num.toString()
    let index = num.indexOf('.')
    if (index !== -1) {
        num = num.substring(0, decimal + index + 1)
    } else {
        num = num.substring(0)
    }
    return parseFloat(num).toFixed(decimal)
  }
  const transcodeNumberK = (value, decimal = 4) => {
    let n = value
    if (n>=1000000000) {
      return n = parseFloat(formatDecimal(n/1000000000, 2)) + 'B';
    } else if(n>=1000000) {
      return n = parseFloat(formatDecimal(n/1000000, 2)) + 'M';
    } else if(n>=10000){
      return n = parseFloat(formatDecimal(n/1000, 2)) + 'K';
    }
    return !isNaN(new BigNumber(n).toFixed(decimal, BigNumber.ROUND_HALF_UP)) ? new BigNumber(n).toFixed(decimal, BigNumber.ROUND_HALF_UP) : '--'
  }
  const openLogin = () => {
    if (isApp()) {
      useCommonData().jsAppBridge('loginJump')
    } else {
      const router = useRouter()
      const redirect = window.location.href.includes('/my/subAccount') ? encodeURIComponent(`${window.location.origin}${window.location.pathname.split('/').slice(0, 2).join('/')}/my/dashboard`) : encodeURIComponent(window.location.href)
      const nuxtApp = useNuxtApp()
      const lang = nuxtApp.$i18n.locale.value
      router.push(`/${lang}/login?redirect=${redirect}${router.currentRoute.value.query.invite_code ? `&invite_code=${router.currentRoute.value.query.invite_code}` : ''}`)
    }
    
  }
  const openRegister = () => {
    if (isApp()) {
      useCommonData().jsAppBridge('registerJump')
    } else {
      const router = useRouter()
      const nuxtApp = useNuxtApp()
      const lang = nuxtApp.$i18n.locale.value
      router.push(`/${lang}/login/register${router.currentRoute.value.query.invite_code ? `?invite_code=${router.currentRoute.value.query.invite_code}` : ''}`)
    }
  }
  const loginout = async() => {
    try {
      cookies.remove('isLogin')
    } catch (error) {
      console.info('loginout error ', error)
    }
    const { data } = await loginOut()
    if (data) {
      cookies.remove('isLogin')
      cookies.remove('session_id')
      cookies.remove('session_id_origin')
      cookies.remove('futureslpcNotOrderConfirmAgain')
      cookies.remove('showMessage')
      try {
        cookies.remove('isLogin')
        cookies.remove('session_id')
        cookies.remove('session_id_origin')
        cookies.remove('futureslpcNotOrderConfirmAgain')
        cookies.remove('showMessage')
      } catch (error) {
        console.info('loginout error ', error)
      }
      location.reload()
    }
  }
  const formatCoinList = (list) => {
    let arr = []
    list.forEach((item) => {
      arr.push({
        'value': item.general_name,
        'label': item.general_name
      })
    })
    return arr
  }
  const formatPairList = (list) => {
    const arr = []
    list.forEach((v) => {
      arr.push({
        'value': v,
        'label': v.includes('_SWAP') ? v.replace('_SWAP', '').replace('_', '') : v.replace('_', '/')
      })
    })
    return arr
  }
  return {
    domain,
    jsAppBridge,
    zendeskUrl,
    formatPairList,
    formatCoinList,
    convert,
    hideAssets,
    getFuturesSymbol,
    showMsg,
    copy,
    err,
    userLevelMethod,
    cbuConver,
    cbcConver,
    transcodeNumberK,
    openRegister,
    openLogin,
    loginout
  }
}