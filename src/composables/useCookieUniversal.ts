import Cookie from 'cookie'
import { SITE_NAME, BASE_URL_DIRECTORY } from '~/config/index'
console.info(BASE_URL_DIRECTORY, 'BASE_URL_DIRECTORYBASE_URL_DIRECTORY')
export const useCookieUniversal = (req: any, res: any, parseJSON = true) => {
  let isClient =
    typeof document === 'object' && typeof document.cookie === 'string'
  let isServer =
    typeof req === 'object' &&
    typeof res === 'object' &&
    typeof module !== 'undefined'
  let isNeither = (!isClient && !isServer) || (isClient && isServer)

  const getHeaders = (fromRes?: any) => {
    if (isServer) {
      let h = req.headers.cookie || ''
      if (fromRes) {
        h = res.getHeaders()
        h = h['set-cookie']
          ? h['set-cookie'].map((c: any) => c.split(';')[0]).join(';')
          : ''
      }
      return h
    }
    if (isClient) return document.cookie || ''
  }

  const getResponseCookies = () => {
    let cookies = res.getHeader('Set-Cookie')
    cookies = typeof cookies === 'string' ? [cookies] : cookies
    return cookies || []
  }
  const setResponseCookie = (cookieList: any) =>
    res.setHeader('Set-Cookie', cookieList)

  const parseToJSON = (val: any, enableParsing: any) => {
    if (!enableParsing) return val
    try {
      return JSON.parse(val)
    } catch (err) {
      return val
    }
  }

  // public api
  const state: any = {
    parseJSON,

    set(name = '', value = '', opts = { path: '/' }) {
      opts = {
        ...opts,
        path: BASE_URL_DIRECTORY
      }
      if (isNeither) return
      value = typeof value === 'object' ? JSON.stringify(value) : value

      if (isServer) {
        const cookies = getResponseCookies()
        cookies.push(Cookie.serialize(`${name}`, value, opts))
        setResponseCookie(cookies)
      } else {
        document.cookie = Cookie.serialize(`${name}`, value, opts)
      }
    },

    setAll(cookieList = []) {
      if (isNeither) return
      if (!Array.isArray(cookieList)) return
      cookieList.forEach(cookie => {
        let { name = '', value = '', opts = { path: '/' } } = cookie
        opts = {
          ...opts,
          path: BASE_URL_DIRECTORY
        }
        state.set(`${name}`, value, opts)
      })
    },

    get(name = '', opts = { fromRes: false, parseJSON: state.parseJSON, path: '/' }) {
      if (isNeither) return ''
      opts = {
        ...opts,
        path: BASE_URL_DIRECTORY
      }
      const cookies = Cookie.parse(getHeaders(opts.fromRes))
      const cookie = cookies[`${name}`]
      return parseToJSON(cookie, opts.parseJSON)
    },

    getAll(opts = { fromRes: false, parseJSON: state.parseJSON, path: '/' }) {
      opts = {
        ...opts,
        path: BASE_URL_DIRECTORY
      }
      if (isNeither) return {}
      const cookies = Cookie.parse(getHeaders(opts.fromRes))
      for (const cookie in cookies) {
        cookies[cookie] = parseToJSON(cookies[cookie], opts.parseJSON)
      }
      return cookies
    },

    remove(name: string = '', opts: any = { path: '/' }) {
      if (isNeither) return
      opts = {
        ...opts,
        path: BASE_URL_DIRECTORY
      }
      opts.expires = new Date(0)
      state.set(`${name}`, '', opts)
    },

    removeAll(opts: any = { path: '/' }) {
      opts = {
        ...opts,
        path: BASE_URL_DIRECTORY
      }
      if (isNeither) return
      const cookies = Cookie.parse(getHeaders())
      for (const cookie in cookies) {
        state.remove(cookie, opts)
      }
    },

    // expose cookie library
    nodeCookie: Cookie,
  }

  return state
}