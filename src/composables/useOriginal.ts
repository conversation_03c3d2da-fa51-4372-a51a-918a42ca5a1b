const textColorDark = 'rgba(194, 214, 255, 0.48)'
const gridColorDark = '#282928'
const axisLineColorDark = '#363a45'
const crossTextBackgroundColorDark = '#363a45'

const textColorLight = '#76808F'
const gridColorLight = '#ededed'
const axisLineColorLight = '#eeeeee'
const crossTextBackgroundColorLight = '#686d76'
const textGreenColor = '#1CCB91'
const textRedColor = '#F55369'
const greenColor = '#3dc087'
const redColor = '#ff6262'
const subGreenColor = 'rgba(61, 192, 135, 0.5)'
const subRedColor = 'rgba(255, 98, 98, 0.5)'
export default function getThemeOptions (theme: string, colorType: string, priceType: string) {
  const textColor = theme === 'dark' ? textColorDark : textColorLight
  const gridColor = theme === 'dark' ? gridColorDark : gridColorLight
  const axisLineColor = theme === 'dark' ? axisLineColorDark : axisLineColorLight
  const crossLineColor = theme === 'dark' ? axisLineColorDark : axisLineColorLight
  const crossTextBackgroundColor = theme === 'dark' ? crossTextBackgroundColorDark : crossTextBackgroundColorLight
  const upColor = colorType === 'green-up' ? greenColor : redColor
  const downColor = colorType === 'green-up' ? redColor : greenColor
  const noChangeColor = upColor
  const subUpColor = colorType === 'green-up' ? subGreenColor : subRedColor
  const subDownColor = colorType === 'green-up' ? subRedColor : subGreenColor
  const subNoChangeColor = subUpColor
  const textUpColor = colorType === 'green-up' ? textGreenColor : textRedColor
  const textDownColor = colorType === 'green-up' ? textRedColor : textGreenColor

  const custom = [
    { title: 'open', value: '{open}' },
    { title: 'high', value: '{high}' },
    { title: 'low', value: '{low}' },
    { title: 'close', value: '{close}' },
    { title: 'volume', value: '{volume}' }
  ]

  return {
    overlay: {
      backgroundColor: crossTextBackgroundColor
    },
    grid: {
      horizontal: {
        color: gridColor
      },
      vertical: {
        color: gridColor
      }
    },
    technicalIndicator: {
      margin: {
      }
    },
    candle: {
      priceMark: {
        high: {
          color: textColor
        },
        low: {
          color: textColor
        },
        last: {
          upColor: textUpColor,
          downColor: textDownColor,
          noChangeColor: textUpColor
        }
      },
      tooltip: {
        text: {
          color: textColor,
        },
        custom
      },
      bar: {
        upColor,
        downColor,
        upBorderColor: upColor,
        downBorderColor: downColor,
        noChangeColor,
        noChangeBorderColor: noChangeColor,
        upWickColor: upColor,
        downWickColor: downColor,
        noChangeWickColor: noChangeColor
      }
    },
    indicator: {
      tooltip: {
        text: {
          color: textColor
        }
      },
      bars: [
        {
          upColor: subUpColor,
          downColor: subDownColor,
          noChangeColor: subNoChangeColor
        }
      ]
    },
    xAxis: {
      size: 40,
      axisLine: {
        color: axisLineColor
      },
      tickLine: {
        color: axisLineColor
      },
      tickText: {
        color: textColor
      }
    },
    yAxis: {
      axisLine: {
        color: axisLineColor
      },
      tickLine: {
        color: axisLineColor
      },
      tickText: {
        color: textColor
      },
      scroll: {
        enabled: true,
        speedRatio: 0.1,
        moveSlop: 5
      }
    },
    separator: {
      color: axisLineColor
    },
    crosshair: {
      horizontal: {
        line: {
          style: 'dashed',
          dashedValue: [4, 4],
          size: 1,
          color: crossLineColor
        },
        text: {
          backgroundColor: crossTextBackgroundColor
        }
      },
      vertical: {
        line: {
          style: 'dashed',
          dashedValue: [4, 4],
          size: 1,
          color: crossLineColor
        },
        text: {
          backgroundColor: crossTextBackgroundColor
        }
      }
    }
  }
}



// 基础k线默认技术指标配置
export const baseTechnicalIndicatorMap: any = {
  MA: {
    name: 'MA-移动平均线',
    type: 'main',
    setting: {
      MA1: {
        show: true, // 是否显示
        value: 5, // 值
        color: '#FF9600'
      },
      MA2: {
        show: true, // 是否显示
        value: 10, // 值
        color: '#9D65C9'
      },
      MA3: {
        show: true, // 是否显示
        value: 20, // 值
        color: '#f0b90b'
      },
      MA4: {
        show: false, // 是否显示
        value: 30, // 值
        color: '#E11D74'
      },
      MA5: {
        show: false, // 是否显示
        value: 60, // 值
        color: '#01C5C4'
      }
    }
  },
  EMA: {
    name: 'EMA-指数平滑移动平均线',
    type: 'main',
    setting: {
      EMA1: {
        show: true, // 是否显示
        value: 5, // 值
        color: '#FF9600'
      },
      EMA2: {
        show: true, // 是否显示
        value: 10, // 值
        color: '#9D65C9'
      },
      EMA3: {
        show: true, // 是否显示
        value: 20, // 值
        color: '#f0b90b'
      },
      EMA4: {
        show: false, // 是否显示
        value: 30, // 值
        color: '#E11D74'
      },
      EMA5: {
        show: false, // 是否显示
        value: 60, // 值
        color: '#01C5C4'
      }
    }
  },
  SMMA: {
    name: 'SMMA-平滑移动平均线',
    type: 'main',
    setting: {
      SMMA1: {
        show: true,
        value: 5,
        color: '#FF9600'
      },
      SMMA2: {
        show: true,
        value: 10,
        color: '#9D65C9'
      },
      SMMA3: {
        show: true,
        value: 20,
        color: '#f0b90b'
      },
      SMMA4: {
        show: false,
        value: 30,
        color: '#E11D74'
      },
      SMMA5: {
        show: false,
        value: 60,
        color: '#01C5C4'
      }
    }
  },
  BOLL: {
    name: 'BOLL-布林线',
    type: 'main',
    setting: {
      cycle: { // 周期
        name: '周期',
        value: 20
      },
      deviation: { // 标准差
        name: '标准差',
        value: 2
      },
      BOLL: {
        color: '#FF9600'
      },
      UB: {
        color: '#9D65C9'
      },
      DB: {
        color: '#f0b90b'
      }
    }
  },
  SAR: {
    name: 'SAR-停损点指向指标',
    type: 'main',
    setting: {
      start: {
        name: '开始',
        value: 2
      },
      step: {
        name: '步长',
        value: 2
      },
      max: {
        name: '最大',
        value: 20
      },
      SAR: {
        color: '#FF9600'
      }
    }
  },
  VOL: {
    name: 'VOL成交量',
    type: 'sub',
    setting: {
      MA1: {
        show: true, // 是否显示
        value: 5, // 值
        color: '#FF9600'
      },
      MA2: {
        show: true, // 是否显示
        value: 10, // 值
        color: '#9D65C9'
      },
      MA3: {
        show: true, // 是否显示
        value: 20, // 值
        color: '#f0b90b'
      }
    }
  },
  MACD: {
    name: 'MACD平滑异同平均线',
    type: 'sub',
    setting: {
      MACD1: {
        name: '快线周期',
        value: 12 // 值
      },
      MACD2: {
        name: '慢线周期',
        value: 26 // 值
      },
      MACD3: {
        name: '信号周期',
        value: 9 // 值
      },
      DIF: {
        color: '#FF9600'
      },
      DEA: {
        color: '#9D65C9'
      }
    }
  },
  RSI: {
    name: 'RSI相对强弱指标',
    type: 'sub',
    setting: {
      RSI1: {
        show: true,
        value: 6,
        color: '#FF9600'
      },
      RSI2: {
        show: true,
        value: 12,
        color: '#9D65C9'
      },
      RSI3: {
        show: true,
        value: 24,
        color: '#f0b90b'
      }
    }
  },
  KDJ: {
    name: 'KDJ随机指标',
    type: 'sub',
    setting: {
      KDJ1: {
        name: '计算周期',
        value: 9
      },
      KDJ2: {
        name: '移动平均周期1',
        value: 3
      },
      KDJ3: {
        name: '移动平均周期2',
        value: 3
      },
      K: {
        color: '#FF9600'
      },
      D: {
        color: '#9D65C9'
      },
      J: {
        color: '#f0b90b'
      }
    }
  },
  OBV: {
    name: 'OBV能量潮指标',
    type: 'sub',
    setting: {
      OBV: {
        color: '#FF9600'
      },
      MAOBV: {
        value: 30,
        color: '#9D65C9'
      }
    }
  },
  CCI: {
    name: 'CCI商品路径指标',
    type: 'sub',
    setting: {
      CCI: {
        name: '周期',
        value: 9,
        color: '#FF9600'
      }
    }
  },
  WR: {
    name: 'WR威廉姆斯指标',
    type: 'sub',
    setting: {
      WR1: {
        name: '周期',
        value: 14,
        color: '#FF9600'
      }
    }
  },
  DMI: {
    name: 'DMI方向运动指标',
    type: 'sub',
    setting: {
      N: {
        value: 14
      },
      MM: {
        value: 6
      },
      PDI: {
        color: '#FF9600'
      },
      MDI: {
        color: '#9D65C9'
      },
      ADX: {
        color: '#f0b90b'
      },
      ADXR: {
        color: '#E11D74'
      }
    }
  },
  MTM: {
    name: 'MTM动量指标',
    type: 'sub',
    setting: {
      N: {
        name: '计算周期',
        value: 12
      },
      MM: {
        name: '移动平均周期',
        value: 6
      },
      MTM: {
        color: '#FF9600'
      },
      MAMTM: {
        color: '#9D65C9'
      }
    }
  },
  EMV: {
    name: 'EMV简易波动指标',
    type: 'sub',
    setting: {
      EMV1: {
        name: '移动平均周期1',
        value: 14
      },
      EMV2: {
        name: '移动平均周期2',
        value: 9
      },
      EMV: {
        color: '#FF9600'
      },
      MAEMV: {
        color: '#9D65C9'
      }
    }
  }
}

