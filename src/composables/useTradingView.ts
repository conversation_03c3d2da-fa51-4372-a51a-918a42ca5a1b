export const overrides = {

  'paneProperties.vertGridProperties.style': 3,
  'paneProperties.horzGridProperties.style': 3,

  // 横纵坐标的字体大小
  'scalesProperties.fontSize': 12,
  // kline上面字体显示的颜色
  'scalesProperties.textColor': '#7F86A1',
  // 默认隐藏左上角标题，可手动打开
  'paneProperties.legendProperties.showLegend': true,
  // 展示开盘价，最高价，最低价，收盘价
  'paneProperties.legendProperties.showSeriesOHLC': true
}

export default function useTradingView (theme: string = 'dark', colorType: string = 'green-up') {

  // const textGreenColor = '#1CCB91'
  // const textRedColor = '#F55369'
  // const greenColor = '#13BD8A'
  // const redColor = '#EB465B'
  // const subGreenColor = 'rgba(19, 189, 138, 0.5)'
  // const subRedColor = 'rgba(235, 70, 91, 0.5)'
  let UP_COLOR = '#3cc188'
  let DOWN_COLOR = '#ff6262'

  let UP_VOLUME_COLOR = '#3cc188'
  let DOWN_VOLUME_COLOR = '#ff6262'

  let UP_COLOR_WHITE = '#3cc188'
  let DOWN_COLOR_WHITE = '#ff6262'

  let UP_VOLUME_COLOR_WHITE = '#3cc188'
  let DOWN_VOLUME_COLOR_WHITE = '#ff6262'

  const backgroundColor = theme === 'dark' ? '#181a1f' : '#ffffff'
  const lineColor = theme === 'dark' ? '#363a45' : '#eeeeee'
  if (colorType === 'red-up') {
    UP_COLOR = '#ff6262'
    DOWN_COLOR = '#3cc188'

    UP_VOLUME_COLOR = '#ff6262'
    DOWN_VOLUME_COLOR = '#3cc188'

    UP_COLOR_WHITE = '#ff6262'
    DOWN_COLOR_WHITE = '#3cc188'

    UP_VOLUME_COLOR_WHITE = '#ff6262'
    DOWN_VOLUME_COLOR_WHITE = '#3cc188'
  }
  const option = {
    dark: {
      theme: 'Dark',
      overrides: {
        ...overrides,
        'paneProperties.background': backgroundColor,
        'paneProperties.backgroundGradientStartColor': backgroundColor,
        'paneProperties.backgroundGradientEndColor': backgroundColor,
        // 水平分割线
        'paneProperties.vertGridProperties.color': lineColor,
        // 竖直分割线
        'paneProperties.horzGridProperties.color': lineColor,

        'scalesProperties.lineColor': lineColor,

        'mainSeriesProperties.candleStyle.wickUpColor': UP_COLOR,
        'mainSeriesProperties.candleStyle.upColor': UP_COLOR,
        'mainSeriesProperties.candleStyle.borderUpColor': UP_COLOR,
        // // 蜡烛线跌颜色
        'mainSeriesProperties.candleStyle.wickDownColor': DOWN_COLOR,
        'mainSeriesProperties.candleStyle.downColor': DOWN_COLOR,
        'mainSeriesProperties.candleStyle.borderDownColor': DOWN_COLOR
      },
      studies_overrides: {
        'volume.volume.color.0': DOWN_VOLUME_COLOR,
        'volume.volume.color.1': UP_VOLUME_COLOR,
        'volume.precision': 2 //!TODO
      },
      loading_screen: {
        'foregroundColor': backgroundColor,
        'backgroundColor': backgroundColor
      }
    },
    light: {
      theme: 'Light',
      overrides: {
        ...overrides,
        'paneProperties.background': backgroundColor,
        'paneProperties.backgroundGradientStartColor': backgroundColor,
        'paneProperties.backgroundGradientEndColor': backgroundColor,
        // 水平分割线
        'paneProperties.vertGridProperties.color': lineColor,
        // 竖直分割线
        'paneProperties.horzGridProperties.color': lineColor,

        'scalesProperties.lineColor': lineColor,

        'mainSeriesProperties.candleStyle.wickUpColor': UP_COLOR_WHITE,
        'mainSeriesProperties.candleStyle.upColor': UP_COLOR_WHITE,
        'mainSeriesProperties.candleStyle.borderUpColor': UP_COLOR_WHITE,
        // // 蜡烛线跌颜色
        'mainSeriesProperties.candleStyle.wickDownColor': DOWN_COLOR_WHITE,
        'mainSeriesProperties.candleStyle.downColor': DOWN_COLOR_WHITE,
        'mainSeriesProperties.candleStyle.borderDownColor': DOWN_COLOR_WHITE
      },
      studies_overrides: {
        'volume.volume.color.0': DOWN_VOLUME_COLOR_WHITE,
        'volume.volume.color.1': UP_VOLUME_COLOR_WHITE
      },
      loading_screen: {
        'foregroundColor': backgroundColor,
        'backgroundColor': backgroundColor
      }
    }
  }

  const tradingviewLangMap = {
    'zh': 'zh',
    'zh-Hant': 'zh_TW',
    'en': 'en',
    'ja': 'ja',
    'ko': 'ko'
  }

  return {
    option,
    tradingviewLangMap,
  }
}
