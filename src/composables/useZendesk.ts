declare global {
  interface Window {
    zE?: any;
    zESettings?: {
      webWidget?: ZendeskWidgetConfig;
    };
  }
}

export const useZendesk = () => {
  const { locale } = useI18n();
  const isChatOpen = ref(false);
  const zendeskLoaded = ref(false);

  // 转换语言代码
  const getZendeskLocale = (locale: string): string => {
    const localeMap: Record<string, string> = {
      'en': 'en-US',
      'zh': 'zh-CN',
      'zh-Hant': 'zh-TW',
      'ja': 'ja',
      'ko': 'ko',
      'de': 'de',
      'fr': 'fr',
      'es': 'es'
    };
    return localeMap[locale] || 'en-US';
  };

  // 初始化Zendesk并设置监听
  const initializeZendesk = () => {
    if (process.client) {
      // 确保只初始化一次
      if (window.zE || zendeskLoaded.value) return;

      // 加载Zendesk脚本
      const script = document.createElement('script');
      script.id = 'ze-snippet';
      script.src = 'https://static.zdassets.com/ekr/snippet.js?key=YOUR_KEY';
      script.async = true;
      
      script.onload = () => {
        zendeskLoaded.value = true;
        setupEventListeners();
      };
      
      document.body.appendChild(script);
    }
  };

  // 设置事件监听
  const setupEventListeners = () => {
    if (process.client && window.zE) {
      window.zE('messenger:on', 'open', () => isChatOpen.value = true);
      window.zE('messenger:on', 'close', () => isChatOpen.value = false);
    }
  };

  // 语言变化监听
  watch(locale, (newLocale) => {
    if (process.client && window.zE) {
      window.zE('messenger:set', 'locale', getZendeskLocale(newLocale));
    }
  }, { immediate: true });

  // 显示聊天窗口（自动处理加载状态）
  const showChat = () => {
    // 如果Zendesk已加载，直接打开
    if (process.client && window.zE) {
      window.zE('messenger', 'show');
      window.zE('messenger', 'open');
      return;
    }
    
    // 如果未加载，先初始化再打开
    initializeZendesk();
    
    // 轮询检查直到可以打开
    const checkAndOpen = setInterval(() => {
      if (process.client && window.zE) {
        window.zE('messenger', 'show');
        window.zE('messenger', 'open');
        clearInterval(checkAndOpen);
      }
    }, 100);
  };

  // 组件挂载时预加载
  onMounted(initializeZendesk);

  return {
    showChat,
    isChatOpen
  };
};