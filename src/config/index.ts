const port = 3009
const imgDmain = 'https://res.ktx.com'
const imgKycDmain = 'https://res.ktx.com/'
// websocket 是否启用压缩 默认true
const SOCKET_ISZIP = true
// const BASE_EXCHANGE_URL = 'http://ma-tapi.tonetou.com'
// 接口地址
const BASE_EXCHANGE_URL = 'http://tapi.bost.co'
const PROD_EXCHANGE_URL = 'https://api.ktx.com'
const PROD_ORDER_EXCHANGE_URL = 'https://api-user.tonetou.com'

const BASE_SOCKET_URL = 'wss://stream-market.tonetou.com'
// pm启动key
const PROJECT_KEY = 'madex-pro'

// This option is given directly to the vue-router base
const BASE_URL_DIRECTORY = '/'

const getSiteName = () => {
  let name = BASE_URL_DIRECTORY
  if (name.startsWith('/')) {
    name = name.substring(1)
  }
  if (name.endsWith('/')) {
    name = name.substring(0, name.length - 1)
  }
  return name.replace('/', '-')
}

const SITE_NAME = getSiteName()


const LOCALES = [
  {
    code: 'en',
    label: 'English',
    zendesk: 'en-us'
  },
  {
    code: 'ja',
    label: '日本語',
    zendesk: 'ja'
  },
  {
    code: 'ko',
    label: '한국어',
    zendesk: 'en-us'
  },
  {
    code: 'zh-Hant',
    label: '繁体中文',
    zendesk: 'zh-Hant'
  },
  {
    code: 'zh',
    label: '简体中文',
    zendesk: 'zh-cn'
  }
]
const RATES = [
  {
    rate: 'USD',
    symbol: '$'
  },
  {
    rate: 'JPY',
    symbol: '¥'
  },
  {
    rate: 'KRW',
    symbol: '₩'
  },
  {
    rate: 'CNY',
    symbol: '¥'
  }
]
const ORDER_STATUS_MAP = { // 订单所有状态
  'accepted': '未成交',
  'cancelled': '已撤销',
  'triggered': '已触发',
  'untriggered': '未触发',
  'received': '已接收',
  'cancelling': '取消中',
  'partially-cancelled': '部分成交',
  'partially-filled': '部分成交',
  'rejected': '已废弃',
  'filled': '完全成交'
}
const PROFIT_LOSS_MAP = { // 止盈止损类型
  'stop-limit': '止损',
  'take-profit-limit': '止盈',
  'stop': '止损',
  'take-profit': '止盈'
}
const TRADE_BILLS_MAP = { // 交易账户类型
  'transfer': '资金划转',
  'trade': '交易',
  'fee': '手续费',
  'rebate': '手续费返还',
  'funding': '资金费率',
  'pnl': '平仓盈亏',
  'adl': 'ADL',
  '--': '--'
}
const ORDER_TYPE_ALL_MAP = { // 现货下单策略
  1: {
    sign: 'market',
    label: '市价委托'
  },
  2: {
    sign: 'limit',
    label: '普通委托'
  }
}
const ORDER_TYPE_SIGN_MAP: any = {}
Object.entries(ORDER_TYPE_ALL_MAP).forEach(([type, value]) => {
  ORDER_TYPE_SIGN_MAP[value.sign] = type
})
export {
  imgDmain,
  imgKycDmain,
  LOCALES,
  RATES,
  port,
  SOCKET_ISZIP,
  ORDER_STATUS_MAP,
  PROFIT_LOSS_MAP,
  BASE_EXCHANGE_URL,
  PROD_EXCHANGE_URL,
  PROD_ORDER_EXCHANGE_URL,
  BASE_SOCKET_URL,
  BASE_URL_DIRECTORY,
  SITE_NAME,
  PROJECT_KEY,
  ORDER_TYPE_ALL_MAP,
  TRADE_BILLS_MAP,
  ORDER_TYPE_SIGN_MAP
}
