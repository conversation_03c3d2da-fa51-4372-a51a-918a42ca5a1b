<template>
  <BoxLoading v-if="loading" />
  <div class="mad-default-layout" >
    <NuxtLoadingIndicator />
    <div>
      <slot />
    </div>
    <ZendexkTips v-if="!(curPage.includes('/google-callback') || curPage.includes('/exchange/') || curPage.includes('/future/'))" />
    <GoogleSetting v-if="isShowBindGoogle" :googleData="googleData" :showClose="false" :googleKeyLoading="googleKeyLoading" :currentItem="currentItem" :dialogVisible="isShowBindGoogle" @confirm="bindGoogle" @cancelDialog="cancelFun()" />
  </div>
</template>
<script lang="ts" setup>
import { commonStore } from '~/stores/commonStore'
import { addUserByEmail, bindTotpAsk, bindTotpConfirm1 } from '~/api/user'
import { getI18nInfoApi } from '~/api/public'
import ZendexkTips from '~/components/common/ZendeskTips'
import i18nLocales from '~/locales/i18n'
import GoogleSetting from '~/components/my/subAccount/GoogleSetting.vue'
import { useUserStore } from '~/stores/useUserStore'
import { useCommonData } from '~/composables/index'
const useCommon = useCommonData()
const userStore = useUserStore()
const { getUserInfoAction } = userStore
const { userInfo } = storeToRefs(userStore)
const store = commonStore()
const colorMode = useColorMode()
const { getMessageError, getCurrencyRate } = store
const { locale, t } = useI18n()
const router = useRouter()
const i18n = useI18n()
const visible = ref(true)
const languageObj = ref({})
const loading = ref(false)
const googleData = ref({})
const isBindGooglezDialog = ref(false)
const isShowBindGoogle = computed(() =>{
  return userInfo.value && userInfo.value.user_type * 1 === 100 && userInfo.value.is_switch_login * 1 === 0 && userInfo.value.is_bind_totp * 1 === 0 && isBindGooglezDialog.value
})
watch(() => isShowBindGoogle.value, (val) => {
  if (val) {
    getGoogleInfo()
  }
})
const googleKeyLoading = ref(false)
const getGoogleInfo = async() => {
  googleKeyLoading.value = true
  const { data, error } = await bindTotpAsk({
    type: 1
  })
  if (data) {
    googleData.value = data
  } else {
    useCommon.showMsg('error', useCommon.err(error.code, error))
  }
  googleKeyLoading.value = false
}
const bindGoogle = async(code) => {
  const { data, error } = await bindTotpConfirm1({
    code
  })
  if (data) {
    await getUserInfoAction()
    useCommon.showMsg('success', t('绑定谷歌验证码成功'))
  } else {
    useCommon.showMsg('error', useCommon.err(error.code, error))
  }
}
const cancelFun = async() => {
  await useCommon.loginout()
  isBindGooglezDialog.value = false
}
const getLangList = async(lang) => {
  if (typeof window !== 'undefined' && window.localStorage) {
    const cachedData = !localStorage.getItem(`lang_${lang}`) ? '' : localStorage.getItem(`lang_${lang}`)
    console.log(localStorage.getItem(`lang_${lang}`), 'djeidjeidiejeij')
    if (cachedData && cachedData !== 'undefined') {
      languageObj.value = JSON.parse(cachedData)
      i18n.setLocaleMessage(lang, languageObj.value)
      loading.value = false
    } else {
      loading.value = true
    }
    const { data } = await getI18nInfoApi({
      lang,
      platform: 2,
      module: 1
    })
    if (data) {
      languageObj.value = data.data[1]
      localStorage.setItem(`lang_${lang}`, JSON.stringify(languageObj.value))  // Store in localStorage
      i18n.setLocaleMessage(lang, languageObj.value)
      loading.value = false
    }
  }
}
const curPage = computed(() => {
  const pathAry = router.currentRoute.value.path.split('/')
  pathAry.splice(1, 1)
  if (pathAry.join('/').endsWith('/')) {
    return pathAry.join('/').replace(/\/$/g, '')
  }
  return pathAry.join('/')
})
watch(() => locale.value, async(lang) => {
  await getLangList(lang)
}, {
  immediate: true
})
watch(() => userInfo.value, () => {
  isBindGooglezDialog.value = userInfo.value && userInfo.value.user_type * 1 === 100 && userInfo.value.is_switch_login * 1 === 0 && userInfo.value.is_bind_totp * 1 === 0
})
const __ = (key: string, fallback?: string): string => {
   const keys = key.split('.');
  // 首先尝试从动态加载的语言包中获取翻译
  const translation = t(key);
  if (translation !== key) {
    return translation;
  }
  const staticTranslations = i18nLocales[locale.value];
  let result = staticTranslations;
  for (const k of keys) {
    result = result?.[k];
    if (!result) break;
  }
  return result || fallback || key
}
const headTitle = computed(() => __('head.title'));
const headDescription = computed(() => __('head.description'));
const headKeywords = computed(() => __('head.keywords'));
onBeforeMount(async() => {
  await getCurrencyRate()
  await getMessageError()
  await getLangList(locale.value)
  setInterval(async () => {
    await getLangList(locale.value)
  }, 60 * 60 * 1000)
})
useHead({
  title: () => headTitle.value,
  titleTemplate: `${t('KTX')} | %s`,
  meta: [
    { hid: 'description', name: 'description', content: headDescription.value },
    { hid: 'keywords', name: 'keywords', content: headKeywords.value }
  ]
})
</script>
<style lang="scss" scoped>
</style>