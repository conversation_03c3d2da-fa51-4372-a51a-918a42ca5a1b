// import { commonStore } from '~/stores/commonStore'
// const store = commonStore()
// const { languageObj } = storeToRefs(store)
export default [
  {
    code: 'en',
    label: 'English',
    zendesk: 'en-us'
  },
  {
    code: 'ja',
    label: '日本語',
    zendesk: 'ja'
  },
  {
    code: 'ko',
    label: '한국어',
    zendesk: 'en-us'
  },
  {
    code: 'zh-Hant',
    label: '繁体中文',
    zendesk: 'zh-cn'
  },
  {
    code: 'zh',
    label: '简体中文',
    zendesk: 'zh-cn'
  },
].map(lang => ({ ...lang }))
