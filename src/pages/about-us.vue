<template>
  <Header />
  <div class="about-us-container">
    <div class="about-us-wrapper">
      <div class="banner-content">
        <div class="banner-center flex-box flex-column space-center align-start">
          <h1>{{ $t('赋能全球交易者') }}</h1>
          <h1>{{ $t('重塑加密未来​') }}</h1>
          <p class="font-size-18 fit-tc-secondary pd-t24">{{ $t('打造下一代加密货币交易生态') }}</p>
        </div>
      </div>
      <div class="about-us-content">
        <div class="def-text font-size-14 fit-tc-primary">
          {{ $t('KTX是一家面向全球的创新型加密货币交易所，致力于提供顶级交易体验、卓越安全保障和无缝资产管理。我们深知加密行业的快速发展和多元需求，因此构建了一个高度自由、极致流畅、对MEME生态友好的交易环境，助力用户掌控市场先机，释放数字资产的无限潜力。') }}
        </div>
        <div class="flex-box info-list space-between align-start">
          <div class="info-item">
            <div class="title-box">
              <div class="title-text">
                {{ $t('愿景') }}
                <div class="title-bg"></div>
              </div>
            </div>
            <div class="info-title">{{ $t('构建全球领先的加密货币交易平台​') }}</div>
            <div class="info-text">{{ $t('我们致力于打造一个安全、透明、高效的加密货币交易平台，促进全球数字资产的自由流通，推动区块链技术的广泛应用，助力全球金融体系的创新与发展。') }}</div>
          </div>
          <div class="info-item">
            <div class="title-box">
              <div class="title-text">
                {{ $t('使命') }}
                <div class="title-bg"></div>
              </div>
            </div>
            <div class="info-title">{{ $t('为用户提供卓越的交易体验​') }}</div>
            <div class="info-text">
              <p>{{ $t('用户至上：以用户需求为导向，提供优质的服务和支持，确保用户的满意度和信任度。') }}</p>
              <p>{{ $t('技术创新：持续研发和引进先进技术，提升平台性能和安全性，保持行业领先地位。') }}</p>
              <p>{{ $t('生态共建：与行业伙伴合作，共同推动区块链生态的发展，实现共赢。') }}</p>
            </div>
          </div>
        </div>
        <div class="hx-container">
          <div class="title-box">
            <div class="title-text">
              {{ $t('核心优势') }}
              <div class="title-bg"></div>
            </div>
          </div>
          <div class="sub-title font-size-18 fit-tc-primary pd-t16">{{ $t('领先市场的交易体验') }}</div>
          <div class="hx-content">
            <div class="hx-item">
              <dl>
                <dt>
                  <img src="~@/assets/images/about-us/img-1.png" />
                </dt>
                <dd>
                  <h3>{{ $t('MEME 生态最友好的交易所') }}</h3>
                  <p>{{ $t('首发&优先上架热门MEME币种，提供充足流动性，梗文化运营以及土狗预审机制，让CEX不再“高冷”') }}</p>
                </dd>
              </dl>
            </div>
            <div class="hx-item">
              <dl>
                <dt>
                  <img src="~@/assets/images/about-us/img-2.png" />
                </dt>
                <dd>
                  <h3>{{ $t('统一账户系统，灵活高效') }}</h3>
                  <p>{{ $t('独创的统一账户架构，支持一人管理多个交易账户高达20个，助力专业交易者优化资产配置、精准执行交易策略。') }}</p>
                </dd>
              </dl>
            </div>
            <div class="hx-item">
              <dl>
                <dt>
                  <img src="~@/assets/images/about-us/img-3.png" />
                </dt>
                <dd>
                  <h3>{{ $t('极速撮合，毫秒级成交') }}</h3>
                  <p>{{ $t('采用高性能撮合引擎，确保市场流动性充足、订单执行毫秒级完成，让交易顺畅无延迟。') }}</p>
                </dd>
              </dl>
            </div>
            <div class="hx-item">
              <dl>
                <dt>
                  <img src="~@/assets/images/about-us/img-4.png" />
                </dt>
                <dd>
                  <h3>{{ $t('极致安全守护') }}</h3>
                  <p class="dot-p">{{ $t('多层次加密存储&冷钱包技术，最大程度保障用户资产安全') }}</p>
                  <p class="dot-p">{{ $t('严格风控体系，实时监控异常交易，防止市场操纵') }}</p>
                  <p class="dot-p">{{ $t('符合全球合规要求，确保用户资产交易合法合规') }}</p>
                </dd>
              </dl>
            </div>
            <div class="hx-item">
              <dl>
                <dt>
                  <img src="~@/assets/images/about-us/img-5.png" />
                </dt>
                <dd>
                  <h3>{{ $t('全球覆盖，24/7 全天候支持') }}</h3>
                  <p>{{ $t('专业客服团队全年无休，支持多语言服务，确保用户随时随地获得及时响应。') }}</p>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="join-us-container">
          <div class="title-box">
            <div class="title-text">
              {{ $t('加入我们，探索无限可能！') }}
              <div class="title-bg"></div>
            </div>
          </div>
          <div class="join-us-cont">
            <p>{{ $t('无论你是专业交易员、长期投资者，还是初入市场的加密爱好者，KTX都为你提供最优质的交易工具和市场资源。') }}</p>
            <p>{{ $t('让每一次交易都成为价值的流动，KTX不止于交易，更在创造未来！') }}</p>
            <el-button type="primary" v-if="!isLogin" @click="useCommon.openRegister()">{{ $t('立即注册') }}</el-button>
            <el-button type="primary" v-else @click="router.push(`/${locale}/exchange/BTC_USDT`)">{{ $t('去交易') }}</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <Footer />
</template>
<script lang="ts" setup>
  import { ElButton } from 'element-plus'
  import { useUserStore } from '~/stores/useUserStore'
  import { useCommonData } from '~/composables/index'
  const useCommon = useCommonData()
  const store = useUserStore()
  const { isLogin } = storeToRefs(store)
  const router = useRouter()
  const { locale, t } = useI18n()
</script>
<style lang="scss" scoped>
  .about-us-container{
    .about-us-wrapper{
      .banner-content{
        max-width:1920px;
        width:100%;
        margin:0 auto;
        height:420px;
        @include get-img('~/assets/images/about-us/banner-bg-light.png', '~/assets/images/about-us/banner-bg-dark.png');
        background-size: 333px auto;
        background-repeat:no-repeat;
        background-position:left center;
        @include bg-color(bg-primary);
        .banner-center{
          width:100%;
          margin:0 auto;
          height:420px;
          @include get-img('~/assets/images/about-us/banner-img-light.png', '~/assets/images/about-us/banner-img-dark.png');
          background-size: 50% auto;
          background-repeat:no-repeat;
          background-position:100% center;
          h1{
            padding-left:24%;
            font-size:40px;
            @include color(tc-primary);
          }
          p{
            padding-left:24%;
          }
        }
      }
      .title-box{
        .title-text{
          display:inline-block;
          position:relative;
          padding:0 10px;
          font-size:40px;
          z-index:9;
          @include color(tc-primary);
          .title-bg{
            position:absolute;
            left:0;
            right:0;
            bottom:6px;
            height:23px;
            width:100%;
            z-index:-1;
            background:rgba(240, 185, 11, 0.5);
          }
        }
      }
      .about-us-content{
        width:90%;
        max-width:1700px;
        margin:0 auto;
        .def-text{
          padding-top:44px;
        }
        .info-list{
          flex-wrap:wrap;
          .info-item{
            padding-top:40px;
            width:48%;
            .info-title{
              font-size:18px;
              padding-top:14px;
              @include color(tc-primary);
            }
            .info-text{
              font-size:14px;
              padding-top:8px;
              @include color(tc-secondary);
              padding-bottom:40px;
              line-height:24px;
            }
          }
        }
        .hx-content{
          display:flex;
          flex-wrap:wrap;
          padding-top:32px;
          .hx-item{
            width:calc(50% - 12px);
            border:1px solid;
            border-radius:12px;
            margin-right:24px;
            margin-bottom:24px;
            @include border-color(border);
            &:nth-child(2n){
              margin-right:0;
            }
            dl{
              padding: 24px;
              display:flex;
              align-items:center;
              dt{
                width:100px;
                height:100px;
                margin-right:24px;
                img{
                  width:100%;
                  height:auto;
                }
              }
              dd{
                flex:1;
                h3{
                  font-size:24px;
                  padding-bottom:12px;
                  @include color(tc-primary);
                }
                p{
                  font-size:14px;
                  line-height:24px;
                  position:relative;
                  @include color(tc-secondary);
                  &.dot-p{
                    padding-left:8px;
                    &:before{
                      content: '';
                      position:absolute;
                      top:50%;
                      margin-top:-2px;
                      left:0;
                      width:4px;
                      height:4px;
                      border-radius:50%;
                      @include bg-color(tc-secondary);
                    }
                  }
                }
              }
            }
          }
        }
      }
      .join-us-container{
        padding-top:20px;
        .join-us-cont{
          padding-top:32px;
          padding-bottom:44px;
          p{
            font-size:18px;
            padding-bottom:8px;
            @include color(tc-primary);
          }
          .el-button{
            width:280px;
            height:40px;
            margin-top:20px;
          }
        }
      }
    }
  }
  @include mb {
    .about-us-container{
      .about-us-wrapper{
        .banner-content{
          max-width:100%;
          width:100%;
          margin:0 auto;
          height:auto;
          @include get-img('~/assets/images/about-us/banner-bg-light.png', '~/assets/images/about-us/banner-bg-dark.png');
          background-size: 112px auto;
          background-repeat:no-repeat;
          background-position:left 40px;
          @include bg-color(bg-primary);
          .banner-center{
            width:100%;
            margin:0 auto;
            height:auto;
            @include get-img('~/assets/images/about-us/banner-img-light.png', '~/assets/images/about-us/banner-img-dark.png');
            background-size: 70% auto;
            background-repeat:no-repeat;
            background-position:right -60px;
            padding-top:40%;
            h1{
              padding-left:0;
              font-size:28px;
              text-align:center;
              width:90%;
              margin:0 auto;
              @include color(tc-primary);
            }
            p{
              padding-left:0;
              text-align:center;
              width:90%;
              margin:0 auto;
            }
          }
        }
        .title-box{
          .title-text{
            font-size:24px;
            .title-bg{
              bottom:4px;
              height:14px;
            }
          }
        }
        .about-us-content{
          width:90%;
          max-width:90%;
          margin:0 auto;
          .def-text{
            padding-top:20px;
          }
          .info-list{
            flex-wrap:wrap;
            &.flex-box{
              display:block;
            }
            .info-item{
              padding-top:20px;
              width:100%;
              .info-title{
                font-size:16px;
              }
              .info-text{
                padding-bottom:0;
              }
            }
          }
          .hx-container{
            padding-top:20px;
            .sub-title{
              &.font-size-18{
                font-size:16px !important;
                line-height:16px !important;
              }
              &.pd-t16{
                padding-top:14px !important;
              }
            }
          }
          .hx-content{
            display:block;
            padding-top:20px;
            .hx-item{
              width:100%;
              border-radius:8px;
              margin-right:0;
              margin-bottom:20px;
              dl{
                padding: 16px;
                display:flex;
                align-items:center;
                dt{
                  width:50px;
                  height:50px;
                  margin-right:12px;
                }
                dd{
                  flex:1;
                  h3{
                    font-size:18px;
                    padding-bottom:8px;
                    @include color(tc-primary);
                  }
                  p{
                    font-size:12px;
                    line-height:20px;
                    &.dot-p{
                      &:before{
                        top:10px;
                        left:0;
                      }
                    }
                  }
                }
              }
            }
          }
        }
        .join-us-container{
          .join-us-cont{
            padding-top:18px;
            p{
              font-size:14px;
            }
            .el-button{
              width:100%;
            }
          }
        }
      }
    }
  }
</style>