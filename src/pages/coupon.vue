<template>
  <Header modeType="coupon" />
  <div class="coupon-container">
    <div class="coupon-wrapper">
      <div class="coupon-banner">
        <div class="banner-center flex-box space-between">
          <div class="banner-left">
            <h1>{{ $t('参与交易赢取您的') }}<span class="fit-theme">{{ $t('专属优惠') }}</span></h1>
            <h2>{{ $t('参与交易赢取您的') }}<div class="fit-theme">{{ $t('专属优惠') }}</div></h2>
            <p>{{ $t('限量') }}<span class="fit-theme">{{ $t('卡券') }}</span>、{{ $t('丰厚') }}<span class="fit-theme">{{ $t('奖励') }}</span></p>
            <p>{{ $t('快来赢取您的专属福利！') }}</p>
          </div>
          <div class="banner-right">
            <img src="~/assets/images/coupon/banner-img.png" />
          </div>
        </div>
      </div>
      <NuxtPage/>
    </div>
  </div>
  <Footer />
</template>
<script lang="ts" setup>
  import { useUserStore } from '~/stores/useUserStore'
  import { commonStore } from '~/stores/commonStore'
  const useCommon = useCommonData()
  const useStore = useUserStore()
  const { isLogin, userInfo, isVisibleUserInfo } = storeToRefs(useStore)
  const { getUserInfoAction } = useStore
  // watch(() => isLogin.value, async(val) => {
  //   if (!val) {
  //     useCommon.openLogin()
  //     return false
  //   }
  // })
  // onMounted(async() => {
  //   if (!isLogin.value) {
  //     useCommon.openLogin()
  //     return false
  //   }
  // })
</script>
<style lang="scss">
  @import url('@/assets/style/coupon.scss');
</style>