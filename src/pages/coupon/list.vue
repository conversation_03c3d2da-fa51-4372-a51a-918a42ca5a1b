<template>
  <div class="coupon-list-container">
    <div class="coupon-list-title flex-box space-between">
      <ul class="flex-box coupon-nav-list">
        <li :class="{'active': status === ''}" @click="status = ''">{{ $t('全部') }}</li>
        <li :class="{'active': status * 1 === 1}" @click="status = 1">{{ $t('我的卡券') }}</li>
      </ul>
      <div class="coupon-title-right flex-box">
         <el-select v-model="type"
          :empty-values="[null, undefined]"
          :value-on-clear="null"
          clearable
          placeholder="Select">
          <el-option v-for="(item, index) in typeList" :key="item.value" :value="item.value" :label="item.label">
            {{ item.label }}
          </el-option>
        </el-select>
      </div>
    </div>
    <div v-loading="isLoading" class="coupon-list-cont">
      <div v-if="!isLoading && canUseCouponList.length > 0" class="coupon-list-wrap flex-box flex-wrap">
        <div v-for="(item, index) in canUseCouponList" class="coupon-item-wrap" :class="{'disabled': item.show_status * 1 === 2 || item.show_status * 1 === 3 || (item.show_status * 1 === 0 && item.remain_quantity * 1 === 0) || item.show_status * 1 === 4 }">
          <div class="coupon-item-cont">
            <div v-if="item.show_status * 1 === 0 && item.remain_quantity * 1 === 0 " class="tag no-has">
              {{ $t('已领完') }}
            </div>
            <div v-if="item.show_status * 1 === 4" class="tag no-has">
              {{ $t('已作废') }}
            </div>
            <div v-if="item.show_status * 1 === 3" class="tag no-has">
              {{ $t('已过期') }}
            </div>
            <dl>
              <dt class="flex-box flex-column space-center">
                <div class="bg-wrap" :class="voucherTypeClass[item.voucher_type]"></div>
                <div class="text-box">
                  <span v-if="item.voucher_type * 1 === 1 && item.show_status * 1 === 0" class="fit-theme font-size-14">{{ $t('最高') }}</span>
                  <h3 v-if="item.voucher_type * 1 === 1">{{ format(item.show_status * 1 === 0 ? item.face_value : item.real_face_value, 4, true) }}</h3>
                  <h3 v-else>{{ format(item.face_value, 4, true) }}</h3>
                  <p>USDT</p>
                </div>
              </dt>
              <dd class="flex-box flex-column space-start align-start">
                <div class="info-title flex-box">
                  <div class="info-icon" :class="voucherTypeClass[item.voucher_type]"></div>
                  {{ voucherTypeObj[item.voucher_type] }}
                </div>
                <div class="info-desc">
                  <template v-if="item.remark !== '{}' && item.remark">
                    {{ getTxt(item.remark, 'desc') }}
                  </template>
                  <template v-else>
                    <template v-if="item.voucher_type * 1 === 1">
                      {{ $t('配资券可与真实资金合并使用，用于合约开仓交易，提升可用资金。支持按比例抵扣亏损，有效降低风险。') }}
                    </template>
                    <template v-if="item.voucher_type * 1 === 2">
                      {{ $t('本券为合约交易体验金，可用于开仓交易，提升初始资金，体验金不可提现或划转。') }}
                    </template>
                    <template v-if="item.voucher_type * 1 === 3">
                      {{ $t('充值券可用于合约交易开仓，不可用于抵扣手续费、资金费用或亏损。') }}
                    </template>
                    <template v-if="item.voucher_type * 1 === 4">
                      {{ $t('手续费返现券适用于合约交易，可在产生手续费时享受部分返还，用于抵扣交易成本。') }}
                    </template>
                    <template v-if="item.voucher_type * 1 === 5">
                      {{ $t('亏损补偿金适用于合约交易中的亏损补偿，有效缓冲交易风险，仅对已平仓亏损订单生效。') }}
                    </template>
                  </template>
                </div>
                <div class="info-btn-box flex-box space-between">
                  <div>
                    <p>{{ $t('有效期至') }}：{{ timeFormat(item.valid_end, 'yy-MM-dd hh:mm:ss') }}</p>
                    <p v-if="item.voucher_type * 1 === 5 && item.show_status * 1 === 2" class="font-size-12 fit-tc-secondary">{{ $t('已使用') }} {{ format(item.used_value, 4, true) }} USDT</p>
                  </div>
                  <el-button v-if="item.show_status * 1 === 1" type="primary" :loading="isGetLoading[item.id]" @click="ToUseFun(item)">{{ item.voucher_type * 1 === 5 ? $t('领取补偿') : $t('去使用') }}</el-button>
                  <el-button :loading="isGetLoading[item.id]" v-if="item.show_status * 1 === 0" type="primary" :disabled="item.remain_quantity * 1 === 0" @click="getCouponFun(item)">{{ $t('立即领取') }}</el-button>
                  <el-button :disabled="true" v-if="item.show_status * 1 === 2" type="primary">{{ $t('已使用') }}</el-button>
                  <el-button :disabled="true" v-if="item.show_status * 1 === 3" type="primary">{{ $t('已过期') }}</el-button>
                  <el-button :disabled="true" v-if="item.show_status * 1 === 4" type="primary">{{ $t('已作废') }}</el-button>
                </div>
                <div class="info-rule flex-box space-between" @click="rulusShowFun(item)">
                  {{ $t('详细说明') }}
                  <MonoRightArrowShort size="14" class="fit-tc-secondary" />
                </div>
              </dd>
            </dl>
          </div>
        </div>
      </div>
      <div v-if="!isLoading && noUseCouponList.length > 0" class="coupon-list-wrap flex-box flex-wrap">
        <div v-for="(item, index) in noUseCouponList" class="coupon-item-wrap" :class="{'disabled': item.show_status * 1 === 2 || item.show_status * 1 === 3 || (item.show_status * 1 === 0 && item.remain_quantity * 1 === 0) || item.show_status * 1 === 4 }">
          <div class="coupon-item-cont">
            <div v-if="item.show_status * 1 === 0 && item.remain_quantity * 1 === 0 " class="tag no-has">
              {{ $t('已领完') }}
            </div>
            <div v-if="item.show_status * 1 === 4" class="tag no-has">
              {{ $t('已作废') }}
            </div>
            <div v-if="item.show_status * 1 === 3" class="tag no-has">
              {{ $t('已过期') }}
            </div>
            <dl>
              <dt class="flex-box flex-column space-center">
                <div class="bg-wrap" :class="voucherTypeClass[item.voucher_type]"></div>
                <div class="text-box">
                  <span v-if="item.voucher_type * 1 === 1 && item.show_status * 1 === 0" class="fit-theme font-size-14">{{ $t('最高') }}</span>
                  <h3 v-if="item.voucher_type * 1 === 1">{{ format(item.show_status * 1 === 0 ? item.face_value : item.real_face_value, 4, true) }}</h3>
                  <h3 v-else>{{ format(item.face_value, 4, true) }}</h3>
                  <p>USDT</p>
                </div>
              </dt>
              <dd class="flex-box flex-column space-start align-start">
                <div class="info-title flex-box">
                  <div class="info-icon" :class="voucherTypeClass[item.voucher_type]"></div>
                  {{ voucherTypeObj[item.voucher_type] }}
                </div>
                <div class="info-desc">
                  <template v-if="item.remark !== '{}' && item.remark">
                    {{ getTxt(item.remark, 'desc') }}
                  </template>
                  <template v-else>
                    <template v-if="item.voucher_type * 1 === 1">
                      {{ $t('配资券可与真实资金合并使用，用于合约开仓交易，提升可用资金。支持按比例抵扣亏损，有效降低风险。') }}
                    </template>
                    <template v-if="item.voucher_type * 1 === 2">
                      {{ $t('本券为合约交易体验金，可用于开仓交易，提升初始资金，体验金不可提现或划转。') }}
                    </template>
                    <template v-if="item.voucher_type * 1 === 3">
                      {{ $t('充值券可用于合约交易开仓，不可用于抵扣手续费、资金费用或亏损。') }}
                    </template>
                    <template v-if="item.voucher_type * 1 === 4">
                      {{ $t('手续费返现券适用于合约交易，可在产生手续费时享受部分返还，用于抵扣交易成本。') }}
                    </template>
                    <template v-if="item.voucher_type * 1 === 5">
                      {{ $t('亏损补偿金适用于合约交易中的亏损补偿，有效缓冲交易风险，仅对已平仓亏损订单生效。') }}
                    </template>
                  </template>
                </div>
                <div class="info-btn-box flex-box space-between">
                  <div>
                    <p>{{ $t('有效期至') }}：{{ timeFormat(item.valid_end, 'yy-MM-dd hh:mm:ss') }}</p>
                    <p v-if="item.voucher_type * 1 === 5 && item.show_status * 1 === 2" class="font-size-12 fit-tc-secondary">{{ $t('已使用') }} {{ format(item.used_value, 4, true) }} USDT</p>
                  </div>
                  <el-button v-if="item.show_status * 1 === 1" type="primary" :loading="isGetLoading[item.id]" @click="ToUseFun(item)">{{ item.voucher_type * 1 === 5 ? $t('领取补偿') : $t('去使用') }}</el-button>
                  <el-button :loading="isGetLoading[item.id]" v-if="item.show_status * 1 === 0" type="primary" :disabled="item.remain_quantity * 1 === 0" @click="getCouponFun(item)">{{ $t('立即领取') }}</el-button>
                  <el-button :disabled="true" v-if="item.show_status * 1 === 2" type="primary">{{ $t('已使用') }}</el-button>
                  <el-button :disabled="true" v-if="item.show_status * 1 === 3" type="primary">{{ $t('已过期') }}</el-button>
                  <el-button :disabled="true" v-if="item.show_status * 1 === 4" type="primary">{{ $t('已作废') }}</el-button>
                </div>
                <div class="info-rule flex-box space-between" @click="rulusShowFun(item)">
                  {{ $t('详细说明') }}
                  <MonoRightArrowShort size="14" class="fit-tc-secondary" />
                </div>
              </dd>
            </dl>
          </div>
        </div>
      </div>
      <div v-if="!isLoading && couponList.length === 0" style="height:500px;">
        <BoxNoData :text="$t('暂无数据')" />
      </div>
    </div>
  </div>
  <el-dialog v-model="isShowRules" :title="voucherTypeObj[currentData.voucher_type]" width="480px" @close="isShowRules = false">
    <div class="coupon-rule-text">
      <template v-if="currentData.voucher_type * 1 === 1"><!-- 配资券 -->
        <p>{{ $t('配资券可与真实资金合并使用，用于合约开仓交易，提升可用资金。支持按比例抵扣亏损，有效降低风险。') }}</p>
        <div class="pd-t12"></div>
        <p v-if="Number(currentData.return_ratio) > 0" class="flex-box">
          <el-tooltip placement="top">
            <template #content>
              <div style="width:400px;">
                <p>{{ $t('返现比例说明') }}</p>
                <div class="pd-12">
                  <p>{{ $t('返现金额将根据「充值金额」与「净转入金额」（即划入金额 - 划出金额）这两者中较小的一方作为有效金额计算。例如') }}:</p>
                  <p class="pd-l12">· {{ $t('若用户充值 100U，划入 80U，划出 50U，则净转入 = 80U - 50U = 30U') }}</p>
                  <p class="pd-l12">· {{ $t('有效金额 = min(充值 100U，净转入 30U) = 30U') }}</p>
                  <p class="pd-l12">· {{ $t('若返现比例为 10%，则可获得返现券金额为：30U × 10% = 3U 若有效金额 小于 1U，将 不满足领取条件') }}</p>
                </div>
              </div>
            </template>
            <div class="flex-box cursor-pointer">
              {{ $t('返现比例') }}
              <MonoWarn size="14" class="fit-tc-secondary mg-l4" />
            </div>
          </el-tooltip>
          ：{{ format(currentData.return_ratio * 100, 2, true) }}%
        </p>
        <p v-if="Number(currentData.loss_discount_ratio) > 0">{{ $t('亏损抵扣比例') }}：{{ format(currentData.loss_discount_ratio * 100, 2, true) }}%</p>
        <p v-if="Number(currentData.recall_on_transfer) > 0">{{ $t('划转回收比例') }}：{{ format(currentData.recall_on_transfer * 100, 2, true) }}%</p>
        <p v-if="Number(currentData.recall_on_withdraw) > 0">{{ $t('提现回收比例') }}：{{ format(currentData.recall_on_withdraw * 100, 2, true) }}%</p>
        <h3 class="coupon-rule-title">{{ $t('限制说明') }}</h3>
        <p class="fit-tc-secondary">* {{ $t('需与本金配合使用，无法单独使用') }}</p>
        <p class="fit-tc-secondary">* {{ $t('不可用于手续费抵扣') }}</p>
        <p class="fit-tc-secondary">* {{ $t('不可提现') }}</p>
        <p class="fit-tc-secondary">* {{ $t('有效期内使用有效，逾期作废') }}</p>
      </template>
      <template v-if="currentData.voucher_type * 1 === 2"><!-- 合约体验券 -->
        <p>{{ $t('本券为合约交易体验金，可用于开仓交易，提升初始资金，体验金不可提现或划转。') }}</p>
        <div class="pd-t12"></div>
        <p v-if="Number(currentData.loss_discount_ratio) > 0">{{ $t('亏损抵扣比例') }}：{{ format(currentData.loss_discount_ratio * 100, 2, true) }}%</p>
        <p v-if="Number(currentData.fee_discount_ratio) > 0">{{ $t('手续费抵扣比例') }}：{{ format(currentData.fee_discount_ratio * 100, 2, true) }}%</p>
        <p v-if="Number(currentData.fund_discount_ratio) > 0">{{ $t('资金费率抵扣比例') }}：{{ format(currentData.fund_discount_ratio * 100, 2, true) }}%</p>
        <p v-if="Number(currentData.recall_on_transfer) > 0">{{ $t('划转回收比例') }}：{{ format(currentData.recall_on_transfer * 100, 2, true) }}%</p>
        <p v-if="Number(currentData.recall_on_withdraw) > 0">{{ $t('提现回收比例') }}：{{ format(currentData.recall_on_withdraw * 100, 2, true) }}%</p>
        <h3 class="coupon-rule-title">{{ $t('限制说明') }}</h3>
        <p class="fit-tc-secondary">* {{ $t('体验金本身不可提取') }}</p>
        <p class="fit-tc-secondary">* {{ $t('有效期内使用有效，逾期作废') }}</p>
      </template>
      <template v-if="currentData.voucher_type * 1 === 3"><!-- 充值券 -->
        <p>{{ $t('充值券可用于合约交易开仓，不可用于抵扣手续费、资金费用或亏损。') }}</p>
      </template>
      <template v-if="currentData.voucher_type * 1 === 4"><!-- 手续费返现券 -->
        <p>{{ $t('手续费返现券适用于合约交易，可在产生手续费时享受部分返还，用于抵扣交易成本。') }}</p>
        <p>{{ $t('持券交易更划算，降低您的交易成本！') }}</p>
        <div class="pd-t12"></div>
        <p v-if="Number(currentData.fee_discount_ratio) > 0">{{ $t('抵扣比例') }}：{{ format(currentData.fee_discount_ratio * 100, 2, true) }}%</p>
        <p v-if="Number(currentData.recall_on_transfer) > 0">{{ $t('划转回收比例') }}：{{ format(currentData.recall_on_transfer * 100, 2, true) }}%</p>
        <p v-if="Number(currentData.recall_on_withdraw) > 0">{{ $t('提现回收比例') }}：{{ format(currentData.recall_on_withdraw * 100, 2, true) }}%</p>
        <h3 class="coupon-rule-title">{{ $t('限制说明') }}</h3>
        <p class="fit-tc-secondary">* {{ $t('可用于合约手续费返现') }}</p>
        <p class="fit-tc-secondary">* {{ $t('不可抵扣资金费用与亏损') }}</p>
        <p class="fit-tc-secondary">* {{ $t('有效期内使用有效，逾期作废') }}</p>
      </template>
      <template v-if="currentData.voucher_type * 1 === 5"><!-- 亏损补偿券 -->
        <p>{{ $t('亏损补偿金适用于合约交易中的亏损补偿，有效缓冲交易风险，仅对已平仓亏损订单生效。') }}</p>
        <div class="pd-t12"></div>
        <p v-if="Number(currentData.loss_discount_ratio) > 0">{{ $t('补偿比例') }}：{{ format(currentData.loss_discount_ratio * 100, 2, true) }}%</p>
        <p v-if="Number(currentData.recall_on_transfer) > 0">{{ $t('划转回收比例') }}：{{ format(currentData.recall_on_transfer * 100, 2, true) }}%</p>
        <p v-if="Number(currentData.recall_on_withdraw) > 0">{{ $t('提现回收比例') }}：{{ format(currentData.recall_on_withdraw * 100, 2, true) }}%</p>
        <h3 class="coupon-rule-title">{{ $t('限制说明') }}</h3>
        <p class="fit-tc-secondary">* {{ $t('平仓亏损可触发补偿返还') }}</p>
        <p class="fit-tc-secondary">* {{ $t('未平仓、盈利、手续费不予补偿') }}</p>
        <p class="fit-tc-secondary">* {{ $t('使用其他卡券（如体验金）产生的亏损不予补偿') }}</p>
        <p class="fit-tc-secondary">* {{ $t('该券仅能领取补偿一次') }}</p>
        <p class="fit-tc-secondary">* {{ $t('有效期内使用有效，逾期作废') }}</p>
        <p class="fit-tc-secondary">*{{ $t('亏损订单时间') }}：{{ timeFormat(currentData.loss_order_start) }} - {{ timeFormat(currentData.loss_order_end) }}</p>
      </template>
      <p class="fit-tc-secondary" v-if="currentData.kyc_country">* {{ $t('国家或地区') }}：{{ selectedCountryInfo[locale] || selectedCountryInfo['en'] }}</p>
      <p class="fit-tc-secondary" v-if="currentData.kyc_required * 1 === 1">* {{ $t('需要完成KYC') }}</p>
      <p class="fit-tc-secondary" v-if="currentData.register_start">* {{ $t('用户注册时间') }}：{{ timeFormat(currentData.register_start) }} - {{ timeFormat(currentData.register_end) }}</p>
      <p class="fit-tc-secondary" v-if="Number(currentData.profit_required) > 0">* {{$t('盈利额要求达到') }}：{{ currentData.profit_required }}</p>
      <p class="fit-tc-secondary" v-if="Number(currentData.trade_value_required) > 0">* {{$t('交易额要求达到') }}：{{ currentData.trade_value_required }}</p>
      <p class="fit-tc-secondary" v-if="Number(currentData.deposit_required) > 0">* {{$t('充值额要求达到') }}：{{ currentData.deposit_required }}</p>
      <h3 class="coupon-rule-title">{{ $t('有效期') }}</h3>
      <p class="fit-tc-secondary">{{ timeFormat(currentData.valid_start) }} - {{ timeFormat(currentData.valid_end) }}</p>
    </div>
    <h3 class="coupon-rule-title">{{ $t('使用规则') }}</h3>
    <div class="coupon-rule-text fit-tc-secondary">
      {{ getTxt(currentData.rule, 'name') || '--' }}
    </div>
    <el-button type="primary" @click="isShowRules = false" class="mg-t24">{{ $t('明白了') }}</el-button>
  </el-dialog>
  <el-dialog v-model="isShowStatus" :title="statusTitle" width="480px" @close="isShowStatus = false">
    <span v-if="statusTitle === $t('卡券领取成功')" class="font-size-14 fit-tc-secondary">{{ $t('{couponTxt}领取成功，现在就去使用吧。', { couponTxt: voucherTypeObj[curCoupon.voucher_type] }) }}</span>
    <template v-if="statusTitle === $t('卡券领取失败')">
      <p class="font-size-16 fit-tc-primary">{{ $t('{couponTxt}领取失败', { couponTxt: voucherTypeObj[curCoupon.voucher_type] }) }}</p>
      <p class="font-size-14 fit-tc-secondary mg-t12">{{ $t('当前您不符合：') }}</p>
      <p class="font-size-14 fit-tc-secondary">{{ errorTxt }}</p>
    </template>
    <el-button class="mg-t24" v-if="statusTitle === $t('卡券领取成功')" type="primary" @click="ToUseFun1(curCoupon)">{{ $t('去使用') }}</el-button>
    <el-button class="mg-t24" v-if="statusTitle === $t('卡券领取失败')" type="primary" @click="isShowStatus = false; errorTxt = ''">{{ $t('明白了') }}</el-button>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElSelect, ElOption, ElButton, ElDialog } from 'element-plus'
  import { timeFormat, format } from '~/utils'
  import { getAttr } from '~/utils/index'
  import countries from '~/utils/countries'
  import MonoWarn from '~/components/common/icon-svg/MonoWarn.vue'
  import { getVoucherList, getVoucherPrize, useLossOrderApi } from '~/api/tt.ts'
  import { useCommonData } from '~/composables/index'
  import { commonStore } from '~/stores/commonStore'
  const useStore = useUserStore()
  const { isLogin, userInfo } = storeToRefs(useStore)
  const useCommon = useCommonData()
  const router = useRouter()
  const { locale, t } = useI18n()
  const status = ref('')
  const type = ref('')
  const couponList = ref([])
  const isLoading = ref(true)
  const canUseCouponList = computed(() => {
    const arr = couponList.value.filter((item) => {
      return (item.show_status * 1 === 0 && item.remain_quantity * 1 !== 0) || item.show_status * 1 === 1
    })
    console.log(arr, 'dhdhuehdhuehueuheuhhu')
    return arr
  })
  const noUseCouponList = computed(() => {
    return couponList.value.filter((item) => {
      return item.show_status * 1 === 2 || item.show_status * 1 === 3 || (item.show_status * 1 === 0 && item.remain_quantity * 1 === 0) || item.show_status * 1 === 4
    })
  })
  const pager = ref({
    page: 1,
    size: 100,
    total: 0
  })
  const voucherTypeClass = ref({
    1: 'peizi-icon',
    2: 'tiyanjin-icon',
    3: 'recharge-icon',
    4: 'fee-icon',
    5: 'kuisun-icon'
  })
  const voucherTypeObj = ref({
    1: t('配资券'),
    2: t('合约体验券'),
    3: t('充值券'),
    4: t('手续费返现券'),
    5: t('亏损补偿券')
  })
  const typeList = ref([
    { label: t('全部'), value: '' },
    { label: t('配资券'), value: 1 },
    { label: t('合约体验券'), value: 2 },
    { label: t('充值券'), value: 3 },
    { label: t('手续费返现券'), value: 4 },
    { label: t('亏损补偿券'), value: 5 }
  ])
  const statusList = ref([
    { label: t('全部'), value: '' },
    { label: t('未领取'), value: 2 },
    { label: t('已领取'), value: 1 }
    // { label: t('已使用'), value: 3 },
    // { label: t('已过期'), value: 4 }
  ])
  const selectedCountryInfo = computed(() => {
    return getAttr(countries, currentData.value.kyc_country) || {}
  })
  const getCouponList = async() => {
    const { data } = await getVoucherList({
      page: pager.value.page,
      size: pager.value.size,
      voucher_type: type.value,
      use_status: status.value
    })
    if (data) {
      pager.total = data.count
      couponList.value = data.rows
    }
    isLoading.value = false
  }
  const getTxt = (item, name) => {
    if (item.includes('[')) {
      const output = JSON.parse(item).reduce((acc, item) => {
        acc[item.lang] = { [name]: item[name] };
        return acc;
      }, {})
      return item.includes('[') ? (output[locale.value] || output['en'] || output['zh'] || {})[name] : '--'
    } else {
      return '--'
    }
  }
  const isGetLoading = ref({})
  const useLossFun = async(item) => {
    isGetLoading.value[item.id] = true
    const { data, error } = await useLossOrderApi({
      id: item.id
    })
    if (data) {
      getCouponList()
      isShowStatus.value = false
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
    isGetLoading.value[item.id] = false
  }
  const isShowStatus = ref(false)
  const curCoupon = ref({})
  const statusTitle = ref('')
  const errorTxt = ref('')
  const getCouponFun = async(item) => {
    if (!isLogin.value) {
      useCommon.openLogin()
      return false
    }
    curCoupon.value = item
    isGetLoading.value[item.id] = true
    const { data, error } = await getVoucherPrize({
      id: item.id
    })
    if (data) {
      getCouponList()
      isShowStatus.value = true
      statusTitle.value = t('卡券领取成功')
    } else {
      errorTxt.value = useCommon.err(error.code, error)
      isShowStatus.value = true
      statusTitle.value = t('卡券领取失败')
    }
    isGetLoading.value[item.id] = false
  }
  const ToUseFun = (item) => {
    if (item.voucher_type * 1 === 5) {
      useLossFun(item)
    } else {
      isShowStatus.value = false
      router.push(`/${locale.value}/future/BTC_USDT_SWAP`)
    }
  }
  const ToUseFun1 = () => {
    isShowStatus.value = false
    router.push(`/${locale.value}/future/BTC_USDT_SWAP`)
  }
  watch(() => status.value, async(val) => {
    if (val * 1 === 1 && !isLogin.value) {
      useCommon.openLogin()
      return false
    }
    isLoading.value = true
    await getCouponList()
  })
  watch(() => type.value, async() => {
    isLoading.value = true
    await getCouponList()
  })
  const currentData = ref({})
  const isShowRules = ref(false)
  const rulusShowFun = (item) => {
    isShowRules.value = true
    currentData.value = item
  }
  onBeforeMount(async () => {
    await getCouponList()
  })
</script>
<style lang="scss">
  @import url('@/assets/style/coupon.scss');
</style>