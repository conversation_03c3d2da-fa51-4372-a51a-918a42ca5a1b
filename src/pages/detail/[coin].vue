<template>
  <Header />
  <div v-loading="isLoading" class="currency-container">
    <div v-if="isHasCoinInfo && !isLoading" class="currency-wrapper flex-box pd-lr24 align-start">
      <div class="currency-wrap-left flex-2 mg-r40">
        <div class="chart-box">
          <div class="flex-box">
            <BoxCoinIcon :icon="coinInfo.icon_url" class="font-size-40 mg-r12"/>
            <span class="font-size-40 fit-tc-primary">{{ curCoin }}({{ $t('价格') }})</span>
            <!-- <span class="mg-l8 fit-tc-secondary font-size-20">({{ curCoin }})</span> -->
            <div class="drop-down">
              <el-dropdown trigger="click">
                <div class="flex-box down-box">
                  <BoxCoinIcon :icon="coinInfo.icon_url" class="font-size-24" />
                  <span class="font-size-16 text">{{ curCoin }}</span>
                  <MonoDownArrowMin size="12" />
                </div>
                <template #dropdown>
                  <el-dropdown-menu :style="`width:400px`">
                    <Markets :markets="markets" :pair="pair" />
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
          <div v-show="isShowKline" class="flex-box fit-tc-primary">
          <span class="font-size-32">{{ useCommon.convert(coinInfo.price_usd, 'USDT') }}</span>
            <span class="font-size-32" :class="{ 'fit-fall': coinInfo.price_change_today.replace(/,/g, '') * 1 < 0, 'fit-rise': coinInfo.price_change_today.replace(/,/g, '') * 1 > 0 }" style="margin:0 5px;">{{ coinInfo.percent_change_display }}</span>
            <!-- <span class="font-size-20 fit-tc-secondary">({{ resolution === 'day' ? $t('1天') : $t('1周') }})</span> -->
          </div>
          <div v-show="isShowKline" class="flex-box nav">
            <div class="list">
              <div v-for="(item, index) in allTimeOptions" :key="index"
                   class="list-item" :class="{ active: resolution === item.resolution }" @click="dateChange(item)"
              >{{
                item.text
              }}</div>
            </div>
            <div class="zy-btn fit-tc-primary" @click="hanldeGoExchange">{{ $t('专业版') }}</div>
          </div>
          <div v-show="isShowKline" ref="myChart" class="myCharts" style="height:320px;"></div>
        </div>
        <!-- 市场信息 -->
        <div class="currency-info">
          <div class="info-title">{{ $t('市场信息') }}</div>
          <div class="flex-box align-start">
            <div class="info-left flex-1 mg-r12">
              <dl>
                <dt>{{ $t('排行') }}</dt>
                <dd>{{ coinInfo.rank }}</dd>
              </dl>
              <dl>
                <dt>{{ $t('市值') }}</dt>
                <dd>{{ coinInfo.market_cap_usd }}</dd>
              </dl>
              <dl>
                <dt>{{ $t('全网24H成交额') }}</dt>
                <dd>{{ coinInfo.volume_24h_usd }}</dd>
              </dl>
              <dl>
                <dt>{{ $t('历史最高价格') }}</dt>
                <dd>{{ format(coinInfo.history_high_price_web, (pairInfo[pair] || {}).price_scale, true) }}</dd>
              </dl>
              <dl>
                <dt>{{ $t('历史最低价格') }}</dt>
                <dd>{{ format(coinInfo.history_low_price_web, (pairInfo[pair] || {}).price_scale, true) }}</dd>
              </dl>
              <dl>
                <dt>{{ $t('流通供应量') }}</dt>
                <dd>{{ coinInfo.available_supply }}</dd>
              </dl>
              <!-- <dl>
                <dt>{{ $t('流通供应量量百分比') }}</dt>
                <dd>{{ coinInfo.supply_ratio }}</dd>
              </dl> -->
              <dl>
                <dt>{{ $t('最大供应量') }}</dt>
                <dd>{{ coinInfo.max_supply }}</dd>
              </dl>
              <!-- <dl>
                <dt>{{ $t('总供应量') }}</dt>
                <dd>{{ coinInfo.total_supply }}</dd>
              </dl> -->
              <dl>
                <dt>{{ $t('流通率') }}</dt>
                <dd>{{ coinInfo.available_supply_ratio }}</dd>
              </dl>
            </div>
            <div class="info-right flex-1">
              <dl>
                <dt>{{ $t('初始价格') }}</dt>
                <dd>{{ coinInfo.start_trading_price_show_web }}</dd>
              </dl>
              <dl>
                <dt>{{ $t('基础链') }}</dt>
                <dd>{{ coinInfo.blockchain }}</dd>
              </dl>
              <dl>
                <dt>{{ $t('核心算法') }}</dt>
                <dd>{{ coinInfo.algorithm }}</dd>
              </dl>
              <dl>
                <dt>{{ $t('交易开始日期') }}</dt>
                <dd>{{ coinInfo.start_trading }}</dd>
              </dl>
              <dl>
                <dt>{{ $t('上架交易所数量') }}</dt>
                <dd>{{ coinInfo.online_exchange_number }}</dd>
              </dl>
              <dl>
                <dt>{{ $t('全球总市值占比') }}</dt>
                <dd>{{ coinInfo.market_cap_share }}</dd>
              </dl>
              <dl>
                <dt>{{ $t('首次发行方式') }}</dt>
                <dd>{{ coinInfo.start_distribution_type || '-' }}</dd>
              </dl>
              <dl>
                <dt>{{ $t('项目启动日期') }}</dt>
                <dd>{{ coinInfo.start_date }}</dd>
              </dl>
            </div>
          </div>
        </div>
        <!-- 信息 -->
        <div class="link-info">
          <div class="info-title">{{ $t('信息') }}</div>
          <div class="address-item flex-box">
            <span class="test font-size-14">{{ $t('官方地址') }}</span>
            <a :href="coinInfo.website" target="_blank">{{ coinInfo.website }}</a>
          </div>
          <div class="address-item flex-box">
            <span class="test font-size-14">{{ $t('白皮书') }}</span>
            <a :href="coinInfo.whitepaper" target="_blank">{{ coinInfo.whitepaper }}</a>
          </div>
          <div class="address-item flex-box">
            <span class="test font-size-14">{{ $t('GitHub') }}</span>
            <a :href="coinInfo.github" target="_blank">{{ coinInfo.github }}</a>
          </div>
        </div>
        <!-- 项目介绍 -->
        <div class="product-info">
          <div class="info-title">{{ $t('项目介绍') }}</div>
          <div class="info-Text" v-html="coinInfo.description">
          </div>
        </div>
        <!-- 项目规划 -->
        <!-- <div class="product-info">
          <div class="info-title">{{ $t('项目预期') }}</div>
          <div class="info-Text">
            {{ $t('在互联网贸易中，人们习惯依赖于中心化金融机构来完成一笔订单的支付。在这个过程中，往往面临诸多限制且数据不透明，面临篡改的风险。比特币通过基于密码学的分布式账本技术提供一个去中心化的电子支付系统，实现了透明公开的交易数据存储、追溯、查询，同时全球任何人都可以参与其中，没有任何地域、交易金额的限制。') }}
          </div>
        </div> -->
        <!-- 市场分析 -->
        <!-- <div class="product-info">
          <div class="info-title">{{ $t('项目分析') }}</div>
          <div class="info-Text">
            {{ $t('提供一个去中心化、免许可的支付系统是大众一直所期待的，在比特币发行之前，世界上已有存在多种不同的电子货币技术和产品，最早的起源来自大卫·乔姆和史戴分·布蓝兹所提出之以发行者为主的“ecash”协议。接着许多以“ecash”协议为基础的数位货币等产品开始出现，其中以亚当·贝克的“hashcash”、戴维(Wei Dai)的“b-money”、尼克·萨博的“bit-gold”，以及哈尔·芬尼在“hashcash”技术上发展的“RPOW”等。 以往的实现中，往往难以解决“拜占廷将军问题”，系统面临中心化的风险。') }}
          </div>
        </div> -->
        <!-- 项目方案 -->
        <!-- <div class="product-info">
          <div class="info-title">{{ $t('项目方案') }}</div>
          <div class="info-Text">
            {{ $t('比特币系统通过Pow（Proof of work）工作量证明算法解决了分布式系统中面临的共识问题，系统中超过51%诚实算力即可确保系统的安全性。') }}
          </div>
        </div> -->
      </div>
      <div class="currency-wrap-right flex-1">
        <ExchangeList v-if="curCoin !== ''" :coin="curCoin" :markets="markets" />
        <NoteList :noteList="noteList" />
        <RankList :markets="markets" />
      </div>
    </div>
    <div v-if="!isHasCoinInfo && !isLoading" class="flex-box empty">
      <img v-if="colorMode.preference.includes('light')" src="~/assets/images/currency/empty-light.png" alt="" />
      <img v-else src="~/assets/images/currency/empty-dark.png" alt="" />
      <div class="fit-tc-secondary font-size-16">{{ $t('暂无数据') }}</div>
      <div class="font-size-12 back" @click="handleBack">{{  $t('返回查看其他币种') }}</div>
    </div>
  </div>
  <Footer />
</template>
<script lang="ts" setup>
  import * as echarts from 'echarts'
  import UAParser from 'ua-parser-js'
  import md5 from 'blueimp-md5'
  import { ElDropdown, ElDropdownMenu } from 'element-plus'
  import { timeFormat, format } from '~/utils'
  import { cookies } from '~/utils/cookies'
  import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
  import RankList from '~/components/currency/RankList.vue'
  import NoteList from '~/components/currency/NoteList.vue'
  import ExchangeList from '~/components/currency/ExchangeList.vue'
  import Markets from '~/components/currency/Markets.vue'
  import { getCurrencyDesc, getNoteListApi } from '~/api/public'
  import { useCommonData } from '~/composables/index'
  import { commonStore } from '~/stores/commonStore'
  const store = commonStore()
  const { marketsObj, pairInfo } = storeToRefs(store)
  const { locale, t } = useI18n()
  const router = useRouter()
  const colorMode = useColorMode()
  const isHasCoinInfo = ref(false)
  const isShowKline = ref(true)
  const resolution = ref('day')
  const useCommon = useCommonData()
  const allTimeOptions = computed(() => {
    return [
      { text: t('日线'), resolution: 'day' },
      { text: t('周线'), resolution: 'week' }
    ]
  })
  const curCoin = computed(() => {
    return router.currentRoute.value.params.coin || ''
  })
  const pair = computed(() => {
    if (curCoin === 'USDT') {
      return 'USDT_USDC'
    } else {
      return `${curCoin.value}_USDT`
    }
  })
  const chart = ref(null)
  const myChart = ref(null)
  const isLoading = ref(true)
  const chartData = ref([])
  const exchangeRate = ref(cookies.get('ktx-exchangeRates'))
  watch(() => exchangeRate.value, (val) => {
    draw()
  }, {
    deep: true
  })
  const draw = () => {
    const chartEl = myChart.value
    if (chartEl) {
      chart.value = echarts.init(chartEl)
      // 1. 计算 yAxis 的最小值和最大值
      const closeValues = chartData.value.map(v => v.close);
      const minValue = Math.min(...closeValues);
      const maxValue = Math.max(...closeValues);
      const colorOption = {
        tertiary: {
          light: '#CBCED8',
          dark: '#2D2D2D'
        },
        primary: {
          light: '#414655',
          dark: '#ffffff'
        },
        border: {
          light: '#EEEEEE',
          dark: '#363a45'
        },
        secondary: {
          light: '#9497A0',
          dark: '#cccccc'
        }
      }
      const fontFamily = '"DINPro", "SF Pro SC", "SF Pro Text", "Helvetica Neue", "Helvetica, Arial", "PingFang SC", "Microsoft YaHei", "微软雅黑", "sans-serif"'
      const option = {
        grid: {
          top: '40px',
          bottom: '20px',
          left: '5%',
          right: '5%',
          containLabel: true
        },
        tooltip: {
          trigger: 'item',
          formatter: params => {
            const value = params.data
            const time = params.name
            return timeFormat(time, 'yy-MM-dd hh:mm:ss') + ': ' + format(value, (pairInfo.value[pair.value] || {}).price_scale, true)
          },
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: colorOption.secondary[colorMode.preference] ? colorOption.secondary[colorMode.preference] : colorOption.secondary.light,
              formatter: params => {
                if (params.axisDimension === 'x') {
                  return timeFormat(params.value, 'yy-MM-dd hh:mm:ss')
                } else {
                  return format(params.value, (pairInfo.value[pair.value] || {}).price_scale, true)
                }
              }
            }
          }
        },
        xAxis: {
          type: 'category',
          scale: true,
          axisTick: {
            alignWithLabel: true
          },
          axisLine: { // x轴线的颜色以及宽度
            show: true,
            lineStyle: {
              color: colorOption.border[colorMode.preference] ? colorOption.border[colorMode.preference] : colorOption.border.light
            }
          },
          axisLabel: {
            textStyle: {
              color: colorOption.secondary[colorMode.preference] ? colorOption.secondary[colorMode.preference] : colorOption.secondary.light,
              fontSize: 12,
              fontWeight: 500,
              fontFamily
            },
            formatter: value => {
              if (resolution.value === 'week') {
                return timeFormat(value, 'MM-dd')
              } else {
                return timeFormat(value, 'hh:mm')
              }
            }
          },
          data: chartData.value.map(v => {
            return v.time
          })
        },
        yAxis: {
          type: 'value',
          name: '', // y轴上方的单位 exchangeRate.value.rate
          nameTextStyle: { // y轴上方单位的颜色
            color: colorOption.primary[colorMode.preference] ? colorOption.primary[colorMode.preference] : colorOption.primary.light,
            fontFamily,
            fontWeight: 500,
            fontSize: 14
          },
          position: 'right',
          min: minValue * 0.95,
          max: maxValue * 1.05,
          axisLine: { // y轴线的颜色以及宽度
            show: true,
            lineStyle: {
              color: colorOption.border[colorMode.preference] ? colorOption.border[colorMode.preference] : colorOption.border.light
            }
          },
          axisLabel: {
            textStyle: {
              color: colorOption.secondary[colorMode.preference] ? colorOption.secondary[colorMode.preference] : colorOption.secondary.light,
              fontSize: 12,
              fontWeight: 500,
              fontFamily
            },
            formatter: value => {
              return format(value, (pairInfo.value[pair.value] || {}).price_scale, true)
            }
          },
          splitLine: { // 分割线配置
            show: true,
            lineStyle: {
              color: colorOption.border[colorMode.preference] ? colorOption.border[colorMode.preference] : colorOption.border.light
            }
          }
        },
        series: [{
          data: chartData.value.map(v => {
            return v.close
          }),
          type: 'line',
          lineStyle: {
            color: '#F0B90B'
          },
          symbol: 'circle', // 拐点设置为实心
          symbolSize: 1, // 拐点大小
          showSymbol: false,
          itemStyle: {
            normal: {
              color: '#F0B90B', // 拐点颜色
              borderColor: '#fff', // 拐点边框颜色
              borderWidth: 1, // 拐点边框大小
              shadowColor: '#F0B90B',
              shadowBlur: 2
            }
          },
          areaStyle: {
            opacity: 0.8,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: '#F0B90B'
              },
              {
                offset: 1,
                color: 'rgba(240,185,11,0)'
              }
            ])
          }
        }],
        dataZoom: [{
          type: 'inside',
          throttle: 50
        }]
      }
      chart.value && chart.value.setOption(option)
      setTimeout(() => {
        chart.value && chart.value.hideLoading()
      }, 500)
    }
  }
  const dateChange = (item) => {
    if (chart.value) {
      chart.value.clear()
      chart.value.showLoading({
        text: '',
        color: '#F0B90B'
      })
    }
    resolution.value = item.resolution
    chartData.value = item.resolution === 'day' ? coinInfo.value.kline_day_arr : [...coinInfo.value.kline_week_arr].reverse()
    draw()
  }
  watch(() => colorMode.preference, () => {
    draw()
  })
  const hanldeGoExchange = () => {
    router.push(`/${locale.value}/exchange/${pair.value}`)
  }
  const handleBack = () => {
    if (window.history.length <= 1) {
      router.push(`/${locale.value}`)
    } else {
      router.go(-1)
    }
  }
  const coinInfo = ref({})
  const getCoinInfo = async() => {
    const { data, error } = await getCurrencyDesc({
      lang: locale.value,
      general_name: router.currentRoute.value.params.coin.toUpperCase()
    })
    if (data) {
      isLoading.value = false
      isHasCoinInfo.value = true
      coinInfo.value = data
      data.kline_day_arr.map((item) => {
        item.time = item.time * 1000
        return item
      })
      data.kline_week_arr.map((item) => {
        item.time = item.time * 1000
        return item
      })
      chartData.value = resolution.value === 'day' ? data.kline_day_arr : [...data.kline_week_arr].reverse()
      nextTick(() => {
        draw()
      })
    } else {
      isLoading.value = false
      isHasCoinInfo.value = false
    }
  }
  const markets = ref({})
  const requestAnimationFrameInterval = ref(null)
  const socketDateAnimation = () => {
    markets.value = marketsObj.value
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  }
  const noteList = ref([])
  const getNoteList = async() => {
    if (cookies.get('KTX_device_id')) {
      localStorage.setItem('KTX_device_id', cookies.get('KTX_device_id'))
    }
    const Ua = new UAParser(window.navigator.userAgent)
    const browser = Ua.getBrowser() || {}
    const os = Ua.getOS() || {}
    const device = Ua.getDevice()
    const deviceName = device.model ? device.model : `${os.name} ${browser.name}`
    const deviceId = cookies.get('KTX_device_id') || localStorage.getItem('KTX_device_id') || md5(`${Date.now()}-${Math.floor(Math.random() * 10000000000).toString(16)}-${os.name}-${browser.name}`)
    const { data } = await getNoteListApi({
      device_id: deviceId,
      page: 1,
      size: 5,
      notice_type: 3
    })
    if (data) {
      noteList.value = data.rows.map((item) => {
        let obj = {}
        JSON.parse(item.content).forEach((ite) => {
          obj[ite.lang] = ite
        })
        item.context = obj
        return item
      })
    }
  }
  onMounted(() => {
    getCoinInfo()
    getNoteList()
    socketDateAnimation()
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  })
  onBeforeUnmount(() => {
    requestAnimationFrameInterval.value && cancelAnimationFrame(requestAnimationFrameInterval.value)
  })
</script>
<style lang="scss" scoped>
.currency-container{
  min-height:calc(100vh - 100px);
  .empty {
    padding: 100px 0;
    flex-direction: column;
    justify-content: center;
    .back {
      @include color(theme);
      margin-top: 10px;
      cursor: pointer;
    }
    img{
      width:300px;
      height:auto;
    }
  }
  .currency-wrapper{
    padding: 62px 0;
    .chart-box {
      .symbol_icon {
        margin-right: 12px;
      }
      .drop-down {
        flex: 1;
        display: flex;
        justify-content: flex-end;

        .down-box {
          height: 44px;
          border-radius: 4px;
          @include bg-color(bg-quaternary);
          padding: 0 12px;
          cursor: pointer;

          .text {
            margin: 0 5px;
          }
        }
      }
      .nav {
        margin: 24px 0;
        justify-content: space-between;

        .list {
          display: flex;

          .list-item {
            cursor: pointer;
            height: 24px;
            line-height: 24px;
            font-size: 12px;
            @include color(tc-secondary);
            padding: 0 12px;
            margin-right: 10px;
            border-radius: 2px;

            &.active {
              @include color(theme);
              @include bg-color(bg-quaternary);
            }

            &:last-child {
              margin-right: 0;
            }
          }
        }

        .zy-btn {
          font-size: 12px;
          @include bg-color(bg-quaternary);
          border-radius: 2px;
          height: 24px;
          line-height: 24px;
          padding: 0 12px;
          cursor: pointer;
        }
      }
    }
    .info-title{
      font-size:20px;
      padding:24px 0 12px;
      @include color(tc-primary);
    }
    .info-left, .info-right{
      border-radius:16px;
      padding:16px;
      border:1px solid;
      @include border-color(border);
      dl{
        display:flex;
        justify-content:space-between;
        line-height:44px;
        dt{
          font-size:14px;
          @include color(tc-secondary);
        }
        dd{
          font-size:14px;
          @include color(tc-primary);
        }
      }
    }
    .info-Text{
      font-size:14px;
      line-height:20px;
      @include color(tc-secondary);
    }
    .address-item{
      line-height:40px;
      .test{
        width:150px;
        display:block;
        @include color(tc-secondary);
      }
      a{
        font-size:14px;
        @include color(theme);
      }
    }
  }
}
</style>