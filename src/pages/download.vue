<template>
  <Header />
  <div class="download-container">
    <div class="download-wrapper">
      <div class="download-banner">
        <div class="banner-title">
          <h1>
            <p>{{ $t('安全交易，信赖之选') }}</p>
            <p>{{ $t('随时随地，畅享交易') }}</p>
          </h1>
        </div>
        <div class="banner-cont-center">
          <div class="download-banner-left">
            <template v-if="locale === 'zh'">
              <img v-show="colorMode.preference === 'light'" src="~/assets/images/download/banner-img-light-zh.png" />
              <img v-show="colorMode.preference === 'dark'" src="~/assets/images/download/banner-img-dark-zh.png" />
            </template>
            <template v-if="locale === 'en'">
              <img v-show="colorMode.preference === 'light'" src="~/assets/images/download/banner-img-light-en.png" />
              <img v-show="colorMode.preference === 'dark'" src="~/assets/images/download/banner-img-dark-en.png" />
            </template>
            <template v-if="locale === 'ja'">
              <img v-show="colorMode.preference === 'light'" src="~/assets/images/download/banner-img-light-ja.png" />
              <img v-show="colorMode.preference === 'dark'" src="~/assets/images/download/banner-img-dark-ja.png" />
            </template>
            <template v-if="locale === 'ko'">
              <img v-show="colorMode.preference === 'light'" src="~/assets/images/download/banner-img-light-ko.png" />
              <img v-show="colorMode.preference === 'dark'" src="~/assets/images/download/banner-img-dark-ko.png" />
            </template>
            <template v-if="locale === 'zh-Hant'">
              <img v-show="colorMode.preference === 'light'" src="~/assets/images/download/banner-img-light-zh-Hant.png" />
              <img v-show="colorMode.preference === 'dark'" src="~/assets/images/download/banner-img-dark-zh-Hant.png" />
            </template>
          </div>
          <div class="download-banner-right">
            <dl class="flex-box align-start">
              <dt>
                <p>IOS</p>
                <a href="https://apps.apple.com/app/KTX-pro/id6745578008" target="_blank" class="flex-box space-center">
                  <span class="icon-box apple"></span>
                  <div>
                    <b class="font-size-12">Get it on the</b>
                    <b class="font-size-18">App Store</b>
                  </div>
                </a>
                <a href="https://testflight.apple.com/join/6vANbBYb" target="_blank" class="flex-box space-center mg-t8">
                  <span class="icon-box apple"></span>
                  <div>
                    <b class="font-size-12">Get it on the</b>
                    <b class="font-size-18">Test Flight</b>
                  </div>
                </a>
                <p class="mg-t40">Android</p>
                <a class="flex-box space-center disabled-btn">
                  <span class="icon-box google"></span>
                  <div>
                    <b class="font-size-12">Get it on the</b>
                    <b class="font-size-18">GooglePlay</b>
                  </div>
                </a>
                <a :href="androidUrl ? `${androidUrl}?${new Date().getTime()}` : 'javascript:;'" :class="androidUrl === '' ? 'disabled-btn' : ''" :target="androidUrl === '' ? '' : '_blank'" rel="noopener noreferrer" class="flex-box space-center mg-t8">
                  <span class="icon-box android"></span>
                  <div>
                    <b class="font-size-12">Download the APK</b>
                    <b class="font-size-18">Android</b>
                  </div>
                </a>
              </dt>
              <dd>
                <p>{{ $t('扫码下载') }}<span>(iOS & Android)</span></p>
                <div class="scan-img-wrap">
                  <BoxQrcode :size="166" :value="`https://www.ktx.com/${locale}/download`" />
                </div>
              </dd>
            </dl>
          </div>
        </div>
      </div>
      <div class="download-more-cont">
        <div class="more-center-cont">
          <div class="more-title">
            <h2>{{ $t('下载谷歌验证器') }}</h2>
            <p>Download Google Authenticator</p>
          </div>
          <div class="more-btn-cont flex-box space-center">
            <a href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2" target="_blank">
              <dl class="flex-box space-between">
                <dt class="icon-google"></dt>
                <dd>
                  <p class="font-size-12">Get it on the</p>
                  <p class="font-size-18">GooglePlay</p>
                </dd>
                <div class="more-arrow flex-box space-center">
                  <MonoRightArrow size="16" class="fit-tc-primary" />
                </div>
              </dl>
            </a>
            <a href="https://itunes.apple.com/cn/app/google-authenticator/id388497605?mt=8" target="_blank">
              <dl class="flex-box space-between">
                <dt class="icon-ios"></dt>
                <dd>
                  <p class="font-size-12">Get it on the</p>
                  <p class="font-size-18">AppStore</p>
                </dd>
                <div class="more-arrow flex-box space-center">
                  <MonoRightArrow size="16" class="fit-tc-primary" />
                </div>
              </dl>
            </a>
          </div>
          <div class="more-title">
            <h2>KTX {{ $t('API文档') }}</h2>
          </div>
          <div class="api-btn flex-box space-center">
            <a :href="`https://ktx-private.github.io/api-${locale === 'zh' ? 'zh' : 'en'}/`" target="_blank">
              <dl class="flex-box space-between">
                <dt class="icon-api"></dt>
                <dd class="text-api">
                  KTX API Developer Guide
                </dd>
                <div class="more-arrow flex-box space-center">
                  <MonoRightArrow size="16" class="fit-tc-primary" />
                </div>
              </dl>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
  <Footer />
</template>
<script lang="ts" setup>
  import MonoRightArrow from '~/components/common/icon-svg/MonoRightArrow.vue'
  import { commonStore } from '~/stores/commonStore'
  const store = commonStore()
  const { getDownLoadInfo } = store
  const { downLoadInfo } = storeToRefs(store)
  const colorMode = useColorMode()
  const { locale, t } = useI18n()
  const androidUrl = computed(() => {
  const url = downLoadInfo.value && downLoadInfo.value[4] && downLoadInfo.value[4].download_url || ''
  return url || ''
})
onBeforeMount(() => {
  getDownLoadInfo()
})
</script>
<style lang="scss" scoped>
  .download-container{
    .download-wrapper{
      .download-banner{
        width:100%;
        height:auto;
        @include bg-color(bg-primary);
        .banner-title{
          text-align:center;
          padding:36px 0;
          h1{
            font-size:32px;
            @include color(tc-primary);
          }
        }
        .banner-cont-center{
          display:flex;
          justify-content: center;
          .download-banner-left{
            width:40%;
          }
          .download-banner-right{
            width:40%;
            margin-left:120px;
            dl{
              dt{
                p{
                  font-size:22px;
                  @include color(tc-primary);
                  padding-bottom:8px;
                }
                a{
                  width:190px;
                  height:60px;
                  border-radius:8px;
                  @include color(bg-primary);
                  @include bg-color(tc-primary);
                  &.disabled-btn{
                    opacity:0.2;
                    cursor:default;
                  }
                  .icon-box{
                    display:block;
                    width:36px;
                    height:36px;
                    margin-right:20px;
                    &.apple{
                      @include get-img('~/assets/images/download/ios-white-icon.png', '~/assets/images/download/ios-black-icon.png');
                      background-size:100% 100%;
                    }
                    &.google{
                      background: url('~/assets/images/download/anzhuo-color-icon.png') no-repeat center;
                      background-size:100% 100%;
                    }
                    &.android{
                      @include get-img('~/assets/images/download/anzhuo-white-icon.png', '~/assets/images/download/anzhuo-black-icon.png');
                      background-size:100% 100%;
                    }
                  }
                  b{
                    font-weight:normal;
                    display:block;
                    @include color(bg-primary);
                    &:last-child{
                      margin-top:-8px;
                    }
                  }
                }
              }
              dd{
                margin-left:40px;
                p{
                  font-size:20px;
                  text-align:center;
                  padding-bottom:12px;
                  @include color(tc-primary);
                  span{
                    font-size:14px;
                    @include color(tc-secondary);
                  }
                }
                .scan-img-wrap{
                  width:190px;
                  height:190px;
                  padding:12px;
                  border-radius:12px;
                  border:1px solid;
                  @include border-color(border);
                }
              }
            }
          }
        }
      }
      .download-more-cont{
        width:100%;
        height:auto;
        padding:60px 0 40px;
        @include bg-color(bg-quaternary);
        .more-center-cont{
          width:800px;
          margin:0 auto;
          .more-title{
            text-align:center;
            h2{
              font-size:32px;
              @include color(tc-primary);
            }
            p{
              font-size:14px;
              @include color(tc-secondary);
            }
          }
          .more-btn-cont{
            padding-top:24px;
            padding-bottom:60px;
            margin-bottom:60px;
            border-bottom:1px solid;
            @include border-color(border);
            a{
              display:block;
              @include color(tc-primary);
              margin:0 16px;
              dl{
                width:208px;
                height:72px;
                padding:0 16px;
                border-radius:12px;
                @include bg-color(bg-primary);
                dt{
                  width:36px;
                  height:36px;
                  &.icon-google{
                    background: url('~/assets/images/download/anzhuo-color-icon.png') no-repeat center;
                    background-size:100% 100%;
                  }
                  &.icon-ios{
                    @include get-img('~/assets/images/download/ios-black-icon.png', '~/assets/images/download/ios-white-icon.png');
                    background-size:100% 100%;
                  }
                }
              }
            }
          }
          .api-btn{
            margin-top:24px;
            a{
              display:block;
              dl{
                height:68px;
                width:345px;
                border-radius:12px;
                padding:0 16px;
                @include bg-color(bg-primary);
                .icon-api{
                  width:47px;
                  height:47px;
                  background: url('~/assets/images/download/api-icon.png') no-repeat center;
                  background-size:100% 100%;
                }
                .text-api{
                  font-size:18px;
                  @include color(tc-primary);
                }
              }
            }
          }
          .more-arrow{
            width:32px;
            height:32px;
            border-radius:8px;
            @include bg-color(bg-quaternary);
          }
        }
      }
    }
  }
  @include md{
    .download-container{
      .download-wrapper{
        .download-banner{
          .banner-cont-center{
            display:flex;
            justify-content: center;
            flex-direction: column;
            align-items:center;
            .download-banner-left{
              width:70%;
            }
            .download-banner-right{
              margin-top:24px;
              padding-bottom:40px;
              width:50%;
              margin-left:0;
            }
          }
        }
      }
    }
  }
  @include mb {
     .download-container{
      .download-wrapper{
        .download-banner{
          .banner-cont-center{
            display:flex;
            justify-content: center;
            flex-direction: column;
            align-items:center;
            .download-banner-left{
              width:90%;
            }
            .download-banner-right{
              margin-top:24px;
              padding-bottom:40px;
              width:90%;
              margin-left:0;
              dl{
                dt{
                  width:100%;
                  a{
                    width:100%;
                  }
                }
                dd{
                  display:none;
                }
              }
            }
          }
        }
        .download-more-cont{
          padding:40px 0;
          .more-center-cont{
            width:90%;
            margin:0 auto;
            .more-btn-cont{
              padding-top:24px;
              padding-bottom:40px;
              margin-bottom:40px;
              border-bottom:1px solid;
              @include border-color(border);
              &.flex-box{
                flex-direction: column;
              }
              a{
                display:block;
                @include color(tc-primary);
                margin:0;
                width:100%;
                margin-bottom:24px;
                dl{
                  width:100%;
                  height:72px;
                  padding:0 16px;
                  border-radius:12px;
                  @include bg-color(bg-primary);
                  dt{
                    width:36px;
                    height:36px;
                    &.icon-google{
                      background: url('~/assets/images/download/anzhuo-color-icon.png') no-repeat center;
                      background-size:100% 100%;
                    }
                    &.icon-ios{
                      @include get-img('~/assets/images/download/ios-black-icon.png', '~/assets/images/download/ios-white-icon.png');
                      background-size:100% 100%;
                    }
                  }
                }
              }
            }
            .api-btn{
              margin-top:24px;
              a{
                display:block;
                width:100%;
                dl{
                  height:68px;
                  width:100%;
                  border-radius:12px;
                  padding:0 16px;
                  @include bg-color(bg-primary);
                  .icon-api{
                    width:47px;
                    height:47px;
                    background: url('~/assets/images/download/api-icon.png') no-repeat center;
                    background-size:100% 100%;
                  }
                  .text-api{
                    font-size:18px;
                    @include color(tc-primary);
                  }
                }
              }
            }
            .more-arrow{
              width:32px;
              height:32px;
              border-radius:8px;
              @include bg-color(bg-quaternary);
            }
          }
        }
      }
    }
  }
</style>