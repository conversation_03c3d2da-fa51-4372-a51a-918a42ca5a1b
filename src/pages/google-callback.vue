<template>
  <BoxLoading />
</template>
<script lang="ts" setup>
  import md5 from 'blueimp-md5'
  import UAParser from 'ua-parser-js'
  import { useCommonData } from '~/composables/index'
  import { thirdLogin } from '~/api/user'
  import { cookies } from '~/utils'
  const { locale } = useI18n()
  const useCommon = useCommonData()
  const router = useRouter()
  const GoogleLogin = async() => {
    if (cookies.get('KTX_device_id')) {
      localStorage.setItem('KTX_device_id', cookies.get('KTX_device_id'))
    }
    const Ua = new UAParser(window.navigator.userAgent)
    const device = Ua.getDevice()
    const os = Ua.getOS() || {}
    const browser = Ua.getBrowser() || {}
    const deviceId = cookies.get('KTX_device_id') || localStorage.getItem('KTX_device_id') || md5(`${Date.now()}-${Math.floor(Math.random() * 10000000000).toString(16)}-${os.name}-${browser.name}`)
    const deviceName = (device.model ? device.model : `${os.name} ${browser.name}`)
    const code = router.currentRoute.value.query.code
    const googleRouter = localStorage.getItem('ktx_googole_router')
    const { data, error } = await thirdLogin({
      login_type: '1',
      token: '',
      google_code: code,
      redirect_uri: `${window.location.origin}/google-callback`,
      device_id: deviceId,
      device_name1: deviceName
    })
    if (data) {
      window.location.href = googleRouter
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
      window.location.href = `/${locale.value}`
    }
  }
  onBeforeMount(async() => {
    await GoogleLogin()
  })
</script>