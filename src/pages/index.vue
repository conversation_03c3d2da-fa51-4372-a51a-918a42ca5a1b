<template>
<Header modeType="index" />
  <Message />
  <BannerSwiper :bannerList="bannerList" />
  <LandingPair />
  <div v-if="!isMin && locale === 'ko'" class="kora-download-cont">
    <div class="download-cont-center flex-box space-center align-start">
      <div class="download-cont-left">
        <h2 class="left-title">암호화폐를 시작하는 가장 쉬운 방법</h2>
        <div class="jl-tips">
          <p>신규 사용자 전용 가입 즉시 혜택 증정!</p>
        </div>
        <div class="download-wrap">
          <NuxtLink :to="`/${locale}/download`">더보기<MonoRightArrowShort size="14" /></NuxtLink>
          <dl class="flex-box">
            <dt v-if="androidUrl">
              <BoxQrcode :size="120" :value="`${androidUrl}?${new Date().getTime()}`" />
            </dt>
            <dd>
              <p>스캔하여 앱 다운로드 방법 확인</p>
              <p>APK 다운로드</p>
            </dd>
          </dl>
        </div>
        <div class="lw-cont flex-box">
          <div class="lw-icon"></div>
          <div>
            <p class="pd-b8">친구 초대로 최소 <span class="fit-theme">20%</span> 커미션 환급</p>
            <p>최대 <span class="fit-theme">50%</span>까지 수익 환급 가능</p>
          </div>
        </div>
      </div>
      <div class="download-cont-right">
        <img v-show="colorMode.preference === 'light'" src="~/assets/images/home/<USER>" />
        <img v-show="colorMode.preference === 'dark'" src="~/assets/images/home/<USER>" />
      </div>
    </div>
  </div>
  <Markets :is-mobile="isMobile" :is-loading="isLoading" :is-min="isMin" />
  <downLoadMode />
  <Footer />
</template>
<script lang="ts" setup>
  import Header from '~/components/header/index.vue'
  import Footer from '~/components/footer/index.vue'
  import BannerSwiper from '~/components/home/<USER>'
  import LandingPair from '~/components/home/<USER>'
  import Markets from '~/components/home/<USER>'
  import Message from '~/components/message/index.vue'
  import downLoadMode from '~/components/home/<USER>'
  import { getUsefulLink } from '~/api/public'
  import { cookies } from '~/utils/cookies'
  import { commonStore } from '~/stores/commonStore'
const store = commonStore()
const { getDownLoadInfo } = store
const { downLoadInfo } = storeToRefs(store)
  const { locale, t } = useI18n()
  const colorMode = useColorMode()
  const screenWidth = ref(0)
  const isMobile = ref(true)
  const isLoading = ref(false)
  const isMin = ref(false)
  const updateScreenWidth = () => {
    screenWidth.value = window.innerWidth
    isMobile.value = screenWidth.value <= 1024
    isMin.value = screenWidth.value <= 768
  }
  const userFullList  = ref([])
  const bannerList = computed(() => {
    try {
      const activityCoinList = {}
      const bannerList = []
      for (const element of userFullList.value) {
        const banners = JSON.parse(element.url)
        const title = element.name
        if (element.type === 4 && banners) {
          const result = {}
          result.id = element.id
          result.title = title
          for (const banner of banners) {
            result[banner.lang] = banner
            result[banner.lang].weight = element.weight
            let coinSymbol = banners[0].coin_symbol
            if (coinSymbol) {
              coinSymbol = coinSymbol.toUpperCase()
              activityCoinList[coinSymbol] = activityCoinList[coinSymbol] || {}
              activityCoinList[coinSymbol][banner.lang + 'Url'] = banner.url
            }
          }
          bannerList.push(result)
        }
      }
      return bannerList
    } catch (err) {
      return []
    }
  })
  const getAdvList = async() => {
    const { data } = await getUsefulLink({
      type: 4
    })
    if (data) {
      userFullList.value = data.items
    }
  }
  const androidUrl = computed(() => {
    const url = downLoadInfo.value && downLoadInfo.value[4] && downLoadInfo.value[4].download_url || ''
    return url || ''
  })
  const router = useRouter()
  onMounted(() => {
    try {
      if (process.client) {
        window.sensors?.track('show', {
          from_page: router.options.history.state.back || document.referrer,
          page: 'main_activity'
        });
      }
    } catch (err) {
      console.error('Sensors tracking error:', err);
    }
    updateScreenWidth()
    window.addEventListener('resize', updateScreenWidth)
  })
  onBeforeMount(() => {
    isLoading.value = true
    getAdvList()
    getDownLoadInfo()
  })
</script>
<style lang="scss" scoped>
  .kora-download-cont{
    padding:12px 20px;
    .download-cont-center{
      width:100%;
      height:auto;
      border-radius:20px;
      background: linear-gradient(180deg, rgba(240, 185, 11, 0.1) 0%, rgba(240, 185, 11, 0) 100%);
      .download-cont-left{
        padding-top:18px;
        padding-right:8%;
        .left-title{
          font-size:40px;
          @include color(tc-primary);
        }
        .left-info{
          font-size:14px;
          padding-top:12px;
          @include color(tc-primary);
        }
      }
      .download-cont-right{
        padding-top:18px;
        width:324px;
        height:auto;
      }
      .jl-tips{
        padding:24px 0;
        font-size:24px;
        @include color(tc-primary);
      }
      .download-wrap{
        width:100%;
        border-radius:12px;
        padding:20px 0;
        position:relative;
        @include bg-color(bg-primary);
        a{
          position:absolute;
          top:20px;
          right:20px;
          @include color(theme);
        }
        dl{
          padding-left:20px;
          dt{
            margin-right:12px;
          }
          dd{
            @include color(tc-primary);
            p{
              font-size:16px;
              &:first-child{
                padding-bottom:12px;
              }
            }
          }
        }
      }
      .lw-cont{
        padding-top:24px;
        font-size:18px;
        @include color(tc-primary);
        .lw-icon{
          width:50px;
          height:50px;
          margin-right:16px;
          background: url('~/assets/images/home/<USER>') no-repeat center;
          background-size:100% auto;
        }
      }
    }
  }
</style>