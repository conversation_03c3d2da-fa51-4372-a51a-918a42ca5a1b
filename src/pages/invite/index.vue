<template>
  <Header />
  <div class="invite-container">
    <div v-loading="isLoading" class="invite-wrapper">
      <div class="invite-banner" :class="{'en': locale !== 'zh' && locale !== 'zh-Hant'}">
        <div v-if="!isLogin || (isLogin && (userInfo || {}).is_crm_user * 1 === 0)" class="banner-text-box">
          <div class="banner-text flex-box space-center">
            <dl class="flex-box flex-1">
              <dt class="icon-my-peoplecircle"></dt>
              <dd>
                <h3>{{ $t('邀请好友') }}</h3>
                <p>{{ $t('通过分享链接邀请好友注册 KTX') }}</p>
              </dd>
            </dl>
            <div class="box-line mg-r20"></div>
            <dl class="flex-box flex-1">
              <dt class="icon-my-signcircle"></dt>
              <dd>
                <h3>
                  {{ $t('NFT返佣') }}
                  <a v-if="isLogin" class="fit-theme font-size-14 cursor-pointer" @click="goNftFun()">{{ $t('充值') }} NFT</a>
                </h3>
                <p>{{ $t('充值即锁定NFT，即可获得好友 50% 手续费返佣') }}</p>
              </dd>
            </dl>
            <div class="box-line mg-l20 mg-r20"></div>
            <dl class="flex-box flex-1">
              <dt class="icon-my-giftcircle"></dt>
              <dd>
                <h3>{{ $t('二级返佣') }}</h3>
                <p>{{ $t('邀请好友30人即可申请二级返佣；一次申请，终身有效') }}</p>
              </dd>
            </dl>
          </div>
        </div>
        <div class="invite-center-container">
          <div class="invitr-center-cont flex-box space-center align-start">
            <div v-if="!isLogin || (isLogin && (userInfo || {}).is_crm_user * 1 === 0)" class="banner-left">
              <div>
                <h1 v-if="isShowHtMl" v-html="$t('邀友返佣20%起')"></h1>
                <div class="banner-text-bg flex-box space-between">
                  {{ $t('收益无上限！') }}
                  <a href="#rule" class="flex-box">
                    {{ $t('详细规则') }}
                    <MonoRightArrowShort size="14" class="fit-theme" />
                  </a>
                </div>
              </div>
              <div class="left-img-cont">
                <img src="~/assets/images/invite/banner-right-img.png" />
              </div>
            </div>
            <div v-else class="banner-left text-center">
              <div>
                <h1 v-html="$t('邀请好友')"></h1>
                <div class="banner-text-bg flex-box space-center def">
                  {{ $t('一起交易吧') }}
                </div>
              </div>
              <div class="left-img-cont">
                <img src="~/assets/images/invite/banner-right-img.png" />
              </div>
            </div>
            <div class="banner-right">
              <div v-if="invitationDefault && isLogin" class="invite-banner-cont-box" :class="{'def': (userInfo || {}).is_crm_user * 1 === 1}">
                <div class="invite-title flex-box space-between">
                  <el-dropdown trigger="click" @command="handleCommand">
                    <div class="flex-box cursor-pointer">
                      <span class="mg-r4">{{ invitationDefault.desc ? invitationDefault.desc : $t('邀请链接') }}</span>
                      <MonoDownArrowMin size="12" />
                    </div>
                    <template #dropdown>
                      <el-dropdown-menu style="width:200px;max-height:calc(100vh / 2);overflow:auto;">
                        <el-dropdown-item v-for="(item, index) in invitationlList" :command="item"  :class="{ active: item.id === invitationDefault.id }">{{ item.desc ? item.desc : $t('邀请链接') }}</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                  <NuxtLink :to="`/${locale}/invite/inviteLinkList`" class="flex-box">
                    {{ $t('设置邀请规则') }}
                    <MonoRightArrowShort size="14" />
                  </NuxtLink>
                </div>
                <div v-if="(userInfo || {}).is_crm_user * 1 === 0" class="invite-level flex-box">
                  <div class="level-item flex-1">
                    <h3>{{ $t('当前等级') }}</h3>
                    <p class="fit-theme"><span>Lv.{{ rateInfo.level + 1 }}</span></p>
                  </div>
                  <div class="level-item flex-1">
                    <h3>{{ $t('您的返佣比例') }}</h3>
                    <p class="fit-theme"><span>{{ format(invitationDefault.lv1_self_part * 100, 2, true, true) }}%</span></p>
                  </div>
                  <div class="level-item flex-1">
                    <h3>{{ $t('好友返现比例') }}</h3>
                    <p class="fit-theme"><span>{{ format(invitationDefault.lv1_other_part * 100, 2, true, true) }}%</span></p>
                  </div>
                </div>
                <div>
                  <div v-if="invitationDefault" class="cont-box flex-box has-bg">
                    <h3>{{ $t('邀请码') }}：</h3>
                    <div class="flex-box space-between flex-1">
                      <p class="ts-20 tw-5 fit-tc-primary">
                        {{ invitationDefault.code }}
                      </p>
                      <MonoCopy size="20" class="fit-theme cursor-pointer mg-l4" @click="useCommon.copy(invitationDefault.code, $t('复制成功！'))" />
                    </div>
                  </div>
                  <div v-if="invitationDefault" class="cont-box flex-box has-bg">
                    <h3>{{ $t('邀请链接') }}：</h3>
                    <div class="flex-box space-between flex-1">
                      <p class="ts-16 tw-5 fit-tc-primary">
                        {{ invitationLink }}
                      </p>
                      <MonoCopy size="20" class="fit-theme cursor-pointer mg-l4" @click="useCommon.copy(invitationLink, $t('复制成功！'))" />
                    </div>
                  </div>
                  <div class="email-box flex-box">
                    <span class="label-item">{{ $t('邮箱邀请') }}</span>
                    <div class="flex-1 flex-box">
                      <el-input v-model="email" type="text" :placeholder="$t('输入邮箱地址发送邀请链接')" class="input-email-text" />
                    </div>
                  </div>
                  <div class="email-btn flex-box" v-if="invitationDefault">
                    <el-button type="primary" class="flex-1 mg-r16" :disabled="isDisabled" @click="sendInviteFun">{{ $t('发送邀请') }}</el-button>
                    <el-tooltip trigger="hover">
                      <template #content>
                        <div class="share-cont-box flex-box flex-column">
                          <div class="share-logo"></div>
                          <div class="share-sub-title">{{ $t('邀请码') }}</div>
                          <div class="share-title">{{ invitationDefault.code }}</div>
                          <div class="share-code-box">
                            <BoxQrcode :size="180" :value="invitationLink" />
                          </div>
                        </div>
                      </template>
                      <div class="flex-1 share-btn flex-box space-center">
                        {{ $t('分享') }}
                        <span class="share-ewm-icon"></span>
                      </div>
                    </el-tooltip>
                  </div>
                </div>
              </div>
              <div v-if="!isLogin" class="no-login-container">
                <div class="no-login-wrapper flex-box space-center flex-column">
                  <h2 class="font-size-24 fit-tc-primary tw-3 mg-b32">{{ $t('您还未登录，登录后即可分享好友') }}</h2>
                  <div class="login-btn-box flex-box">
                    <el-button type="primary" @click="useCommon.openLogin()">{{ $t('登录') }}</el-button>
                    <p class="flex-box font-size-14 fit-tc-secondary mg-l8">
                      {{ $t('还没有账号?') }}
                      <a class="fit-theme mg-l4 font-size-14 cursor-pointer" @click="useCommon.openRegister()">{{ $t('注册') }}</a>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div v-if="isLogin && (userInfo || {}).is_crm_user * 1 === 0" class="invite-cont-box">
          <h2 class="zonglan-title">{{ $t('总览') }}</h2>
          <div class="zonglan-cont flex-box">
            <div class="zonglan-left flex-box flex-wrap">
              <ul class="flex-box flex-1">
                <li>
                  <h3>{{ $t('总返佣') }}<span class="font-size-12">USDT{{ $t('估值') }}</span></h3>
                  <p class="fit-theme">≈<span class="font-size-32">{{ format(todayInvInfo.today_back, 4) }}</span> USDT</p>
                </li>
              </ul>
              <ul class="flex-box flex-1">
                <li class="flex-1">
                  <h3 class="flex-box">
                    {{ $t('有效邀请好友数') }}
                    <el-tooltip placement="top">
                      <MonoWarn size="16" class="cursor-pointer fit-tc-secondary mg-l4" />
                      <template #content>
                        <div class="font-size-12" style="max-width:400px;">
                          <div>{{ $t('好友充值≥100USDT，或交易达到200USDT') }}</div>
                          <div>{{ $t('返佣估值与有效邀请好友数每30分钟更新一次') }}</div>
                        </div>
                      </template>
                    </el-tooltip>
                  </h3>
                  <p class="fit-theme"><span class="font-size-32">{{ todayInvInfo.total_inv || 0 }}</span></p>
                </li>
              </ul>
              <ul class="flex-box flex-1">
                <li class="flex-1">
                  <h3>{{ $t('邀请好友数') }}</h3>
                  <p class="fit-theme"><span class="font-size-32">{{ todayInvInfo.grand_total_inv || 0 }}</span></p>
                </li>
              </ul>
            </div>
            <div class="zonglan-right flex-box flex-wrap flex-1">
              <div class="today-box flex-box flex-column align-start space-center">
                <span>{{ $t('今日返佣') }}</span><div class="flex-box"><em class="font-size-18 mg-r4">{{ format(todayInvInfo.tomorrow_back, 4) }}</em> USDT</div>
              </div>
              <div class="today-box flex-box flex-column align-start space-center">
                <span>{{ $t('今日交易人数') }}</span><div class="flex-box"><em class="font-size-18 mg-r4">{{ todayInvInfo.today_trade_user }}</em></div>
              </div>
              <div class="today-box flex-box flex-column align-start space-center">
                <span>{{ $t('好友今日交易额') }}</span><div class="flex-box"><em class="font-size-18 mg-r4">{{ format(todayInvInfo.today_trade_usdt, 4) }}</em> USDT</div>
              </div>
              <div class="today-box flex-box flex-column align-start space-center">
                <span>{{ $t('今日邀请人数') }}</span><div class="flex-box"><em class="font-size-18 mg-r4">{{ todayInvInfo.today_inv }}</em></div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="isLogin && (userInfo || {}).is_crm_user * 1 === 0" class="list-container-box">
          <div class="list-pd">
            <ul class="list-tab flex-box">
              <li :class="{'active': curTab === 0}" @click="curTab = 0">{{ $t('返佣记录') }}</li>
              <li :class="{'active': curTab === 2}" @click="curTab = 2">{{ $t('返现记录') }}</li>
              <li :class="{'active': curTab === 1}" @click="curTab = 1">{{ $t('邀请记录') }}</li>
            </ul>
            <div class="container-box">
              <backRateTable v-if="curTab === 0" :isLogin="isLogin" />
              <backMoneyTable v-if="curTab === 2" :isLogin="isLogin" />
              <inviteRecordTable v-if="curTab === 1" :isLogin="isLogin" />
            </div>
          </div>
        </div>
      </div>
      <template v-if="!isLogin || (isLogin && (userInfo || {}).is_crm_user * 1 === 0)">
        <div id="rule" class="rate-info-list">
          <h2 class="invite-title">{{ $t('返佣规则') }}</h2>
          <el-table :data="currentList" class="invite-rule-table" style="width: 100%">
            <el-table-column :label="$t('现货')" align="left">
              <template #header>
                <span class="fit-tc-primary" style="padding-left:16px;">{{ $t('现货') }}</span>
              </template>
              <el-table-column :label="$t('等级')" prop="level" width="80" align="center">
                <template #default="scope">
                  Lv.{{ scope.row.level + 1 }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('有效邀请好友数量(人)')" prop="invite" align="center">
                <template #default="scope">
                  <span style="white-space:nowrap;">{{ scope.row.invite }}</span>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column :label="$t('一级返佣比例')" align="center">
              <el-table-column prop="backNo1" :label="$t('未充值NFT')" width="200px" align="center"></el-table-column>
              <el-table-column prop="backHas1" :label="$t('充值NFT')" min-width="200px" align="center">
                <template #header>
                  <p>{{ $t('充值NFT') }}</p>
                  <div class="flex-box space-between">
                    <span class="flex-1 text-center">{{ $t('普通') }} NFT</span>
                    <span class="flex-1 text-center">VIP NFT</span>
                  </div>
                </template>
                <template #default="{ row }">
                  <div class="flex-box space-between">
                    <span class="flex-1 text-center">{{ row.backHas1N }}</span>
                    <span class="flex-1 text-center">{{ row.backHas1 }}</span>
                  </div>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column :label="$t('二级返佣比例')" align="center">
              <el-table-column prop="back2" :label="$t('有效邀请好友30人即可申请二级返佣')" align="center">
                <template #header>
                  <el-tooltip placement="top">
                    <template #content>
                      <div style="width:264px;">
                        <p>{{ $t('有效邀请好友30人即可申请二级返佣') }}</p>
                        <div class="pd-12">
                          <p>{{ $t('举例说明：') }}</p>
                          <p>{{ $t('你开启了二级返佣，仅邀请了好友Chad，Chad邀请了100位好友。') }}</p>
                          <p>{{ $t('你将获得，Chad最高50%手续费返佣+Chad邀请的100位好友的10%手续费返佣。') }}</p>
                        </div>
                      </div>
                    </template>
                    <div class="flex-box cursor-pointer space-center">
                      <span class="mg-r4">{{ $t('有效邀请好友30人即可申请二级返佣') }}</span>
                      <MonoWarn :size="16" class="fit-tc-secondary" />
                    </div>
                  </el-tooltip>
                </template>
                <template #default="{ row }">
                  <div class="flex-box space-center pd-l12">
                    {{ row.back2 }}
                    <span v-if="(row.back2 === '10%' || row.back2 === '5%') && rateInfo.back2 * 1 === 0 && todayInvInfo.total_inv * 1 >= 30" class="font-size-14 fit-theme cursor-pointer mg-l8" @click="applyFun()">{{ $t('申请') }}</span>
                  </div>
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>
        </div>
        <div class="info-cont-box">
          <h2 class="invite-title">{{ $t('活动说明') }}</h2>
          <div class="info-text-box">
            <p>{{ $t('1.活动期间，邀请人可获取有效受邀好友的手续费返佣，无考核期。') }}</p>
            <p>{{ $t('2.子账户无法成为邀请人。') }}</p>
            <p>{{ $t('3.受邀人在账户注册成功后的七日内，充值100 USDT及以上或交易200 USDT及以上，即为有效邀请好友。') }}</p>
            <p>{{ $t('4.被邀请用户的子账户交易手续费将计入母账户后再进行奖励发放。') }}</p>
            <p>{{ $t('5.受邀好友数量没有限制，返佣金额没有上限。') }}</p>
            <p>{{ $t('6.现货返佣次日08:00（UTC+8)统一发放，实际发放时间可能有延迟，以次日实际发放时间为准。') }}</p>
            <p>{{ $t('7.邀请人充值锁定NFT，还将获得不定期空投；锁定NFT数量越多，空投奖励就越大。') }}</p>
            <p>{{ $t('8.邀请好友20人即可申请二级返佣；一次申请，终身有效。二级返佣遵循一级返佣同等规则。') }}</p>
          </div>
        </div>
        <div class="info-cont-box">
          <h2 class="invite-title">{{ $t('返佣说明') }}</h2>
          <div class="info-text-box">
            <p>{{ $t('1.通过子账户、第三方渠道或者从平台其他账号进行的充值将不计入充值额度；子账户交易量将计入交易额度。') }}</p>
            <p>{{ $t('2.如果被邀请用户的某个交易订单中，交易对手方为负费率，则本订单会先剔除平台补贴部分后再进行返佣奖励发放。') }}</p>
            <p>{{ $t('3.邀友返佣福利不可与代理返佣福利同期享受。') }}</p>
            <p>{{ $t('4.以下将不产生返佣：受邀用户为特殊费率用户，如做市商、VIP等；邀请或被邀请账户被冻结、被标记为高风险账户等；受限制区域；搭建与本平台相似的第三方网站并引导跳转至官网；异常交易的订单手续费等。') }}</p>
            <p>{{ $t('5.若邀请人在活动期间涉嫌参与欺诈或滥用活动，包括但不限于通过同一 IP 或设备注册多个账户、批量注册账户以获得额外奖励等，一经发现违反推广活动的相应风控规则，推荐人奖励资格将被取消，同时被推荐人的推广以及从中产生的奖励将视为无效。') }}</p>
            <p>{{ $t('6.活动如有调整，以平台公告为准，由于市场环境的改变，欺诈风险的存在等原因，平台保留随时对奖励规则作出调整的最终解释权。') }}</p>
          </div>
        </div>
      </template>
    </div>
  </div>
  <Footer />
  <el-dialog v-model="isShowApply" :title="$t('申请开通-二级返佣')" width="480px" @close="isShowApply = false">
    <div class="apply-cont">
      <template  v-if="todayInvInfo.total_inv * 1 >= 30">
        <div class="apply-title flex-box space-center font-size-18 fit-rise">{{ $t('您的二级返佣申请成功！') }}</div>
        <div class="apply-info font-size-14 fit-tc-primary pd-t24">
          <h3 class="font-size-16 pd-b12">{{ $t('当前您可以获得') }}：</h3>
          <p>{{ $t('您邀请好友的手续费返佣') }}： {{ rateInfo.back1Rate }}%</p>
          <p>{{ $t('您好友邀请好友的手续费返佣') }}： {{ rateInfo.back2Rate }}%</p>
          <p class="fit-tc-secondary pd-t24">{{ $t('详细返佣比例您可以查看返佣规则') }}</p>
        </div>
      </template>
      <template v-else>
        <div class="apply-title flex-box space-center font-size-18 fit-fall">{{ $t('您的二级返佣申请失败！') }}</div>
        <div class="apply-info font-size-14 fit-tc-primary pd-t24">
          <p>{{ $t('经检测您的有效邀请好友人数不足，暂未获得二级返佣福利') }}</p>
          <p class="fit-tc-secondary pd-t24">{{ $t('特殊申请可以联系') }}<span class="fit-theme cursor-pointer">{{ $t('在线客服') }}</span></p>
        </div>
      </template>
      <el-button type="primary" class="mg-t24" @click="isShowApply = false; getRateInfo()">{{ $t('明白了') }}</el-button>
    </div>
  </el-dialog>
  <el-dialog v-if="isShowDeposit" v-model="isShowDeposit" width="900" class="deposit-withdawl-dialog" :close-on-click-modal="false" @close="isShowDeposit = false">
    <NFTDeposit />
  </el-dialog>
  <AddInviteLink
    v-if="isShowInviteLink"
    :is-show="isShowInviteLink" 
    :level="rateInfo.level" 
    :rateInfo="rateInfo"
    @closeAddInvite="isShowInviteLink = false"
    @updateData="updateInvitation"
  />
</template>
<script lang="ts" setup>
  import BigNumber from 'bignumber.js'
  import { ElInput, ElButton, ElDialog, ElTable, ElTableColumn, ElTooltip, ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus'
  import { format, isValidEmail } from '~/utils'
  import MonoWarn from '~/components/common/icon-svg/MonoWarn.vue'
  import AddInviteLink from '~/components/invite/AddInviteLink.vue'
  import NFTDeposit from '~/components/nft/my/deposit.vue'
  import OrderstableBox from '~/components/common/OrderstableBox.vue'
  import MonoAddBg from '~/components/common/icon-svg/MonoAddBg.vue'
  import MonoCopy from '~/components/common/icon-svg/MonoCopy.vue'
  import MonoEdit from '~/components/common/icon-svg/MonoEdit.vue'
  import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
  import inviteLinkTable from '~/components/invite/inviteLinkTable.vue'
  import backRateTable from '~/components/invite/backRateTable.vue'
  import backMoneyTable from '~/components/invite/backMoneyTable.vue'
  import inviteRecordTable from '~/components/invite/inviteRecordTable.vue'
  import { useCommonData } from '~/composables/index'
  import { useUserStore } from '~/stores/useUserStore'
  import { getMyInvAPI, getInvitattionLinkListAPI, sendLinkAPI, editInvitationLinkAPI, getRateAPI, applyBackTwo } from '~/api/tt.ts'
  const { locale, t } = useI18n()
  const router = useRouter()
  const useCommon = useCommonData()
  const store = useUserStore()
  const { isLogin, userInfo } = storeToRefs(store)
  const curTab = ref(0)
  const todayInvInfo = ref({})
  const isShowInviteLink = ref(false)
  const isShowApply = ref(false)
  const isLoading = ref(false)
  const isMobile = ref(false)
  const screenWidth = ref(0)
  const updateScreenWidth = () => {
    screenWidth.value = window.innerWidth
    isMobile.value = screenWidth.value <= 768
  }
  const isShowHtMl = ref(false)
  const pages = ref({
    page: 1,
    size: 100,
    total: 0
  })
  const currentList = ref([
    {
      level: 0,
      invite: '0-9',
      backNo1: '20%',
      backHas1N: '40%',
      backHas1: '50%',
      back2: t('无')
    },
    {
      level: 1,
      invite: '10-29',
      backNo1: '25%',
      backHas1N: '40%',
      backHas1: '50%',
      back2: t('无')
    },
    {
      level: 2,
      invite: '30-99',
      backNo1: '30%',
      backHas1N: '40%',
      backHas1: '50%',
      back2: '5%'
    },
    {
      level: 3,
      invite: t('100及以上'),
      backNo1: '40%',
      backHas1N: '40%',
      backHas1: '50%',
      back2: '10%'
    }
  ])
  const applyFun = async() => {
    if (todayInvInfo.value.total_inv * 1 >= 30) {
      const { data } = await applyBackTwo()
      if (data) {
        isShowApply.value = true
      }
    } else {
      isShowApply.value = true
    }
  }
  const invitationDefault = ref(null)
  const isShowDeposit = ref(false)
  const goNftFun = () => {
    isMobile.value ? router.push(`/${locale.value}/nft/my/deposit`) : isShowDeposit.value = true
  }
  const getInviteInfo = async() => {
    const { data } = await getMyInvAPI()
    if (data) {
      console.info(data, 'dheuduehue')
      todayInvInfo.value = data
    }
  }
  const invitationlList = ref([])
  const checked = ref({})
  const inviteLength = ref(0)
  const isShow = ref(false)
  const email = ref('')
  const curItem = ref({})
  const bzText = ref('')
  const rateInfo = ref({})
  const nextInfo = ref({})
  const isDisabled = computed(() => {
    if (email.value === '' || !isValidEmail(email.value)) {
      return true
    } else {
      return false
    }
  })
  const sendInviteFun = async() => {
    if (email.value === '' || !isValidEmail(email.value)) {
      return false
    }
    const { data, error } = await sendLinkAPI({
      url: invitationLink.value,
      type: 1,
      to: email.value,
      lang: locale.value === 'zh' ? 'zh' : 'en'
    })
    if (data) {
      useCommon.showMsg('success', t('发送成功'))
      email.value = ''
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const getRateInfo = async() => {
    const { data } = await getRateAPI()
    
    if (data) {
      rateInfo.value = data
      rateInfo.value.back1Rate = rateInfo.value && format(new BigNumber(rateInfo.value.back1).multipliedBy(100).toNumber())
      rateInfo.value.back2Rate = rateInfo.value && format(new BigNumber(rateInfo.value.back2).multipliedBy(100).toNumber())
      nextInfo.value = data.next_level
      nextInfo.value.level = nextInfo.value.level + 1
      nextInfo.value.back1Rate = nextInfo.value && format(new BigNumber(nextInfo.value.back1).multipliedBy(100).toNumber())
      nextInfo.value.back2Rate = nextInfo.value && format(new BigNumber(nextInfo.value.back2).multipliedBy(100).toNumber())
      console.info(rateInfo.value, nextInfo.value, 'dhdududeudhue')
    }
  }
  const updateInvitation = (isDefault) => {
    if (isDefault) {
      pages.value.page = 1
    }
    getInvitattionList()
  }
  const getInvitattionList = async() => {
    const { data } = await getInvitattionLinkListAPI({
      page: pages.value.page,
      size: pages.value.size
    })
    if (data) {
      invitationlList.value = data.rows
      invitationlList.value.forEach((item) => {
        checked.value[item.id] = item.is_default === 1
        if (item.is_default === 1) {
          invitationDefault.value = item
        }
      })
      inviteLength.value = data.count
      pages.value.total = data.count
    }
  }
  const editDefault = (item) => {
    ElMessageBox.confirm(t('您确定要设置为默认吗？'), t('提示'), {
      distinguishCancelAndClose: true,
      confirmButtonText: t('确认'),
      cancelButtonText: t('取消'),
      beforeClose: async(action, instance, done) => {
        if (action === 'confirm') {
          instance.confirmButtonLoading = true
          const { data, error } = await editInvitationLinkAPI({
            id: item.id,
            is_default: 1
          })
          if (data) {
            useCommon.showMsg('success', t('修改成功'))
            getInvitattionList()
            instance.confirmButtonLoading = false
            done()
          } else {
            useCommon.showMsg('error', useCommon.err(error.code, error))
            instance.confirmButtonLoading = false
          }
        } else {
          done()
        }
      }
    })
  }
  const handleCommand = (item) => {
    if (item.id === invitationDefault.value.id) {
      return false
    }
    editDefault(item)
  }
  const invitationLink = computed(() => {
    return `https:${useCommon.domain.value}/${locale.value}/login/register?invite_code=${invitationDefault.value.code}`
  })
  watch(() => isLogin.value, async(val) => {
    isLoading.value = true
    if (val) {
      await getInviteInfo()
      await getRateInfo()
      await getInvitattionList()
    }
    isLoading.value = false
  }, {
    immediate: true
  })
  onMounted(() => {
    updateScreenWidth()
    setTimeout(() => {
      isShowHtMl.value = true
    })
  })
</script>
<style lang="scss">
  .invite-container{
    .invite-wrapper{
      .invite-banner{
        width:100%;
        height:auto;
        overflow:hidden;
        &.en{
          .invite-center-container{
            .invitr-center-cont{
              &.align-start{
                align-items:center;
              }
              .banner-left{
                margin-right:8%;
                h1{
                  font-size:40px;
                }
              }
            }
          }
        }
        .banner-text-box{
          width:100%;
          height:87px;
          margin-bottom:20px;
          background: linear-gradient(90deg, #14151A 0%, #33343A 100%);
          .banner-text{
            margin:0 auto;
            color:#fff;
            dl{
              height:87px;
              margin:0 5%;
              dt{
                width:50px;
                height:50px;
                margin-right:16px;
                &.icon-my-peoplecircle{
                  background:url('@/assets/images/invite/icon-my-peoplecircle.png') no-repeat center;
                  background-size:100% auto;
                }
                &.icon-my-signcircle{
                  background:url('@/assets/images/invite/icon-my-signcircle.png') no-repeat center;
                  background-size:100% auto;
                }
                &.icon-my-giftcircle{
                  background:url('@/assets/images/invite/icon-my-giftcircle.png') no-repeat center;
                  background-size:100% auto;
                }
              }
              dd{
                h3{
                  font-size:18px;
                }
                p{
                  font-size:12px;
                  margin-top:4px;
                }
              }
            }
          }
        }
        .invite-center-container{
          width:100%;
          height:auto;
          padding:40px 0;
          border-radius:40px;
          background: linear-gradient(180deg, rgba(240, 185, 11, 0) 0%, rgba(240, 185, 11, 0.1) 100%);
          .invitr-center-cont{
            max-width:1920px;
            margin:0 auto;
            .banner-left{
              width:40%;
              margin-right:10%;
              h1{
                font-size:54px;
                @include color(tc-primary);
                span{
                  @include color(theme);
                }
              }
              .banner-text-bg{
                padding-bottom:24px;
                font-size:28px;
                @include color(tc-secondary);
                &.def{
                  font-size:32px;
                  @include color(theme);
                }
                a{
                  padding:8px 20px;
                  background: rgba(240, 185, 11, 0.1);
                  font-size:14px;
                  border-radius:40px;
                  @include color(theme);
                }
              }
              .left-img-cont{
                width:428px;
                height:auto;
              }
            }
            .banner-right{
              width:600px;
              height:auto;
              border-radius:12px;
              border:1px solid;
              @include bg-color(bg-primary);
              @include border-color(border);
              .invite-banner-cont-box{
                padding:0 24px 32px;
                &.def{
                  padding:24px 24px 60px;
                  .cont-box, .email-box, .email-btn{
                    margin-top:32px;
                  }
                }
                .invite-title{
                  border-bottom:1px solid;
                  margin:0 -24px;
                  padding:24px 24px 12px;
                  @include border-color(border);
                  a{
                    font-size:14px;
                    @include color(theme);
                  }
                }
                .invite-level{
                  padding-top:20px;
                  .level-item{
                    h3{
                      font-size:16px;
                      @include color(tc-secondary);
                    }
                    p{
                      font-size:32px;
                      padding-top:4px;
                    }
                  }
                }
                .cont-box{
                  margin-top:20px;
                  &.has-bg{
                    padding:12px 20px;
                    border-radius:6px;
                    @include bg-color(bg-quaternary);
                  }
                  h3{
                    font-weight:normal;
                    white-space:nowrap;
                    font-size:14px;
                    margin-right:8px;
                    @include color(tc-secondary);
                  }
                  p{
                    width:95%;
                  }
                }
                .email-box{
                  margin-top:20px;
                  .label-item{
                    font-size:14px;
                    margin-right:16px;
                    font-weight:400;
                    @include color(tc-secondary);
                  }
                }
                .email-btn{
                  margin-top:20px;
                  .el-button{
                    height:44px;
                  }
                  .share-btn{
                    height:44px;
                    color:rgba(65, 70, 85, 1);
                    border-radius:4px;
                    cursor:pointer;
                    @include bg-color(theme);
                    span{
                      display:block;
                      margin-left:8px;
                      width:20px;
                      height:20px;
                      background: url('@/assets/images/invite/ewm-icon.png') no-repeat center;
                      background-size:100% auto;
                    }
                  }
                }
              }
            }
          }
        }
      }
      .no-login-container{
        padding:20px;
        .no-login-wrapper{
          height:426px;
          border-radius:12px;
        }
      }
      .invite-cont-box{
        margin-top:20px;
        padding:0 20px 20px;
        &.flex-box{
          flex-wrap: wrap;
        }
        .invite-pd{
          padding:20px;
          font-size:14px;
          @include color(tc-primary);
        }
        .zonglan-title{
          padding:20px 0;
          font-size:24px;
          @include color(tc-primary);
        }
        .zonglan-cont{
          padding:0 0 20px 0;
          .zonglan-left{
            flex:1.5;
            ul{
              li {
                h3{
                  font-size:14px;
                  @include color(tc-primary);
                }
                p{
                  font-size:14px;
                  margin-top:18px;
                }
              }
            }
          }
          .zonglan-right{
            .today-box{
              width:calc(50% - 8px);
              margin:8px 0;
              margin-right:16px;
              border-radius:12px;
              height:auto;
              padding:16px 24px;
              border: 1px solid;
              font-size:14px;
              @include color(tc-primary);
              @include border-color(border);
              span{
                display:block;
                padding-bottom:4px;
                @include color(tc-secondary);
              }
              &:nth-child(2n){
                margin-right:0;
              }
            }
          }
        }
      }
      .list-container-box{
        margin:0 20px;
        border:1px solid;
        border-radius:12px;
        @include border-color(border);
        .list-pd{
          padding:20px;
        }
        .list-tab{
          border-bottom:1px solid;
          @include border-color(border);
          li {
            height:46px;
            line-height:46px;
            margin-right:14px;
            font-size:14px;
            position:relative;
            cursor:pointer;
            @include color(tc-secondary);
            &.active{
              @include color(tc-primary);
              &:after{
                content: '';
                width:14px;
                height:2px;
                display:block;
                position:absolute;
                bottom:0;
                left:50%;
                margin-left:-7px;
                @include bg-color(theme);
              }
            }
          }
        }
        .container-box{

        }
      }
      .rate-info-list{
        margin:20px;
        border:1px solid;
        border-radius:12px;
        padding: 0 20px;
        @include border-color(border);
        .invite-title{
          padding:20px 0px 0;
          font-size:20px;
          @include color(tc-primary);
        }
        .el-table{
          margin-top:20px;
          &.invite-rule-table{
            th.el-table__cell{
              border-bottom:1px solid !important;
              border-right:1px solid !important;
              @include border-color(border);
            }
            th.el-table_1_column_4, th.el-table_1_column_7{
              background: rgba(240, 185, 11, 0.1) !important;
            }
          }
          &:before, &:after{
            // display:none;
          }
          .el-table__border-left-patch{
            // display:none;
          }
          // .is-group{
          //   th{
          //     &.el-table_1_column_1, &.el-table_1_column_2, &.el-table_1_column_3, &.el-table_1_column_6{
          //       background: rgba(240, 185, 11, 0.1) !important;
          //       .cell{
          //         @include color(tc-primary);
          //       }
          //     }
          //   }
          //   .el-table_1_column_3_column_4, .el-table_1_column_3_column_5, .el-table_1_column_6_column_7{
          //     background:rgba(240, 185, 11, 0.04) !important;
          //   }
          // }
          .el-table__inner-wrapper{
            border:0;
            &:after{
              // display:none;
            }
          }
        }
      }
      .info-cont-box{
        padding:0px 40px 20px;
        .invite-title{
          padding-bottom:12px;
          font-size:20px;
          @include color(tc-primary);
        }
        p{
          font-size:14px;
          @include color(tc-secondary);
          padding-bottom:8px;
        }
      }
    }
  }
  .share-cont-box{
    width:320px;
    padding:32px 0;
    .share-logo{
      width:130px;
      height:40px;
      background: url('~/assets/images/common/logo.png') no-repeat center;
      background-size:100% auto;
    }
    .share-sub-title{
      font-size:14px;
      padding:32px 0 8px;
    }
    .share-title{
      font-size:24px;
      padding-bottom:16px;
    }
    .share-code-box{
      border-radius:12px;
      overflow:hidden;
      padding:10px;
      background-color:#ffffff;
    }
  }
  @include md {
    .invite-container{
      .invite-wrapper{
        .invite-banner{
          .banner-text-box{
            height:60px;
            .banner-text{
              dl{
                height:60px;
                dt{
                  width:40px;
                  height:40px;
                }
                dd{
                  p{
                    display:none;
                  }
                }
              }
            }
          }
          .invite-center-container{
            padding:20px 0;
            border-radius:32px;
            .invitr-center-cont{
              .banner-left{
                margin-right:5%;
                h1{
                  font-size:48px;
                }
                .banner-text-bg{
                  padding-bottom:24px;
                  font-size:24px;
                  @include color(tc-secondary);
                  &.def{
                    font-size:32px;
                    @include color(theme);
                  }
                  a{
                    padding:8px 20px;
                    background: rgba(240, 185, 11, 0.1);
                    font-size:14px;
                    border-radius:40px;
                    @include color(theme);
                  }
                }
                .left-img-cont{
                  width:380px;
                  height:auto;
                }
              }
              .banner-right{
                width:460px;
                .invite-banner-cont-box{
                  padding:0 24px 32px;
                  &.def{
                    padding:24px 24px 60px;
                    .cont-box, .email-box, .email-btn{
                      margin-top:32px;
                    }
                  }
                  .invite-title{
                    border-bottom:1px solid;
                    margin:0 -24px;
                    padding:24px 24px 12px;
                    @include border-color(border);
                    a{
                      font-size:14px;
                      @include color(theme);
                    }
                  }
                  .invite-level{
                    padding-top:20px;
                    .level-item{
                      h3{
                        font-size:14px;
                        @include color(tc-secondary);
                      }
                      p{
                        font-size:28px;
                        padding-top:4px;
                      }
                    }
                  }
                  .cont-box{
                    margin-top:20px;
                    &.has-bg{
                      padding:12px 20px;
                      border-radius:6px;
                      @include bg-color(bg-quaternary);
                    }
                    h3{
                      font-weight:normal;
                      white-space:nowrap;
                      font-size:14px;
                      margin-right:8px;
                      @include color(tc-secondary);
                    }
                    p{
                      width:95%;
                    }
                  }
                  .email-box{
                    margin-top:20px;
                    .label-item{
                      font-size:14px;
                      margin-right:16px;
                      font-weight:400;
                      @include color(tc-secondary);
                    }
                  }
                  .email-btn{
                    margin-top:20px;
                    .el-button{
                      height:44px;
                    }
                    .share-btn{
                      height:44px;
                      color:rgba(65, 70, 85, 1);
                      border-radius:4px;
                      cursor:pointer;
                      @include bg-color(theme);
                      span{
                        display:block;
                        margin-left:8px;
                        width:20px;
                        height:20px;
                        background: url('@/assets/images/invite/ewm-icon.png') no-repeat center;
                        background-size:100% auto;
                      }
                    }
                  }
                }
              }
            }
          }
        }
        .no-login-container{
          .no-login-wrapper{
            height:144px;
            .font-size-24{
              font-size:20px !important;
            }
          }
        }
        .invite-cont-box{
          .invite-all-left{
            height:268px;
            margin-right:20px;
            .li-w50{
            }
            ul{
              width:100%;
              h3{
                font-size:18px;
                span{
                  font-size:14px;
                  margin-left:4px;
                }
              }
              p{
                padding-top:20px;
              }
            }
          }
          .invite-all-center{
            margin-right:0;
            margin-bottom:20px;
            width:100%;
            flex:inherit;
            order:-1;
          }
          .invite-all-right{
            border:1px solid;
            border-radius:12px;
            @include border-color(border);
            .invite-pd{
              height:100%;
              .cont-box,.pos-bottom-box{
                width:100%;
              }
              .cont-box{
                h3{
                  font-size:14px;
                  font-weight:normal;
                  @include color(tc-secondary);
                }
              }
              h3{
                font-weight:normal;
              }
            }
          }
          .zonglan-cont{
            margin-top:20px;
            &.flex-box{
              display:block;
            }
            .zonglan-left{
              flex:1;
            }
            .zonglan-right{
              margin-top:20px;
            }
          }
        }
      }
    }
  }
  @include mb{
    .invite-container{
      .invite-wrapper{
        .invite-banner{
          .banner-text-box{
            height:auto;
            padding:12px 0;
            margin-bottom:0;
            .banner-text{
              align-items:flex-start;
              .mg-l20{
                margin-left:0 !important;
              }
              .mg-r20{
                margin-right:0 !important;
              }
              dl{
                height:auto;
                margin:0 5%;
                &.flex-box{
                  flex-direction: column;
                }
                dt{
                  width:42px;
                  height:42px;
                  margin-right:0;
                }
                dd{
                  text-align:center;
                  h3{
                    font-size:14px;
                    margin-top:10px;
                    a{
                      display:block;
                    }
                  }
                  p{
                    display:none;
                  }
                }
              }
            }
          }
          &.en{
            .invite-center-container{
              .invitr-center-cont{
                .banner-left{
                  h1{
                    font-size:24px;
                  }
                }
              }
            }
          }
          .invite-center-container{
            padding:24px 0;
            border-radius:0;
            background: none;
            .invitr-center-cont{
              max-width:100%;
              &.flex-box{
                flex-direction: column;
              }
              .banner-left{
                width:auto;
                margin-right:0;
                border-radius:12px;
                display:flex;
                align-items:center;
                justify-content: space-between;
                background: linear-gradient(180deg, rgba(240, 185, 11, 0) 0%, rgba(240, 185, 11, 0.1) 100%);
                h1{
                  font-size:24px;
                  padding-left:16px;
                  @include color(tc-primary);
                  span{
                    @include color(theme);
                  }
                }
                .banner-text-bg{
                  padding-left:16px;
                  padding-bottom:0;
                  padding-top:8px;
                  font-size:16px;
                  @include color(tc-secondary);
                  &.def{
                    font-size:18px;
                    @include color(theme);
                  }
                  a{
                    padding:4px;
                    background: none;
                    font-size:12px;
                    border-radius:0;
                    @include color(theme);
                  }
                }
                .left-img-cont{
                  width:30%;
                  height:auto;
                  margin-right:16px;
                }
              }
              .banner-right{
                width:100%;
                height:auto;
                border-radius:0;
                border:0;
                .invite-banner-cont-box{
                  padding:0 16px 32px;
                  &.def{
                    padding:0px 16px 32px;
                    .cont-box, .email-box, .email-btn{
                      margin-top:20px;
                    }
                  }
                  .invite-title{
                    border-bottom:1px solid;
                    margin:0 -16px;
                    padding:24px 16px 12px;
                    @include border-color(border);
                    a{
                      font-size:14px;
                      @include color(theme);
                    }
                  }
                  .invite-level{
                    .level-item{
                      h3{
                        font-size:14px;
                      }
                      p{
                        font-size:24px;
                        padding-top:4px;
                      }
                    }
                  }
                  .cont-box{
                    margin-top:20px;
                    &.has-bg{
                      padding:12px;
                      border-radius:6px;
                      @include bg-color(bg-quaternary);
                      &.flex-box{
                        flex-direction: column;
                        align-items:flex-start;
                        .flex-1{
                          width:100%;
                          svg{
                            margin-top:8px;
                            width:20px !important;
                            height:20px !important;
                          }
                        }
                      }
                    }
                    h3{
                      font-weight:normal;
                      white-space:nowrap;
                      font-size:14px;
                      margin-right:8px;
                      @include color(tc-secondary);
                    }
                    p{
                      padding-top:8px;
                    }
                  }
                  .email-box{
                    margin-top:20px;
                    .label-item{
                      font-size:14px;
                      margin-right:16px;
                      font-weight:400;
                      @include color(tc-secondary);
                    }
                  }
                  .email-btn{
                    margin-top:20px;
                    .el-button{
                      height:44px;
                      &.mg-r16{
                        margin-right:0 !important;
                      }
                    }
                    .share-btn{
                      display:none;
                    }
                  }
                }
              }
            }
          }
        }
        .no-login-container{
          margin:16px;
          border:1px solid;
          border-radius:12px;
          @include border-color(border);
          .no-login-wrapper{
            height:auto;
            h2{
              &.font-size-24{
                font-size:16px !important;
              }
            }
          }
        }
        .invite-cont-box{
          position:relative;
          z-index:30;
          margin-top:-10%;
          padding:0 16px 20px;
          .invite-pd{
            padding:16px;
          }
          &.flex-box{
            flex-direction: column;
          }
          .invite-all-left{
            width:100%;
            height:auto;
            border-radius:12px;
            border: 1px solid;
            margin-bottom:12px;
            background: linear-gradient(90deg, rgba(240, 185, 11, 0.08) 0%, rgba(240, 185, 11, 0.02) 100%);
            @include border-color(border);
            &.mg-lr20{
              margin-left:0 !important;
              margin-right:0 !important;
            }
            .invite-pd{
              height:100%;
            }
            ul{
              width:100%;
              h3{
                font-size:18px;
                span{
                  font-size:14px;
                  margin-left:4px;
                }
              }
              p{
                padding-top:32px;
              }
              h4{
                padding-bottom:12px;
                span{
                  font-weight:normal;
                  margin-right:8px;
                }
                font-weight:600;
              }
            }
          }
          .invite-all-center{
            margin-bottom:12px;
            order: -1;
            width:100%;
            @include bg-color(bg-primary);
            height:auto;
            border:1px solid;
            border-radius:12px;
            position:relative;
            @include border-color(border);
            .add-link-box{
              font-size:14px;
              position:absolute;
              right:20px;
              top:20px;
              span{
                font-size:14px;
              }
            }
            .cont-box{
              &.has-bg{
                padding:0;
                @include bg-color(bg-primary);
              }
              margin-bottom:20px;
              &.flex-box{
                display:block;
              }
              h3{
                font-weight:normal;
                font-size:14px;
                @include color(tc-secondary);
              }
            }
            .email-box{
              width:100%;
              .label-item{
                display:block;
              }
              .input-email-text{
                margin:0 10px;
                .el-input__inner{
                  height:44px !important;
                }
              }
              .share-btn{
                display:none;
              }
            }
          }
          .invite-all-right{
            width:100%;
            height:auto;
            border:1px solid;
            border-radius:12px;
            @include border-color(border);
            .invite-pd{
              height:100%;
              .cont-box,.pos-bottom-box{
                width:100%;
              }
              .cont-box{
                h3{
                  font-size:14px;
                  font-weight:normal;
                  @include color(tc-secondary);
                }
              }
              h3{
                font-weight:normal;
              }
              .el-button{
                margin-top:40px;
              }
            }
          }
          .zonglan-title{
            padding-left:0;
          }
          .zonglan-cont{
            padding-left:0;
            padding-bottom:0;
            padding-top:16px;
            &.flex-box{
              display:block;
            }
            .zonglan-left{
              flex:1;
              ul{
                width:50%;
                margin-bottom:16px;
                &.flex-1{
                  flex:none;
                }
              }
            }
            .zonglan-right{
              .today-box{
                // width:100%;
                // margin-right:0;
                // margin-top:0;
                padding:12px 16px;
                span{
                  font-size:12px;
                }
                .font-size-18{
                  line-height:24px;
                  font-size:14px !important;
                }
              }
            }
          }
        }
        .list-container-box{
          margin:0px;
          border:0;
          .list-pd{
            padding:0 20px;
          }
          .list-tab{
            border-bottom:1px solid;
            @include border-color(border);
            li {
              line-height:20px;
              padding:14px 0;
              height:auto;
              margin-right:14px;
              font-size:14px;
              position:relative;
              cursor:pointer;
              @include color(tc-secondary);
              &.active{
                @include color(tc-primary);
                &:after{
                  content: '';
                  width:14px;
                  height:2px;
                  display:block;
                  position:absolute;
                  bottom:0;
                  left:50%;
                  margin-left:-7px;
                  @include bg-color(theme);
                }
              }
            }
          }
          .container-box{

          }
        }
        .rate-info-list{
          margin:20px;
          border:0;
          border-radius:0;
          padding:0;
          .invite-title{
            padding:0;
            font-size:20px;
            @include color(tc-primary);
          }
        }
        .info-cont-box{
          padding:0px 20px 20px;
          .invite-title{
            padding-bottom:12px;
            font-size:20px;
            @include color(tc-primary);
          }
          p{
            font-size:14px;
            @include color(tc-secondary);
            padding-bottom:8px;
          }
        }
      }
    }
    html.dark .invite-container .invite-wrapper .no-login-container .no-login-wrapper{
      background: linear-gradient(0deg, #181a1f, #181a1f),linear-gradient(90deg, #181a1f 0%, #181a1f 100%);
    }
  }
</style>