<template>
  <div class="rate-info-list">
    </br>
    <h2 class="invite-title">{{ $t('返佣规则') }}</h2>
    <el-table :data="currentList" class="invite-rule-table">
      <el-table-column :label="$t('现货')" align="left">
        <template #header>
          <span class="fit-tc-primary" style="padding-left:16px;">{{ $t('现货') }}</span>
        </template>
        <el-table-column :label="$t('等级')" prop="level" width="80" align="center">
          <template #default="scope">
            Lv.{{ scope.row.level + 1 }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('有效邀请好友数量(人)')" prop="invite" align="center" />
      </el-table-column>
      <el-table-column :label="$t('一级返佣比例')" align="center">
        <el-table-column prop="backNo1" :label="$t('未充值NFT')" align="center"></el-table-column>
        <el-table-column prop="backHas1" :label="$t('充值NFT')" align="center">
          <template #header>
            <p>{{ $t('充值NFT') }}</p>
            <div class="flex-box space-between">
              <span>{{ $t('普通') }} NFT</span>
              <span>VIP NFT</span>
            </div>
          </template>
          <template #default="{ row }">
            <div class="flex-box space-between">
              <span>{{ row.backHas1N }}</span>
              <span>{{ row.backHas1 }}</span>
            </div>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column :label="$t('二级返佣比例')" align="center">
        <el-table-column prop="back2" :label="$t('有效邀请好友30人即可申请二级返佣')" align="right">
          <template #header>
            <el-tooltip placement="top">
              <template #content>
                <div style="width:264px;">
                  <p>{{ $t('有效邀请好友30人即可申请二级返佣') }}</p>
                  <div class="pd-12">
                    <p>{{ $t('举例说明：') }}</p>
                    <p>{{ $t('你开启了二级返佣，仅邀请了好友Chad，Chad邀请了100位好友。') }}</p>
                    <p>{{ $t('你将获得，Chad最高50%手续费返佣+Chad邀请的100位好友的10%手续费返佣。') }}</p>
                  </div>
                </div>
              </template>
              <div class="flex-box cursor-pointer space-end">
                <span class="mg-r4">{{ $t('有效邀请好友30人即可申请二级返佣') }}</span>
                <MonoWarn :size="16" class="fit-tc-secondary" />
              </div>
            </el-tooltip>
          </template>
          <template #default="{ row }">
            <div class="flex-box space-center pd-l12">
              {{ row.back2 }}
            </div>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
  </div>
  <div class="info-cont-box">
    <h2 class="invite-title">{{ $t('活动说明') }}</h2>
    <div class="info-text-box">
      <p>{{ $t('1.活动期间，邀请人可获取有效受邀好友的手续费返佣，无考核期。') }}</p>
      <p>{{ $t('2.子账户无法成为邀请人。') }}</p>
      <p>{{ $t('3.受邀人在账户注册成功后的七日内，充值100 USDT及以上或交易200 USDT及以上，即为有效邀请好友。') }}</p>
      <p>{{ $t('4.被邀请用户的子账户交易手续费将计入母账户后再进行奖励发放。') }}</p>
      <p>{{ $t('5.受邀好友数量没有限制，返佣金额没有上限。') }}</p>
      <p>{{ $t('6.现货返佣次日08:00（UTC+8)统一发放，实际发放时间可能有延迟，以次日实际发放时间为准。') }}</p>
      <p>{{ $t('7.邀请人充值锁定NFT，还将获得不定期空投；锁定NFT数量越多，空投奖励就越大。') }}</p>
      <p>{{ $t('8.邀请好友20人即可申请二级返佣；一次申请，终身有效。二级返佣遵循一级返佣同等规则。') }}</p>
    </div>
  </div>
  <div class="info-cont-box">
    <h2 class="invite-title">{{ $t('返佣说明') }}</h2>
    <div class="info-text-box">
      <p>{{ $t('1.通过子账户、第三方渠道或者从平台其他账号进行的充值将不计入充值额度；子账户交易量将计入交易额度。') }}</p>
      <p>{{ $t('2.如果被邀请用户的某个交易订单中，交易对手方为负费率，则本订单会先剔除平台补贴部分后再进行返佣奖励发放。') }}</p>
      <p>{{ $t('3.邀友返佣福利不可与代理返佣福利同期享受。') }}</p>
      <p>{{ $t('4.以下将不产生返佣：受邀用户为特殊费率用户，如做市商、VIP等；邀请或被邀请账户被冻结、被标记为高风险账户等；受限制区域；搭建与本平台相似的第三方网站并引导跳转至官网；异常交易的订单手续费等。') }}</p>
      <p>{{ $t('5.若邀请人在活动期间涉嫌参与欺诈或滥用活动，包括但不限于通过同一 IP 或设备注册多个账户、批量注册账户以获得额外奖励等，一经发现违反推广活动的相应风控规则，推荐人奖励资格将被取消，同时被推荐人的推广以及从中产生的奖励将视为无效。') }}</p>
      <p>{{ $t('6.活动如有调整，以平台公告为准，由于市场环境的改变，欺诈风险的存在等原因，平台保留随时对奖励规则作出调整的最终解释权。') }}</p>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ElTooltip, ElTable, ElTableColumn } from 'element-plus'
  import { format } from '~/utils'
  import MonoWarn from '~/components/common/icon-svg/MonoWarn.vue'
  const { locale, t } = useI18n()
  const currentList = ref([
    {
      level: 0,
      invite: '0-9',
      backNo1: '20%',
      backHas1N: '40%',
      backHas1: '50%',
      back2: t('无')
    },
    {
      level: 1,
      invite: '10-29',
      backNo1: '30%',
      backHas1N: '40%',
      backHas1: '50%',
      back2: t('无')
    },
    {
      level: 2,
      invite: '30-99',
      backNo1: '40%',
      backHas1N: '40%',
      backHas1: '50%',
      back2: '5%'
    },
    {
      level: 3,
      invite: t('100及以上'),
      backNo1: '40%',
      backHas1N: '40%',
      backHas1: '50%',
      back2: '10%'
    }
  ])
</script>
<style lang="scss">
  .rate-info-list{
    margin:20px;
    border:1px solid;
    border-radius:12px;
    padding: 0 20px;
    @include border-color(border);
    .invite-title{
      padding:20px 0px 0;
      font-size:20px;
      @include color(tc-primary);
    }
    .el-table{
          margin-top:20px;
          &.invite-rule-table{
            th.el-table__cell{
              border-bottom:1px solid !important;
              border-right:1px solid !important;
              @include border-color(border);
            }
            th.el-table_1_column_4, th.el-table_1_column_7{
              background: rgba(240, 185, 11, 0.1) !important;
            }
          }
          &:before, &:after{
            // display:none;
          }
          .el-table__border-left-patch{
            // display:none;
          }
          .el-table__inner-wrapper{
            border:0;
            &:after{
              // display:none;
            }
          }
        }
  }
  .info-cont-box{
    padding:0px 40px 20px;
    .invite-title{
      padding-bottom:12px;
      font-size:20px;
      @include color(tc-primary);
    }
    p{
      font-size:14px;
      @include color(tc-secondary);
      padding-bottom:8px;
    }
  }
  @include mb {
    .rate-info-list{
      margin:20px;
      border:0;
      border-radius:0;
      padding:0;
      .invite-title{
        padding:0;
        font-size:20px;
        @include color(tc-primary);
      }
    }
    .info-cont-box{
      padding:0px 20px 20px;
      .invite-title{
        padding-bottom:12px;
        font-size:20px;
        @include color(tc-primary);
      }
      p{
        font-size:14px;
        @include color(tc-secondary);
        padding-bottom:8px;
      }
    }
  }
</style>