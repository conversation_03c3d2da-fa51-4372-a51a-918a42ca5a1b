<template>
  <Header />
  <div v-loading="isLoading" class="invite-link-container">
    <div class="invite-link-wrapper">
      <div class="invite-link-title-h5-cont flex-box">
        <NuxtLink :to="`/${locale}/invite`" class="fit-tc-secondary">{{ $t('邀请返佣') }}</NuxtLink>
        <MonoRightArrowShort :size="16" class="fit-tc-secondary mg-lr4" />
        <span class="active">{{ $t('邀请链接') }}</span>
      </div>
      <div class="invite-link-title flex-box space-between">
        {{ $t('我的邀请链接') }}
        <el-button type="primary" :disabled="pages.total * 1 === 20" @click="addInviteFun">{{ $t('新增邀请链接') }}</el-button>
      </div>
      <div class="invite-link-table">
        <inviteLinkTable :isLogin="isLogin" :user="user" :currentList="invitationlList" :checked="checked" @editRemark="editBz" @getInviteList="pages.page = 1; getInvitattionList()"  />
      </div>
    </div>
  </div>
  <el-dialog v-model="isShow" :title="$t('修改备注')" width="400px" class="bz-dialog" @close="isShow = false">
    <div class="invite-container">
      <div class="invite-cont-box">
        <div class="input-box">
          <el-input v-model="bzText" />
        </div>
        <div class="btn-box mg-t24">
          <el-button type="primary" :disabled="isBzDisabled" @click="editBzFun">{{ $t('确定') }}</el-button>
        </div>
      </div>
    </div>
  </el-dialog>
  <AddInviteLink
    v-if="isShowInviteLink"
    :is-show="isShowInviteLink" 
    :level="rateInfo.level" 
    :rateInfo="rateInfo"
    :user="user"
    @closeAddInvite="isShowInviteLink = false"
    @updateData="updateInvitation"
  />
  <Footer />
</template>
<script lang="ts" setup>
  import { ElDialog } from 'element-plus'
  import BigNumber from 'bignumber.js'
  import { editInvitationLinkAPI, getInvitattionLinkListAPI, getRateAPI } from '~/api/tt.ts'
  import { useCommonData } from '~/composables/index'
  import inviteLinkTable from '~/components/invite/inviteLinkTable.vue'
  import AddInviteLink from '~/components/invite/AddInviteLink.vue'
  import { useUserStore } from '~/stores/useUserStore'
  const { locale } = useI18n()
  const useCommon = useCommonData()
  const store = useUserStore()
  const { isLogin, userInfo } = storeToRefs(store)
  const checked = ref({})
  const invitationlList = ref([])
  const curItem = ref({})
  const isShow = ref(false)
  const bzText = ref('')
  const isLoading = ref(true)
  const pages = ref({
    page: 1,
    size: 20,
    total: 0
  })
  const rateInfo = ref({})
  const nextInfo = ref({})
  const isBzDisabled = computed(() => {
    if (bzText.value !== '') {
      return false
    } else {
      return true
    }
  })
  const user = computed(() => {
    return userInfo.value
  })
  const getRateInfo = async() => {
    const { data } = await getRateAPI()
    if (data) {
      rateInfo.value = data
      rateInfo.value.back1Rate = rateInfo.value && format(new BigNumber(rateInfo.value.back1).multipliedBy(100).toNumber())
      rateInfo.value.back2Rate = rateInfo.value && format(new BigNumber(rateInfo.value.back2).multipliedBy(100).toNumber())
      nextInfo.value = data.next_level
      nextInfo.value.level = nextInfo.value.level + 1
      nextInfo.value.back1Rate = nextInfo.value && format(new BigNumber(nextInfo.value.back1).multipliedBy(100).toNumber())
      nextInfo.value.back2Rate = nextInfo.value && format(new BigNumber(nextInfo.value.back2).multipliedBy(100).toNumber())
    }
  }
  const isShowInviteLink = ref(false)
  const addInviteFun = () => {
    if (pages.value.total * 1 === 20) {
      return false
    }
    isShowInviteLink.value = true
  }
  const updateInvitation = (isDefault) => {
    if (isDefault) {
      pages.value.page = 1
    }
    getInvitattionList()
  }
  const getInvitattionList = async() => {
    const { data } = await getInvitattionLinkListAPI({
      page: pages.value.page,
      size: pages.value.size
    })
    if (data) {
      isLoading.value = false
      invitationlList.value = data.rows
      invitationlList.value.forEach((item) => {
        checked.value[item.id] = item.is_default === 1
      })
      pages.value.total = data.count
    }
    isLoading.value = false
  }
  const editBz = (item) => {
    curItem.value = item
    isShow.value = true
    bzText.value = item.desc
  }
  const editBzFun = async() => {
    const { data, error } = await editInvitationLinkAPI({
      id: curItem.value.id,
      desc: bzText.value
    })
    if (data) {
      useCommon.showMsg('success', t('修改成功'))
      getInvitattionList()
      isShow.value = false
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  watch(() => isLogin.value, async(val) => {
    isLoading.value = true
    if (val) {
      await getRateInfo()
      await getInvitattionList()
    } else {
      useCommon.openLogin()
    }
  })
  onMounted(async() => {
    isLoading.value = true
    if (!isLogin.value) {
      useCommon.openLogin()
      return false
    }
    await getRateInfo()
    await getInvitattionList()
  })
</script>
<style lang="scss" scoped>
  .invite-link-container{
    max-width:1920px;
    margin:0 auto;
    min-height:calc(100vh - 700px);
    .invite-link-wrapper{
      padding:0 24px;
      .invite-link-title-h5-cont{
        padding:20px 0;
        span.active{
          @include color(tc-primary);
        }
      }
      .invite-link-title{
        padding-bottom:20px;
        font-size:20px;
        @include color(tc-primary);
      }
      .invite-link-table{
        padding-bottom:40px;
      }
    }
  }
</style>
