<template>
  <Header />
  <div class="join-us-container">
    <div class="join-us-banner-w100">
      <div class="join-us-banner">
        <div class="banner-center flex-box flex-column space-center align-start">
          <h1>{{ $t('加入我们') }}</h1>
          <p class="font-size-32 fit-tc-secondary pd-t12">{{ $t('共同开创加密货币交易的未来') }}</p>
        </div>
      </div>
    </div>
    <div class="join-us-wrapper">
      <div class="join-us-cont1">
        <h2 class="join-us-title">{{ $t('为什么选择我们？') }}</h2>
        <div class="item-list-one flex-box space-center">
          <dl class="item-box">
            <dt>
              <img v-show="colorMode.preference === 'light'" src="~@/assets/images/join-us/cont-one-img1-light.png" />
              <img v-show="colorMode.preference === 'dark'" src="~@/assets/images/join-us/cont-one-img1-dark.png" />
            </dt>
            <dd>
              <h3>{{ $t('有竞争力的薪酬') }}</h3>
              <p>{{ $t('我们提供具有市场竞争力的薪资并设有绩效奖金激励') }}</p>
            </dd>
          </dl>
          <dl class="item-box">
            <dt>
              <img v-show="colorMode.preference === 'light'" src="~@/assets/images/join-us/cont-one-img2-light.png" />
              <img v-show="colorMode.preference === 'dark'" src="~@/assets/images/join-us/cont-one-img2-dark.png" />
            </dt>
            <dd>
              <h3>{{ $t('丰富的员工福利') }}</h3>
              <p>{{ $t('提供多项员工关怀政策和激励措施帮助员工在工作中保持动力与成就感') }}</p>
            </dd>
          </dl>
          <dl class="item-box">
            <dt>
              <img v-show="colorMode.preference === 'light'" src="~@/assets/images/join-us/cont-one-img3-light.png" />
              <img v-show="colorMode.preference === 'dark'" src="~@/assets/images/join-us/cont-one-img3-dark.png" />
            </dt>
            <dd>
              <h3>{{ $t('职业发展机会') }}</h3>
              <p>{{ $t('公司重视员工成长提供持续学习和职业发展的支持') }}</p>
            </dd>
          </dl>
        </div>
      </div>
      <div class="join-us-cont2">
        <h2 class="join-us-title">{{ $t('我们的核心价值观') }}</h2>
        <div class="item-list-two flex-box space-center">
          <dl class="item-box">
            <dt>{{ $t('用户至上') }}</dt>
            <dd>
              {{ $t('我们始终将用户需求放在首位，致力于提供卓越的服务体验。') }}
            </dd>
          </dl>
          <dl class="item-box">
            <dt>{{ $t('诚信为本') }}</dt>
            <dd>
              {{ $t('我们坚持诚实守信，建立信任，确保透明运营。') }}
            </dd>
          </dl>
          <dl class="item-box">
            <dt>{{ $t('团队合作') }}</dt>
            <dd>
              {{ $t('我们倡导协作精神，共同实现公司的愿景与目标。') }}
            </dd>
          </dl>
          <dl class="item-box">
            <dt>{{ $t('持续创新') }}</dt>
            <dd>
              {{ $t('我们鼓励创新思维，持续改进，以保持行业领先地位。') }}
            </dd>
          </dl>
        </div>
      </div>
      <div class="join-us-cont3">
        <h2 class="join-us-title">{{ $t('招聘流程') }}</h2>
        <p class="join-us-sub-title">{{ $t('我们秉持公开、公平、公正的原则，招聘流程如下：') }}</p>
        <div class="item-list-three flex-box space-center align-start">
          <dl>
            <dt>01</dt>
            <dd>
              <h3>{{ $t('简历筛选') }}</h3>
              <p>{{ $t('人力资源团队将筛选符合职位要求的简历') }}</p>
            </dd>
          </dl>
          <dl>
            <dt>02</dt>
            <dd>
              <h3>{{ $t('面试环节') }}</h3>
              <p>{{ $t('通过初面和复面，深入了解候选人的专业能力和文化契合度') }}</p>
            </dd>
          </dl>
          <dl>
            <dt>03</dt>
            <dd>
              <h3>{{ $t('发放Offer') }}</h3>
              <p>{{ $t('对通过面试的候选人，发放正式录用通知') }}</p>
            </dd>
          </dl>
          <dl>
            <dt>04</dt>
            <dd>
              <h3>{{ $t('入职培训') }}</h3>
              <p>{{ $t('新员工将参加入职培训，熟悉公司文化和业务流程') }}</p>
            </dd>
          </dl>
        </div>
        <div class="contract-cont flex-box space-center flex-column">
          <a :href="`mailto:<EMAIL>?subject=${$t('加入KTX，与我们一起引领加密货币交易的未来！')}`" class="join-btn">{{ $t('立即投送简历') }}</a>
          <p>{{ $t('如有任何招聘相关咨询，请联系') }}<a :href="`mailto:<EMAIL>?subject=${$t('加入KTX，与我们一起引领加密货币交易的未来！')}`" class="fit-theme"><EMAIL></a></p>
          <h3>{{ $t('加入KTX，与我们一起引领加密货币交易的未来！') }}</h3>
        </div>
      </div>
    </div>
  </div>
  <Footer />
</template>
<script lang="ts" setup>
  import { ElButton } from 'element-plus'
  const colorMode = useColorMode()
</script>
<style lang="scss" scoped>
  .join-us-container{
    margin:0 auto;
    height:auto;
    .join-us-banner-w100{
      width:100%;
      height:auto;
      @include bg-color(bg-secondary);
      .join-us-banner{
        max-width:1920px;
        margin:0 auto;
        height:320px;
        @include get-img('~/assets/images/join-us/banner-bg-light.png', '~/assets/images/join-us/banner-bg-dark.png');
        background-size: 320px auto;
        background-repeat:no-repeat;
        background-position:left center;
        @include bg-color(bg-secondary);
        .banner-center{
          max-width:1920px;
          margin:0 auto;
          height:320px;
          @include get-img('~/assets/images/join-us/banner-img-light.png', '~/assets/images/join-us/banner-img-dark.png');
          background-size: 40% auto;
          background-repeat:no-repeat;
          background-position:95% center;
          h1{
            font-size:60px;
            @include color(tc-primary);
            padding-left:24%;
          }
          p{
            padding-left:24%;
            width:780px;
          }
        }
      }
    }
    .join-us-wrapper{
      max-width:1920px;
      margin:0 auto;
    }
    .join-us-title{
      font-size:40px;
      text-align:center;
      padding:40px;
      @include color(tc-primary);
    }
    .join-us-sub-title{
      font-size:16px;
      font-weight:400;
      margin-top:-28px;
      text-align:center;
      @include color(tc-primary);
    }
    .item-list-one{
      .item-box{
        width:380px;
        height:340px;
        border-radius:20px;
        background: linear-gradient(180deg, rgba(240, 185, 11, 0.08) 0%, rgba(240, 185, 11, 0) 100%);
        &:nth-child(2){
          margin:0 12px;
        }
        dt{
          width:200px;
          height:200px;
          margin:20px auto;
          img{
            width:100%;
            height:auto;
          }
        }
        dd{
          text-align:center;
          padding:0 50px;
          @include color(tc-primary);
          h3{
            font-size:20px;
          }
          p{
            padding-top:12px;
            font-size:14px;
            font-weight:400;
          }
        }
      }
    }
    .item-list-two{
      margin-top:20px;
      .item-box{
        width:285px;
        height:103px;
        border:1px solid;
        border-radius:12px;
        margin:0 10px;
        position:relative;
        @include border-color(border);
        dt{
          position:absolute;
          top:-24px;
          left:25%;
          font-size:22px;
          padding:8px 0;
          width:50%;
          text-align:center;
          @include color(tc-primary);
          @include bg-color(bg-primary);
        }
        dd{
          padding:32px 23px 0;
          font-size:14px;
          font-weight:400;
          @include color(tc-primary);
        }
      }
    }
    .item-list-three{
      margin-top:20px;
      dl{
        width:300px;
        text-align:center;
        dt{
          font-size:100px;
          font-weight:400;
          position:relative;
          @include color(tc-tertiary);
          &:before{
            content:'';
            display:block;
            width:12px;
            height:12px;
            border-radius:50%;
            position:absolute;
            left:50%;
            margin-left:-6px;
            bottom:14px;
            z-index:2;
            @include bg-color(theme);
          }
          &:after{
            content: '';
            display:block;
            width:100%;
            position:absolute;
            height:1px;
            bottom:20px;
            @include bg-color(border);
            left:0;
            right:0;
          }
        }
        dd{
          padding:0 24px;
          h3{
            font-size:20px;
            @include color(tc-primary);
          }
          p{
            font-size:14px;
            padding-top:8px;
            @include color(tc-secondary);
          }
        }
      }
    }
    .join-us-cont2{
      padding:40px 0;
    }
    .join-btn{
      height:40px;
      line-height:40px;
      text-align:center;
      border-radius:4px;
      min-width:160px;
      font-size:14px;
      margin:40px auto 20px;
      @include color(tc-primary);
      @include bg-color(theme);
    }
    .contract-cont{
      padding-bottom:40px;
      @include color(tc-primary);
      p{
        font-size:14px;
        font-weight:400;
      }
      h3{
        font-size:20px;
        margin-top:20px;
      }
    }
  }
  @include mb {
    .join-us-container{
      max-width:100%;
      .join-us-banner-w100{
        max-width:100%;
        .join-us-banner{
          max-width:100%;
          width:100%;
          margin:0 auto;
          height:auto;
          @include get-img('~/assets/images/join-us/banner-bg-light.png', '~/assets/images/join-us/banner-bg-dark.png');
          background-size: 112px auto;
          background-repeat:no-repeat;
          background-position:left top;
          .banner-center{
            width:100%;
            margin:0 auto;
            height:auto;
            @include get-img('~/assets/images/join-us/banner-img-light.png', '~/assets/images/join-us/banner-img-dark.png');
            background-size: 70% auto;
            background-repeat:no-repeat;
            background-position:right top;
            padding-top:160px;
            h1{
              padding-left:0;
              font-size:28px;
              text-align:center;
              width:90%;
              margin:0 auto;
              @include color(tc-primary);
            }
            p{
              padding-left:0;
              text-align:center;
              width:90%;
              margin:0 auto;
              padding-bottom:28px;
              &.font-size-32{
                font-size:16px !important;
                line-height:24px !important;
              }
            }
          }
        }
      }
      .join-us-title{
        font-size:24px;
        padding:24px 16px;
      }
      .join-us-sub-title{
        font-size:14px;
        margin-top:-18px;
      }
      .item-list-one{
        padding:0 16px;
        &.flex-box{
          flex-direction: column;
        }
        .item-box{
          width:100%;
          height:auto;
          padding-bottom:24px;
          &:nth-child(2){
            margin:0;
          }
          dt{
            width:160px;
            height:160px;
            margin:20px auto;
          }
        }
      }
      .item-list-two{
        margin-top:0;
        padding: 0 16px;
        &.flex-box{
          flex-direction:column;
        }
        .item-box{
          width:100%;
          height:auto;
          margin:20px 0;
          dt{
            font-size:20px;
          }
          dd{
            padding:32px 23px 32px;
            font-size:14px;
            font-weight:400;
            @include color(tc-primary);
          }
        }
      }
      .item-list-three{
        margin-top:0;
        padding:0 16px;
        &.flex-box{
          flex-direction:column;
          align-items:center;
        }
        dl{
          width:100%;
          dt{
            font-size:80px;
            &:before{
              bottom:4px;
            }
            &:after{
              bottom:10px;
            }
          }
          dd{
            padding:0 24px;
            h3{
              font-size:20px;
              @include color(tc-primary);
            }
            p{
              font-size:14px;
              padding-top:8px;
              @include color(tc-secondary);
            }
          }
        }
      }
      .join-us-cont2{
        padding:0;
      }
      .join-btn{
        height:40px;
        min-width:180px;
        margin:40px auto 20px;
      }
      .contract-cont{
        padding:0 16px;
        padding-bottom:40px;
        @include color(tc-primary);
        p{
          font-size:14px;
          font-weight:400;
        }
        h3{
          text-align:center;
          font-size:20px;
          margin-top:14px;
        }
      }
    }
  }
</style>