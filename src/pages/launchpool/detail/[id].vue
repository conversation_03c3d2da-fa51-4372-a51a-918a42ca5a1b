<template>
  <Header modeType="launchpool" />
  <div v-loading="detailLoading" class="launch-pool-container">
    <div class="launch-pool-container-center">
      <div class="launch-pool-title-h5-cont flex-box">
        <NuxtLink :to="`/${locale}/launchpool`" class="fit-tc-secondary">Launchpool</NuxtLink>
        <MonoRightArrowShort :size="16" class="fit-tc-secondary mg-lr4" />
        <span class="active">{{ $t('项目详情') }}</span>
      </div>
      <div v-if="JSON.stringify(detailInfo) !== '{}'" class="launch-pool-wrapper-detail">
        <div class="back-cont-box flex-box fit-tc-primary">
          <NuxtLink :to="`/${locale}/launchpool`" class="fit-tc-primary">
            <MonoRightArrowShort size="18" class="fit-tc-primary" />
            Launchpool
          </NuxtLink>
        </div>
        <div class="launch-pool-detail-info">
          <dl class="flex-box">
            <dt>
              <img :src="getImgUrl(detailInfo.icon)" class="info-icon" />
            </dt>
            <dd class="flex-box space-between align-start">
              <div class="">
                  <h2 class="flex-box">
                    <span class="fit-tc-primary font-size-24">{{ getJsonObj(detailInfo.name) }}</span>
                    <div class="status-tag mg-l8 flex-box" :class="{'fall': detailInfo.status * 1 === 0, 'rise': detailInfo.status * 1 === 1}">
                      <MonoUnstartIcon v-if="detailInfo.status * 1 === 0" :size="14" class="fit-fall mg-t4" />
                      <MonoWaitIcon v-if="detailInfo.status * 1 === 2" :size="14" class="fit-tc-secondary mg-t4" />
                      <MonoEndIcon v-if="detailInfo.status * 1 === 1" :size="14" class="fit-rise mg-t4" />
                      <div class="mg-l4">{{ statusObj[detailInfo.status] }}</div>
                    </div>
                  </h2>
                  <ul class="flex-box info-link-box">
                    <li><a :href="detailInfo.website" target="_blank">{{ $t('官网') }}</a></li>
                    <li><a :href="detailInfo.white_paper" target="_blank">{{ $t('白皮书') }}</a></li>
                    <li><a :href="detailInfo.rule" target="_blank">{{ $t('详细规则') }}</a></li>
                    <li><a :href="`${useCommon.zendeskUrl(locale)}/articles/4662559501982`" target="_blank">{{ $t('教程') }}</a></li>
                  </ul>
              </div>
              <div class="end-time-box flex-box flex-column">
                <p v-if="detailInfo.status * 1 === 0 || detailInfo.status * 1 === 2">
                  <span class="font-size-14 fit-tc-secondary mg-r8">{{ $t('开始日期') }}</span>
                  <span class="font-size-14 fit-tc-primary">{{ timeFormat(detailInfo.start_time, 'yy-MM-dd hh:mm') }}</span>
                </p>
                <p v-if="detailInfo.status * 1 === 2" class="pd-t16">
                  <span class="font-size-14 fit-tc-secondary mg-r8">{{ $t('结束日期') }}</span>
                  <span class="font-size-14 fit-tc-primary">{{ timeFormat(detailInfo.end_time, 'yy-MM-dd hh:mm') }}</span>
                </p>
                <p v-if="detailInfo.status * 1 === 1 && new Date(detailInfo.end_time).getTime() > new Date().getTime()" class="flex-box">
                  <span class="font-size-14 fit-tc-secondary mg-r8">{{ $t('截止时间') }}</span>
                  <div v-if="JSON.stringify(leftTimeMap) !== '{}'" class="font-size-14 fit-tc-primary flex-box">
                    <span class="font-size-16 mg-lr4">{{ leftTimeMap.d }}</span>
                    <span class="fit-tc-secondary">{{ $t('天') }}</span>
                    <span class="font-size-16 mg-lr4">{{ leftTimeMap.h }}</span>
                    <span class="fit-tc-secondary">{{ $t('小时') }}</span>
                    <span class="font-size-16 mg-lr4">{{ leftTimeMap.m }}</span>
                    <span class="fit-tc-secondary">{{ $t('分') }}</span>
                  </div>
                </p>
              </div>
            </dd>
          </dl>
          <div class="info-desc">
            {{ getJsonObj(detailInfo.desc) }}
          </div>
          <div class="info-amount flex-box pd-t32 pd-b20">
            <span class="fit-tc-secondary font-size-14 mg-r4">{{ $t('今日') }}</span>
            <span class="fit-tc-primary font-size-14 mg-r4">{{ detailInfo.symbol }}</span>
            <span class="fit-tc-secondary font-size-14 mg-r4">{{ $t('挖币额度') }}</span>
            <span class="fit-tc-primary font-size-20">{{ detailInfo.day_total }}</span>
          </div>
          <div class="pd-b24 lauch-pool-info-li-cont">
            <ul class="flex-box flex-wrap">
              <li class="flex-1">
                <span class="fit-tc-secondary font-size-14">{{ $t('锁仓代币') }}</span>
                <p class="fit-tc-primary font-size-20 pd-t8">{{ detailInfo.lock_symbol }}</p>
              </li>
              <li class="flex-1">
                <span class="fit-tc-secondary font-size-14">{{ $t('空投总量') }}</span>
                <p class="fit-tc-primary font-size-20 pd-t8">{{ detailInfo.total }}</p>
              </li>
              <li v-if="detailInfo.lock_type * 1 === 1" class="flex-1">
                <span class="fit-tc-secondary font-size-14">{{ $t('NFT当前锁仓量') }}</span>
                <p class="fit-tc-primary font-size-20 pd-t8">{{ detailInfo.lock_value }}</p>
              </li>
              <li class="flex-1">
                <span class="fit-tc-secondary font-size-14">{{ $t('锁仓时长') }}</span>
                <p class="fit-tc-primary font-size-20 pd-t8">{{ Number(detailInfo.lock_time / 24).toFixed(1, true) }} {{ $t('天') }}</p>
              </li>
              <li class="flex-1">
                <span class="fit-tc-secondary font-size-14">{{ $t('每小时空投上限') }}</span>
                <p class="fit-tc-primary font-size-20 pd-t8">{{ detailInfo.hour_total }}</p>
              </li>
              <li class="flex-1">
                <span class="fit-tc-secondary font-size-14">{{ $t('参与人数') }}</span>
                <p class="fit-tc-primary font-size-20 pd-t8">{{ detailInfo.users }}</p>
              </li>
            </ul>
          </div>
          <div v-if="isLogin" class="launch-pool-box flex-box space-between align-start">
            <div class="box-info-cont">
              <div class="box-info-cont-pd">
                <div class="info-dl flex-box">
                  <img :src="getImgUrl(detailInfo.lock_icon)" class="dl-icon" />
                  <div class="info-dd">
                    <h2>{{ $t('我的锁仓') }}</h2>
                    <p>{{ $t('您当前锁仓的数额') }}</p>
                  </div>
                </div>
                <div>
                  <span class="font-size-14 fit-tc-secondary">{{ detailInfo.lock_type * 1 === 1 ? 'NFT' : detailInfo.lock_symbol }} {{ $t('锁仓数量') }}</span>
                  <p class="font-size-32 fit-tc-primary">{{ detailInfo.user_data.lock_value }}</p>
                </div>
                <el-button v-if="detailInfo.status * 1 === 1" type="primary" class="launch-btn" @click="lockFun()">{{ $t('锁仓') }} {{ detailInfo.lock_type * 1 === 1 ? 'NFT' : detailInfo.lock_symbol }}</el-button>
                <div v-if="detailInfo.status * 1 === 1" class="info-text flex-box space-end">
                  <NuxtLink v-if="detailInfo.lock_type * 1 === 1" class="cursor-pointer" @click="recharge">{{ $t('充值NFT') }}</NuxtLink>
                  <NuxtLink v-else class="cursor-pointer" @click="recharge">{{ $t('充值') }} {{ detailInfo.lock_symbol }}</NuxtLink>
                </div>
              </div>
            </div>
            <div class="box-info-cont">
              <div class="box-info-cont-pd">
                <div class="info-dl flex-box">
                  <img :src="getImgUrl(detailInfo.icon)" class="dl-icon" />
                  <div class="info-dd">
                    <h2>{{ $t('我的空投') }}</h2>
                    <p>{{ $t('您可以每小时手动领取，也可以等项目完结后系统统一发放') }}</p>
                  </div>
                </div>
                <div>
                  <span class="font-size-14 fit-tc-secondary">{{ $t('已领取空投{symbol}数量', { symbol: detailInfo.symbol }) }}</span>
                  <p class="font-size-32 fit-tc-primary">{{ detailInfo.user_data.airdrop_value }}</p>
                </div>
                <el-button v-if="detailInfo.status * 1 === 1" type="primary" :loading="isGetLoading" class="launch-btn" :disabled="detailInfo.user_data.unprize_airdrop_value * 1 === 0" @click="getAirDropFun()">
                  {{ $t('领取空投') }}
                </el-button>
                <div v-if="detailInfo.status * 1 === 1" class="info-text flex-box">
                  <p class="font-size-14 fit-tc-secondary">{{ $t('Tips:领取的空投会放入您的钱包账户') }}</p>
                </div>
              </div>
            </div>
            <div class="box-info-btn flex-box space-end">
              <el-button @click="linkFun()" type="primary">{{ $t('领取记录') }}</el-button>
            </div>
          </div>
          <div v-else class="launch-pool-login-cont flex-box space-center">
            <div class="no-login-wrapper flex-box space-center flex-column">
              <h2 class="font-size-24 fit-tc-primary tw-3 mg-b32">{{ $t('您还未登录，登录后即可挖矿') }}</h2>
              <div class="login-btn-box flex-box">
                <el-button type="primary" @click="useCommon.openLogin()">{{ $t('登录') }}</el-button>
                <p class="flex-box font-size-14 fit-tc-secondary mg-l8">
                  {{ $t('还没有账号?') }}
                  <a class="fit-theme mg-l4 font-size-14 cursor-pointer" @click="useCommon.openRegister()">{{ $t('注册') }}</a>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <LockListDialog v-if="isShowLock" :isShow="isShowLock" :detail="detailInfo" :nftLockWeight="detailInfo.user_data.nft_lock_weight" :detailWeight="detailInfo.weight" :nftId="detailInfo.nft_collection_id" @close="isShowLock = false" @request="lockNFTFun" />
  <RecordList v-if="isShowRecord" :isShow="isShowRecord" @close="isShowRecord = false" :projectId="detailInfo.id" :projectName="getJsonObj(detailInfo.name)" :projectSymbol="detailInfo.symbol" :projectAirdropStatus="detailInfo.user_data.unprize_airdrop_value" @LpPrizeResquest="getDetail" />
  <el-dialog v-if="isShowTokenLock" v-model="isShowTokenLock" :title="`${$t('锁仓')} ${detailInfo.lock_symbol}`" width="480px" @close="isShowTokenLock = false">
    <div class="flex-box space-between">
      <p class="fit-tc-secondary font-size-14 pd-b12">{{ $t('锁仓数量') }}</p>
      <span class="fit-tc-primary font-size-14">{{ $t('限额') }} {{ detailInfo.hard_limit }} {{ detailInfo.lock_symbol }}</span>
    </div>
    <el-input v-model="lockAmount" :placeholder="$t('请输入锁仓数量')">
      <template #suffix>
          <div class="flex-box">
            <span class="fit-tc-secondary">{{ detailInfo.lock_symbol }}</span>
            <span class="fit-theme pd-l12 cursor-pointer" @click="lockAmount = format(balance, 10, true, true).replace(/,/g, '') || 0">{{ $t('全部') }}</span>
          </div>
        </template>
    </el-input>
    <p class="flex-box space-between font-size-14 fit-tc-secondary pd-t4">
      <span class="cursor-pointer fit-theme" @click="isShowTransfer = true">{{ $t('资金划转') }}</span>
      <span>{{ $t('可用') }} {{ format(balance, 10, true, true) }} {{ detailInfo.lock_symbol }}</span>
    </p>
    <el-button type="primary" class="mg-t24" :disabled="lockAmount * 1 <= 0" @click="lockTokenFun()">{{ $t('锁仓') }} {{ detailInfo.lock_symbol }}</el-button>
  </el-dialog>
  <TransferDialog v-if="isShowTransfer" :visibleDialog="isShowTransfer" :defaultSymbol="detailInfo.lock_symbol" @close="isShowTransfer = false" />
  <Footer />
  <el-dialog v-if="isShowDeposit" v-model="isShowDeposit" width="900" class="deposit-withdawl-dialog" :close-on-click-modal="false" @close="isShowDeposit = false">
    <NFTDeposit />
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElButton, ElDialog } from 'element-plus'
  import TransferDialog from '~/components/common/TransferDialog.vue'
  import MonoUnstartIcon from '~/components/common/icon-svg/MonoUnstartIcon.vue'
  import MonoWaitIcon from '~/components/common/icon-svg/MonoWaitIcon.vue'
  import MonoEndIcon from '~/components/common/icon-svg/MonoEndIcon.vue'
  import MonoRightArrowShort from '~/components/common/icon-svg/MonoRightArrowShort.vue'
  import LockListDialog from '~/components/launchpool/LockDialog.vue'
  import NFTDeposit from '~/components/nft/my/deposit.vue'
  import RecordList from '~/components/launchpool/recordList.vue'
  import { getLpDetailAPI, getLpTokenLockAPI, getLpPrizeGeAPI } from '~/api/tt.ts'
  import { imgDmain } from '~/config'
  import { format, timeFormat, getTimeLeft, isApp } from '~/utils'
  import { useUserStore } from '~/stores/useUserStore'
  import { useCommonData } from '~/composables/index'
  import { commonStore } from '~/stores/commonStore'
  const publicStore = commonStore()
  const { getAssetsByCoin } = publicStore
  const { mainAssetObj } = storeToRefs(publicStore)
  const useCommon = useCommonData()
  const store = useUserStore()
  const { isLogin, userInfo } = storeToRefs(store)
  const router = useRouter()
  const isShowLock = ref(false)
  const isShowRecord = ref(false)
  const isShowTokenLock = ref(false)
  const isShowTransfer = ref(false)
  const { locale, t } = useI18n()
  const detailInfo = ref({})
  const detailLoading = ref(false)
  const lockAmount = ref('')
  const isMobile = ref(false)
  const screenWidth = ref(0)
  const isLoading = ref(false)
  const isShowDeposit = ref(false)
  const updateScreenWidth = () => {
    screenWidth.value = window.innerWidth
    isMobile.value = screenWidth.value <= 768
  }
  const balance = computed(() => {
    return JSON.stringify(mainAssetObj.value) !== '{}' ? (mainAssetObj.value[detailInfo.value.lock_symbol] ? mainAssetObj.value[detailInfo.value.lock_symbol].balance : 0) : 0
  })
  const curId = computed(() => {
    return router.currentRoute.value.params.id || ''
  })
  const statusObj = ref({ // -1 未激活 0 未开始 1 进行中 2结束 
    '-1': t('未激活'),
    0: t('未开始'),
    1: t('进行中'),
    2: t('已结束')
  })
  const getDetail = async() => {
    detailLoading.value = true
    const { data } = await getLpDetailAPI({
      id: curId.value
    })
    if (data) {
      detailInfo.value = data
    }
    detailLoading.value = false
  }
  const recharge = () => {
    if (isApp()) {
      if (detailInfo.value.lock_type * 1 === 1) {
        goNftFun()
      } else {
        useCommon.jsAppBridge('rechargeJump', {
          symbol: detailInfo.value.lock_symbol
        })
      }
    } else {
      if (detailInfo.value.lock_type * 1 === 1) {
        goNftFun()
      } else {
        router.push(`/${locale.value}/my/assets/deposit?symbol=${detailInfo.value.lock_symbol}`)
      }
    }
  }
  const leftTimeMap = ref({})
  const interval = ref(null)
  const countDown = () => {
    interval.value && clearInterval(interval.value)
    interval.value = setInterval(() => {
      const time = new Date().getTime()
      const endTime = new Date(detailInfo.value.end_time).getTime() // detailInfo.value.end_time
      const leftTime = endTime - time
      const leftTimeMapP = getTimeLeft(leftTime)
      if (leftTime <= 0) {
        interval.value && clearInterval(interval.value)
        getDetail()
      }
      leftTimeMap.value = leftTimeMapP
    })
  }
  const getImgUrl = (url) => {
    return url.includes('http') ? url : `${imgDmain}${url}`
  }
  const getJsonObj = (obj) => {
    return (JSON.parse(obj)[locale.value] || JSON.parse(obj)['en'])
  }
  const nftList = ref([])
  const lockFun = () => {
    if (detailInfo.value.lock_type * 1 === 1) {
      isMobile.value ? router.push(`/${locale.value}/launchpool/detail/lock?id=${detailInfo.value.id}`) : isShowLock.value = true
      
    } else {
      getAssetsByCoin()
      isShowTokenLock.value = true
    }
  }
  const lockNFTFun = () => {
    isShowLock.value = false
    getDetail()
  }
  const linkFun = () => {
    isMobile.value ? router.push(`/${locale.value}/launchpool/detail/recordList?id=${detailInfo.value.id}&symbol=${detailInfo.value.symbol}&airdropStatus=${detailInfo.value.user_data.unprize_airdrop_value}`) : isShowRecord.value = true
  }
  const goNftFun = () => {
    isMobile.value ? router.push(`/${locale.value}/nft/my/deposit`) : isShowDeposit.value = true
  }
  const lockTokenFun = async() => {
    if (lockAmount.value * 1 <= 0) {
      return false
    }
    if (lockAmount.value === '' || isNaN(lockAmount.value)) {
      useCommon.showMsg('error', t('请输入锁仓数量'))
      return false
    }
    const { data, error } = await getLpTokenLockAPI({
      airdrop_id: detailInfo.value.id,
      symbol: detailInfo.value.lock_symbol,
      amount: lockAmount.value
    })
    if (data) {
      isShowTokenLock.value = false
      getDetail()
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const isGetLoading = ref(false)
  const getAirDropFun = async() => {
    if (detailInfo.value.user_data.unprize_airdrop_value * 1 <= 0) {
      return false
    }
    isGetLoading.value = true
    const { data, error } = await getLpPrizeGeAPI({
      airdrop_id: detailInfo.value.id
    })
    if (data) {
      isGetLoading.value = false
      getDetail()
    } else {
      isGetLoading.value = false
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  watch(() => userInfo.value, () => {
    getDetail()
  })
  onMounted(async() => {
    updateScreenWidth()
    isLoading.value = true
    window.addEventListener('resize', updateScreenWidth)
    await getDetail()
    if (detailInfo.value.status * 1 === 1 && new Date(detailInfo.value.end_time).getTime() > new Date().getTime()) {
      countDown()
    }
  })
</script>
<style lang="scss" scoped>
  @import url('@/assets/style/launch-pool.scss');
</style>