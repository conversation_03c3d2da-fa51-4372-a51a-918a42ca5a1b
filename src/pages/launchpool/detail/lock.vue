<template>
  <Header />
  <div class="launch-pool-container">
    <div class="launch-pool-title-h5-cont flex-box">
      <span @click="goListFun()">Launchpool</span>
      <MonoRightArrowShort :size="16" class="fit-tc-secondary mg-lr4" />
      <span @click="goDetailFun()">{{ $t('项目详情') }}</span>
      <MonoRightArrowShort :size="16" class="fit-tc-secondary mg-lr4" />
      <span class="active">{{ $t('选择您锁仓的NFT') }}</span>
    </div>
    <div v-if="JSON.stringify(detailInfo) !== '{}'">
      <LockDetail :detail="detailInfo" @request="goDetail" />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { getLpDetailAPI } from '~/api/tt.ts'
  import LockDetail from '~/components/launchpool/LockDetail.vue'
  const { locale, t } = useI18n()
  const router = useRouter()
  const nftCollectionId = computed(() => {
    return router.currentRoute.value.query.id
  })
  const detailInfo = ref({})
  const getDetail = async() => {
    const { data } = await getLpDetailAPI({
      id: nftCollectionId.value
    })
    console.log(data, 'ppppppp')
    if (data) {
      detailInfo.value = data
    }
  }
  const goListFun = () => {
    router.push(`/${locale.value}/launchpool`)
  }
  const goDetailFun = () => {
    router.push(`/${locale.value}/launchpool/detail/${nftCollectionId.value}`)
  }
  const goDetail = () => {
    router.push(`/${locale.value}/launchpool/detail/${nftCollectionId.value}`)
  }
  onMounted(() => {
    getDetail()
  })
</script>
<style lang="scss">
  @import url('@/assets/style/launch-pool.scss');
</style>
