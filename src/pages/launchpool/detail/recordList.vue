<template>
  <Header />
  <div class="launch-pool-container">
    <div class="launch-pool-title-h5-cont flex-box">
      <span @click="goListFun()">Launchpool</span>
      <MonoRightArrowShort :size="16" class="fit-tc-secondary mg-lr4" />
      <span @click="goDetailFun()">{{ $t('项目详情') }}</span>
      <MonoRightArrowShort :size="16" class="fit-tc-secondary mg-lr4" />
      <span class="active">{{ $t('领取记录') }}</span>
    </div>
    <div v-if="JSON.stringify(detailInfo) !== '{}'" class="pd-lr16 pd-tb24">
      <RecordDetail :projectId="nftCollectionId" :projectSymbol="projectSymbol" :projectAirdropStatus="projectAirdropStatus" @LpPrizeResquest="goDetailFun()" />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import RecordDetail from '~/components/launchpool/RecordDetail.vue'
  const { locale, t } = useI18n()
  const router = useRouter()
  const nftCollectionId = computed(() => {
    return router.currentRoute.value.query.id
  })
  const projectAirdropStatus = computed(() => {
    return router.currentRoute.value.query.airdropStatus
  })
  const projectSymbol = computed(() => {
    return router.currentRoute.value.query.symbol
  })
  const goListFun = () => {
    router.push(`/${locale.value}/launchpool`)
  }
  const goDetailFun = () => {
    router.push(`/${locale.value}/launchpool/detail/${nftCollectionId.value}`)
  }
</script>
<style lang="scss">
  @import url('@/assets/style/launch-pool.scss');
</style>
