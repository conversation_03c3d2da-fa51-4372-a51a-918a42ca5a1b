<template>
  <div class="login-register-container">
    <BoxLoading v-if="isThirdLoading" />
    <div class="login-register-wrapper flex-column space-between">
      <div class="login-register-box">
        <div class="login-register-box-wrap">
          <div class="login-logo">
            <NuxtLink :to="`/${locale}`">
              <img src="~/assets/images/common/logo.png" />
            </NuxtLink>
          </div>
          <div class="login-register-title">{{ $t('登录') }}</div>
          <div class="login-nav-type">
            <ul class="flex-box">
              <li :class="{'active': loginType * 1 === 1}" @click="loginType = 1">{{ $t('账户') }}</li>
              <li :class="{'active': loginType * 1 === 2}" @click="loginType = 2">{{ $t('二维码') }}</li>
            </ul>
          </div>
          <div v-if="loginType * 1 === 1">
            <ElForm ref="loginFormRef" :model="loginForm" :rules="loginRules" class="login-register-form">
              <ElFormItem prop="userName" :label="$t('账户')" class="first-label-box">
                <InputAutoComplete
                  v-model="loginForm.userName"
                  :isTypeLogin="true"
                  :placeholder="$t('输入账号或者子账号')"
                  :locale="locale"
                  @enter="onSubmit(loginFormRef)"
                />
                <input type="password" autocomplete="new-password" style="width: 0px; height: 0px; visibility: hidden; position: absolute;" />
              </ElFormItem>
              <ElFormItem prop="password" :label="$t('密码')">
                <ElInput
                  v-model="loginForm.password"
                  :type="showPwd ? 'text' : 'password'"
                  :placeholder="$t('请输入密码')"
                  name="password"
                  autocomplete="current-password"
                  @keyup.enter.native="onSubmit(loginFormRef)"
                >
                  <template #suffix>
                    <div v-if="loginForm.password !== ''" class="flex-box space-end eye-icon" @click="showPwd = !showPwd">
                      <MonoEyeClose v-if="!showPwd" />
                      <MonoEyeOpen v-if="showPwd" />
                    </div>
                  </template>
                </ElInput>
              </ElFormItem>
              <div class="forget-pwd">
                <NuxtLink :to="`/${locale}/login/reset`">{{ $t('忘记密码') }}</NuxtLink>
              </div>
              <ElButton type="primary" :loading="isLoading" class="normal-btn" @click="onSubmit(loginFormRef)">{{ $t('登录') }}</ElButton>
            </ElForm>
            <div class="or-line-box flex-box">
              <div class="line-box flex-1"></div>
              <div class="fit-tc-secondary font-size-14 pd-lr12">{{ $t('或') }}</div>
              <div class="line-box flex-1"></div>
            </div>
            <div class="login-more-btn flex-box" @click="handleGoogleLogin">
              <div class="google-more-icon"></div>
              <span class="flex-1 text-center">{{ $t('通过Google登录') }}</span>
            </div>
            <div class="login-more-btn flex-box mg-t16" @click="handleAppleLogin">
              <div class="apple-more-icon"></div>
              <span class="flex-1 text-center">{{ $t('通过AppleID登录') }}</span>
            </div>
          </div>
          <div v-if="loginType * 1 === 2" class="ewm-cont-box flex-box flex-column align-center space-center">
            <div v-loading="JSON.stringify(qrcodeInfo) === '{}'" class="ewm-cont flex-box space-center">
              <BoxQrcode v-if="JSON.stringify(qrcodeInfo) !== '{}'" :value="qrcodeInfo" :logo="logo" :padding="12" :size="180" style="border-radius: 0px; boder:0;" />
              <div v-if="qrcodeIsExpired" class="qrcode-expired flex-box space-center">
                <div>
                  <div class="qrcode-expired-text ts-16 mg-b8 text-center">{{ $t('二维码已失效') }}</div>
                  <el-button :loading="qrcodeLoading" type="primary" style="display: block; margin: 0 auto;" class="btn-small" @click="qrcodeIsExpired = false; qrcodeInfo = {}; requestLoginQrcode();">{{ $t('点击刷新') }}</el-button>
                </div>
              </div>
            </div>
            <p class="font-size-14 fit-tc-primary pd-t16">{{ $t('使用KTX APP 扫描二维码登录') }}</p>
            <!-- <a href="" target="_blank" class="fit-theme font-size-14 pd-t8">{{ $t('下载 KTX APP') }}</a> -->
          </div>
        </div>
      </div>
      <p class="login-register-text fit-tc-primary" :style="loginType * 1 === 2 ? 'padding-bottom:90px;' : ''">
        {{ $t('还没有账号?') }}
        <a @click="useCommon.openRegister()" class="cursor-pointer">{{ $t('注册') }}</a>
      </p>
    </div>
  </div>
  <el-dialog v-if="successScane" v-model="successScane" class="scan-success-dialog" width="420" :show-close="false" :close-on-click-modal="false">
    <div class="text-center pd-t28" style="width:100%;">
      <div class="text-center">
        <div class="success-logo"></div>
        <h3 class="font-size-24 pt-t16 fit-new-tc-primary">{{ $t('扫描成功') }}</h3>
        <p class="fit-new-tc-primary font-size-14 mg-b36 mg-t8">{{ $t('请在 KTX App 中授权登录') }}</p>
      </div>
    </div>
  </el-dialog>
  <VerifyCodeDialog
    v-if="isShowConfirm"
    :dialogVisible="isShowConfirm"
    :isGoogle="loginUser.is_totp * 1 === 0 ? false : true"
    :isEmail="loginUser.is_totp * 1 === 0"
    :isLogin="true"
    :params="loginUser"
    @request="getCodeSuccess"
    @handleClose="isShowConfirm = false"
  />
  <BoxScript id="gt" ref="gt" append-to-body src="https://res.ktx.com/common/js/gt4.js" />
</template>
<script lang="ts" setup>
import md5 from 'blueimp-md5'
import UAParser from 'ua-parser-js'
import { ElForm, ElFormItem, ElInput, ElButton, ElDialog } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import InputAutoComplete from '~/components/common/InputAutoComplete.vue'
import { useCommonData } from '~/composables/index'
import LoginHeader from '~/components/login/header.vue'
import VerifyCodeDialog from '~/components/common/VerifyCodeDialog.vue'
import MonoEyeOpen from '~/components/common/icon-svg/MonoEyeOpen.vue'
import MonoEyeClose from '~/components/common/icon-svg/MonoEyeClose.vue'
import { isValidPwd, isValidEmail, cookies } from '~/utils'
import Verify from '~/utils/verify'
import { userLogin, loginConfirm, qrContent, loginByQrId, emailConfirmAsk, thirdLogin, qrCodeUploadApI } from '~/api/user'
import { useUserStore } from '~/stores/useUserStore'
import logo from '~/assets/images/common/app-icon.png'
const useUser = useUserStore()
const { userInfo } = storeToRefs(useUser)
const { locale, t, localePath } = useI18n()
const useCommon = useCommonData()
const router = useRouter()
const dialogVisible = ref(false)
const showPwd = ref(false)
const isLoading = ref(false)
const isThirdLoading = ref(false)
interface LoginForm{
  userName: string,
  password: string
}
const loginFormRef = ref<FormInstance>()
const loginForm = reactive<LoginForm>({
  userName: '',
  password: ''
})
const validatorPwd = (rule, value, callback) => {
  if (!isValidPwd(value)) {
    return callback(new Error(t('请输入正确的密码格式')))
  } else {
    return callback()
  }
}
const valideEmail = (rule, value, callback) => {
  if (value.includes('@outlook')) {
    return callback(new Error(t('目前暂不支持outlook邮箱注册，您可以更换其他邮箱注册')))
  } else {
    return callback()
  }
}
const loginRules = reactive<FormRules<LoginForm>>({
  userName: [
    { required: true, message: t('请输入账号'), trigger: 'change' },
    { validator: valideEmail, trigger: 'change' }
  ],
  password: [
    { required: true, message: t('请输入密码'), trigger: 'change' },
    { validator: validatorPwd, trigger: 'change' }
  ]
})
const isDisabled = computed (() => {
  return loginForm.userName === ''
  return false
})
const redirect = computed (() => {
  let routeRedirect = router.currentRoute.value.query.redirect
  if ((routeRedirect && routeRedirect.includes('login')) || !routeRedirect) {
    return '/'
  }
  return decodeURIComponent(routeRedirect)
})
const verifyCode = ref(null)
const isShowConfirm = ref(false)
const loginUser = ref({})
const loginType= ref(1)
const login = async (e, res) => {
  if (e) {
    isLoading.value = false
    return
  }
  if (res.type === 'success' || res.type === 'resend') {
    isLoading.value = false
    const Ua = new UAParser(window.navigator.userAgent)
    const device = Ua.getDevice()
    const os = Ua.getOS() || {}
    const browser = Ua.getBrowser() || {}
    const deviceId = cookies.get('KTX_device_id') || localStorage.getItem('KTX_device_id') || md5(`${Date.now()}-${Math.floor(Math.random() * ***********).toString(16)}-${os.name}-${browser.name}`)
    const deviceName = (device.model ? device.model : `${os.name} ${browser.name}`)
    const params = {
      pwd: loginForm.password,
      ...res.param,
      device_id: deviceId,
      device_name1: deviceName || '--'
    }
    sessionStorage.setItem('hashId', deviceId)
    if (isValidEmail(loginForm.userName.trim())) {
      params.name = loginForm.userName.trim()
    } else {
      params.son = loginForm.userName.trim()
    }
    const { data, error } = await userLogin(params)
    if (data && data.session_id_origin) {
      cookies.set('session_id_origin', data.session_id_origin, Infinity)
      cookies.set('session_id', data.session_id_origin, Infinity)
    }
    if (data) {
      cookies.set('KTX_device_id', deviceId, Infinity)
      localStorage.setItem('KTX_device_id', deviceId, Infinity)
      if (data.needTotp === 1 || data.needEmailVerifiy === 1) {
        loginUser.value = data
        if (data.is_totp * 1 === 0) {
          const paramVals = {
            ...res.param,
            lang: locale.value
          }
          getCodeFun(paramVals)
        } else {
          isShowConfirm.value = true
        }
      } else {
        await useUser.getUserInfoAction()
        window.sensors.track('login', {
          uid: userInfo.value.user_id
        })
        window.location.href = redirect.value
      }
    } else {
      console.info(error, 'djdidjiejidijeerror')
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
}
const getCodeFun = async(params) => {
  const { data, error } = await emailConfirmAsk(params)
  if (data) {
    isShowConfirm.value = true
  } else {
    useCommon.showMsg('error', useCommon.err(error.code, error))
  }
}
const onSubmit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async(valid, fields) => {
    if (valid) {
      isLoading.value = true
      await submitData()
    } else {
      console.info('error submit!', fields)
    }
  })
}
const getCodeSuccess = async (params) => {
  const { data, error } = await loginConfirm(Object.assign({}, params))
  if (data) {
    await useUser.getUserInfoAction()
    window.sensors.track('login', {
      uid: userInfo.value.user_id
    })
    window.location.href = redirect.value
  } else {
    useCommon.showMsg('error', useCommon.err(error.code, error))
  }
}
const qrcodeIsExpired = ref(false)
const qrcodeLoading = ref(false)
const qr_code_login_time = ref(null)
const qrcodeData = ref({})
const qrcodeInfo = ref({})
const qrcodeDataObj = ref('')
const flag = ref(false)
const timer = ref(null)
const successScane = ref(false)
const requestLoginQrcode = async() => {
  const { data, error } = await qrContent()
  if (data) {
    qrcodeData.value = data
    const Ua = new UAParser(window.navigator.userAgent)
    const device = Ua.getDevice()
    const os = Ua.getOS() || {}
    const browser = Ua.getBrowser() || {}
    const deviceId = cookies.get('KTX_device_id') || localStorage.getItem('KTX_device_id') || md5(`${Date.now()}-${Math.floor(Math.random() * ***********).toString(16)}-${os.name}-${browser.name}`)
    const deviceName = device.model ? device.model : `${os.name} ${browser.name}`
    qrcodeDataObj.value = JSON.stringify({
      qr_id: data.qr_id,
      key: data.key,
      device_id: deviceId,
      device_name: deviceName,
      platform: 'Web',
      login_ip_address: data.login_ip_address,
      ip_province: data.ip_province,
      isKTX: 1
    })
    await uploadQrcode()
  } else {
    useCommon.showMsg('error', useCommon.err(error.code, error))
  }
}
const uploadQrcode = async() => {
  const { data, error } = await qrCodeUploadApI({
    qr_id: JSON.parse(qrcodeDataObj.value).qr_id,
    data: qrcodeDataObj.value
  })
  if (data) {
    qrcodeInfo.value = JSON.stringify({
      qr_id: JSON.parse(qrcodeDataObj.value).qr_id,
      isKTX: 1
    })
    console.lo
    await rotateRequest()
  } else {
    useCommon.showMsg('error', useCommon.err(error.code, error))
  }
}
const rotateRequest = () => {
  flag.value = false
  timer.value && clearInterval(timer.value)
  timer.value = setInterval(async() => {
    const etime = new Date().getTime()
    const { data, error } = await loginByQrId({
      qr_id: qrcodeData.value.qr_id,
      key: qrcodeData.value.key
    })
    if (data) {
      const Ua = new UAParser(window.navigator.userAgent)
      const device = Ua.getDevice()
      const os = Ua.getOS() || {}
      const browser = Ua.getBrowser() || {}
      const deviceId = cookies.get('KTX_device_id') || localStorage.getItem('KTX_device_id') || md5(`${Date.now()}-${Math.floor(Math.random() * ***********).toString(16)}-${os.name}-${browser.name}`)
      if (!cookies.get('KTX_device_id')) {
        cookies.set('KTX_device_id', deviceId, Infinity)
        localStorage.setItem('KTX_device_id', deviceId)
      }
      successScane.value = false
      useCommon.showMsg('success', t('登录中，请稍后...'))
      timer.value && clearInterval(timer.value)
      if (data && data.session_id_origin) {
        cookies.set('session_id_origin', data.session_id_origin, Infinity)
        cookies.set('session_id', data.session_id_origin, Infinity)
      }
      await useUser.getUserInfoAction()
      window.sensors.track('login', {
        uid: userInfo.value.user_id
      })
      window.location.href = redirect.value
    } else if ((Number(error.code) === 2129)) { // app 已授权
      if (!flag.value) {
        successScane.value = true
        setTimeout(() => {
          successScane.value = false
          flag.value = true
        }, 3000)
      }
    } else if (Number(error.code) === 2127) { // 用户未授权
      flag.value = false
    } else if (Number(error.code) === 2126) {
      successScane.value = false
      qrcodeIsExpired.value = true
      timer.value && clearInterval(timer.value)
    }
  }, 1000)
}
watch(() => loginType.value , async(val) => {
  if (val *   1 === 2) {
    qrcodeIsExpired.value = false
    qrcodeInfo.value = {}
    await requestLoginQrcode()
  } else {
    timer.value && clearInterval(timer.value)
  }
})
onMounted(() => {
  if (useUser.isLogin) {
    window.location.href = redirect.value
    return false
  }
})
watch(() => verifyCode.value, (val) => {
  if (val.state * 1 === 5) {
    isLoading.value = false
  }
}, {
  deep: true
})
const submitData = async() =>{
  verifyCode.value = new Verify(login)
  verifyCode.value.verify()
}
const langObj = ref({
  'zh': 'zh-CN',
  'en': 'en-GB',
  'ja': 'ja',
  'ko': 'ko',
  'zh-Hant': 'zh-TW'
})
const handleGoogleLogin = () => {
  localStorage.setItem('ktx_googole_router', redirect.value)
  const lang = langObj.value[locale.value]
  const redirectUri = encodeURIComponent(`${window.location.origin}/google-callback`)
  window.location.href = `https://accounts.google.com/o/oauth2/auth?client_id=************-ob8bjqpjfjlne1j58mkora15cnja5v5c.apps.googleusercontent.com&redirect_uri=${redirectUri}&response_type=code&scope=openid%20email%20profile&hl=${lang}&prompt=select_account`
}
const handleAppleLogin = async () => {
  try {
    // 确保 AppleID 已加载
    if (!window.AppleID) {
      throw new Error('AppleID SDK not loaded');
    }
    // 检查是否已初始化
    if (!window.AppleID.auth) {
      await initializeAppleAuth();
    }
    const response = await window.AppleID.auth.signIn();
    if (response.error) {
      console.error('Apple login error:', response.error);
      return;
    }
    const token = response.authorization?.id_token;
    if (token) {
      isThirdLoading.value = true
      const Ua = new UAParser(window.navigator.userAgent)
      const device = Ua.getDevice()
      const os = Ua.getOS() || {}
      const browser = Ua.getBrowser() || {}
      const deviceId = cookies.get('KTX_device_id') || localStorage.getItem('KTX_device_id') || md5(`${Date.now()}-${Math.floor(Math.random() * ***********).toString(16)}-${os.name}-${browser.name}`)
      const deviceName = (device.model ? device.model : `${os.name} ${browser.name}`)
      const { data, error } = await thirdLogin({
        login_type: '2',
        token,
        device_id: deviceId,
        device_name1: deviceName
      })
      if (data) {
        await useUser.getUserInfoAction()
        window.sensors.track('login', {
          uid: userInfo.value.user_id
        })
        window.location.href = redirect.value
        isThirdLoading.value = false
      } else {
        useCommon.showMsg('error', useCommon.err(error.code, error))
        isThirdLoading.value = false
      }
    }
  } catch (error) {
    console.error('Apple login failed:', error);
    // 可以在这里显示用户友好的错误信息
  }
}
// 初始化 Apple Auth
const initializeAppleAuth = async () => {
  return new Promise((resolve, reject) => {
    if (window.AppleID && window.AppleID.auth) {
      window.AppleID.auth.init({
        clientId: 'com.ktx.service',
        scope: 'email',
        redirectURI: `${window.location.origin}/appleid-callback`,
        state: generateState(), // 使用随机生成的 state
        usePopup: true
      });
      resolve();
    } else {
      reject(new Error('AppleID SDK not available'));
    }
  });
};
// 生成随机的 state 参数
const generateState = () => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}
onMounted(async () => {
  // 预加载 Apple SDK
  await loadAppleSDK()
  try {
    await initializeAppleAuth()
    console.log('Apple Auth initialized successfully')
  } catch (error) {
    console.error('Failed to initialize Apple Auth:', error)
  }
});

// 加载 Apple JS SDK
const loadAppleSDK = () => {
  return new Promise((resolve) => {
    if (document.getElementById('appleid-sdk')) {
      resolve();
      return;
    }
    const script = document.createElement('script');
    script.id = 'appleid-sdk';
    script.src = 'https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js';
    script.onload = resolve;
    script.onerror = () => console.error('Failed to load Apple SDK');
    document.head.appendChild(script);
  });
};
</script>
<style lang="scss">
@import '@/assets/style/login/index.scss';
.el-dialog{
  &.scan-success-dialog{
    .success-logo{
      width:200px;
      height:200px;
      margin:0 auto;
      background:url('@/assets/images/login/register-success.png') no-repeat center;
      background-size:100% auto;
    }
  }
}

</style>
