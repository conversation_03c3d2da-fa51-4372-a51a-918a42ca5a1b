<template>
  <div class="login-register-container">
    <BoxLoading v-if="isThirdLoading" />
    <div class="login-register-wrapper flex-column space-between">
      <div class="login-register-box">
        <div class="login-register-box-wrap">
          <div class="login-logo">
            <NuxtLink :to="`/${locale}`">
              <img src="~/assets/images/common/logo.png" />
            </NuxtLink>
          </div>
          <div class="login-register-title">{{ $t('注册') }}</div>
          <el-form ref="registerFormRef" :model="registerForm" :rules="registerRules" class="login-register-form">
            <el-form-item prop="email" :label="$t('邮箱')">
              <InputAutoComplete 
                v-model="registerForm.email"
                :placeholder="$t('输入邮箱地址')"
                :locale="locale"
                @enter="onSubmit(registerFormRef)"
              />
              <input type="password" autocomplete="new-password" style="width: 0; height: 0; visibility: hidden; position: absolute;" />
            </el-form-item>
            <el-form-item prop="password" :label="$t('密码')">
              <el-input
                v-model="registerForm.password"
                :type="showPwd ? 'text' : 'password'"
                :placeholder="$t('输入密码')"
                autocomplete="off"
                @keyup.enter.native="onSubmit(registerFormRef)"
                >
                <template #suffix>
                  <div v-if="registerForm.password !== ''" class="flex-box space-end eye-icon" @click="showPwd = !showPwd">
                    <MonoEyeClose v-if="!showPwd" />
                    <MonoEyeOpen v-if="showPwd" />
                  </div>
                </template>
              </el-input>
              <PasswordValidate :password="registerForm.password" @changeStatus="passwordvalidate" />
            </el-form-item>
            <el-form-item prop="passwordAgain" :label="$t('确认密码')">
              <el-input
                v-model="registerForm.passwordAgain"
                :type="showPwd1 ? 'text' : 'password'"
                :placeholder="$t('输入密码')"
                autocomplete="off"
                @keyup.enter.native="onSubmit(registerFormRef)"
              >
                <template #suffix>
                  <div v-if="registerForm.passwordAgain !== ''" class="flex-box space-end eye-icon" @click="showPwd1 = !showPwd1">
                    <MonoEyeClose v-if="!showPwd1" />
                    <MonoEyeOpen v-if="showPwd1" />
                  </div>
                </template>
              </el-input>
            </el-form-item>
            <div class="yq-cont">
              <div class="yq-label flex-box" @click="isShowYq = !isShowYq">
                <em>{{ $t('邀请码(选填)') }}</em>
                <MonoDownArrowMin class="fit-tc-primary" size="14" :class="{'up': isShowYq}" />
              </div>
              <el-input v-if="isShowYq" v-model="registerForm.inviteCode" :disabled="isInviteCodeEditDisable" />
            </div>
            <el-checkbox v-model="registerForm.xyChecked" class="checkBox-xy flex-box">
              <p class="ts-12" style="white-space:break-spaces; line-height:18px;">
                <em class="fit-tc-primary">{{ $t('我已知晓并同意KTX的') }}</em>
                <span class="v-span">
                  <a :href="`${useCommon.zendeskUrl(locale)}/articles/4662556551966`" target="_blank" class="fit-new-theme">{{ $t('隐私政策') }}</a>
                  <em class="fit-tc-primary">{{ $t('和') }}</em>
                  <a :href="`${useCommon.zendeskUrl(locale)}/articles/4733818896670`" target="_blank" class="fit-new-theme">{{ $t('用户协议') }}</a>
                </span>
              </p>
            </el-checkbox>
            <el-button type="primary" :disabled="isDisabled" :loading="isLoading" class="normal-btn" @click="onSubmit(registerFormRef)">{{ $t('注册') }}</el-button>
          </el-form>
          <div class="or-line-box flex-box">
            <div class="line-box flex-1"></div>
            <div class="fit-tc-secondary font-size-14 pd-lr12">{{ $t('或') }}</div>
            <div class="line-box flex-1"></div>
          </div>
          <div class="login-more-btn flex-box" @click="handleGoogleLogin">
            <div class="google-more-icon"></div>
            <span class="flex-1 text-center">{{ $t('通过Google继续') }}</span>
          </div>
          <div class="login-more-btn flex-box mg-t16" @click="handleAppleLogin">
            <div class="apple-more-icon"></div>
            <span class="flex-1 text-center">{{ $t('通过AppleID继续') }}</span>
          </div>
        </div>
      </div>
      <p class="login-register-text fit-tc-primary">
        {{ $t('已有账号？') }}
        <NuxtLink :to="`/${locale}/login`">{{ $t('登录') }}</NuxtLink>
      </p>
    </div>
  </div>
  <ElDialog v-model="isShowSuccess" width="480" class="success-register-dialog" @close="goHome()">
    <div class="success-box">
      <div class="success-logo"></div>
      <div class="succsss-text">{{ $t('注册成功') }}</div>
      <p v-if="locale=== 'en'" class="fit-tc-secondary font-size-14 mg-b12">redirect in {{ timerChange }} seconds</p>
      <p v-else class="fit-tc-secondary font-size-14 mg-b12">{{ timerChange }}s {{ $t('后自动跳转') }}</p>
      <el-button type="primary" @click="goHome()">{{ $t('去首页') }}</el-button>
    </div>
  </ElDialog>
  <VerifyCodeDialog
    v-if="dialogVisible"
    :dialogVisible="dialogVisible"
    :isEmail="true"
    :isRegister="true"
    :params="{ email: registerForm.email, lang: locale, pwd: registerForm.password, invite_code: registerForm.inviteCode }"
    @request="getCodeSuccess"
    @handleClose="dialogVisible = false"
  />
  <BoxScript id="gt" ref="gt" append-to-body src="https://res.ktx.com/common/js/gt4.js" />
</template>
<script lang="ts" setup>
import { ElForm, ElFormItem, ElInput, ElButton, ElCheckbox, ElMessage, ElDialog, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useCommonData } from '~/composables/index'
import md5 from 'blueimp-md5'
import UAParser from 'ua-parser-js'
import MonoRigthChecked from '~/components/common/icon-svg/MonoRigthChecked.vue'
import InputAutoComplete from '~/components/common/InputAutoComplete.vue'
import VerifyCodeDialog from '~/components/common/VerifyCodeDialog.vue'
import LoginHeader from '~/components/login/header.vue'
import MonoEyeOpen from '~/components/common/icon-svg/MonoEyeOpen.vue'
import MonoEyeClose from '~/components/common/icon-svg/MonoEyeClose.vue'
import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
import PasswordValidate from '~/components/login/password-validate.vue'
import { isValidPwd, isValidEmail, cookies } from '~/utils/index'
import { addUserByEmail, getInvClickApi, addUserByEmailAsk, thirdLogin } from '~/api/user'
import { cashPacketAPI } from '~/api/tt'
import Verify from '~/utils/verify'
import { useUserStore } from '~/stores/useUserStore'
const useUser = useUserStore()
const { userInfo } = storeToRefs(useUser)
const { locale, t } = useI18n()
const useCommon = useCommonData()
const router = useRouter()
const validatorPwd = (rule, value, callback) => {
  if (registerForm.passwordAgain) {
    if (!value) {
      return callback(new Error(t('请输入确认密码')))
    } else if (value !== registerForm.passwordAgain) {
      return callback(new Error(t('两次输入的密码不一致')))
    } else {
      return callback()
    }
  } else if (registerForm.passwordAgain === '') {
    if (!value) {
      return callback(new Error(t('请输入密码')))
    } else {
      return callback()
    }
  }
}
const valideAgainPwd = (rule, value, callback) => {
  if (registerForm.password) {
    if (!value) {
      return callback(new Error(t('请输入确认密码')))
    } else if (value !== registerForm.password) {
      return callback(new Error(t('两次输入的密码不一致')))
    } else {
      return callback()
    }
  } else if (registerForm.password === '') {
    return callback(new Error(t('请输入密码')))
  }
}
const valideEmail = (rule, value, callback) => {
  if (!isValidEmail(value)) {
    return callback(new Error(t('请输入正确的邮箱地址')))
  } else if (value.includes('@outlook')) {
    return callback(new Error(t('目前暂不支持outlook邮箱注册，您可以更换其他邮箱注册')))
  } else {
    return callback()
  }
}
const isShowYq = ref(false)
const isInviteCodeEditDisable = ref(false)
const showPwd = ref(false)
const showPwd1 = ref(false)
const isLoading = ref(false)
const timerChange = ref(3)
interface RegisterForm{
  email: string,
  password: string,
  passwordAgain: string,
  inviteCode: string,
  xyChecked: string
}
const registerFormRef = ref<FormInstance>()
const registerForm = reactive<RegisterForm>({
  email: '',
  password: '',
  passwordAgain: '',
  inviteCode: '',
  xyChecked: ''
})
const registerRules = reactive<FormRules<RegisterForm>>({
  email: [
    { required: true, message: t('请输入邮箱'), trigger: 'blur' },
    { validator: valideEmail, trigger: 'change' }
  ],
  password: [
    { required: true, message: t('请输入密码'), trigger: 'change' },
    { validator: validatorPwd, trigger: 'change' }
  ],
  passwordAgain: [
    { required: true, message: t('请再次输入密码'), trigger: 'change' },
    { validator: valideAgainPwd, trigger: 'change' }
  ]
})
const passwordValid = ref(false)
const passwordvalidate = (val) => {
  passwordValid.value = val
}
const isPasswordValid = computed(() => isValidPwd(registerForm.password));
const isConfirmPasswordValid = computed(() => registerForm.password === registerForm.passwordAgain);
watch([() => registerForm.password, () => registerForm.passwordAgain], () => {
  if (isPasswordValid.value && isConfirmPasswordValid.value) {
    registerFormRef.value.validateField('password')
    registerFormRef.value.validateField('passwordAgain')
  }
})
const verifyForm = async () => {
  if (!registerForm.xyChecked) {
    useCommon.showMsg('error', t('请勾选用户协议和隐私策略'))
  } else {
    isLoading.value = true
    await submitDataFun()
  }
}
const verifyCode = ref(null)
const dialogVisible = ref(false)
const goHome = () => {
  router.push(`/${locale.value}`)
}
const submitDataFun = () => {
  verifyCode.value = new Verify(sendCodeFun)
  nextTick(() => {
    verifyCode.value.verify()
  })
}
const sendCodeFun = async(err, res) => {
  if (err) {
    isLoading.value = false
    return
  }
  if (res.type === 'success' || res.type === 'resend') {
    const params = {
      email: registerForm.email,
      lang: locale.value, 
      pwd: registerForm.password,
      invite_code: registerForm.inviteCode,
      ...res.param
    }
    const { data, error } = await addUserByEmailAsk(params)
    if (data) {
      isLoading.value = false
      dialogVisible.value = true
      await useUser.getUserInfoAction()
      window.sensors.track('register', {
        uid: userInfo.value.user_id
      })
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
}
const getCodeSuccess = (params) => {
  register({
    type: 'success',
    code: params.code || params.phone_code || params.email_code
  })
}
const isShowSuccess = ref(false)
const countDown = () => {
  if (timerChange.value === 1) {
    isShowSuccess.value = false
    window.clearInterval(() => {
      countDown()
    })
    goHome()
  }
  timerChange.value--
}
const isShowRegistered = ref(true)
const register = async (res) => {
  if (registerForm.inviteCode) {
    cashPacketAPI({
      invite_code: registerForm.inviteCode,
      account: registerForm.email
    })
    getInvClickApi({
      code: registerForm.inviteCode
    })
  }
  const Ua = new UAParser(window.navigator.userAgent)
  const browser = Ua.getBrowser() || {}
  const os = Ua.getOS() || {}
  const device = Ua.getDevice()
  const deviceName = device.model ? device.model : `${os.name} ${browser.name}`
  const deviceId = cookies.get('KTX_device_id') || localStorage.getItem('KTX_device_id') || md5(`${Date.now()}-${Math.floor(Math.random() * ***********).toString(16)}-${os.name}-${browser.name}`)
  const type = router.currentRoute.value.query.userFrom || router.currentRoute.value.query.user_from || localStorage.getItem('userFrom') || 'web-pc'
  const params = {
    lang: locale.value,
    email: registerForm.email,
    invite_code: registerForm.inviteCode,
    code: res.code,
    device_id: deviceId,
    device_name1: deviceName || '--',
    platform: device.model || os.name,
    type
  }
  const { data, error } = await addUserByEmail(params)
  if (data) {
    if (!cookies.get('KTX_device_id')) {
      cookies.set('KTX_device_id', deviceId, Infinity)
      localStorage.setItem('KTX_device_id', deviceId)
    }
    if (data && data.session_id_origin) {
      cookies.set('session_id_origin', data.session_id_origin, Infinity)
      cookies.set('session_id', data.session_id_origin, Infinity)
    }
    dialogVisible.value = false
    isShowSuccess.value = true
    timerChange.value = 3
    setInterval(() => {
      countDown()
    }, 1000)
  } else if (error.code * 1 === 2052) {
    ElMessageBox.confirm(t('您输入的邮箱已注册，是否要立即登录'), t('账号已存在'), {
      distinguishCancelAndClose: true,
      confirmButtonText: t('去登录'),
      cancelButtonText: t('取消'),
      beforeClose: async(action, instance, done) => {
        if (action === 'confirm') {
          router.push(`/${locale.value}/login`)
          done()
        } else {
          isLoading.value = false
          dialogVisible.value = false
          done()
        }
      }
    })
  } else {
    useCommon.showMsg('error', useCommon.err(error.code, error))
  }
}
const onSubmit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid && passwordValid.value) {
      verifyForm()
    } else {
      console.info('error submit!', fields)
    }
  })
}
const defaultInfo = () => {
  const inviteCode = router.currentRoute.value.query.invite_code
  if (inviteCode) {
    isInviteCodeEditDisable.value = true
    isShowYq.value = true
    cookies.set('madex_invite_code', inviteCode, Infinity)
  }
  registerForm.inviteCode = cookies.get('madex_invite_code') || ''
}
const langObj = ref({
  'zh': 'zh-CN',
  'en': 'en-GB',
  'ja': 'ja',
  'ko': 'ko',
  'zh-Hant': 'zh-TW'
})
const handleGoogleLogin = () => {
  localStorage.setItem('ktx_googole_router', `${window.location.origin}/${locale.value}`)
  const lang = langObj.value[locale.value]
  const redirectUri = encodeURIComponent(`${window.location.origin}/google-callback`)
  window.location.href = `https://accounts.google.com/o/oauth2/auth?client_id=************-ob8bjqpjfjlne1j58mkora15cnja5v5c.apps.googleusercontent.com&redirect_uri=${redirectUri}&response_type=code&scope=openid%20email%20profile&hl=${lang}&prompt=select_account`
}
const isThirdLoading = ref(false)
const handleAppleLogin = async () => {
  try {
    // 确保 AppleID 已加载
    if (!window.AppleID) {
      throw new Error('AppleID SDK not loaded');
    }
    // 检查是否已初始化
    if (!window.AppleID.auth) {
      await initializeAppleAuth();
    }
    const response = await window.AppleID.auth.signIn();
    if (response.error) {
      console.error('Apple login error:', response.error);
      return;
    }
    const token = response.authorization?.id_token;
    if (token) {
      isThirdLoading.value = true
      const Ua = new UAParser(window.navigator.userAgent)
      const device = Ua.getDevice()
      const os = Ua.getOS() || {}
      const browser = Ua.getBrowser() || {}
      const deviceId = cookies.get('KTX_device_id') || localStorage.getItem('KTX_device_id') || md5(`${Date.now()}-${Math.floor(Math.random() * ***********).toString(16)}-${os.name}-${browser.name}`)
      const deviceName = (device.model ? device.model : `${os.name} ${browser.name}`)
      const { data, error } = await thirdLogin({
        login_type: '2',
        token,
        device_id: deviceId,
        device_name1: deviceName
      })
      if (data) {
        await useUser.getUserInfoAction()
        window.sensors.track('register', {
          uid: userInfo.value.user_id
        })
        window.location.href = `${window.location.origin}/${locale.value}`
        isThirdLoading.value = false
      } else {
        useCommon.showMsg('error', useCommon.err(error.code, error))
        isThirdLoading.value = false
      }
    }
  } catch (error) {
    console.error('Apple login failed:', error);
    // 可以在这里显示用户友好的错误信息
  }
}
// 初始化 Apple Auth
const initializeAppleAuth = async () => {
  return new Promise((resolve, reject) => {
    if (window.AppleID && window.AppleID.auth) {
      window.AppleID.auth.init({
        clientId: 'com.ktx.service',
        scope: 'email',
        redirectURI: `${window.location.origin}/appleid-callback`,
        state: generateState(), // 使用随机生成的 state
        usePopup: true
      });
      resolve();
    } else {
      reject(new Error('AppleID SDK not available'));
    }
  });
};
// 生成随机的 state 参数
const generateState = () => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}
onMounted(() => {
  defaultInfo()
})
</script>
<style lang="scss">
@import '@/assets/style/login/index.scss';
.el-dialog{
  &.success-register-dialog{
    .success-box{
      padding:40px 32px;
      text-align:center;
      .success-logo{
        width:200px;
        height:200px;
        margin:0 auto;
        background:url('@/assets/images/login/register-success.png') no-repeat center;
        background-size:100% auto;
      }
      .succsss-text{
        padding:24px 0;
        font-size:28px;
        text-align:center;
        @include color(tc-primary);
      }
      .el-button{
        width:100%;
      }
    }
  }
}
</style>
