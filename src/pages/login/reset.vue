<template>
  <div class="login-register-container">
    <div class="login-register-wrapper flex-column space-between">
      <div class="login-register-box">
        <div class="login-register-box-wrap">
          <div class="login-logo">
            <NuxtLink :to="`/${locale}`">
              <img src="~/assets/images/common/logo.png" />
            </NuxtLink>
          </div>
          <div class="login-mobile-header flex-box space-between">
            <span class="back-home cursor-pointer" @click="toHome">{{ $t('返回') }}</span>
          </div>
          <div class="login-register-title">{{ $t('找回登录密码') }}</div>
          <template v-if="step === 1">
            <el-form ref="resetFormRef" :model="resetForm" :rules="resetRules" class="login-register-form" @submit.native.prevent>
              <div class="input-box email-input-box">
                <el-form-item prop="email" :label="$t('邮箱')">
                  <InputAutoComplete 
                    v-model="resetForm.email"
                    :placeholder="$t('请输入您的注册邮箱')"
                    :locale="locale"
                    @enter="confirmAccount(resetFormRef)"
                  />
                </el-form-item>
              </div>
              <el-form-item style="margin-top:32px;">
                <el-button type="primary" :disabled="isDisabled" :isLoading="isFirstLoading" class="normal-btn" @click="confirmAccount(resetFormRef)">{{ $t('下一步') }}</el-button>
              </el-form-item>
            </el-form>
          </template>
          <template v-if="step === 2">
              <el-form ref="resetFormRef" :model="resetForm" :rules="resetRules" class="login-register-form">
                <el-form-item :label="$t('设置新密码')" class="pwd-item pwd-item-pt14" prop="password">
                  <el-input v-model="resetForm.password" :type="showPwd ? 'text' : 'password'">
                    <template #suffix>
                      <div v-if="resetForm.password !== ''" class="flex-box space-end eye-icon" @click="showPwd = !showPwd">
                        <MonoEyeClose v-if="!showPwd" />
                        <MonoEyeOpen v-if="showPwd" />
                      </div>
                    </template>
                  </el-input>
                  <PasswordValidate :password="resetForm.password" @changeStatus="passwordvalidate" />
                </el-form-item>
                <el-form-item :label="$t('确认新密码')" prop="againPassword">
                  <el-input v-model="resetForm.againPassword" :type="showPwd1 ? 'text' : 'password'">
                    <template #suffix>
                      <div v-if="resetForm.againPassword !== ''" class="flex-box space-end eye-icon" @click="showPwd1 = !showPwd1">
                        <MonoEyeClose v-if="!showPwd1" />
                        <MonoEyeOpen v-if="showPwd1" />
                      </div>
                    </template>
                  </el-input>
                </el-form-item>
                <el-form-item v-if="isHasTotpCode" :label="$t('谷歌验证')" prop="totp_code">
                  <GoogleCodeInputMin v-model="resetForm.totp_code" :defaultFocus="false" />
                </el-form-item>
                <el-form-item class="mg-t32">
                  <el-button type="primary" :disabled="isDisabled" :loading="isLoading" class="normal-btn" @click="resetConfirm">{{ $t('确认') }}</el-button>
                </el-form-item>
              </el-form>
            </template>
        </div>
      </div>
    </div>
  </div>
  <el-dialog v-model="showSuccess" :show-close="false">
    <div class="pd-t36" style="width:100%;text-align:center;">
      <div>
        <span style="width:64px;height:64px;border-radius:50%;margin:0 auto;" class="fit-rise_bg flex-box space-center">
          <MonoRigthChecked class="fit-tc-button" size="40" />
        </span>
        <h3 class="ts-16 fit-tc-primary mg-t16 lh24 mg-b16">{{ $t('密码修改成功') }}</h3>
        <p v-if="locale.code === 'en'" class="fit-tc-secondary ts-14 mg-t14 mg-b0 mg-t0">redirect in {{ timerChange }} seconds</p>
        <p v-else class="fit-tc-secondary ts-14 mg-t14 mg-b0 mg-t0">{{ timerChange }}s {{ $t('后自动跳转') }}</p>
        <a class="mg-t8 ts-14 mg-b0 tw-6 mg-b16 fit-theme cursor-pointer" style="display:block;text-decoration:none;" @click="useCommon.openLogin()">{{ $t('登录') }}</a>
      </div>
    </div>
  </el-dialog>
  <VerifyCodeDialog
    v-if="dialogVisible"
    :dialogVisible="dialogVisible"
    :isEmail="true"
    :isReset="true"
    :params="{ email: resetForm.email, lang: locale }"
    :isLoading="isVerifyLoading"
    @request="getCodeSuccess"
    @handleClose="dialogVisible = false"
  />
  <BoxScript id="gt" ref="gt" append-to-body src="https://res.ktx.com/common/js/gt4.js" />
</template>
<script lang="ts" setup>
import { ElForm, ElFormItem, ElInput, ElButton, ElDialog } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useCommonData } from '~/composables/index'
import MonoRigthChecked from '~/components/common/icon-svg/MonoRigthChecked.vue'
import LoginHeader from '~/components/login/header.vue'
import VerifyCodeDialog from '~/components/common/VerifyCodeDialog.vue'
import InputAutoComplete from '~/components/common/InputAutoComplete.vue'
import GoogleCodeInputMin from '~/components/common/GoogleCodeInput.vue'
import MonoEyeOpen from '~/components/common/icon-svg/MonoEyeOpen.vue'
import MonoEyeClose from '~/components/common/icon-svg/MonoEyeClose.vue'
import PasswordValidate from '~/components/login/password-validate.vue'
import { isValidPwd, isValidEmail } from '~/utils/index'
import Verify from '~/utils/verify'
import { resetLoginConfirm, resetLoginByEmail, resetLoginByEmailAsk } from '~/api/user'
const { locale, t, localePath } = useI18n()
const useCommon = useCommonData()
const router = useRouter()
const isHasTotpCode = ref(false)
const valideEmail = (rule, value, callback) => {
  if (!(isValidEmail(value))) {
    return callback(new Error(t('请输入正确的邮箱地址')))
  } else {
    return callback()
  }
}
const validePwd = (rule, value, callback) => {
  /* eslint no-lonely-if: "error" */
  if (resetForm.againPassword) {
    if (!value) {
      return callback(new Error(t('请输入确认密码')))
    } else if (value !== resetForm.againPassword) {
      return callback(new Error(t('两次输入的密码不一致')))
    } else {
      return callback()
    }
  } else if (resetForm.againPassword === '') {
    if (!value) {
      return callback(new Error(t('请输入密码')))
    } else {
      return callback()
    }
  }
}
const valideAgainPwd = (rule, value, callback) => {
  if (resetForm.password) {
    if (!value) {
      return callback(new Error(t('请输入确认密码')))
    } else if (value !== resetForm.password) {
      return callback(new Error(t('两次输入的密码不一致')))
    } else {
      return callback()
    }
  } else if (resetForm.password === '') {
    return callback(new Error(t('请输入密码')))
  }
}
const step = ref(1)
interface ResetForm{
  email: string,
  password: string,
  againPassword: string
}
const resetFormRef = ref<FormInstance>()
const resetForm = reactive<ResetForm>({
  email: '',
  password: '',
  againPassword: '',
  totp_code: ''
})
const resetRules = reactive<FormRules<RegisterForm>>({
  email: [
    { required: true, message: t('请输入邮箱'), trigger: 'change' },
    { validator: valideEmail, trigger: 'change' }
  ],
  password: [
    { required: true, message: t('请输入密码'), trigger: 'change' },
    { validator: validePwd, trigger: 'change' }
  ],
  againPassword: [
    { required: true, message: t('请输入确认密码'), trigger: ['blur', 'change'] },
    { validator: valideAgainPwd, trigger: ['blur', 'change'] }
  ],
  totp_code: [
    { required: true, message: t('请输入谷歌'), trigger: ['blur', 'change'] }
  ]
})
const showPwd = ref(false)
const showPwd1 = ref(false)
const emailSuccess = ref(false)
const pwdSuccess = ref(false)
const againPwdSuccess = ref(false)
const passwordValid = ref(false)
const passwordvalidate = (val) => {
  passwordValid.value = val
}
const isDisabled = computed (() => {
  if (step.value === 1 && emailSuccess.value) {
    return false
  } else if (step.value === 2 && pwdSuccess.value && passwordValid.value && againPwdSuccess.value && ((isHasTotpCode.value && resetForm.totp_code.length === 6) || (!isHasTotpCode.value && resetForm.totp_code.length < 6))) {
    return false
  } else {
    return true
  }
})
watch(() => resetForm.email, (val) => {
  emailSuccess.value = val !== '' && isValidEmail(val)
})
watch(() => resetForm.password, (val) => {
  pwdSuccess.value = val !== ''
  againPwdSuccess.value = val === resetForm.password
  if (val === resetForm.againPassword) {
    resetFormRef.value.validateField(['password', 'againPassword'])
  }
})
watch(() => resetForm.againPassword, (val) => {
  againPwdSuccess.value = val === resetForm.password
  console.info(resetForm.password, val, 'resetForm.againPassword')
  if (val === resetForm.password) {
    resetFormRef.value.validateField(['password', 'againPassword'])
  }
})
const verifyCode = ref(null)
const isFirstLoading = ref(false)
const isLoading = ref(false)
const confirmAccount = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      isFirstLoading.value = true
      verifyCode.value.verify()
    } else {
      console.info('error submit!', fields)
    }
  })
}
const dialogVisible = ref(false)
const verifyEmail = async(e, res) => {
  if (e) { return }
  if (res.type === 'success' || res.type === 'resend') {
    const params = {
      email: resetForm.email,
      lang: locale.value,
      ...res.param
    }
    const { data, error } = await resetLoginByEmailAsk(params)
    if (data) {
      isFirstLoading.value = false
      dialogVisible.value = true
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
}
const isVerifyLoading = ref(false)
const getCodeSuccess = async(params) => {
  isVerifyLoading.value = true
  const param = {
    country: locale.value,
    code: params.code || params.phone_code || params.email_code,
    type: 2,
    email: resetForm.email
  }
  const { data, error } = await resetLoginConfirm(param)
  if (data) {
    isHasTotpCode.value = data.is_bind_totp
    dialogVisible.value = false
    step.value = 2
    isVerifyLoading.value = false
  } else {
    useCommon.showMsg('error', useCommon.err(error.code, error))
  }
}
const toHome = () => {
  window.history.go(-1)
}
const showSuccess = ref(false)
const timerChange = ref(3)
const countDown = () => {
  if (timerChange.value === 1) {
    showSuccess.value = false
    window.clearInterval(countDown)
    router.push(`/${locale.value}/login`)
  }
  timerChange.value--
}
const resetConfirm = async () => {
  isLoading.value = true
  const params = {
    email: resetForm.email,
    pwd: resetForm.password,
    totp_code: resetForm.totp_code,
    country: locale.value
  }
  const { data, error } = await resetLoginByEmail(params)
  if (data) {
    isLoading.value = false
    showSuccess.value = true
    setInterval(countDown, 1000)
  } else {
    isLoading.value = false
    useCommon.showMsg('error', useCommon.err(error.code, error))
  }
}
onMounted(() => {
  setTimeout(() => {
    verifyCode.value = new Verify(verifyEmail)
  }, 500)
})
</script>
<style lang="scss">
@import '@/assets/style/login/index.scss';
.back-home {
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  border-radius: 2px;
  padding: 4px 8px;
  display:inline-block;
  margin-bottom:16px;
  @include bg-color(bg-secondary);
  @include color(tc-secondary);
}
</style>
