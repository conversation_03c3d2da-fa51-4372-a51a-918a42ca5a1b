<template>
  <div class="fit-bg-primary_bg">
    <Header />
    <Message />
    <div class="my-container">
      <div class="my-wrapper">
        <div class="my-menu">
          <MyMenu v-if="menuList.length > 0" :menuList="menuList" :userInfo="userInfo" @showMenu="changeMenu" />
        </div>
        <div v-if="isShowContent" class="my-content">
          <div class="my-content-pd">
            <NuxtPage :user="userInfo" :isNeedSecondVerify="isNeedSecondVerify" :useUser="useStore" :isVisibleUserInfo="isVisibleUserInfo" :useCommon="useCommon" @getUserInfoAction="getUserInfoAction()" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import Header from '~/components/header/index.vue'
  import Message from '~/components/message/index.vue'
  import MyMenu from '~/components/my/Menu.vue'
  import { useCommonData } from '~/composables/index'
  import { useUserStore } from '~/stores/useUserStore'
  import { commonStore } from '~/stores/commonStore'
  const store = commonStore()
  const { getAssetsByCoin } = store
  const useCommon = useCommonData()
  const useStore = useUserStore()
  const { isLogin, userInfo, isVisibleUserInfo } = storeToRefs(useStore)
  const { getUserInfoAction } = useStore
  const router = useRouter()
  const menuList = computed(() => {
    const list = [
      {
        id: 1,
        name: '总览',
        iconName: 'zonglan',
        url: '/my/dashboard',
        isOpen: false,
        isActive: false,
        isShow: true,
        children: []
      },
      {
        id: 2,
        name: '资产',
        iconName: 'zichan',
        isOpen: false,
        isActive: false,
        url: 'assets',
        isShow: true,
        children: [
          {
            id: 21,
            name: '钱包账户',
            url: '/my/assets/wallet',
            subUrl: ['/my/assets/deposit', '/my/assets/withdrawal', `/my/assets/detail/${router.currentRoute.value.params._coin}?type=wallet`, '/my/assets/addresslist']
          },
          {
            id: 22,
            name: '交易账户',
            url: '/my/assets/trade',
            subUrl: [`/my/assets/detail/${router.currentRoute.value.params._coin}?type=trade`]
          }
        ]
      },
      {
        id: 3,
        name: '子账户',
        iconName: 'zhanghu',
        isOpen: false,
        isActive: false,
        url: 'subAccount',
        isShow: userInfo.value && userInfo.value.user_type * 1 !== 100,
        children: [
          {
            id: 31,
            name: '子账户管理',
            url: '/my/subAccount',
            subUrl: ['/my/subAccount/create', `/my/subAccount/apiDetail/${router.currentRoute.value.params.id}_${router.currentRoute.value.params.name}`]
          },
          {
            id: 32,
            name: 'API管理',
            url: '/my/subAccount/api'
          }
        ]
      },
      {
        id: 4,
        name: '订单',
        iconName: 'dingdan',
        isOpen: false,
        isActive: false,
        url: 'orders',
        isShow: true,
        children: [
          {
            id: 41,
            name: '现货交易',
            url: '/my/orders/spot',
            subUrl: ['/my/orders/spot', '/my/orders/spot/history', '/my/orders/spot/deal', `/my/orders/spot?pair=${router.currentRoute.value.query.pair}`, `/my/orders/spot/history?pair=${router.currentRoute.value.query.pair}`, `/my/orders/spot/deal?pair=${router.currentRoute.value.query.pair}`]
          },
          {
            id: 41,
            name: '合约交易',
            url: '/my/orders/future/current',
            subUrl: ['/my/orders/future/current', '/my/orders/future/history', '/my/orders/future/deals']
          },
          {
            id: 42,
            name: '账单',
            url: '/my/orders/bills'
          }
        ]
      },
      {
        id: 1,
        name: '返佣',
        iconName: 'yaoqing',
        url: '/invite',
        isOpen: false,
        isActive: false,
        isShow: userInfo.value && userInfo.value.user_type * 1 !== 100,
        children: []
      },
      {
        id: 5,
        name: '设置',
        iconName: 'shezhi',
        isOpen: false,
        isActive: false,
        url: 'account',
        isShow: userInfo.value && userInfo.value.user_type * 1 !== 100,
        children: [
          {
            id: 51,
            name: '身份认证',
            url: '/my/kyc/result',
            subUrl: ['/my/kyc']
          },
          {
            id: 52,
            name: '安全设置',
            url: '/my/account/security',
            subUrl: ['/my/account/bindGoogle', '/my/account/bindGoogle?step=1', '/my/account/bindGoogle?step=2']
          },
          {
            id: 53,
            name: '账号活动',
            url: '/my/account/device'
          }
        ]
      },
      {
        id: 1,
        name: 'API管理',
        iconName: 'zonglan',
        url: '/my/account/api',
        isOpen: false,
        isActive: false,
        isShow: userInfo.value && userInfo.value.user_type * 1 !== 100,
        children: []
      }
    ]
    return list.filter((item) => {
      return item.isShow
    })
  })
  const isShowContent = ref(true)
  const changeMenu = (item) => {
    isShowContent.value = !item
  }
  const isNeedSecondVerify = computed(() => {
    return userInfo.value && !userInfo.value.is_login_need_totp && !userInfo.value.is_bind_totp
  })
  watch(() => isLogin.value, async(val) => {
    if (!val) {
      useCommon.openLogin()
      return false
    }
    if (JSON.stringify(userInfo.value) === '{}') {
      await getUserInfoAction()
    }
    getAssetsByCoin()
  })
  onMounted(async() => {
    if (!isLogin.value) {
      useCommon.openLogin()
      return false
    }
    console.log(JSON.stringify(userInfo.value) === '{}', 'dhdhduehduheuheuehu')
    if (JSON.stringify(userInfo.value) === '{}') {
      await getUserInfoAction()
    }
    getAssetsByCoin()
  })
</script>
<style lang="scss" scoped>
.my-container{
  padding:20px 0;
  .my-wrapper{
    .my-menu{
      width:230px;
      display: inline-block;
      vertical-align: top;
    }
    .my-content{
      display:inline-block;
      width:calc(100% - 230px);
      min-height:calc(100vh - 119px);
    }
  }
}
@include md {
  .my-container{
    .my-wrapper{
      .my-menu{
        width:192px;
      }
      .my-content{
        width:calc(100% - 192px);
      }
    }
  }
}
@include mb {
  .fit-bg-primary_bg{
    height:100%;
    display:flex;
    flex-direction: column;
  }
  .my-container{
    padding:0;
    height:calc(100% - 106px);
    .my-wrapper{
      height:calc(100% - 48px);
      .my-menu{
        width:100%;
        display:block;
      }
      .my-content{
        width:100%;
        height:calc(100%);
        display:block;
      }
    }
  }
}
</style>