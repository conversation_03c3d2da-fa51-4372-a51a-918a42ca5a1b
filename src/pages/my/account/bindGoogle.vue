<template>
  <div class="account-container">
    <div class="account-wrapper">
      <div class="account-nav-box">
        <el-breadcrumb separator-icon="MonoRightArrowShort">
          <el-breadcrumb-item :to="{ path: `/${locale}/my/account/security` }">{{ $t('安全设置') }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ $t('Google验证') }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div v-if="step * 1 === 1" class="account-title">
        <h2>{{ $t('现在绑定您的Google Authenticator') }}</h2>
        <p>{{ $t('如果你未安装Google验证器，请点击') }}<span class="fit-theme cursor-pointer mg-l4" style="text-decoration: underline;" @click="goNextFun(2)">{{ $t('去安装') }}</span></p>
      </div>
      <div v-if="step * 1 === 2" class="account-title">
        <h2>{{ $t('下载安装Google Authenticator安全验证') }}</h2>
        <p>{{ $t('您手机中还没有安装Google Authenticator？') }}</p>
      </div>
      <div class="account-body-container">
        <div class="account-body-wrapper">
          <template v-if="step * 1 === 1">
            <div class="google-setting-cont">
              <div class="google-setting-wrap">
                <div class="setting-title">
                  {{ $t('1.使用 Google Authenticator 扫描二维码或复制密钥，填写到您的Google Authenticator中，完成绑定。') }}
                </div>
                <div class="setting-cont-box">
                  <dl class="flex-box align-start">
                    <dt class="flex-1">
                      <h3>{{ $t('扫描二维码：') }}</h3>
                      <div class="ewm-cont">
                        <BoxQrcode :size="160" :value="ewmUri" />
                      </div>
                    </dt>
                    <dd class="flex-1">
                      <h3>{{ $t('密钥：') }}</h3>
                      <div v-if="secret" class="code-text">
                        {{ secret }}
                        <MonoCopy @click="useCommon.copy(secret, $t('密钥复制成功！'))" />
                      </div>
                    </dd>
                  </dl>
                </div>
                <div class="setting-title">
                  {{ $t('2.请输入在您Google Authenticator中生成的6位验证码，完成绑定。') }}
                </div>
                <div class="google-code-box">
                  <GoogleCodeInputMin v-model="code" :isShowInstall="true" :defaultFocus="false" @success="bindGoogle" />
                </div>
              </div>
            </div>
          </template>
          <template v-if="step * 1 === 2">
            <div class="google-install-cont">
              <div class="install-box install-left">
                <h3>{{ $t('扫码安装') }}</h3>
                <div class="ewm-box">
                  <BoxQrcode :size="160" :value="`https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2`" />
                </div>
                <p>{{ $t('使用手机摄像头扫描二维码安装Google Authenticator') }}</p>
              </div>
              <div class="install-box install-right">
                <h3>{{ $t('下载安装') }}</h3>
                <div class="install-btn-box">
                  <a href="https://itunes.apple.com/cn/app/google-authenticator/id388497605?mt=8" target="_blank" style="display:block;">
                    <dl class="flex-box">
                      <dt>
                        <img src="~/assets/images/my/appStore-img.png">
                      </dt>
                      <dd class="flex-1 flex-box space-between">
                        <span>{{ $t('Apple store下载安装') }}</span>
                        <span class="icon-bg">
                          <MonoRightArrow />
                        </span>
                      </dd>
                    </dl>
                  </a>
                  <a href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2" target="_blank" style="display:block;">
                    <dl class="flex-box">
                      <dt>
                        <img src="~/assets/images/my/google-img.png">
                      </dt>
                      <dd class="flex-1 flex-box space-between">
                        <span>{{ $t('Google play下载安装') }}</span>
                        <span class="icon-bg">
                          <MonoRightArrow />
                        </span>
                      </dd>
                    </dl>
                  </a>
                </div>
                <p>{{ $t('点击按钮下载安装 Google Authenticator') }}</p>
              </div>
            </div>
            <div class="google-install-btn">
              <el-button type="primary" @click="goNextFun(1)">{{ $t('我已完成安装Google Authenticator') }}</el-button>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import GoogleCodeInputMin from '~/components/common/GoogleCodeInput.vue'
  import MonoCopy from '~/components/common/icon-svg/MonoCopy.vue'
  import MonoRightArrow from '~/components/common/icon-svg/MonoRightArrow.vue'
  import { bindTotpAsk, bindTotpConfirm1 } from '~/api/user'
  const { locale, t } = useI18n()
  const router = useRouter()
  const props = defineProps({
    user: {
      type: Object,
      default () {
        return {}
      }
    },
    useCommon: {
      type: Object,
      default () {
        return {}
      }
    },
    isVisibleUserInfo: {
      type: Boolean,
      default: false
    },
    useUser: {
      type: Object,
      default () {
        return {}
      }
    },
    isNeedSecondVerify: {
      type: Boolean,
      default: false
    }
  })
  const code = ref('')
  const step = computed(() => {
    let num = router.currentRoute.value.query.step ? router.currentRoute.value.query.step * 1 : 1
    return num
  })
  const secret = ref('')
  const ewmUri = ref('')
  const getBindTotpAsk = async() => {
    const { data, error } = await bindTotpAsk({
      code: '',
      type: 1
    })
    if (data) {
      console.info(data, 'bindTotpAsk')
      secret.value = data.secret
      ewmUri.value = data.uri
    } else {
      props.useCommon.showMsg('error', props.useCommon.err(error.code, error))
    }
  }
  watch(() => step.value, (val) => {
    if (val * 1 === 1) {
      getBindTotpAsk()
    }
  }, {
    immediate: true
  })
  const bindGoogle = async() => {
    const { data, error } = await bindTotpConfirm1({
      code: code.value
    })
    if (data) {
      await props.useUser.getUserInfoAction()
      props.useCommon.showMsg('success', t('绑定谷歌验证码成功'))
      router.replace(`/${locale.value}/my/account/security`)
    } else {
      props.useCommon.showMsg('error', props.useCommon.err(error.code, error))
    }
  }
  const goNextFun = (num) => {
    router.push({
      query: {
        step: num
      }
    })
  }
</script>
<style lang="scss" scoped>
  @import url('@/assets/style/my/account.scss');
</style>