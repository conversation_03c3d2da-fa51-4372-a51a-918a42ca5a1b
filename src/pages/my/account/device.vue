<template>
  <div class="account-container">
    <div class="account-wrapper">
      <div class="account-title flex-box space-between">
        <div>
          <h2>{{ $t('账号活动') }}</h2>
          <p>
            {{ $t('* 管理和查看您的账号登录情况。展示最近10条登录信息。') }}
          </p>
        </div>
      </div>
      <div class="account-body-container">
        <div class="account-body-wrapper">
          <div class="subAccount-cont">
            <div class="subAccount-wrap">
              <div class="device-cont-box" style="min-height:500px;">
                <OrderstableBox :isLoading="isLoadingAccount" :mSortList="mSortList" :headers="headersList" :list="currentList">
                  <template #last_login="scope">
                    <div class="flex-box space-start" style="text-align:left;">
                      <span class="fit-tc-secondary">{{ timeFormat(scope.data.last_login, 'yyyy-MM-dd hh:mm:ss') }}</span>
                    </div>
                  </template>
                  <template #is_trusted_device="scope">
                    <el-switch v-model="(switchRow[scope.data.device_id] || {}).isSwitch" :loading="(switchRow[scope.data.device_id] || {}).isLoading"  @change="checkVerify(scope.data)" />
                  </template>
                  <template #online="scope">
                    <span class="fit-tc-secondary">{{ scope.data.is_self * 1 === 1 ? $t('当前设备') : (scope.data.online * 1 === 1 ? $t('已登录') : $t('已退出')) }}</span>
                  </template>
                  <template #comment="scope">
                    <el-button v-if="scope.data.online * 1 === 1" type="primary" class="last-btn-cont" @click="deleteFun(scope.data)">{{ $t('退出') }}</el-button>
                  </template>
                </OrderstableBox>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <AccountVerifyCodeDialog
      v-if="showVertify"
      :dialogVisible="showVertify"
      @request="setAuthNotVerifyFun"
      @handleClose="closeFun()"
    />
    <AccountVerifyCodeDialog
      v-if="showDeleteVertify"
      :dialogVisible="showDeleteVertify"
      @request="deleteVertifyFun"
      @handleClose="showDeleteVertify = false"
    />
  </div>
</template>
<script lang="ts" setup>
  import { ElMessageBox, ElButton, ElSwitch } from 'element-plus'
  import { timeFormat, cookies } from '~/utils'
  import { getWebMange, setAuthNotVerify, delDevice } from '~/api/user'
  import OrderstableBox from '~/components/common/OrderstableBox.vue'
  import AccountVerifyCodeDialog from '~/components/common/AccountVerifyCodeDialog.vue'
  import { useCommonData } from '~/composables/index'
  const props = defineProps({
    user: {
      type: Object,
      default () {
        return {}
      }
    }
  })
  const useCommon = useCommonData()
  const { locale, t } = useI18n()
  const isLoadingAccount = ref(true)
  const mSortList = ref([
    { text: t('上次登录时间'), key: 'last_login', align: 'left', wid: '', style: 'auto'},
    { text: t('设备'), key: 'name', align: '', wid: '', style: ''},
    { text: t('IP地址'), key: 'last_ip', align: '', wid: 'flex-2', style: ''},
    { text: t('登录城市'), key: 'ip_province', align: '', wid: '', style: ''},
    { text: t('登录免验证'), key: 'is_trusted_device', align: '', wid: '', style: 'auto'},
    { text: t('状态'), key: 'online', align: '', wid: '', style: 'auto'},
    { text: t('操作'), key: 'comment', align: '', wid: '', style: 'auto'}
  ])
  const headersList = ref([
    { text: t('上次登录时间'), key: 'last_login', align: 'left', wid: '', style: 'auto'},
    { text: t('设备'), key: 'name', align: 'left', wid: '', style: ''},
    { text: t('IP地址'), key: 'last_ip', align: 'left', wid: 'flex-2', style: ''},
    { text: t('登录城市'), key: 'ip_province', align: 'left', wid: '', style: ''},
    { text: t('登录免验证'), key: 'is_trusted_device', align: '', wid: '', style: 'auto'},
    { text: t('状态'), key: 'online', align: '', wid: '', style: 'auto'},
    { text: t('操作'), key: 'comment', align: '', wid: 'right', style: 'auto'}
  ])
  const currentList = ref([])
  const switchRow = ref({})
  const getWebMangeList = async() => {
    if (cookies.get('KTX_device_id')) {
      localStorage.setItem('KTX_device_id', cookies.get('KTX_device_id'))
    }
    const { data } = await getWebMange({
      device_id: cookies.get('KTX_device_id') || localStorage.getItem('KTX_device_id')
    })
    if (data) {
      currentList.value = data
      data.forEach((item) => {
        switchRow.value[item.device_id] = {
          isSwitch: item.is_trusted_device * 1 === 1,
          isLoading: false
        }
      })
    }
    isLoadingAccount.value = false
  }
  const showVertify = ref(false)
  const showDeleteVertify = ref(false)
  const curItem = ref({})
  const closeFun = () => {
    showVertify.value = false
    switchRow.value[curItem.value.device_id] = {
      isSwitch: curItem.value.is_trusted_device * 1 === 1,
      isLoading: false
    }
  }
  const checkVerify = (row) => {
    if (!(props.user.is_totp || props.user.is_bind_totp)) {
      useCommon.showMsg('error', t('您还没有绑定完成谷歌验证码'))
      return false
    }
    switchRow.value[row.device_id] = {
      isSwitch: row.is_trusted_device * 1 === 1,
      isLoading: true
    }
    showVertify.value = true
    curItem.value = row
  }
  const setAuthNotVerifyFun = async(code) => {
    const { data, error } = await setAuthNotVerify({
      device_id: curItem.value.device_id,
      code,
      is_open: curItem.value.is_trusted_device * 1 === 1 ? 0 : 1
    })
    if (data) {
      showVertify.value = false
      switchRow.value[curItem.value.device_id] = {
        isSwitch: curItem.value.is_trusted_device * 1 === 1 ? false : true,
        isLoading: false
      }
      getWebMangeList()
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const deleteFun = (row) => {
    if (!(props.user.is_totp || props.user.is_bind_totp)) {
      useCommon.showMsg('error', t('您还没有绑定完成谷歌验证码'))
      return false
    }
    showDeleteVertify.value = true
    curItem.value = row
  }
  const deleteVertifyFun = async(code) => {
    const { data, error } = await delDevice({
      code,
      device_id: curItem.value.device_id
    })
    if (data) {
      if (curItem.value.is_self * 1 === 1) {
        window.location.href = '/'
      } else {
        showDeleteVertify.value = false
        getWebMangeList()
      }
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  } 
  onMounted(() => {
    getWebMangeList()
  })
</script>
<style lang="scss">
  @import url('@/assets/style/my/account.scss');
  .el-button{
    &.last-btn-cont{
      background:none !important;
      border:0 !important;
      padding:0;
      span{
        color:#f0b90b !important;
      }
      color:#f0b90b !important;
      &.el-button--primary{
        &:hover{
          background:none !important;
        }
      }
      
    }
  }
  @include mb {
    .device-cont-box{
      .div-table-m .div-table-row-m{
          li{
            &:last-child{
              dl{
                width:100%;
                dt{
                  display:none;
                }
                dd{
                  width:100%;
                }
              }
            }
          }
      }
    }
    .el-button{
      &.last-btn-cont{
        flex:1;
        display:block;
        background:#f0b90b !important;
        border:1px !important;
        width:100%;
        height:40px;
        margin-bottom:8px;
        span{
          color: getSingleColor(tc-primary, 1, light) !important;
        }
        color: getSingleColor(tc-primary, 1, light) !important;
        &.el-button--primary{
          &:hover{
            background:#f0b90b !important;
          }
        }
      }
    }
  }
  
</style>