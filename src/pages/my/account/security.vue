<template>
  <div class="account-container">
    <div class="account-wrapper">
      <div class="account-title">
        <h2>{{ $t('安全设置') }}</h2>
        <p>{{ $t('用户登录、提币、修改敏感信息等操作时，会使用一下安全验证。') }}</p>
      </div>
      <div class="account-body-container">
        <div class="account-body-wrapper">
          <ul class="security-list">
            <li class="flex-box space-between">
              <div class="item-left">
                <h3>{{ $t('邮箱') }}</h3>
              </div>
              <div v-if="user.email !== '***'" class="item-right flex-box">
                <p>{{ isVisibleUserInfo ? user.name_visible : user.name }}</p>
                <el-button @click="updateUserState(isVisibleUserInfo)">{{ isVisibleUserInfo ? $t('隐藏') : $t('查看') }}</el-button>
              </div>
              <div v-if="user.email === '***'" class="item-right flex-box">
                <el-button>{{ $t('设置') }}</el-button>
              </div>
            </li>
            <li class="flex-box space-between">
              <div class="item-left">
                <h3>{{ $t('登录密码') }}</h3>
                <p class="warn">{{ $t('修改登录密码后，24小时内禁止提币！') }}</p>
              </div>
              <div class="item-right flex-box">
                <p class="warn">{{ $t('修改登录密码后，24小时内禁止提币！') }}</p>
                <el-button @click="changePwdFun">{{ $t('修改') }}</el-button>
              </div>
            </li>
            <li class="flex-box space-between">
              <div class="item-left">
                <h3>{{ $t('Google 验证') }}</h3>
              </div>
              <div class="item-right flex-box">
                
                <el-button v-if="!(user.is_totp || user.is_bind_totp)" @click="bindGoogleFun">{{ $t('绑定') }}</el-button>
                <template v-else>
                  <!-- <el-checkbox v-model="isNeedGoogle" v-loading="isLoadingNeed" style="margin-right:24px;" @change="changeYanGoogle">
                    {{ $t('登录免验证谷歌') }}
                  </el-checkbox> -->
                  <span class="fit-theme font-size-14">{{ $t('已绑定') }}</span>
                </template>
              </div>
            </li>
            <li class="flex-box space-between">
              <div class="item-left">
                <h3>{{ $t('邮件防钓鱼码') }}</h3>
                <p>{{ $t('* 设置您专属的防钓鱼码，防止邮件钓鱼，维护您账户资产安全。') }}</p>
              </div>
              <div class="item-right flex-box">
                <el-button v-if="user.email_verify" @click="isShowAntiChangeDialog = true">{{ $t('查看/修改') }}</el-button>
                <el-button v-else @click="setAntiFun()">{{ $t('设置') }}</el-button>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
  <AccountVerifyCodeDialog
    v-if="showVertify"
    :dialogVisible="showVertify"
    @request="verifyUserState"
    @handleClose="showVertify = false"
  />
  <ChangePwdDialog v-if="isShowPwdChangeDialog" :params="params" :user="user" :visibleDialog="isShowPwdChangeDialog" @close="isShowPwdChangeDialog = false" />
  <ChangeAntiDialog :emailVerify="user.email_verify" v-if="isShowAntiChangeDialog" :visibleDialog="isShowAntiChangeDialog" @close="isShowAntiChangeDialog = false" @success="useUser.getUserInfoAction()" />
  <BoxScript id="gt" ref="gt" append-to-body src="https://res.ktx.com/common/js/gt4.js" />
</template>
<script lang="ts" setup>
  import { ElCheckbox } from 'element-plus'
  import AccountVerifyCodeDialog from '~/components/common/AccountVerifyCodeDialog.vue'
  import ChangePwdDialog from '~/components/my/account/ChangePwdDialog.vue'
  import ChangeAntiDialog from '~/components/my/account/ChangeAntiDialog.vue'
  import Verify from '~/utils/verify'
  import { UasetLoginNotVerifyTotp, setUserVisible } from '~/api/user.ts'
  const { locale, t } = useI18n()
  const router = useRouter()
  const props = defineProps({
    user: {
      type: Object,
      default () {
        return {}
      }
    },
    useCommon: {
      type: Object,
      default () {
        return {}
      }
    },
    isVisibleUserInfo: {
      type: Boolean,
      default: false
    },
    useUser: {
      type: Object,
      default () {
        return {}
      }
    },
    isNeedSecondVerify: {
      type: Boolean,
      default: false
    }
  })
  const emit = defineEmits(['getUserInfoAction'])
  const verifyCode = ref(null)
  const isShowPwdChangeDialog = ref(false)
  const isShowAntiChangeDialog = ref(false)
  const isOpen = ref(false)
  const showVertify = ref(false)
  const isLoadingNeed = ref(false)
  const isNeedGoogle = computed(() => {
    return props.user && props.user.login_not_verify_totp * 1 === 1
  })
  const changeYanGoogle = async(val) => {
    console.info(val, 'dndhhdhdhdhdhdh')
    isLoadingNeed.value = true
    const { data } = await UasetLoginNotVerifyTotp({
      is_open: val ? 1 : 0
    })
    if (data) {
      await emit('getUserInfoAction')
      isLoadingNeed.value = false
    } else {
      isLoadingNeed.value = false
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const updateUserState = (val) => {
    if (!(props.user.is_totp || props.user.is_bind_totp)) {
      props.useCommon.showMsg('error', t('您还没有绑定完成谷歌验证码'))
      return false
    }
    isOpen.value = val ? 0 : 1
    if (!val) {
      showVertify.value = true
    } else {
      verifyUserState()
    }
  }
  const verifyUserState = async(code) => {
    const { data, error } = await setUserVisible({
      is_open: isOpen.value,
      totp_code: code ? code : undefined
    })
    if (data) {
      showVertify.value = false
      props.useUser.getUserInfoAction()
    } else {
      props.useCommon.showMsg('error', props.useCommon.err(error.code, error))
    }
  }
  const params = ref('')
  const ChangePwdFormFun = (e, res) => {
    if (e) {
      return
    }
    if (res.type === 'success' || res.type === 'resend') {
      params.value = res.param
      isShowPwdChangeDialog.value = true
    }
  }
  const changePwdFun = () => {
    verifyCode.value = new Verify(ChangePwdFormFun)
    verifyCode.value.verify()
  }
  const setAntiFun = () => {
    if (!(props.user.is_totp || props.user.is_bind_totp)) {
      props.useCommon.showMsg('error', t('您还没有绑定完成谷歌验证码'))
      return false
    }
    isShowAntiChangeDialog.value = true
  }
  const bindGoogleFun = () => {
    router.push(`/${locale.value}/my/account/bindGoogle?step=1`)
  }
</script>
<style lang="scss" scoped>
  @import url('@/assets/style/my/account.scss');
</style>