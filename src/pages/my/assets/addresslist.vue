<template>
  <div class="deposit-container">
    <div class="deposit-nav-box" :class="{'hasad': isShowAd * 1 === 1 && !isApp(), 'headNo': isApp(), 'hasadHeadNo': isShowAd * 1 === 1 && isApp()}">
      <el-breadcrumb separator-icon="MonoRightArrowShort">
        <el-breadcrumb-item :to="{ path: `/${locale}/my/assets/wallet` }">{{ $t('钱包账户') }}</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: `/${locale}/my/assets/withdrawal` }">{{ $t('提币') }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ $t('地址簿') }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="deposit-body-cont">
      <div class="white-list-body">
        <div class="address-add-cont flex-box space-between align-start">
          <div class="white-mode-box">
            <div class="white-mode-switch flex-box space-between">
              <h2>{{ $t('地址白名单模式') }}</h2>
              <el-switch v-model="search.isCloseWhiteList" :before-change="changeWhiteStatus" />
            </div>
            <p>{{ $t('* 将您信任的地址添加至提币白名单，可提升提币安全度，提币更快速。') }}</p>
            <p>{{ $t('* 提币白名单开启后，仅限白名单中地址可以提现。') }}</p>
          </div>
          <el-button type="primary" @click="addWhiteItemFun()">{{ $t('添加地址') }}</el-button>
        </div>
        <div class="white-search flex-box space-between">
          <div v-if="!isMobile" class="search-left flex-box flex-wrap">
            <el-input v-model="search.remark" type="text" :placeholder="$t('输入地址备注')" clearable class="white-search-input mg-b12" @keyup.enter.native="getWhiteList()">
              <template #prepend>
                <MonoSearch size="16" />
              </template>
            </el-input>
            <DropdownSelect class="mg-b12" v-if="search.isCloseWhiteList" v-model="search.address_type" :list="addressTypeOptions" :labelName="$t('地址类型')" />
            <DropdownSelect class="mg-b12" v-model="search.is_acl" :list="aclOptions" :wid="locale === 'en' ? 300 : 180" :labelName="$t('提现免验证')" />
            <DropdownSelect class="mg-b12" isSearch v-model="search.coin_symbol" :list="filterCoinList" :labelName="$t('币种')" wid="200" @change="searchInput = ''">
              <div class="orders-search-box-input">
                <el-input v-model="searchInput" clearable>
                  <template #prepend>
                    <MonoSearch size="16" />
                  </template>
                </el-input>
              </div>
            </DropdownSelect>
          </div>
          <template v-if="isMobile">
            <div class="search-icon-box" @click="showParamsDialog">
              <MonoNormalSelectIcon :size="22" />
            </div>
          </template>
        </div>
        <div>
          <OrderstableBox :isLoading="isLoading" :mSortList="mSortList" :headers="headersList" :list="currentList">
            <template #header-checked="scope">
              <el-checkbox v-model="selectAll" @change="handleSelectAllChange" />
            </template>
            <template #checked="scope">
              <el-checkbox v-model="selectedItems" :value="scope.data.id" @change="handleItemSelectChange" />
            </template>
            <template #coin_symbol="scope">
              <BoxCoinIcon :icon="scope.data.icon_url" class="icon-box mg-r12" style="font-size:30px;" />
              <span>{{ scope.data.general_symbol }}</span>
            </template>
            <template #addr_type="scope">
              <div v-if="scope.data.addr_type === 1" class="flex-box space-center">
                <!-- <MonoRigthChecked size="12" class="fit-tc-button" /> -->
                {{ $t('是') }}
              </div>
              <div v-else class="flex-box space-center">
                {{ $t('否') }}
              </div>
              <!-- <MonoCloseBg v-else size="18" class="fit-tc-secondary" /> -->
            </template>
            <template #address="scope">
              <el-tooltip placement="top" :hide-after="0" popper-class="dsj-tooltip">
                <template #content>
                  <div class="dsj-tooltip-box" style="width:240px;">
                    {{ scope.data.address }}
                  </div>
                </template>
                <span class="fit-tc-primary cursor-pointer" style="word-break: break-all;text-decoration:underline;">{{ codeLinkFormat(scope.data.address) }}</span>
              </el-tooltip>
            </template>
            <template #chain_type="scope">
              <span class="text-left">{{ scope.data.chain_type }}</span>
            </template>
            <template #updatedAt="scope">
              <span class="text-left time-info-box">{{ timeFormat(scope.data.updatedAt, 'yyyy-MM-dd hh:mm:ss') }}</span>
            </template>
            <template #acl="scope">
              <el-switch v-model="(switchRow[scope.data.id] || {}).isSwitch" :loading="(switchRow[scope.data.id] || {}).isLoading" @change="checkVerify(scope.data)" />
            </template>
            <template #id="scope">
              <el-dropdown v-if="search.isCloseWhiteList" trigger="click" popper-class="caozuo-box" @command="(v) => choisePercent(v, scope.data)">
                <div class="flex-box cursor-pointer fit-tc-primary">
                  <MonoMoreDot :size="24" class="fit-tc-primary" />
                </div>
                <template #dropdown>
                  <el-dropdown-menu style="width:136px;">
                    <el-dropdown-item :command="0">
                      {{ scope.data.addr_type * 1 === 1 ? $t('移除白名单') : $t('加入白名单') }}
                    </el-dropdown-item>
                    <el-dropdown-item :command="1">
                      {{ $t('删除') }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <div v-else class="caozuo-text fit-tc-secondary" @click="choisePercent(1, scope.data)">
                <MonoDelete size="18" class="fit-tc-secondary" />
              </div>
            </template>
          </OrderstableBox>
        </div>
        <div v-if="selectedItems.length > 0 && isBulkOperation" class="pl-btn-cont flex-box space-end">
          <div v-if="hasSelectedNonWhitelist && search.isCloseWhiteList" class="pl-btn" @click="batchTransferWhiteFun(2)">{{ $t('加入白名单') }}</div>
          <div v-if="hasSelectedWhitelist && search.isCloseWhiteList" class="pl-btn" @click="batchTransferWhiteFun(1)">{{ $t('移除白名单') }}</div>
          <div class="pl-btn" @click="batchDeleteFun">{{ $t('删除') }}</div>
        </div>
      </div>
    </div>
  </div>
  <WhitelistAddDialog v-if="isShowAddWhiteItem" :dialogVisible="isShowAddWhiteItem" :isOpenWhite="search.isCloseWhiteList" @close="isShowAddWhiteItem = false" @success="getWhiteList()" />
  <ParamsSelect
    v-if="isShowParamsSelect"
    :coinList="filterCoinList"
    :typeList="addressTypeOptions"
    :aclList ="aclOptions"
    :isShowSlectParamsCont="isShowSlectParamsCont"
    @closeDialog="closeParamsDialog"
    @confirmParams="changeParams"
    @change="searchInput = ''"
    >
    <div class="orders-search-box-input">
      <el-input v-model="searchInput" clearable>
        <template #prepend>
          <MonoSearch size="16" />
        </template>
      </el-input>
    </div>
  </ParamsSelect>
  <AccountVerifyCodeDialog
    v-if="isShowVerify"
    :dialogVisible="isShowVerify"
    @request="openWhiteFun"
    @handleClose="isShowVerify = false"
  />
  <withdrawalConfirm
    v-if="isShowNoVerify"
    :dialogVisible="isShowNoVerify"
    @confirm="updateSwitch"
    @close="closeNoVerifyFun()"
  />
  <withdrawalConfirm
    v-if="isShowTransfer"
    :dialogVisible="isShowTransfer"
    @confirm="transferAddrTypeFun"
    @close="isShowTransfer = false"
  />
  <CommonVerifyIsHasGoogleDialog v-if="isShowVerifyHas" :isShow="isShowVerifyHas" @close="isShowVerifyHas = false" />
</template>
<script lang="ts" setup>
  import { isApp } from '~/utils'
  import { cookies } from '~/utils/cookies'
  import { ElInput, ElButton, ElSwitch, ElMessageBox, ElTooltip, ElCheckbox, ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus'
  import MonoRigthChecked from '~/components/common/icon-svg/MonoRigthChecked.vue'
  import MonoCloseBg from '~/components/common/icon-svg/MonoCloseBg.vue'
  import MonoMoreDot from '~/components/common/icon-svg/MonoMoreDot.vue'
  import AccountVerifyCodeDialog from '~/components/common/AccountVerifyCodeDialog.vue'
  import MonoNormalSelectIcon from '~/components/common/icon-svg/MonoNormalSelectIcon.vue'
  import DropdownSelect from '~/components/common/DropdownSelect'
  import OrderstableBox from '~/components/common/OrderstableBox.vue'
  import CommonVerifyIsHasGoogleDialog from '~/components/common/CommonVerifyIsHasGoogleDialog.vue'
  import WhitelistAddDialog from '~/components/my/assets/addresslist/AddDialog.vue'
  import withdrawalConfirm from '~/components/my/assets/withdrawalConfirm.vue'
  import ParamsSelect from '~/components/my/assets/addresslist/ParamsSelect.vue'
  import { getWithdrawAddrApi, getWithdrawAclStatus, changeWithdrawAclStatus, delWhiteItemApi, addrAclChangeStatusApi, transferWhiteItemApi} from '~/api/tf'
  import { useCommonData } from '~/composables/index'
  import { timeFormat } from '~/utils'
  import { commonStore } from '~/stores/commonStore'
  const store = commonStore()
  const { getCoinList } = store
  const { coinList } = storeToRefs(store)
  const useCommon = useCommonData()
  const { locale, t } = useI18n()
  const props = defineProps({
    user: {
      type: Object,
      default () {
        return {}
      }
    }
  })
  const isBulkOperation = ref(false)
  const router = useRouter()
  const isShowAddWhiteItem = ref(false)
  const isShowParamsSelect = ref(false)
  const isShowSlectParamsCont = ref(false)
  const isShowVerifyHas = ref(false)
  const showParamsDialog = () => {
    isShowParamsSelect.value = true
    setTimeout(() => {
      isShowSlectParamsCont.value = true
    })
  }
  const closeParamsDialog = () => {
    isShowSlectParamsCont.value = false
    setTimeout(() => {
      isShowParamsSelect.value = false
    })
  }
  const codeLinkFormat = (str) => {
    return `${str.slice(0, 6)}......${str.slice(-6)}`
  }
  const headersList = computed(() => {
    if (search.value.isCloseWhiteList) {
      return [
        { text: '选择', key: 'checked', align: 'left', wid: 'w80', style: 'auto', headerAuto: true, fixed: 'left' },
        { text: t('地址备注'), key: 'name', align: 'left', wid: '', style: '' },
        { text: t('白名单'), key: 'addr_type', align: 'center', wid: 'w80',style: 'auto' },
        { text: t('币种'), key: 'coin_symbol', align: 'left', wid: '',style: 'auto' },
        { text: t('地址'), key: 'address', align: 'left', wid: 'flex-2', style: 'auto' },
        { text: t('转账网络'), key: 'chain_type', align: 'left', wid: 'flex-2', style: 'auto' },
        { text: t('更新时间'), key: 'updatedAt', align: 'left', wid: 'flex-2', style: 'auto' },
        { text: t('提现免验证'), key: 'acl', align: 'left', wid: '', style: 'auto' },
        { text: t('操作'), key: 'id', align: 'right', wid: '', style: 'auto', fixed: 'right' }
      ]
    } else {
      return [
      { text: '选择', key: 'checked', align: 'left', wid: 'w80', style: 'auto', headerAuto: true, fixed: 'left' },
      { text: t('地址备注'), key: 'name', align: 'left', wid: '', style: '' },
      { text: t('币种'), key: 'coin_symbol', align: 'left', wid: '',style: 'auto' },
      { text: t('地址'), key: 'address', align: 'left', wid: 'flex-2', style: 'auto' },
      { text: t('转账网络'), key: 'chain_type', align: 'left', wid: 'flex-2', style: 'auto' },
      { text: t('更新时间'), key: 'updatedAt', align: 'left', wid: 'flex-2', style: 'auto' },
      { text: t('提现免验证'), key: 'acl', align: 'left', wid: '', style: 'auto' },
      { text: t('操作'), key: 'id', align: 'right', wid: '', style: 'auto', fixed: 'right' }
    ]
    }
  })
  const mSortList = computed(() => {
    if (search.value.isCloseWhiteList) {
      return [
        { text: t('地址备注'), key: 'name', align: 'left', wid: '', style: '' },
        { text: t('操作'), key: 'id', align: 'right', wid: '', style: 'auto' },
        { text: t('白名单'), key: 'addr_type', align: 'left', wid: '',style: 'auto' },
        { text: t('币种'), key: 'coin_symbol', align: 'left', wid: '',style: 'auto' },
        { text: t('地址'), key: 'address', align: 'left', wid: 'flex-2', style: 'auto' },
        { text: t('转账网络'), key: 'chain_type', align: 'left', wid: '', style: 'auto' },
        { text: t('更新时间'), key: 'updatedAt', align: 'left', wid: '', style: 'auto' }
      ]
    } else {
      return [
        { text: t('地址备注'), key: 'name', align: 'left', wid: '', style: '' },
        { text: t('操作'), key: 'id', align: 'right', wid: '', style: 'auto' },
        { text: t('币种'), key: 'coin_symbol', align: 'left', wid: '',style: 'auto' },
        { text: t('地址'), key: 'address', align: 'left', wid: 'flex-2', style: 'auto' },
        { text: t('转账网络'), key: 'chain_type', align: 'left', wid: '', style: 'auto' },
        { text: t('更新时间'), key: 'updatedAt', align: 'left', wid: '', style: 'auto' }
      ]
    }
  })
  const currentList = ref([])
  const statusMap = ref({
    1: {
      text: '确认中',
      class: 'fit-theme'
    },
    2: {
      text: '成功',
      class: 'fit-rise'
    },
    3: {
      text: '失败',
      class: 'fit-error'
    }
  })
  const search = ref({
    coin_symbol: '',
    is_acl: '',
    address_type: '',
    remark: '',
    isCloseWhiteList: false
  })
  const searchInput = ref('')
  const aclOptions = ref([
    { label: t('全部'), value: '' },
    { label: t('是'), value: 1 },
    { label: t('否'), value: 2 },
  ])
  const addressTypeOptions = ref([
    { label: t('全部'), value: '' },
    { label: t('白名单'), value: 1 },
    { label: t('普通'), value: 2 }
  ])
  const curAddrItem = ref({})
  const isShowTransfer = ref(false)
  const selectAll = ref(false)
  const selectedItems = ref<number[]>([])
  const changeParams = (item) => {
    search.value = {
      coin_symbol: item.coin_symbol,
      address_type: item.address_type,
      remark: item.remark
    }
  }
  const choisePercent = (key, item) => {
    curAddrItem.value = item
    selectedItems.value = [item]
    isBulkOperation.value = false; // 标记为单个操作
    if (key === 0) {
      transferWhiteFun(item)
    } else if (key === 1) {
      deleteFun(item)
    }
  }
  const transferAddrTypeFun = async(params) => {
    const transfers = isBulkOperation.value ? selectedItems.value.map(id => {
          const item = currentList.value.find(item => item.id === id);
          return item ? `${item.id}_${item.addr_type * 1 === 1 ? 2 : 1}` : '';
        })
        .filter(Boolean)
        .join(',') : selectedItems.value.map(obj => {
      const item = currentList.value.find(item => item.id === obj.id);
      return item ? `${item.id}_${item.addr_type * 1 === 1 ? 2 : 1}` : '';
    }).filter(Boolean).join(',')
    console.log(transfers, 'dheuhdueuue')
    const { data, error } = await transferWhiteItemApi({
      transfers,
      email_code: params.emailCode,
      totp_code: params.code
    })
    if (data) {
      const actionText = curAddrItem.value.addr_type * 1 === 2 ? t('加入白名单') : t('移除白名单')
      useCommon.showMsg('success', `${actionText}${t('成功！')}`)
      selectedItems.value = []; // 清空选中项
      selectAll.value = false; // 取消全选
      isShowTransfer.value = false
      getWhiteList()
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const transferWhiteFun = (item) => {
    ElMessageBox.confirm(item.addr_type * 1 === 1 ? t('您确定要将这条地址移除白名单吗？') : t('您确定要将这条地址加入白名单吗？'), t('提示'), {
      distinguishCancelAndClose: true,
      confirmButtonText: t('确认'),
      cancelButtonText: t('取消'),
    }).then(async() => {
      isShowTransfer.value = true
    })
  }
  const filterCoinList = computed(() => {
    if (searchInput.value !== '') {
      return useCommon.formatCoinList(coinList.value).filter(v => {
        return v.value.includes(searchInput.value.toUpperCase())
      })
    } else {
      const arr = useCommon.formatCoinList(coinList.value)
      arr.unshift({
        label: t('全部'),
        value: ''
      })
      console.log(arr, 'djdjdjdjdjdjjdjdj')
      return arr
    }
  })
  const addWhiteItemFun = () => {
    if (!props.user.is_bind_totp) {
      isShowVerifyHas.value = true
      return false
    }
    isShowAddWhiteItem.value = true
  }
  const isMobile = ref(false)
  const screenWidth = ref(0)
  const isLoading = ref(true)
  const updateScreenWidth = () => {
    screenWidth.value = window.innerWidth
    isMobile.value = screenWidth.value <= 768
  }
  const handleSelectAllChange = (val: boolean) => {
    isBulkOperation.value = true; // 标记为批量操作
    if (val) {
      selectedItems.value = currentList.value.map(item => item.id);
    } else {
      selectedItems.value = []
    }
  }
  const handleItemSelectChange = (checked: boolean, id: number) => {
    isBulkOperation.value = true; // 标记为批量操作
    if (checked) {
      if (!selectedItems.value.includes(id)) {
        selectedItems.value = [...selectedItems.value, id];
      }
    } else {
      selectedItems.value = selectedItems.value.filter(itemId => itemId !== id);
    }
    selectAll.value = selectedItems.value.length === currentList.value.length;
  }
  // 计算属性：是否有选中的非白名单项
  const hasSelectedNonWhitelist = computed(() => {
    return selectedItems.value.some(id => {
      const item = currentList.value.find(item => item.id === id);
      return item && item.addr_type !== 1;
    });
  });

  // 计算属性：是否有选中的白名单项
  const hasSelectedWhitelist = computed(() => {
    return selectedItems.value.some(id => {
      const item = currentList.value.find(item => item.id === id);
      return item && item.addr_type === 1;
    });
  });
  const batchTransferWhiteFun = (targetType: number) => {
    isBulkOperation.value = true
    // 过滤出符合目标类型的选中项
    const validItems = selectedItems.value.filter(id => {
      const item = currentList.value.find(item => item.id === id)
      return item && 
        ((targetType === 2 && item.addr_type !== 1) || // 加入白名单时只处理非白名单项
        (targetType === 1 && item.addr_type === 1))    // 移除白名单时只处理白名单项
    })
    const actionText = targetType === 2 ? t('加入白名单') : t('移除白名单')
    const confirmText = targetType === 2 
      ? t('您确定要将选中的非白名单地址加入白名单吗？') 
      : t('您确定要将选中的白名单地址移除白名单吗？')

    ElMessageBox.confirm(confirmText, t('提示'), {
      distinguishCancelAndClose: true,
      confirmButtonText: t('确认'),
      cancelButtonText: t('取消'),
    }).then(() => {
      // 更新选中项为过滤后的有效项
      selectedItems.value = validItems
      curAddrItem.value = {
        addr_type: targetType
      }
      isShowTransfer.value = true
    })
  }
  const batchDeleteFun = () => {
    isBulkOperation.value = true
    ElMessageBox.confirm(t('您确定要删除所选中的地址吗？'), t('提示'), {
      distinguishCancelAndClose: true,
      confirmButtonText: t('确认'),
      cancelButtonText: t('取消'),
    }).then(async() => {
      const { data } = await delWhiteItemApi({
        id: selectedItems.value.join(',')
      })
      if (data) {
        getWhiteList()
        useCommon.showMsg('success', $t('删除成功！'))
        isBulkOperation.value = false
      } else {
        useCommon.showMsg('error', useCommon.err(error.code, error))
      }
    })
  }
  const deleteFun = (row) => {
    isBulkOperation.value = false
    ElMessageBox.confirm(t('您确定要删除这条地址吗？'), t('提示'), {
      distinguishCancelAndClose: true,
      confirmButtonText: t('确认'),
      cancelButtonText: t('取消'),
    }).then(async() => {
      const { data } = await delWhiteItemApi({
        id: row.id
      })
      if (data) {
        getWhiteList()
        useCommon.showMsg('success', $t('删除成功！'))
      } else {
        useCommon.showMsg('error', useCommon.err(error.code, error))
      }
    })
  }
  watch(() => [search.value.coin_symbol, search.value.address_type, search.value.is_acl, search.value.remark], ([val, val1, val2, val3]) => {
    if (val3) {
    } else if (val3 === '') {
      getWhiteList()
    } else {
      getWhiteList()
    }
  })
  watch(() => search.value.isCloseWhiteList, (val) => {
    if (!val) {
      search.value.address_type = 2
    } else {
      search.value.address_type = ''
    }
  }, {
    immediate: true
  })
  const arrayToObject = (arr) => {
    return arr.reduce(function(obj, item) {
      var key = item.general_name;
      var value = item
      obj[key] = value
      return obj
    }, {})
  }
  const switchRow = ref({})
  const curItem = ref({})
  const isShowNoVerify = ref(false)
  const checkVerify = (row) => {
    if (!(props.user.is_totp || props.user.is_bind_totp)) {
      useCommon.showMsg('error', t('您还没有绑定完成谷歌验证码'))
      return false
    }
    switchRow.value[row.id] = {
      isSwitch: row.acl * 1 === 1,
      isLoading: true
    }
    isShowNoVerify.value = true
    curItem.value = row
  }
  const updateSwitch = async(params) => {
    const { data, error } = await addrAclChangeStatusApi({
      addr: curItem.value.address,
      status: curItem.value.acl * 1 === 1 ? 0 : 1,
      email_code: params.emailCode,
      totp_code: params.code
    })
    if (data) {
      isShowNoVerify.value = false
      switchRow.value[curItem.value.id] = {
        isSwitch: curItem.value.acl * 1 === 1 ? 1 : 0,
        isLoading: false
      }
      getWhiteList()
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const closeNoVerifyFun = () => {
    isShowNoVerify.value = false
    switchRow.value[curItem.value.id] = {
      isSwitch: curItem.value.acl * 1 === 1,
      isLoading: false
    }
  }
  const getWhiteList = async() => {
    const { data } = await getWithdrawAddrApi({
      page: 1,
      size: 1000,
      is_acl: search.value.is_acl,
      search: search.value.remark,
      coin_symbol: search.value.coin_symbol,
      addr_type: search.value.address_type
    })
    if (data) {
      data.rows.forEach(item => {
        if (arrayToObject(coinList.value)[item.general_symbol]) {
          item.icon_url = arrayToObject(coinList.value)[item.general_symbol].icon_url
        }
        switchRow.value[item.id] = {
          isSwitch: item.acl * 1 === 1,
          isLoading: false
        }
      })
      currentList.value = data.rows
    }
    isLoading.value = false
  }
  const getWhiteListStatus = async() => {
    const { data } = await getWithdrawAclStatus()
    if (data) {
      search.value.isCloseWhiteList = data.status * 1 === 1
    }
  }
  const isShowVerify = ref(false)
  const openWhiteFun = async(code) => {
    const { data, error } = await changeWithdrawAclStatus({
      totp_code: code,
      status: search.value.isCloseWhiteList ? 0 : 1
    })
    if (data) {
      isShowVerify.value = false
      if (search.value.isCloseWhiteList) {
        ElMessageBox({
          title: t('提币白名单地址功能已关闭'),
          message: h('p', { style: 'text-align:center' }, t('您可以在提币页面进入地址薄管理中再次打开提币白名单功能。')),
          confirmButtonText: t('明白了'),
          center: true,
          'show-close': false
        }).then(() => {
          router.replace(`/${locale.value}/my/assets/withdrawal`)
        })
      } else {
        getWhiteListStatus()
      }
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const changeWhiteStatus = async() => {
    if (!props.user.is_bind_totp) {
      isShowVerifyHas.value = true
    } else {
      isShowVerify.value = true
    }
  }
  const isShowAd = ref(0)
  const requestAnimationFrameInterval = ref(null)
  const socketDateAnimation = () => {
    isShowAd.value = cookies.get('showMessage')
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  }
  onMounted(() => {
    socketDateAnimation()
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
    getWhiteListStatus()
    getCoinList()
    updateScreenWidth()
    getWhiteList()
    window.addEventListener('resize', updateScreenWidth)
  })
</script>
<style lang="scss" scoped>
  @import url('@/assets/style/orders/deposit-withdrawal.scss');
  .caozuo-text{
    font-size:14px;
    cursor:pointer;
    &.add{
      @include color(theme);
    }
    &.delete{
      @include color(fall);
    }
  }
  .right-box-cont{
    width:16px;
    height:16px;
    border-radius:50%;
    @include bg-color(rise);
  }
  .time-info-box{
    width:100px;
  }
</style>
