<template>
  <div class="deposit-container">
    <div class="deposit-nav-box" :class="{'hasad': isShowAd * 1 === 1 && !isApp(), 'headNo': isApp(), 'hasadHeadNo': isShowAd * 1 === 1 && isApp()}">
      <el-breadcrumb separator-icon="MonoRightArrowShort">
        <el-breadcrumb-item :to="{ path: `/${locale}/my/assets/wallet` }">{{ $t('钱包账户') }}</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: `/${locale}/my/assets/deposit` }">{{ $t('充值') }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ $t('充值详情') }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div v-loading="isLoading" class="deposit-body-cont">
      <div class="deposit-body-cont-detail">
        <div class="deposit-info-title">
          <div class="deposit-value">{{ detailInfo.amount }} {{ detailInfo.coin_symbol }}</div>
          <div class="deposit-status flex-box space-center mg-t8" :class="{ 'fall' : detailInfo.status === 3, 'rise': detailInfo.status === 2 }">
            <MonoUnstartIcon v-if="detailInfo.status === 3" :size="16" class="fit-fall mg-t4" />
            <MonoWaitIcon v-if="detailInfo.status === 1" :size="16" class="fit-tc-secondary mg-t4" />
            <MonoEndIcon v-if="detailInfo.status === 2" :size="16" class="fit-rise mg-t4" />
            <span v-if="detailInfo.status === 1" class="fit-tc-secondary">{{ $t('确认中') }}</span>
            <span v-if="detailInfo.status === 2" class="fit-rise">{{ $t('充币成功') }}</span>
            <span v-if="detailInfo.status === 3" class="fit-fall">{{ $t('充币失败') }}</span>
          </div>
        </div>
        <el-steps v-if="detailInfo.status === 1 || detailInfo.status === 2" direction="vertical" :active="activeStep">
          <el-step :title="$t('系统确认')">
            <template v-if="Number(activeStep) === 0" #description>
              <span class="font-size-14 fit-tc-secondary">{{ $t('系统确认中') }}</span>
            </template>
          </el-step>
          <el-step :title="$t('区块确认')">
          </el-step>
          <el-step :title="$t('充币成功')">
            <template #description>
              <span class="font-size-14 fit-tc-secondary">{{ $t('成功后，您将收到短信或Email提醒*邮件可能会被拦截，未收到邮件时请在邮箱垃圾箱中查看') }}</span>
            </template>
          </el-step>
        </el-steps>
        <div class="deposit-detail-info">
          <dl class="flex-box space-between font-size-14 pd-b16">
            <dt class="fit-tc-secondary">{{ $t('类型') }}</dt>
            <dd class="fit-tc-primary">{{ $t('普通充币') }}</dd>
          </dl>
          <dl class="flex-box space-between font-size-14 pd-b16">
            <dt class="fit-tc-secondary">{{ $t('入账时间') }}</dt>
            <dd class="fit-tc-primary">{{ timeFormat(detailInfo.createdAt, 'yyyy-MM-dd hh:mm:ss') }}</dd>
          </dl>
          <dl class="flex-box space-between font-size-14 pd-b16">
            <dt class="fit-tc-secondary">{{ $t('到账时间') }}</dt>
            <dd class="fit-tc-primary">{{ timeFormat(detailInfo.updatedAt, 'yyyy-MM-dd hh:mm:ss') }}</dd>
          </dl>
          <dl class="flex-box space-between font-size-14 pd-b16">
            <dt class="fit-tc-secondary">{{ $t('网络') }}</dt>
            <dd class="fit-tc-primary">{{ detailInfo.chain_type }}</dd>
          </dl>
          <dl class="flex-box space-between font-size-14 pd-b16">
            <dt class="fit-tc-secondary">{{ $t('确认次数') }}</dt>
            <dd class="fit-tc-primary">{{ detailInfo.confirmCount }}</dd>
          </dl>
          <dl class="flex-box space-between font-size-14 pd-b16">
            <dt class="fit-tc-secondary">{{ $t('充值地址') }}</dt>
            <dd class="fit-tc-primary">
              <div class="flex-box">
                <a :href="detailInfo.scan_url" target="_blank" rel="noopener noreferrer" class="fit-tc-primary">
                  <span class="fit-tc-primary" style="word-break: break-all;text-decoration:underline;">{{ detailInfo.to }}</span>
                </a>
                <a :href="detailInfo.scan_url" target="_blank" rel="noopener noreferrer" class="mg-l8 fit-tc-primary mg-t4">
                  <MonoLinkUrl size="16" style="cursor:pointer;" />
                </a>
                <div class="mg-l8 fit-tc-primary mg-t4">
                  <MonoCopy size="16" style="cursor:pointer;" @click="useCommon.copy(detailInfo.to, $t('复制成功！'))" />
                </div>
              </div>
            </dd>
          </dl>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { isApp } from '~/utils'
  import { ElBreadcrumb, ElBreadcrumbItem, ElSteps, ElStep, ElTooltip } from 'element-plus'
  import MonoUnstartIcon from '~/components/common/icon-svg/MonoUnstartIcon.vue'
  import MonoWaitIcon from '~/components/common/icon-svg/MonoWaitIcon.vue'
  import MonoEndIcon from '~/components/common/icon-svg/MonoEndIcon.vue'
  import MonoCopy from '~/components/common/icon-svg/MonoCopy.vue'
  import MonoLinkUrl from '~/components/common/icon-svg/MonoLinkUrl.vue'
  import { transferTransferInList } from '~/api/tf'
  import { timeFormat } from '~/utils'
  const { locale, t } = useI18n()
  const router = useRouter()
  const activeStep = ref(0)
  const isShowAd = ref(0)
  const curId = computed(() => {
    return router.currentRoute.value.query.id || ''
  })
  const requestAnimationFrameInterval = ref(null)
  const socketDateAnimation = () => {
    isShowAd.value = cookies.get('showMessage')
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  }
  const detailInfo = ref({})
  const isLoading = ref(false)
  const getDepositDetail = async() => {
    isLoading.value = true
    const { data } = await transferTransferInList({
      page: 1,
      size: 20,
      id: curId.value,
    })
    detailInfo.value = data.items[0]
    activeStep.value = Number(data.items[0].step) - 1
    isLoading.value = false
  }
  onMounted(() => {
    getDepositDetail()
    socketDateAnimation()
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  })
</script>
<style lang="scss">
  @import url('@/assets/style/orders/deposit-withdrawal.scss');
</style>