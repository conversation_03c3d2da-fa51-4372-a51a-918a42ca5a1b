<template>
  <div class="deposit-container">
    <div class="deposit-nav-box" :class="{'hasad': isShowAd * 1 === 1 && !isApp(), 'headNo': isApp(), 'hasadHeadNo': isShowAd * 1 === 1 && isApp()}">
      <el-breadcrumb separator-icon="MonoRightArrowShort">
        <el-breadcrumb-item :to="{ path: `/${locale}/my/assets/wallet` }">{{ $t('钱包账户') }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ $t('充值') }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="deposit-body-cont">
      <el-steps direction="vertical" :active="activeStep">
        <el-step :title="curType === 'NFT' ? $t('选择充值NFT名称') : $t('选择充值币种')">
          <template #description>
            <div class="step-content">
              <CoinListSelect v-model="search.symbol" :isDeposit="true" :isNFT="curType === 'NFT'" />
            </div>
          </template>
        </el-step>
        <el-step v-if="curType !== 'NFT'" :title="$t('选择网络')">
          <template #description>
            <div v-if="activeStep === 1 || activeStep === 2" class="step-content">
              <WebsiteSelect v-model="search.website" :symbol="search.symbol" :list="websiteList" @changeWebsite="changeWebsite" />
            </div>
          </template>
        </el-step>
        <el-step>
          <template #title>
            <div class="flex-box space-between address-cont">
              {{ $t('充值地址') }}
              <span v-if="activeStep === 2" class="flex-box cursor-pointer fit-theme font-size-14" @click="isShowDepositList = true">
                {{ $t('更多地址') }}
                <MonoRightArrowShort size="14" class="fit-theme mg-l4" />
              </span>
            </div>
          </template>
          <template #description>
            <div v-if="activeStep === 2" class="step-content">
              <div v-loading="isAddressLoading" class="address-cont">
                <dl v-if="JSON.stringify(depostAddress) !== '{}'" class="address-wrap flex-box">
                  <dt class="pd-lr12 pd-tb12">
                    <BoxQrcode :size="150" :value="depostAddress.address" />
                  </dt>
                  <dd class="flex-1">
                    <h3>{{ $t('地址') }}</h3>
                    <p class="flex-box">
                      <span style="word-wrap: break-word;">{{ depostAddress.address}}</span>
                      <MonoCopy size="16" style="cursor:pointer;" @click="useCommon.copy(depostAddress.address, $t('复制成功！'))" />
                    </p>
                    <p v-if="depostAddress.memo">
                      <span class="fit-tc-primary">Memo</span>:
                      {{ depostAddress.memo }}
                      <MonoCopy size="16" style="cursor:pointer;" @click="useCommon.copy(depostAddress.memo, $t('复制成功！'))" />
                    </p>
                  </dd>
                </dl>
                <p class="info-p flex-box space-between">
                  <span>{{ $t('最小充值数量：') }}</span>
                  <em>{{ websiteItme.deposit_min }} {{ search.symbol }}</em>
                </p>
                <p class="info-p flex-box space-between">
                  <span>{{ $t('充值到账钱包：') }}</span>
                  <em>{{ $t('钱包账户') }}</em>
                </p>
              </div>
            </div>
            <div v-if="activeStep === 1 && curType === 'NFT' && JSON.stringify(nftAddressInfo) !== '{}'" class="step-content">
              <div class="address-cont">
                <dl v-if="(nftAddressInfo ||{}).address.includes('http')" class="address-wrap flex-box flex-column">
                  <p class="fit-tc-secondary font-size-14 pd-b12">{{ $t('使用 Mixin Messenger 扫码') }}</p>
                  <dt>
                    <BoxQrcode :size="150" :value="nftAddressInfo.address" />
                  </dt>
                </dl>
                <dl v-else class="address-wrap flex-box">
                  <dd class="flex-1">
                    <h3>{{ $t('地址') }}</h3>
                    <p class="flex-box">
                      <span style="word-wrap: break-word;">{{ nftAddressInfo.address}}</span>
                      <MonoCopy size="16" style="cursor:pointer;" @click="useCommon.copy(nftAddressInfo.address, $t('复制成功！'))" />
                    </p>
                  </dd>
                </dl>
                <p class="info-p flex-box space-between">
                  <span>{{ $t('充值到账钱包：') }}</span>
                  <em>{{ $t('钱包账户') }}</em>
                </p>
              </div>
            </div>
          </template>
        </el-step>
      </el-steps>
      <div class="record-list-cont">
        <div class="record-title">
          {{ $t('最近充值记录') }}
        </div>
        <OrderstableBox :isLoading="isLoading" :mSortList="mSortList" :headers="headersList" :list="currentList">
          <template #coin_symbol="scope">
            <div class="flex-box">
              <BoxCoinIcon :icon="scope.data.icon_url" class="icon-box mg-r12" style="font-size:30px;" />
              <span>{{ scope.data.coin_symbol }}</span>
            </div>
          </template>
          <template #createdAt="scope">
            <span class="fit-tc-secondary">{{ timeFormat(scope.data.createdAt, 'yyyy-MM-dd hh:mm:ss') }}</span>
          </template>
          <template #status="scope">
            <span :class="statusMap[scope.data.status].class">{{ $t(statusMap[scope.data.status].text) }}</span>
          </template>
          <template #comment="scope">
            <span class="fit-theme font-size-14 cursor-pointer" @click="router.push(`/${locale}/my/assets/deposit-detail?id=${scope.data.id}`)">{{ $t('详情') }}</span>
          </template>
        </OrderstableBox>
      </div>
    </div>
  </div>
  <DespositListDialog v-if="isShowDepositList" :defaultAddress="depostAddress" :symbol="search.website" :isShowDialog="isShowDepositList" @close="isShowDepositList = false" @changeAddress="changeAddress" />
</template>
<script lang="ts" setup>
  import { isApp } from '~/utils'
  import { cookies } from '~/utils/cookies'
  import { ElBreadcrumb, ElBreadcrumbItem, ElSteps, ElStep, ElTooltip } from 'element-plus'
  import CoinListSelect from '~/components/common/CoinListSelect.vue'
  import WebsiteSelect from '~/components/my/assets/WebsiteSelect.vue'
  import MonoCopy from '~/components/common/icon-svg/MonoCopy.vue'
  import MonoLinkUrl from '~/components/common/icon-svg/MonoLinkUrl.vue'
  import OrderstableBox from '~/components/common/OrderstableBox.vue'
  import DespositListDialog from '~/components/my/assets/deposit-list-dialog.vue'
  import { getDepositAddressApi, transferTransferInList, getNftAddrApi } from '~/api/tf'
  import { getListByGeneralNameApi } from '~/api/public'
  import { useCommonData } from '~/composables/index'
  import { timeFormat } from '~/utils'
  const router = useRouter()
  const useCommon = useCommonData()
  const { locale, t } = useI18n()
  const curType = computed(() => {
    return router.currentRoute.value.query.type || ''
  })
  const search = ref({
    symbol: '',
    website: ''
  })
  const activeStep = computed(() => {
    let number = 0
    if ((curType.value !== 'NFT' && search.value.symbol !== '' && search.value.symbol !== undefined && search.value.website === '') || (curType.value === 'NFT' && search.value.symbol)) {
      number = 1
    } else if (search.value.symbol !== '' && search.value.symbol !== undefined && search.value.website !== '') {
      number = 2
    }
    return number
  })
  watch(() => activeStep.value, (val) => {
    if (val < 1) {
      search.value.website = ''
    }
  })
  const websiteList = ref([])
  const websiteItme = ref({})
  const depostAddress = ref({})
  const depositType = ref(0)
  watch(() => search.value.symbol, (val) => {
    if (curType.value === 'NFT') {
      getNftAddress()
    } else {
      search.value.website = ''
      getSymboleInfo()
    }
  })
  watch(() => search.value.website, (val) => {
    if (val !== '') {
      getDepositAddress()
    } else {
      depostAddress.value = {}
    }
  })
  const nftAddressInfo = ref({})
  const getNftAddress = async() => {
    const { data } = await getNftAddrApi({
      nft_id: search.value.symbol
    })
    if (data) {
      nftAddressInfo.value = data
    }
  }
  const getSymboleInfo = async() => {
    const { data } = await getListByGeneralNameApi({
      general_name: search.value.symbol
    })
    if (data) {
      websiteList.value = data.filter((item) => {
        return item.enable_deposit * 1 === 1
      })
      websiteItme.value = data[0]
    }
  }
  const changeWebsite = (item) => {
    websiteItme.value = item
  }
  const isAddressLoading = ref(false)
  const getDepositAddress = async() => {
    isAddressLoading.value = true
    const { data, error } = await getDepositAddressApi({
      coin_symbol: search.value.website
    })
    if (data) {
      depostAddress.value = data
    } else {
      depostAddress.value = {}
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
    isAddressLoading.value = false
  }
  const headersList = ref([
    { text: t('时间'), key: 'createdAt', align: 'left', wid: 'flex-2', style: 'auto' },
    { text: t('币种'), key: 'coin_symbol', align: 'left', wid: '',style: 'auto' },
    { text: t('数量'), key: 'amount', align: 'left', wid: '', style: '' },
    { text: t('状态'), key: 'status', align: 'left', wid: '', style: 'auto' },
    { text: t('操作'), key: 'comment', align: 'right', wid: '', style: 'auto' }
  ])
  const mSortList = ref([
    { text: t('币种'), key: 'coin_symbol', align: 'left', wid: 'flex-2',style: '' },
    { text: t('操作'), key: 'comment', align: 'right', wid: '', style: 'auto' },
    { text: t('状态'), key: 'status', align: 'right', wid: '', style: 'auto' },
    { text: t('数量'), key: 'amount', align: '', wid: '', style: '' },
    { text: t('时间'), key: 'createdAt', align: '', wid: '', style: 'fit-tc-secondary' }
  ])
  const currentList = ref([])
  const statusMap = ref({
    1: {
      text: t('确认中'),
      class: 'fit-tc-secondary'
    },
    2: {
      text: t('成功'),
      class: 'fit-rise'
    },
    3: {
      text: t('失败'),
      class: 'fit-fall'
    }
  })
  const codeLinkFormat = (str) => {
    const maxLength = 40
    if (str.length <= maxLength) {
      return str
    }
    const halfLength = Math.floor(maxLength / 2) - 1
    return str.slice(0, 12) + '......' + str.slice(-12)
  }
  const isLoading = ref(false)
  const getDepositList = async() => {
    search.value.symbol = router.currentRoute.value.query.symbol
    isLoading.value = true
    const { data } = await transferTransferInList({
      page: 1,
      size: 20,
      filter_type: 0,
    })
    if (data) {
      currentList.value = data.items
      isLoading.value = false
    }
  }
  const isShowDepositList = ref(false)
  const changeAddress = (item) => {
    depostAddress.value = item
    getDepositAddress()
  }
  const isShowAd = ref(0)
  const requestAnimationFrameInterval = ref(null)
  const socketDateAnimation = () => {
    isShowAd.value = cookies.get('showMessage')
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  }
  onMounted(() => {
    socketDateAnimation()
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
    getDepositList()
  })
</script>
<style lang="scss">
  @import url('@/assets/style/orders/deposit-withdrawal.scss');
</style>
