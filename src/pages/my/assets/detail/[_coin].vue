<template>
  <div class="wallet-container">
    <div class="wallet-wrapper">
      <div class="wallet-assets-box mg-0">
        <div class="wallet-assets wallet-asset-all flex-box space-between">
          <div class="left flex-box">
            <div class="symbol-info flex-box">
              <BoxCoinIcon :icon="iconUrl" class="iconImg" />
              <div class="info-text">
                <h2>{{ curItemObj.asset }}</h2>
              </div>
            </div>
            <div class="info-items flex-box">
              <div class="items">
                <p>{{ $t('数量') }}</p>
                <p>{{ curItemObj.total }} {{ curItemObj.asset }}</p>
              </div>
              <div class="items">
                <p>{{ $t('估值') }}</p>
                <p>{{ curItemObj.eqbtc }} {{ curItemObj.asset }}</p>
              </div>
              <div class="items">
                <p>{{ $t('可用') }}</p>
                <p>{{ curItemObj.balance }} {{ curItemObj.asset }}</p>
              </div>
              <div class="items">
                <p>{{ $t('冻结') }}</p>
                <p> {{ curItemObj.holds }} {{ curItemObj.asset }}</p>
              </div>
            </div>
          </div>
          <div class="right">
            <el-button class="coin-btn" @click="transferFun()">{{ $t('划转') }}</el-button>
          </div>
        </div>
      </div>
      <div class="wallet-table-cont">
        <div class="wallet-table-wrap">
          <div class="trade-title flex-box space-between">
            {{ $t('去交易') }}
            <div class="pd-tb12 pd-lr12 cursor-pointer" style="margin-right:-12px;" @click="toMarketIndex()">
              <MonoRightArrowShort size="16" class="fit-tc-secondary" />
            </div>
          </div>
          <div class="trade-cont">
            <ul>
              <li v-for="(item, index) in CoinPairList" :key="index" @click.stop="toTrade(item.pair)">
                <h3 class="flex-box">
                  {{ item.pair.includes('_SWAP') ? item.pair.replace('_SWAP', '').replace('_', '') : item.pair.replace('_', '/') }}
                  <span class="tag-icon">{{ item.pair.includes('_SWAP') ? $t('U本位') : $t('现货') }}</span>  
                </h3>
                <p>
                  <span>{{ item.equsdt }}</span>
                  <span>{{ format(((Number(item.equsdt) / totalUSDT) * 100), 2) }}%</span>
                </p>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <WalletBills v-if="billType === 'wallet'" :curCoin="curCoin" />
      <TradeBills v-if="billType === 'trade'" :curCoin="curCoin" />
    </div>
    <TransferDialog v-if="isShowTransfer" :defaultAccount="defaultAccount" :visibleDialog="isShowTransfer" @close="isShowTransfer = false" />
  </div>
</template>
<script lang="ts" setup>
import { ElInput, ElCheckbox, ElTable, ElTableColumn, ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus'
import MonoMoreDot from '~/components/common/icon-svg/MonoMoreDot.vue'
import WalletBills from '~/components/my/assets/wallet-bills.vue'
import TradeBills from '~/components/my/assets/trade-bills.vue'
import { getPairList } from '~/api/public'
import { commonStore } from '~/stores/commonStore'
import TransferDialog from '~/components/common/TransferDialog.vue'
const { locale } = useI18n()
const store = commonStore()
const { getAllPairList, getAssetsByCoin } = store
const { allPairList, CoinAssetObj } = storeToRefs(store)
const router = useRouter()
const isMobile = ref(false)
const screenWidth = ref(0)
const isShowTransfer = ref(false)
const defaultAccount = ref('')
const billType = computed(() => {
  return router.currentRoute.value.query.type
})
const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth
  isMobile.value = screenWidth.value <= 768
}
const curCoin = computed(() => {
  return router.currentRoute.value.params._coin
})
const iconUrl = computed(() => {
  return encodeURI(router.currentRoute.value.query.icon)
})
const CoinPairList = computed(() => {
  let arr = []
  if (CoinAssetObj.value && allPairList.value.length > 0) {
    Object.values(CoinAssetObj.value).forEach((item) => {
      allPairList.value.forEach((it) => {
        if (item.asset === it.split('_')[0]) {
          const obj = {
            pair: it,
            asset: item.asset,
            equsdt: item.equsdt,
            total: item.total,
            holds: item.holds,
            balance: item.balance,
            eqbtc: item.eqbtc,
            eqcny: item.eqcny
          }
          arr.push(obj)
        }
      })
    })
  }
  return arr.filter((item) => {
    return router.currentRoute.value.params._coin === 'USDT' ? (item.asset === 'BTC' || item.asset === 'ETH') : router.currentRoute.value.params._coin === item.asset
  })
})
const curItemObj = computed(() => {
  let obj = {}
  if (router.currentRoute.value.params._coin === 'USDT' && CoinAssetObj.value?.USDT) {
    obj = CoinAssetObj.value && CoinAssetObj.value['USDT']
  } else {
    CoinPairList.value.forEach((item) => {
      if (item.asset === router.currentRoute.value.params._coin) {
        obj = item
      }
    })
  }
  return obj
})
const totalUSDT = computed(() => {
  let num = 0
  if (CoinPairList.value.length > 0) {
    CoinPairList.value.forEach((item) => {
      num += Number(item.equsdt)
    })
  }
  console.info(num, 'numnum')
  return num
})
const transferFun = () => {
  defaultAccount.value = billType.value
  isShowTransfer.value = true
}
const toTrade = (pair) => {
  router.push(`/${locale.value}/exchange/${pair}`)
}
const toMarketIndex = () => {
  router.push(`/${locale.value}`)
}
onMounted(() => {
  getAllPairList()
  getAssetsByCoin()
  updateScreenWidth()
})
</script>
<style lang="scss">
  @import url('@/assets/style/orders/wallet.scss');
</style>
