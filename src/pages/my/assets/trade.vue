<template>
  <div class="wallet-container">
    <div class="wallet-wrapper">
      <div class="wallet-assets-box mg-0">
        <div v-if="JSON.stringify(allAsset) !== '{}'" class="wallet-assets wallet-asset-all">
          <div class="left flex-box align-start">
            <div class="flex-1" style="flex:1.5;">
              <div class="title-cont flex-box">
                <div class="icon"></div>
                <span>{{ $t('统一交易账户总资产估值') }}</span>
                <el-tooltip placement="top">
                  <template #content>
                    <div style="width:300px; padding:10px;">
                      <h2 class="font-size-16 pd-b8">{{ $t('什么是统一交易账户') }}</h2>
                      <p>{{ $t('所有资金都整合在“统一交易账户”中，可以跨现货和合约市场灵活调配和使用。您可以在一个账户中进行现货交易和合约交易，不需要为每种交易类型单独开设账户。') }}</p>
                    </div>
                  </template>
                  <MonoWarn size="18" class="fit-tc-secondary" />
                </el-tooltip>
                <MonoEyeOpen v-if="!isHideAssets" @click="setHideAssets" />
                <MonoEyeClose v-if="isHideAssets" @click="setHideAssets" />
              </div>
              <div class="info-text">
                <span style="font-weight:600;">{{ useCommon.hideAssets(format(tradeUSDTAssets, 4, true, true)) }}</span>
                <em>USDT</em>
              </div>
              <div class="info-small-text"> ≈{{ useCommon.hideAssets(useCommon.convert(tradeUSDTAssets, 'USDT', true)) }}</div>
            </div>
            <div class="flex-1">
              <div class="title-cont flex-box">
                <span>{{ $t('合约保证金余额') }}</span>
                <el-tooltip placement="top">
                  <template #content>
                    <div style="width:300px; padding:10px;">
                      <h2 class="font-size-16 pd-b8">{{ $t('什么是保证金余额') }}</h2>
                      <p>{{ $t('保证金余额是指账户中可用于作为保证金的总金额，包括钱包余额和未结算的合约盈亏，如果低于维持保证金，则会触发强制平仓。请注意，保证金余额是估值金额，并非账户中的实际金额。') }}</p>
                    </div>
                  </template>
                  <MonoWarn size="18" class="fit-tc-secondary" />
                </el-tooltip>
              </div>
              <div class="info-text">
                <span style="font-weight:600;">{{ useCommon.hideAssets(format(posMarginBalance, 4, true, true)) }}</span>
                <em>USDT</em>
              </div>
            </div>
            <div class="flex-1">
              <div class="title-cont flex-box">
                {{ $t('永续合约未实现盈亏') }}
              </div>
              <div class="info-text" :class="{'fit-rise': Number(unprofit) > 0, 'fit-fall': Number(unprofit) < 0}">
                <span style="font-weight:600;">{{ useCommon.hideAssets(format(unprofit, 4, true, true)) }}</span>
                <em>USDT</em>
              </div>
            </div>
          </div>
          <div class="right">
            <el-button @click="transferFun('trade')">{{ $t('划转') }}</el-button>
          </div>
        </div>
      </div>
      <div class="wallet-table-cont">
        <div class="wallet-table-wrap">
          <div class="table-header flex-box space-between">
            <div class="head-left none-text">{{ $t('交易资产') }}</div>
            <div class="head-right flex-box none-text">
              <el-input v-model="searchInput" :placeholder="$t('搜索代币')">
                <template #prepend>
                  <MonoSearch size="16" />
                </template>
              </el-input>
              <el-checkbox v-model="isHideAsset1">{{ $t('隐藏小于1U资产') }}</el-checkbox>
            </div>
          </div>
          <div class="table-body" :style="isAssetLoading ? 'height: 300px !important;' : (walletAssetsList.length > 0 ? '' : 'height: 300px !important;')">
            <OrderstableBox :isLoading="isAssetLoading" :mSortList="mSortList" :headers="headersList" :list="walletAssetsList">
              <template #asset="scope">
                <div class="symbol-box flex-box cursor-pointer" @click="toTrade(scope.data)">
                  <BoxCoinIcon :icon="scope.data.icon_url" class="iconImg" />
                  <div class="symbolTxt">
                    <h3>{{ scope.data.assetText }}</h3>
                    <p>{{ scope.data.symbolName }}</p>
                  </div>
                </div>
              </template>
              <template #total="scope">
                <div class="flex-box flex-column space-end align-end">
                  <p>{{ useCommon.hideAssets(format(scope.data.total, 10, true, true)) }}</p>
                  <p>≈{{ useCommon.hideAssets(format(scope.data.equsdt, 10, true, true)) }}USDT</p>
                </div>
              </template>
              <template #balance="scope">
                <p>{{ useCommon.hideAssets(format(scope.data.withdrawable, 10, true, true) || 0) }}</p>
              </template>
              <template #header-discount="scope">
                <el-tooltip placement="top">
                  <span class="cursor-pointer fit-tc-secondary" style="font-weight:400;text-decoration:underline;">{{ $t('开启资产抵押') }}</span>
                  <template #content>
                    <div class="font-size-12" style="max-width:400px;">
                      <div>{{ $t('您可选择将该币种设为抵押资产，开关开启后将按平台折算率折算为USDT，用于现货杠杆和合约交易的仓位保证金。') }}
                        <span class="fit-theme cursor-pointer" @click="isShowCoinPercent = true">{{ $t('币种折算率') }}</span>
                      </div>
                    </div>
                  </template>
                </el-tooltip>
              </template>
              <template #discount="scope">
                <el-switch v-if="(switchRow[scope.data.asset] || {}).isShow" :disabled="scope.data.asset === 'KtxQ1' || scope.data.asset === 'KtxQ2' || scope.data.asset === 'USDT' || scope.data.asset === 'USDC'" v-model="(switchRow[scope.data.asset] || {}).isSwitch" :loading="(switchRow[scope.data.asset] || {}).isLoading" @change="changeSwitch(scope.data)" />
                <!-- <span v-else>{{ t('不支持') }}</span>-->
              </template>
              <template #comment="scope">
                <ul v-if="!(scope.data.asset === 'KtxQ1' || scope.data.asset === 'KtxQ2')" class="flex-box flex-wrap space-end">
                  <li class="mg-l12 cursor-pointer" @click.stop="toTrade(scope.data)">
                    {{ $t('交易') }}
                  </li>
                  <li class="mg-l12 cursor-pointer" @click.stop="transferFunItem(scope.data.asset)">
                    {{ $t('划转') }}
                  </li>
                </ul>
              </template>
            
            </OrderstableBox>
          </div>
        </div>
      </div>
      <TradeBills />
    </div>
  </div>
  <TransferDialog v-if="isShowTransfer" :defaultAccount="defaultAccount" :defaultSymbol="defaultSymbol" :visibleDialog="isShowTransfer" @close="isShowTransfer = false" />
  <el-dialog v-if="isShowCoinPercent" v-model="isShowCoinPercent" :title="$t('币种折算率')" width="680" @close="isShowCoinPercent = false">
    <div>
      <p class="font-size-14 fit-tc-primary">{{ $t('因市场价格波动，该表格会适时调整。') }}</p>
      <p class="font-size-14 fit-tc-secondary">{{ $t('最后更新时间') }}UTC+8：2024-12-04 14:23:04</p>
    </div>
    <OrderstableBox :isLoading="iscoinLoading" :mSortList="mCoinSortList" :headers="coinHeadersList" :list="coinPercentList">
      <template #symbol="scope">
        {{ scope.data.symbol }}
      </template>
      <template #last="scope">
        {{ scope.data.last }}
      </template>
      <template #discount="scope">
        {{ scope.data.discount }}
      </template>
      <template #comment="scope">
        1 {{ scope.data.symbol }} = {{ format(scope.data.last * scope.data.discount, getDecimalPlaces(scope.data.last), true) }} USDT
      </template>
    </OrderstableBox>
  </el-dialog>
</template>
<script lang="ts" setup>
import BigNumber from 'bignumber.js'
import { ElInput, ElCheckbox, ElTable, ElTableColumn, ElDropdown, ElDropdownMenu, ElDropdownItem, ElTooltip, ElSwitch, ElMessageBox, ElDialog } from 'element-plus'
import MonoMoreDot from '~/components/common/icon-svg/MonoMoreDot.vue'
import OrderstableBox from '~/components/common/OrderstableBox.vue'
import MonoWarn from '~/components/common/icon-svg/MonoWarn.vue'
import TransferDialog from '~/components/common/TransferDialog.vue'
import MonoEyeOpen from '~/components/common/icon-svg/MonoEyeOpen.vue'
import MonoEyeClose from '~/components/common/icon-svg/MonoEyeClose.vue'
import TradeBills from '~/components/my/assets/trade-bills.vue'
import { format } from '~/utils'
import { commonStore } from '~/stores/commonStore'
import { getCollaterals } from '~/api/public.ts'
import { setCollateral } from '~/api/order.ts'
const store = commonStore()
const { getCoinList, getAssetsByCoin, setHideAssets } = store
const { allAsset, isHideAssets, posMapObj, tradeAssetObj, coinList } = storeToRefs(store)
const { locale, t } = useI18n()
const router = useRouter()
const searchInput = ref('')
const isHideAsset1 = ref(false)
const props = defineProps({
  useCommon: {
    type: Object,
    default () {
      return {}
    }
  },
  user: {
    type: Object,
    default () {
      return {}
    }
  }
})
const isShowCoinPercent = ref(false)
const mSortList = ref([
  { text: t('名称'), key: 'asset', align: 'left', wid: '', style: 'auto' },
  { text: t('操作'), key: 'comment', align: 'left', wid: '', style: 'auto' },
  { text: t('数量'), key: 'total', align: 'left', wid: '', style: 'auto' },
  { text: t('可用'), key: 'balance', align: 'left', wid: '', style: 'auto' },
  // { text: t('冻结'), key: 'holds', align: 'left', wid: '', style: 'auto' },
  { text: t('资产抵押'), key: 'discount', align: 'right', wid: '', style: 'auto', headerAuto: true }
])
const headersList = ref([
  { text: t('名称'), key: 'asset', align: 'left', wid: '', style: 'auto' },
  { text: t('数量'), key: 'total', align: 'right', wid: 'flex-2', style: 'auto' },
  { text: t('可用'), key: 'balance', align: 'right', wid: 'flex-2', style: 'auto' },
  // { text: t('冻结'), key: 'holds', align: 'right', wid: 'flex-2', style: 'auto' },
  { text: t('资产抵押'), key: 'discount', align: 'right', wid: '', style: 'auto', headerAuto: true },
  { text: t('操作'), key: 'comment', align: 'right', wid: '', style: 'auto' }
])
const unprofitUSDTAssets = computed(() => {
  return posMapObj.value && posMapObj.value['USDT'] && posMapObj.value['USDT'].unprofit
})
const tradeUSDTAssets = computed(() => {
  return new BigNumber(allAsset.value && allAsset.value.trade.equsdt).plus(unprofitUSDTAssets.value)
})
watch(() => isHideAsset1.value, (val) => {
  localStorage.setItem(props.user.user_id + 'assetsChecked', val)
})
watch(() => props.user, (val) => {
  if (JSON.stringify(val) !== '{}') {
    isHideAsset1.value = localStorage.getItem(props.user.user_id + 'assetsChecked') === 'true'
  }
}, {
  immediate: true
})
const posMarginBalance = computed(() => {
  const tradeObj = tradeAssetObj.value && tradeAssetObj.value['USDT'] && tradeAssetObj.value['USDT']
  const posObj = posMapObj.value && posMapObj.value['USDT'] && posMapObj.value['USDT']
  const result = tradeObj.balanceUnify * 1 - Math.abs(posObj.crossUnprofit * 1) - posObj.posMargin * 1
  return result < 0 ? 0 : result
})
const posMargin = computed(() => {
  return tradeAssetObj.value && tradeAssetObj.value['USDT'] && tradeAssetObj.value['USDT'].balanceUnify || 0
})
const unprofit = computed(() => {
  return posMapObj.value && posMapObj.value['USDT'] && posMapObj.value['USDT'].unprofit || 0
})
const arrayToObject = (arr) => {
  return arr.reduce(function(obj, item) {
    var key = item.general_name;
    var value = item
    obj[key] = value
    return obj
  }, {})
}
const collateralsObj = ref([])
const switchRow = ref({})
const setCollateralApi = async(params, instance, done) => {
  const { data, error } = await setCollateral({
    symbol: params.asset,
    collateral: !switchRow.value[params.asset].isSwitch
  })
  if (data) {
    switchRow.value[params.asset] = {
      isShow: params.discount,
      isSwitch: !switchRow.value[params.asset].isSwitch,
      isLoading: false
    }
    instance.confirmButtonLoading = false
    props.useCommon.showMsg('success', t('设置成功！'))
    done()
    await getCoinList()
    await getAssetsByCoin()
  } else {
    switchRow.value[params.asset] = {
      isShow: params.discount,
      isSwitch: switchRow.value[params.asset].isSwitch,
      isLoading: false
    }
    instance.confirmButtonLoading = false
    props.useCommon.showMsg('error', props.useCommon.err(error.code, error))
    done()
  }
}
const changeSwitch = async(params) => {
  switchRow.value[params.asset] = {
    isShow: params.discount,
    isSwitch: params.collateral,
    isLoading: true
  }
  if (switchRow.value[params.asset].isSwitch) {
    ElMessageBox({
      title: t('取消使用该币种抵押'),
      showCancelButton: true,
      confirmButtonText: t('确定关闭'),
      cancelButtonText: t('取消'),
      message: h('div', { class: 'pd-lr8' }, [
        h('div', { class: 'font-size-14' }, [
          h('span', { class: 'fit-tc-secondary' }, t('关闭抵押开关不会影响现有合约持仓，但可能导致账户可用保证金减少，风险等级提高，请确保其他资产足以支持仓位，避免现有持仓合约强制平仓。'))
        ])
      ]),
      beforeClose: async(action, instance, done) => {
        if (action === 'confirm') {
          instance.confirmButtonLoading = true
          await setCollateralApi(params, instance, done)
        } else {
          done()
          switchRow.value[params.asset] = {
            isShow: params.discount,
            isSwitch: params.collateral,
            isLoading: false
          }
        }
      }
    })
  } else {
    ElMessageBox({
      title: t('使用该币种抵押'),
      showCancelButton: true,
      confirmButtonText: t('确定开启'),
      cancelButtonText: t('取消'),
      message: h('div', { class: 'pd-lr8' }, [
        h('div', { class: 'font-size-14' }, [
          h('span', { class: 'fit-tc-secondary' }, t('开启抵押开关后，该币种将按折算率转换为USDT，计入账户权益')),
          h('span', { class: 'fit-theme cursor-pointer mg-l8', id: 'coinPercendRef' }, t('币种折算率'))
        ])
      ]),
      beforeClose: async(action, instance, done) => {
        if (action === 'confirm') {
          instance.confirmButtonLoading = true
          await setCollateralApi(params, instance, done)
        } else {
          done()
          switchRow.value[params.asset] = {
            isShow: params.discount,
            isSwitch: params.collateral,
            isLoading: false
          }
        }
      }
    })
    nextTick(() => {
      const messageEl = document.getElementById('coinPercendRef')
      if (messageEl) {
        messageEl.addEventListener('click', () => {
          isShowCoinPercent.value = true
        })
      }
    })
  }
}
const walletAssetsList = computed(() => {
  const arr = Object.values(tradeAssetObj.value).map((item) => {
    item.assetText = item.asset === 'KtxQ1' ? t('体验金') : (item.asset === 'KtxQ2' ? t('抵扣金') : item.asset),
    item.icon_url = allAsset.value && allAsset.value['assetmap'] && allAsset.value['assetmap'][item.asset] && allAsset.value['assetmap'][item.asset].icon_url
    return item
  }) || []
  arr.forEach(item => {
    if (collateralsObj.value[item.asset]) {
      item.discount = true
    } else {
      item.discount = false
    }
    switchRow.value[item.asset] = {
      isShow: item.discount,
      isSwitch: item.collateral,
      isLoading: false
    }
  })
  const result = arr
  if (searchInput.value !== '') {
    return result.filter((item) => {
      return item.assetText.includes(searchInput.value.toUpperCase())
    })
  } else if (isHideAsset1.value) {
    return result.filter((item) => {
      return item.equsdt * 1 >= 1
    })
  }
  return result
})
const isAssetLoading = ref(true)
watch(() => walletAssetsList.value, (val) => {
  if (val.length > 0) {
    isAssetLoading.value = false
  }
})
const isShowTransfer = ref(false)
const defaultAccount = ref('')
const isMobile = ref(false)
const screenWidth = ref(0)
const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth
  isMobile.value = screenWidth.value <= 768
}
const transferFun = (type) => {
  defaultAccount.value = type
  isShowTransfer.value = true
}
const defaultSymbol = ref('')
const transferFunItem = (symbol) => {
  isShowTransfer.value = true
  defaultSymbol.value = symbol
}
const toTrade = (item) => {
  router.push(`/${locale.value}/my/assets/detail/${item.asset}?type=trade&icon=${encodeURIComponent(item.icon_url)}`)
}
const iscoinLoading = ref(false)
const mCoinSortList = ref([
  { text: t('币种'), key: 'symbol', align: 'left', wid: '', style: 'auto' },
  { text: `${t('当前价格')}(USDT)`, key: 'last', align: 'left', wid: '', style: 'auto' },
  { text: t('折算率'), key: 'discount', align: 'left', wid: '', style: 'auto' },
  { text: t('折算说明(当前价格 * 折算率)'), key: 'comment', align: 'left', wid: '', style: 'auto' }
])
const coinHeadersList = ref([
  { text: t('币种'), key: 'symbol', align: 'left', wid: '', style: 'auto' },
  { text: `${t('当前价格')}(USDT)`, key: 'last', align: 'left', wid: '', style: 'auto' },
  { text: t('折算率(相对于USDT)'), key: 'discount', align: 'left', wid: '', style: 'auto' },
  { text: t('折算说明(当前价格 * 折算率)'), key: 'comment', align: 'right', wid: 'flex-2', style: 'auto' }
])
const coinPercentList = ref([])
const getDecimalPlaces = (num) => {
  // 将数字转为字符串
  const str = num.toString()
  // 用小数点分割
  const parts = str.split('.')
  // 如果有小数部分，返回其长度，否则返回0
  return parts.length > 1 ? parts[1].length : 0
}
const getCoolateralsList = async() => {
  const { data } = await getCollaterals()
  if (data) {
    data.data.push({
      discount: '1',
      symbol: 'USDT'
    })
    data.data.forEach((item) => {
      collateralsObj.value[item.symbol] = item
    })
    coinPercentList.value = data.data.filter((item) => {
      return item.symbol !== 'USDT'
    })
  }
  console.log(coinPercentList.value, 'djdjdiejidjeijdiejijei')
}
onBeforeMount(async() => {
  updateScreenWidth()
  await getCoolateralsList()
  await getCoinList()
  await getAssetsByCoin()
  isAssetLoading.value = false
})
</script>
<style lang="scss">
  @import url('@/assets/style/orders/wallet.scss');
</style>
