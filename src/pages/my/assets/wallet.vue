<template>
  <div class="wallet-container">
    <div class="wallet-wrapper">
      <div class="wallet-assets-box mg-0">
        <div v-if="JSON.stringify(allAsset) !== '{}'" class="wallet-assets wallet-asset-all">
          <div class="left">
            <div class="title-cont flex-box">
              <div class="icon"></div>
              {{ $t('钱包账户') }}
              <MonoEyeOpen v-if="!isHideAssets" @click="setHideAssets" />
              <MonoEyeClose v-if="isHideAssets" @click="setHideAssets" />
            </div>
            <div class="info-text">
              <span style="font-weight:600;">{{ useCommon.hideAssets(formatNumberWithCommas(allAsset.main.equsdt)) }}</span>
              <em>USDT</em>
            </div>
            <div class="info-small-text"> ≈{{ useCommon.hideAssets(useCommon.convert(allAsset.main.equsdt, 'USDT', true)) }}</div>
          </div>
          <div class="right">
            <NuxtLink :to="`/${locale}/my/assets/deposit`" class="primary">{{ $t('充值') }}</NuxtLink>
            <template v-if="user.user_type * 1 !== 100">
              <NuxtLink :to="`/${locale}/my/assets/withdrawal`">{{ $t('提币') }}</NuxtLink>
            </template>
            <a class="cursor-pointer" @click="isShowTransfer = true">{{ $t('划转') }}</a>
          </div>
        </div>
      </div>
      <div class="wallet-table-cont">
        <div class="wallet-table-wrap">
          <div class="table-header flex-box space-between">
            <div class="head-left none-text">{{ $t('钱包资产') }}</div>
            <div class="head-right flex-box none-text">
              <el-input v-model="searchInput" :placeholder="$t('搜索代币')" clearable>
                <template #prepend>
                  <MonoSearch size="16" />
                </template>
              </el-input>
              <el-checkbox v-model="isHideAsset1">{{ $t('隐藏小于1U资产') }}</el-checkbox>
            </div>
          </div>
          <div class="table-body">
            <OrderstableBox :isLoading="isAssetLoading" :mSortList="mSortList" :headers="headersList" :list="walletAssetsList">
              <template #asset="scope">
                <div class="symbol-box flex-box cursor-pointer" @click="toTrade(scope.data)">
                  <BoxCoinIcon :icon="scope.data.icon_url" class="iconImg" />
                  <div class="symbolTxt">
                    <h3>{{ scope.data.assetText }}</h3>
                    <p>{{ scope.data.symbolName }}</p>
                  </div>
                </div>
              </template>
              <template #total="scope">
                <div class="flex-box flex-column space-end align-end">
                  <p>{{ useCommon.hideAssets(format(scope.data.total, 10, true, true)) }}</p>
                  <p>≈{{ useCommon.hideAssets(format(scope.data.equsdt, 10, true, true)) }}USDT</p>
                </div>
              </template>
              <template #balance="scope">
                <p>{{ useCommon.hideAssets(format(scope.data.balance, 10, true, true)) }}</p>
              </template>
              <template #holds="scope">
                <p>{{ useCommon.hideAssets(format(scope.data.holds, 10, true, true)) }}</p>
              </template>
              <template #comment="scope">
                <div v-if="scope.data.enable_deposit * 1 === 1 || scope.data.enable_withdraw * 1 === 1 || scope.data.enable_transfer * 1 === 1">
                  <ul class="flex-box flex-wrap space-end">
                    <li class="mg-l12 cursor-pointer" v-if="scope.data.enable_deposit * 1 === 1" @click.stop="toLink(`/${locale}/my/assets/deposit?symbol=${scope.data.asset}`)">
                      {{ $t('充值') }}
                    </li>
                    <li class="mg-l12 cursor-pointer" v-if="scope.data.enable_withdraw * 1 === 1 && user.user_type * 1 !== 100" :class="{'disabled': scope.data.equsdt * 1 === 0}" @click.stop="toLink(`/${locale}/my/assets/withdrawal?symbol=${scope.data.asset}`, true, scope.data)">
                      {{ $t('提币') }}
                    </li>
                    <li class="mg-l12 cursor-pointer" v-if="scope.data.enable_transfer * 1 === 1 && !(scope.data.asset === 'KtxQ1' || scope.data.asset === 'KtxQ2')" @click.stop="transferFun(scope.data.asset)">
                      {{ $t('划转') }}
                    </li>
                  </ul>
                </div>
                <!-- <el-button v-else type="primary" @click="toTrade(scope.data)">{{ $t('交易') }}</el-button> -->
              </template>
            </OrderstableBox>
          </div>
        </div>
      </div>
      <WalletBills />
    </div>
  </div>
  <TransferDialog v-if="isShowTransfer" :visibleDialog="isShowTransfer" :defaultSymbol="defaultSymbol" @close="isShowTransfer = false" />
</template>
<script lang="ts" setup>
import { ElInput, ElCheckbox, ElTable, ElTableColumn, ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus'
import MonoMoreDot from '~/components/common/icon-svg/MonoMoreDot.vue'
import OrderstableBox from '~/components/common/OrderstableBox.vue'
import TransferDialog from '~/components/common/TransferDialog.vue'
import MonoEyeOpen from '~/components/common/icon-svg/MonoEyeOpen.vue'
import MonoEyeClose from '~/components/common/icon-svg/MonoEyeClose.vue'
import WalletBills from '~/components/my/assets/wallet-bills.vue'
import { timeFormat, format } from '~/utils'
import { MainBillsChange } from '~/api/tf'
import { commonStore } from '~/stores/commonStore'
const store = commonStore()
const { getAssetsByCoin, setHideAssets, getCoinList } = store
const { allAsset, mainAssetObj, isHideAssets, coinList } = storeToRefs(store)
const { locale, t } = useI18n()
const router = useRouter()
const searchInput = ref('')
const isHideAsset1 = ref(false)
const props = defineProps({
  useCommon: {
    type: Object,
    default () {
      return {}
    }
  },
  user: {
    type: Object,
    default () {
      return {}
    }
  }
})
const mSortList = ref([
  { text: t('名称'), key: 'asset', align: 'left', wid: '', style: 'auto' },
  { text: t('操作'), key: 'comment', align: 'left', wid: '', style: 'auto' },
  { text: t('数量'), key: 'total', align: 'left', wid: '', style: 'auto' },
  { text: t('可用'), key: 'balance', align: 'left', wid: '', style: 'auto' },
  { text: t('冻结'), key: 'holds', align: 'left', wid: '', style: 'auto' }
])
const headersList = ref([
  { text: t('名称'), key: 'asset', align: 'left', wid: '', style: 'auto' },
  { text: t('数量'), key: 'total', align: 'right', wid: 'flex-2', style: 'auto' },
  { text: t('可用'), key: 'balance', align: 'right', wid: '', style: 'auto' },
  { text: t('冻结'), key: 'holds', align: 'right', wid: '', style: 'auto' },
  { text: t('操作'), key: 'comment', align: 'right', wid: 'flex-2', style: 'auto' }
])
watch(() => isHideAsset1.value, (val) => {
  localStorage.setItem(props.user.user_id + 'assetsChecked', val)
})
watch(() => props.user, (val) => {
  if (JSON.stringify(val) !== '{}') {
    isHideAsset1.value = localStorage.getItem(props.user.user_id + 'assetsChecked') === 'true'
  }
}, {
  immediate: true
})
const arrayToObject = (arr) => {
  return arr.reduce(function(obj, item) {
    var key = item.general_name;
    var value = item
    obj[key] = value
    return obj
  }, {})
}
const walletAssetsList = computed(() => {
  // 1. 先收集所有有资产的币种（按 mainAssetObj.value 的键顺序）
  const assetsWithBalance = Object.keys(mainAssetObj.value).map(asset => {
    const coin = coinList.value.find(c => c.general_name === asset);
    if (!coin) return null; // 如果 coinList 里没有这个币，跳过
    return {
      ...mainAssetObj.value[asset],
      assetText: asset === 'KtxQ1' ? t('体验金') : (asset === 'KtxQ2' ? t('抵扣金') : asset),
      icon_url: coin.icon_url,
      enable_deposit: coin.enable_deposit,
      enable_transfer: coin.enable_transfer,
      enable_withdraw: coin.enable_withdraw
    };
  }).filter((item) => {
    return !(item.asset === 'KtxQ1' || item.asset === 'KtxQ2')
  }); // 过滤掉 null（无效数据）
  // 2. 收集无资产的币种（按 weight 排序）
  const assetsWithoutBalance = coinList.value
    .filter(coin => !mainAssetObj.value[coin.general_name]) // 只选无资产的
    .sort((a, b) => b.weight - a.weight) // 按 weight 降序
    .map(coin => ({
      asset: coin.general_name,
      assetText: coin.general_name === 'KtxQ1' ? t('体验金') : (coin.general_name === 'KtxQ2' ? t('抵扣金') : coin.general_name),
      icon_url: coin.icon_url,
      enable_deposit: coin.enable_deposit,
      enable_transfer: coin.enable_transfer,
      enable_withdraw: coin.enable_withdraw,
      balance: "0",
      holds: "0",
      total: "0",
      equsdt: "0",
      eqbtc: "0",
      eqcny: "0"
    })).filter((item) => {
    return !(item.asset === 'KtxQ1' || item.asset === 'KtxQ2')
  });
  // 3. 合并：有资产的（按 mainAssetObj 顺序） + 无资产的（按 weight 排序）
  const arr = [...assetsWithBalance, ...assetsWithoutBalance];
  // 4. 应用筛选逻辑（搜索或隐藏小额资产）
  if (searchInput.value !== '') {
    return arr.filter(item => item.assetText.includes(searchInput.value.toUpperCase()));
  } else if (isHideAsset1.value) {
    return arr.filter(item => item.equsdt * 1 >= 1);
  }
  return arr;
})
const isAssetLoading = ref(true)
watch(() => walletAssetsList.value, (val) => {
  if (val.length > 0) {
    isAssetLoading.value = false
  }
})
const isShowTransfer = ref(false)
const isMobile = ref(false)
const screenWidth = ref(0)
const defaultSymbol = ref('')
const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth
  isMobile.value = screenWidth.value <= 768
}
const isShowDropLog = ref({})
const showClick = (symbol) => {
  console.info(symbol, 'dhuehudhueuh')
  isShowDropLog.value = {}
  isShowDropLog.value[symbol] = true
}
const toTrade = (item) => {
  router.push(`/${locale.value}/my/assets/detail/${item.asset}?type=wallet&icon=${encodeURIComponent(item.icon_url)}`)
}
const transferFun = (symbol) => {
  isShowTransfer.value = true
  defaultSymbol.value = symbol
}
const toLink = (link, isWithdrawal, row) => {
  if (isWithdrawal && row.equsdt * 1 === 0) {
    props.useCommon.showMsg('error', t('{symbol} 资产为 0，暂不支持提币', {symbol: row.asset}))
    return false
  }
  router.push(link)
}
onMounted(async() => {
  updateScreenWidth()
  await getCoinList()
  await getAssetsByCoin()
  isAssetLoading.value = false
})
</script>
<style lang="scss">
  @import url('@/assets/style/orders/wallet.scss');
  .cont-list-more-wrap{
    ul{
      li{
        @include color(tc-primary);
        text-decoration:underline;
        margin-left:12px;
        &:hover{
          @include color(theme);
        }
      }
    }
  }
</style>
