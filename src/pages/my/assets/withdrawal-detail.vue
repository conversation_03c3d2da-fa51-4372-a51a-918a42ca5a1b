<template>
  <div class="deposit-container">
    <div class="deposit-nav-box" :class="{'hasad': isShowAd * 1 === 1 && !isApp(), 'headNo': isApp(), 'hasadHeadNo': isShowAd * 1 === 1 && isApp()}">
      <el-breadcrumb separator-icon="MonoRightArrowShort">
        <el-breadcrumb-item :to="{ path: `/${locale}/my/assets/wallet` }">{{ $t('钱包账户') }}</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: `/${locale}/my/assets/withdrawal` }">{{ $t('提币') }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ $t('提币详情') }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div v-loading="isLoading" class="deposit-body-cont">
      <div class="deposit-body-cont-detail">
        <div class="deposit-info-title">
          <div class="deposit-value">{{ detailInfo.amount }} {{ detailInfo.coin_symbol }}</div>
          <div class="deposit-status flex-box space-center mg-t8" :class="{ 'fall' : detailInfo.status === 3, 'rise': Number(activeStep) === 3 }">
            <MonoUnstartIcon v-if="detailInfo.status === 3" :size="16" class="fit-fall mg-t4" />
            <template v-else>
              <MonoWaitIcon v-if="Number(activeStep) === 0 || Number(activeStep) === 1 || Number(activeStep) === 2" :size="16" class="fit-tc-secondary mg-t4" />
              <MonoEndIcon v-if="Number(activeStep) === 3" :size="16" class="fit-rise mg-t4" />
            </template>
            <span v-if="detailInfo.status === 3" class="fit-fall">{{ $t('提币失败') }}</span>
            <template v-else>
              <span v-if="Number(activeStep) === 0 || Number(activeStep) === 1 || Number(activeStep) === 2" class="fit-tc-secondary">{{ $t('确认中') }}</span>
              <span v-if="Number(activeStep) === 3" class="fit-rise">{{ $t('提币成功') }}</span>
            </template>
          </div>
        </div>
        <el-steps v-if="detailInfo.status === 1 || detailInfo.status === 2" direction="vertical" :active="activeStep">
          <el-step :title="$t('提币申请已提交')">
            <template #description>
              <span class="font-size-14 fit-tc-secondary">{{ timeFormat(detailInfo.createdAt, 'yyyy-MM-dd hh:mm:ss') }}</span>
            </template>
          </el-step>
          <el-step :title="$t('系统确认')">
            <template v-if="Number(activeStep) === 1" #description>
              <span class="font-size-14 fit-tc-secondary">{{ $t('系统确认中') }}</span>
            </template>
          </el-step>
          <el-step :title="$t('区块确认')">
          </el-step>
          <el-step :title="$t('提币成功')">
            <template #description>
              <span class="font-size-14 fit-tc-secondary">{{ $t('成功后，您将收到短信或Email提醒*邮件可能会被拦截，未收到邮件时请在邮箱垃圾箱中查看') }}</span>
            </template>
          </el-step>
        </el-steps>
        <div class="deposit-detail-info">
          <dl class="flex-box space-between font-size-14 pd-b16">
            <dt class="fit-tc-secondary">{{ $t('交易ID') }}</dt>
            <dd class="fit-tc-primary">{{ detailInfo.tx_id || '--' }}</dd>
          </dl>
          <dl class="flex-box space-between font-size-14 pd-b16">
            <dt class="fit-tc-secondary">{{ $t('实到金额') }}</dt>
            <dd class="fit-tc-primary">{{ detailInfo.amount_real }} {{ detailInfo.coin_symbol }}</dd>
          </dl>
          <dl class="flex-box space-between font-size-14 pd-b16">
            <dt class="fit-tc-secondary">{{ $t('提现免验证') }}</dt>
            <dd class="fit-tc-primary">
              <el-switch v-model="switchRow.isSwitch" :loading="switchRow.isLoading" @change="changeSwitch(detailInfo)"></el-switch>
            </dd>
          </dl>
          <dl class="flex-box space-between font-size-14 pd-b16">
            <dt class="fit-tc-secondary">{{ $t('提现时间') }}</dt>
            <dd class="fit-tc-primary">{{ timeFormat(detailInfo.createdAt, 'yyyy-MM-dd hh:mm:ss') }}</dd>
          </dl>
          <dl class="flex-box space-between font-size-14 pd-b16">
            <dt class="fit-tc-secondary">{{ $t('完成时间') }}</dt>
            <dd class="fit-tc-primary">{{ timeFormat(detailInfo.updatedAt, 'yyyy-MM-dd hh:mm:ss') }}</dd>
          </dl>
          <dl class="flex-box space-between font-size-14 pd-b16">
            <dt class="fit-tc-secondary">{{ $t('网络') }}</dt>
            <dd class="fit-tc-primary">{{ detailInfo.chain_type }}</dd>
          </dl>
          <dl class="flex-box space-between font-size-14 pd-b16">
            <dt class="fit-tc-secondary">{{ $t('提币地址') }}</dt>
            <dd class="fit-tc-primary">
              <div class="flex-box">
                <a v-if="detailInfo.tx_id" :href="detailInfo.url" target="_blank" class="fit-tc-primary">
                  <span class="fit-tc-primary" style="word-break: break-all;text-decoration:underline;">{{ detailInfo.to_address }}</span>
                </a>
                <span v-else class="fit-tc-primary" style="cursor:default; word-break: break-all;">{{ detailInfo.to_address }}</span>
                <a v-if="detailInfo.tx_id" :href="detailInfo.scan_url" target="_blank" rel="noopener noreferrer" class="mg-l8 fit-tc-primary mg-t4">
                  <MonoLinkUrl size="16" style="cursor:pointer;" />
                </a>
                <div class="mg-l8 fit-tc-primary mg-t4">
                  <MonoCopy size="16" style="cursor:pointer;" @click="useCommon.copy(detailInfo.to_address, $t('复制成功！'))" />
                </div>
              </div>
            </dd>
          </dl>
        </div>
      </div>
    </div>
    <withdrawalConfirm
      v-if="isShowNoVerify"
      :dialogVisible="isShowNoVerify"
      @confirm="updateSwitch"
      @close="isShowNoVerify = false"
    />
  </div>
  <BoxScript id="gt" ref="gt" append-to-body src="https://res.ktx.com/common/js/gt4.js" />
</template>
<script lang="ts" setup>
  import { isApp } from '~/utils'
  import { ElBreadcrumb, ElBreadcrumbItem, ElSteps, ElStep, ElTooltip, ElSwitch } from 'element-plus'
  import MonoUnstartIcon from '~/components/common/icon-svg/MonoUnstartIcon.vue'
  import MonoWaitIcon from '~/components/common/icon-svg/MonoWaitIcon.vue'
  import MonoEndIcon from '~/components/common/icon-svg/MonoEndIcon.vue'
  import MonoCopy from '~/components/common/icon-svg/MonoCopy.vue'
  import MonoLinkUrl from '~/components/common/icon-svg/MonoLinkUrl.vue'
  import withdrawalConfirm from '~/components/my/assets/withdrawalConfirm.vue'
  import { transferTransferOutList, addrAclChangeStatusApi } from '~/api/tf'
  import { timeFormat } from '~/utils'
  import { useCommonData } from '~/composables/index'
  const useCommon = useCommonData()
  const { locale, t } = useI18n()
  const router = useRouter()
  const activeStep = ref(0)
  const isShowAd = ref(0)
  const curId = computed(() => {
    return router.currentRoute.value.query.id || ''
  })
  const requestAnimationFrameInterval = ref(null)
  const socketDateAnimation = () => {
    isShowAd.value = cookies.get('showMessage')
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  }
  const detailInfo = ref({})
  const isLoading = ref(true)
  const switchRow = ref({
    isSwitch: '',
    isLoading: false
  })
  const curAdrItem = ref({})
  const isShowNoVerify = ref(false)
  const getDepositDetail = async() => {
    const { data } = await transferTransferOutList({
      page: 1,
      size: 20,
      id: curId.value,
    })
    detailInfo.value = data.items[0]
    activeStep.value = Number(data.items[0].step) - 1
    switchRow.value = {
      isSwitch: detailInfo.value.acl * 1 === 1,
      isLoading: false
    }
    isLoading.value = false
  }
  const changeSwitch = (params) => {
    switchRow.value = {
      isSwitch: params.acl * 1 === 1,
      isLoading: true
    }
    curAdrItem.value = params
    isShowNoVerify.value = true
    switchRow.value = {
      isSwitch: params.acl * 1 === 1,
      isLoading: false
    }
  }
  const updateSwitch = async(params) => {
    const { data, error } = await addrAclChangeStatusApi({
      addr: curAdrItem.value.to_address,
      status: curAdrItem.value.acl * 1 === 1 ? 0 : 1,
      // pwd: params.loginPassword,
      email_code: params.emailCode,
      totp_code: params.code
    })
    if (data) {
      isShowNoVerify.value = false
      getDepositDetail()
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  onMounted(() => {
    isLoading.value = true
    getDepositDetail()
    socketDateAnimation()
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  })
</script>
<style lang="scss">
  @import url('@/assets/style/orders/deposit-withdrawal.scss');
</style>