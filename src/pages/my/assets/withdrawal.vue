<template>
  <div class="deposit-container">
    <div class="deposit-nav-box" :class="{'hasad': isShowAd * 1 === 1 && !isApp(), 'headNo': isApp(), 'hasadHeadNo': isShowAd * 1 === 1 && isApp()}">
      <el-breadcrumb separator-icon="MonoRightArrowShort">
        <el-breadcrumb-item :to="{ path: `/${locale}/my/assets/wallet` }">{{ $t('钱包账户') }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ $t('提币') }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="deposit-body-cont">
      <el-steps direction="vertical" :active="activeStep">
        <el-step :title="$t('选择提币币种')">
          <template #description>
            <div class="step-content">
              <CoinListSelect v-model="search.symbol" :isWithdrawal="true" />
            </div>
          </template>
        </el-step>
        <el-step :title="$t('输入提币地址和网络')">
          <template #title>
            <div class="address-cont flex-box space-between">
              {{ $t('输入提币地址和网络') }}
              <NuxtLink :to="`/${locale}/my/assets/addresslist`" class="fit-tc-primary flex-box font-size-14">
                {{ $t('地址簿管理') }}
                <MonoRightArrowShort size="14" class="fit-tc-primary mg-l4" />
              </NuxtLink>
            </div>
          </template>
          <template #description>
            <template v-if="activeStep === 1 || activeStep === 2">
              <div class="step-content">
                <div class="address-cont">
                  <template v-if="!isOpenWhite">
                    <div class="font-size-14 fit-tc-primary flex-box space-between mg-t8 mg-b4" style="font-weight:normal;">
                      <span>{{ $t('地址') }}</span>
                    </div>
                    <div class="input-text-box">
                      <el-input v-model="search.url" :placeholder="$t('粘贴地址')" @blur="isInputFocus = false" @focus="isInputFocus = true">
                        <template #append>
                          <div class="address-more-cont flex-box pd-lr8 pd-tb8 cursor-pointer" @click="isShowMenu = !isShowMenu">
                            <MonoAddressBook size="18" />
                          </div>
                        </template>
                      </el-input>
                      <div v-if="isShowMenu" class="list-wrap-dialog" @click="isShowMenu = false"></div>
                      <div v-if="isShowMenu" class="list-select-body small">
                        <div class="select-body-wrap">
                          <div class="select-coin-list-box">
                            <ul v-if="listfilter.length > 0">
                              <li v-for="(item, index) in listfilter" class="noflex" :key="index" @click="changeAddress(item)">
                                <div class="list-p flex-box space-between">
                                  <div class="left flex-box">
                                    <span v-if="item.name">{{ item.name }}</span>
                                    <span class="flex-box verify-icon-text" v-if="item.acl * 1 === 1">{{ $t('免验证') }}</span>
                                  </div>
                                  <div v-if="item.addr_type * 1 === 1" class="white-address-tag">{{ $t('白名单地址') }}</div>
                                </div>
                                <div class="flex-box space-between">
                                  <div>
                                    <p>{{ item.address }}</p>
                                    <p v-if="item.address_tag">{{ item.address_tag }}</p>
                                  </div>
                                </div>
                                <div class="flex-box">
                                  <div class="fit-tc-secondary font-size-14">{{ item.chain_type }}</div>
                                </div>
                              </li>
                            </ul>
                            <div v-if="listfilter.length === 0" style="height:200px;">
                              <BoxNoData :text="$t('暂无数据')" />
                            </div>
                          </div>
                          <div class="bottom-cont-box flex-box space-center">
                            <div class="add-item" @click="addWhiteItemFun()">
                              {{ $t('添加地址') }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <template v-if="websiteItme.address">
                      <p class="info-p flex-box space-between mg-t12">
                        <span>{{ $t('转账网络') }}：</span>
                        <em>{{ websiteItme.chain_type }}</em>
                      </p>
                      <p class="info-p flex-box space-between">
                        <span>{{ $t('备注名称') }}：</span>
                        <em>{{ websiteItme.desc }}</em>
                      </p>
                    </template>
                    <template v-else>
                      <div class="input-text-box mg-t12">
                        <el-input v-model="search.remark" :placeholder="$t('提币备注信息')" />
                      </div>
                      <div class="font-size-14 fit-tc-primary flex-box space-between mg-t8 mg-b4" style="font-weight:normal;">
                        <span>{{ $t('网络') }}</span>
                      </div>
                      <WebsiteSelect :list="websiteList" :isWithdrawal="true" class="mg-tb12" v-model="search.website" :symbol="search.symbol" @changeWebsite="changeWebsite" />
                      <div v-if="websiteItme.issupport_memo * 1 === 1 && search.website !== ''" class="input-text-box mg-t12">
                        <el-input v-model="search.memo" :placeholder="$t('填写MEMO信息')" />
                      </div>
                    </template>
                  </template>
                  <template v-if="isOpenWhite">
                    <div class="font-size-14 fit-tc-primary flex-box space-between mg-t8 mg-b4" style="font-weight:normal;">
                      <span>{{ $t('地址') }}</span>
                    </div>
                    <WebsiteAddressSelect :isOpenWhite="isOpenWhite" :symbol="search.symbol" @changeWebsite="changeWhiteListWebsite" />
                    <template v-if="websiteItme.address">
                      <p class="info-p flex-box space-between mg-t12">
                        <span>{{ $t('转账网络') }}：</span>
                        <em>{{ websiteItme.chain_type }}</em>
                      </p>
                      <p class="info-p flex-box space-between">
                        <span>{{ $t('备注名称') }}：</span>
                        <em>{{ websiteItme.desc }}</em>
                      </p>
                    </template>
                  </template>
                </div>
              </div>
            </template>
          </template>
        </el-step>
        <el-step :title="$t('输入提币金额')">
          <template #description>
            <div v-if="activeStep === 2" class="step-content">
              <div class="address-cont">
                <div class="input-text-box mg-b12">
                  <el-input class="tp-input" v-model="search.amount" :placeholder="`${$t('最小提现额')}${websiteItme.withdraw_min}`">
                    <template #append>
                      <div class="flex-box">
                        <div class="fit-tc-primary mg-r8">{{ search.symbol }}</div>
                        <div class="fit-theme cursor-pointer" @click="allBalance()">{{ $t('全部') }}</div>
                      </div>
                    </template>
                  </el-input>
                </div>
                <p class="info-p flex-box space-between">
                  <span>{{ $t('可提现余额：') }}</span>
                  <em>{{ withdrawBalance }} {{ search.symbol }}</em>
                </p>
                <p class="info-p flex-box space-between">
                  <span>{{ $t('今日可提现剩余额度：') }}</span>
                  <em>{{ limitInfo.limit - limitInfo.used }} {{ search.symbol }}</em>
                </p>
                <p class="info-p flex-box space-between">
                  <span>{{ $t('到账数量：') }}</span>
                  <em>{{ actualAmount }} {{ search.symbol }}</em>
                </p>
                <p class="info-p flex-box space-between">
                  <span>{{ $t('手续费：') }}</span>
                  <em>{{ websiteItme.withdraw_fee }} {{ search.symbol }}</em>
                </p>
                <el-button type="primary" :disabled="isDisabled" class="tb-btn" @click="submitFun()">{{ $t('提币') }}</el-button>
              </div>
            </div>
          </template>
        </el-step>
      </el-steps>
      <div class="record-list-cont">
        <div class="record-title">
          {{ $t('最近提币记录') }}
        </div>
        <el-scrollbar ref="scrollRef" class="table-container mg-t4" :max-height="1000" @scroll="handleScroll">
          <OrderstableBox :moreTxt="moreTxt" :mSortList="mSortList" :headers="headersList" :list="currentList">
            <template #coin_symbol="scope">
              <div class="flex-box space-start">
                <BoxCoinIcon :icon="scope.data.icon_url" class="icon-box mg-r12" style="font-size:30px;" />
                <span>{{ scope.data.coin_symbol }}</span>
              </div>
            </template>
            <template #createdAt="scope">
              <span class="fit-tc-secondary">{{ timeFormat(scope.data.createdAt, 'yyyy-MM-dd hh:mm:ss') }}</span>
            </template>
            <template #updatedAt="scope">
              <span class="fit-tc-secondary">{{ timeFormat(scope.data.updatedAt, 'yyyy-MM-dd hh:mm:ss') }}</span>
            </template>
            <template #to_address="scope">
              <el-tooltip placement="top" :hide-after="0" popper-class="dsj-tooltip">
                <template #content>
                  <div class="dsj-tooltip-box" style="width:240px;">
                    {{ scope.data.to_address }}
                  </div>
                </template>
                <a v-if="scope.data.tx_id" :href="scope.data.url" target="_blank" class="fit-tc-primary">
                  <span class="fit-tc-primary" style="word-break: break-all;text-decoration:underline;">{{ scope.data.to_address }}</span>
                </a>
                <span v-else class="fit-tc-primary" style="cursor:default; word-break: break-all;">{{ scope.data.to_address }}</span>
              </el-tooltip>
              <a v-if="scope.data.tx_id" :href="scope.data.url" target="_blank" class="mg-l8 fit-tc-primary">
                <MonoLinkUrl size="16" style="cursor:pointer;" />
              </a>
              <div class="mg-l8 fit-tc-primary">
                <MonoCopy size="16" style="cursor:pointer;" @click="useCommon.copy(scope.data.to_address, $t('复制成功！'))" />
              </div>
            </template>
            <template #status="scope">
              <span :class="className(scope.data)">{{ scope.data.status * 1 === 3 ? $t(statusMap[scope.data.status].text) : ((scope.data.step * 1 === 1 || scope.data.step * 1 === 2 || scope.data.step * 1 === 3) ? $t('确认中') : (scope.data.step * 1 === 4 ? $t('成功') : '')) }}</span>
            </template>
            <template #acl="scope">
              <el-switch v-model="(switchRow[scope.data.id] || {}).isSwitch" :loading="(switchRow[scope.data.id] || {}).isLoading" @change="changeSwitch(scope.data)"></el-switch>
            </template>
            <template #comment="scope">
              <span class="fit-theme font-size-14 cursor-pointer" @click="router.push(`/${locale}/my/assets/withdrawal-detail?id=${scope.data.id}`)">{{ $t('详情') }}</span>
            </template>
          </OrderstableBox>
        </el-scrollbar>
      </div>
    </div>
  </div>
  <el-dialog v-model="isShowDetail" :title="$t('提币二次确认')" width="480" @close="isShowDetail = false">
    <div class="withdrawal-detail">
      <dl class="flex-box space-between">
        <dt>{{ $t('币种') }}</dt>
        <dd>{{ search.symbol }}</dd>
      </dl>
      <dl class="flex-box space-between">
        <dt>{{ $t('网络') }}</dt>
        <dd>{{ websiteItme.chain_type }}</dd>
      </dl>
      <dl class="flex-box space-between">
        <dt>{{ $t('提币地址') }}</dt>
        <dd>{{ isOpenWhite ? websiteItme.address : search.url }}</dd>
      </dl>
      <dl class="flex-box space-between">
        <dt>{{ $t('MEMO') }}</dt>
        <dd>{{ search.memo ? search.memo : '--' }}</dd>
      </dl>
      <dl class="flex-box space-between">
        <dt>{{ $t('地址备注') }}</dt>
        <dd>{{ search.remark || websiteItme.desc || '--' }}</dd>
      </dl>
      <dl class="flex-box space-between">
        <dt>{{ $t('地址类型') }}</dt>
        <dd>{{ isOpenWhite ? $t('白名单') : $t('普通') }}</dd>
      </dl>
      <dl class="flex-box space-between">
        <dt>{{ $t('账户余额') }}</dt>
        <dd>{{ withdrawBalance }} {{ search.symbol }}</dd>
      </dl>
      <dl class="flex-box space-between">
        <dt>{{ $t('提币数量') }}</dt>
        <dd>{{ search.amount }} {{ search.symbol }}</dd>
      </dl>
      <dl class="flex-box space-between">
        <dt>{{ $t('手续费') }}</dt>
        <dd>{{ websiteItme.withdraw_fee }} {{ search.symbol }}</dd>
      </dl>
      <dl class="flex-box space-between">
        <dt>{{ $t('到账数量') }}</dt>
        <dd>{{ actualAmount }} {{ search.symbol }}</dd>
      </dl>
      <template v-if="!isAddrNoVerify">
        <el-checkbox :disabled="isAddrNoVerify" v-model="isOpenNoVerify">{{ $t('开启提币免验证') }}</el-checkbox>
        <p>{{ $t('* 开启提币免验证后，下次使用该地址提币将免安全验证。') }}</p>
      </template>
      <div v-if="isAddrNoVerify" class="fit-theme font-size-14">* {{ $t('此地址为免验证地址') }}</div>
      <div class="flex-box pd-t24">
        <el-button class="mg-r12" @click="isShowDetail = false">{{ $t('取消') }}</el-button>
        <el-button type="primary" @click="isAddrNoVerify ? transferoutFun() : isShowConfirm = true">{{ $t('确认') }}</el-button>
      </div>
    </div>
  </el-dialog>
  <AccountVerifyCodeDialog
    v-if="isShowVerify"
    :dialogVisible="isShowVerify"
    @request="openWhiteFun"
    @handleClose="isShowVerify = false"
  />
  <withdrawalConfirm
    v-if="isShowNoVerify"
    :dialogVisible="isShowNoVerify"
    @confirm="updateSwitch"
    @close="isShowNoVerify = false"
  />
  <withdrawalConfirm
    v-if="isShowConfirm"
    :dialogVisible="isShowConfirm"
    @close="isShowConfirm = false"
    @confirm="transferoutFun"
  />
  <WhitelistAddDialog v-if="isShowAddWhiteItem" :curSymbol="search.symbol" :dialogVisible="isShowAddWhiteItem" :isOpenWhite="isOpenWhite" @close="isShowAddWhiteItem = false" @success="getWhiteList()" />
  <CommonVerifyIsHasGoogleDialog v-if="isShowVerifyHas" :isShow="isShowVerifyHas" @close="isShowVerifyHas = false" />
  <BoxScript id="gt" ref="gt" append-to-body src="https://res.ktx.com/common/js/gt4.js" />
</template>
<script lang="ts" setup>
  import BigNumber from 'bignumber.js'
  import { cookies } from '~/utils/cookies'
  import { ElScrollbar, ElBreadcrumb, ElBreadcrumbItem, ElSteps, ElSwitch, ElStep, ElTooltip, ElDialog, ElCheckbox, ElPopover } from 'element-plus'
  import CoinListSelect from '~/components/common/CoinListSelect.vue'
  import WebsiteSelect from '~/components/my/assets/WebsiteSelect.vue'
  import MonoCopy from '~/components/common/icon-svg/MonoCopy.vue'
  import WhitelistAddDialog from '~/components/my/assets/addresslist/AddDialog.vue'
  import MonoAddressBook from '~/components/common/icon-svg/MonoAddressBook.vue'
  import MonoLinkUrl from '~/components/common/icon-svg/MonoLinkUrl.vue'
  import OrderstableBox from '~/components/common/OrderstableBox.vue'
  import AccountVerifyCodeDialog from '~/components/common/AccountVerifyCodeDialog.vue'
  import WebsiteAddressSelect from '~/components/my/assets/WebsiteAddressSelect.vue'
  import withdrawalConfirm from '~/components/my/assets/withdrawalConfirm.vue'
  import CommonVerifyIsHasGoogleDialog from '~/components/common/CommonVerifyIsHasGoogleDialog.vue'
  import { getWithdrawAclStatus, changeWithdrawAclStatus, getwithdrawLimitApi, transferTransferOutApi, transferTransferOutList, getAddrAclInfoApi, addrAclChangeStatusApi, getWithdrawAddrApi } from '~/api/tf'
  import { getListByGeneralNameApi } from '~/api/public'
  import { timeFormat } from '~/utils'
  import { useCommonData } from '~/composables/index'
  import { commonStore } from '~/stores/commonStore'
  import { useUserStore } from '~/stores/useUserStore'
  const router = useRouter()
  const useUser = useUserStore()
  const { userInfo } = storeToRefs(useUser)
  const store = commonStore()
  const { getAssetsByCoin } = store
  const { mainAssetObj } = storeToRefs(store)
  const useCommon = useCommonData()
  const { locale, t } = useI18n()
  const isOpenWhite = ref(false)
  const isShowVerify = ref(false)
  const isShowConfirm = ref(false)
  const isShowVerifyHas = ref(false)
  const websiteItme = ref({})
  const websiteList = ref([])
  const limitInfo = ref({})
  const isShowDetail = ref(false)
  const isOpenNoVerify = ref(false)
  const moreTxt = ref('')
  const scrollRef = ref(null)
  const isShowMenu = ref(false)
  const isInputFocus = ref(false)
  const isFinishStep2 = computed(() => {
    const { url, website, memo } = search.value;
    const { issupport_memo, address } = websiteItme.value;
    // 白名单模式直接检查address
    if (isOpenWhite.value) return Boolean(address);
    const isSupportMemo = Number(issupport_memo) === 1;
    const basicCheck = url && (address === '' ? website : true);
    // 支持memo的情况需要额外检查memo字段
    return isSupportMemo 
      ? basicCheck && memo 
      : basicCheck;
  });
  const activeStep = computed(() => {
    console.info(search.value.symbol, isFinishStep2.value, 'isFinishStep2.value')
    let number = 0
    if ((search.value.symbol !== '' && search.value.symbol !== undefined) && !isFinishStep2.value) {
      number = 1
    } else if ((search.value.symbol !== '' && search.value.symbol !== undefined) && isFinishStep2.value) {
      number = 2
    }
    console.info(number, 'isFinishStep2.value')
    return number
  })
  const search = ref({
    remark: '',
    symbol: '',
    website: '',
    url: '',
    memo: '',
    amount: ''
  })
  const withdrawBalance = computed(() => {
    const balance = mainAssetObj.value && mainAssetObj.value[search.value.symbol] && mainAssetObj.value[search.value.symbol].balance || 0
    if (balance < limitInfo.value.limit * 1) {
      return balance
    }
    return limitInfo.value.limit * 1
  })
  const isDisabled = computed(() => {
    return search.value.amount === '' || (search.value.amount !== '' && (search.value.amount * 1 > withdrawBalance.value || search.value.amount * 1 < websiteItme.value.withdraw_min))
  })
  const aboutBalance = computed(() => {
    return new BigNumber(search.value.amount).isGreaterThan(withdrawBalance.value)
  })
  const aboutMin = computed(() => {
    return new BigNumber(websiteItme.value.withdraw_min).isGreaterThan(search.value.amount)
  })
  const allBalance = () => {
    search.value.amount = withdrawBalance.value
  }
  const actualAmount = computed(() => {
    if (!search.value.amount) {
      return 0
    }
    if (aboutBalance.value || aboutMin.value) {
      return 0
    } else {
      const isMoreBalance = new BigNumber(search.value.amount).plus(websiteItme.value.withdraw_fee).isGreaterThan(withdrawBalance.value)
      return Number(new BigNumber(search.value.amount).minus(websiteItme.value.withdraw_fee))
    }
  })
  watch(() => activeStep.value, (val) => {
    if (val < 1) {
      search.value.website = ''
      search.value.url = ''
      search.value.memo = ''
    }
  })
  const headersList = ref([
    { text: t('时间'), key: 'createdAt', align: 'left', wid: '', style: 'auto' },
    { text: t('币种'), key: 'coin_symbol', align: 'left', wid: '',style: 'auto' },
    { text: t('金额'), key: 'amount', align: 'left', wid: '', style: '' },
    { text: t('状态'), key: 'status', align: 'left', wid: '', style: 'auto' },
    { text: t('操作'), key: 'comment', align: 'right', wid: '', style: 'auto' }
  ])
  const mSortList = ref([
    { text: t('币种'), key: 'coin_symbol', align: 'left', wid: '',style: 'auto' },
    { text: t('操作'), key: 'comment', align: 'right', wid: '', style: 'auto' },
    { text: t('状态'), key: 'status', align: 'right', wid: '', style: 'auto' },
    { text: t('金额'), key: 'amount', align: '', wid: '', style: '' },
    { text: t('时间'), key: 'createdAt', align: '', wid: '', style: 'auto' }
  ])
  const currentList = ref([])
  const isLoading = ref(false)
  const statusMap = ref({
    1: {
      text: '确认中',
      class: 'fit-tc-secondary'
    },
    2: {
      text: '成功',
      class: 'fit-rise'
    },
    3: {
      text: '失败',
      class: 'fit-error'
    }
  })
  const className = (row) => {
    if (row.status * 1 === 3) {
      return 'fit-error'
    } else {
      if (row.step * 1 === 1 || row.step * 1 === 2 || row.step * 1 === 3) {
        return 'fit-tc-secondary'
      } else {
        return 'fit-rise'
      }
    }
  }
  const codeLinkFormat = (str) => {
    const maxLength = 40
    if (str.length <= maxLength) {
      return str
    }
    const halfLength = Math.floor(maxLength / 2) - 1
    return str.slice(0, 12) + '......' + str.slice(-12)
  }
  const isAddrNoVerify = ref(false)
  const submitFun = async() => {
    if (!userInfo.value.is_bind_totp) {
      isShowVerifyHas.value = true
      return false
    }
    const { data, error } = await getAddrAclInfoApi({
      addr: isOpenWhite.value ? websiteItme.value.address : search.value.url
    })
    if (data) {
      isOpenNoVerify.value = data.acl * 1 === 1
      isAddrNoVerify.value = data.acl * 1 === 1
      isShowDetail.value = true
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const openWhiteFun = async(code) => {
    const { data, error } = await changeWithdrawAclStatus({
      totp_code: code,
      status: 1
    })
    if (data) {
      isShowVerify.value = false
      isOpenWhite.value = data.status * 1 === 1
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  watch(() => search.value.symbol, (val) => {
    if (val !== '') {
      search.value.url = ''
      search.value.memo = ''
      websiteItme.value = {}
      getSymboleInfo()
      getLimitInfo()
    }
  })
  const changeWebsite = (item) => {
    websiteItme.value = item
  }
  const changeWhiteListWebsite = (item) => {
    websiteList.value.forEach((it) => {
      if (item.coin_symbol === it.symbol) {
        websiteItme.value = { ...Object.assign({}, item, it), desc: item.name }
      }
    })
  }
  const getWhiteListStatus = async() => {
    const { data } = await getWithdrawAclStatus()
    if (data) {
      isOpenWhite.value = data.status * 1 === 1
    }
  }
  watch(() => search.value.url, () => {
    if (isInputFocus.value) {
      websiteItme.value = {}
      search.value.website = ''
      search.value.memo = ''
    }
  })
  const getLimitInfo = async() => {
    const { data } = await getwithdrawLimitApi({
      coin_symbol: search.value.symbol
    })
    limitInfo.value = data
  }
  const getSymboleInfo = async() => {
    const { data } = await getListByGeneralNameApi({
      general_name: search.value.symbol
    })
    if (data) {
      websiteList.value = data.filter((item) => {
        return item.enable_withdraw * 1 === 1
      })
      websiteItme.value = data[0]
    }
  }
  const switchRow = ref({})
  const curAdrItem = ref({})
  const isShowNoVerify = ref(false)
  const changeSwitch = (params) => {
    switchRow.value[params.id] = {
      isSwitch: params.acl * 1 === 1,
      isLoading: true
    }
    curAdrItem.value = params
    isShowNoVerify.value = true
  }
  watch(() => isShowNoVerify.value, (val) => {
    if (!val) {
      currentList.value.forEach((ite) => {
        switchRow.value[ite.id] = {
          isSwitch: ite.acl * 1 === 1,
          isLoading: false
        }
      })
    }
  })
  const updateSwitch = async(params) => {
    const { data, error } = await addrAclChangeStatusApi({
      addr: curAdrItem.value.to_address,
      status: curAdrItem.value.acl * 1 === 1 ? 0 : 1,
      // pwd: params.loginPassword,
      email_code: params.emailCode,
      totp_code: params.code
    })
    if (data) {
      isShowNoVerify.value = false
      getWithdrawalList()
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const pager = ref({
    page: 1,
    size: 100,
    count: 0
  })
  const transferoutFun = async(params) => {
    const { data, error } = await transferTransferOutApi({
      // pwd: params ? params.loginPassword : undefined,
      email_code: params ? params.emailCode : undefined,
      totp_code: params ? params.code : undefined,
      coin_symbol: websiteItme.value.symbol,
      addr: isOpenWhite.value ? websiteItme.value.address : search.value.url,
      addr_remark: search.value.remark,
      memo: search.value.memo,
      amount: search.value.amount,
      allow_addr: isOpenNoVerify.value ? 1 : undefined
    })
    if (data) {
      isShowConfirm.value = false
      isShowDetail.value = false
      search.value = {
        remark: '',
        symbol: '',
        website: '',
        url: '',
        memo: '',
        amount: ''
      }
      pager.value.page = 1
      getWithdrawalList()
      useCommon.showMsg('success', t('请耐心等待，可通过提币记录获取提币进度'))
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const getWithdrawalList = async() => {
    if (pager.value.page  === 1) {
      currentList.value = []
    }
    search.value.symbol = router.currentRoute.value.query.symbol
    isLoading.value = true
    const { data } = await transferTransferOutList({
      page: pager.value.page,
      size: pager.value.size,
      filter_type: 0,
    })
    if (data) {
      pager.value.count = data.count
      currentList.value = [...currentList.value, ...data.items]
      data.items.forEach((ite) => {
        switchRow.value[ite.id] = {
          isSwitch: ite.acl * 1 === 1,
          isLoading: false
        }
      })
      if (pager.value.count > pager.value.page * pager.value.size) {
        moreTxt.value = t('加载中...')
      } else {
        moreTxt.value = t('')
      }
      isLoading.value = false
    }
  }
  const handleScroll = async() => {
    const scrollContainer = scrollRef.value?.$el.querySelector('.el-scrollbar__wrap')
    if (!scrollContainer) return;
    const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
    if (pager.value.count > pager.value.page * pager.value.size  && scrollHeight - (scrollTop + clientHeight) < 50) {
      pager.value.page += 1;
      await getWithdrawalList();
    }
  }
  const list = ref([])
  watch(() => search.value.url, () => {
    if (isInputFocus.value) {
      websiteItme.value.address = ''
    }
  })
  const listfilter = computed(() => {
    return list.value.filter((v) => {
      return v.general_symbol === search.value.symbol && v.addr_type * 1 === 2
    })
  })
  const changeAddress = (item) => {
    isShowMenu.value = false
    search.value.url = item.address
    changeWhiteListWebsite(item)
  }
  const getWhiteList = async() => {
    const { data } = await getWithdrawAddrApi({
      page: 1,
      size:1000
    })
    if (data) {
      list.value = data.rows
    }
  }
  const isShowAddWhiteItem = ref(false)
  const addWhiteItemFun = () => {
    isShowAddWhiteItem.value = true
  }
  const isShowAd = ref(0)
  const requestAnimationFrameInterval = ref(null)
  const socketDateAnimation = () => {
    isShowAd.value = cookies.get('showMessage')
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  }
  onMounted(async() => {
    getWhiteList()
    socketDateAnimation()
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
    await getWithdrawalList()
    getAssetsByCoin()
    getWhiteListStatus()
    const scrollContainer = scrollRef.value?.$el.querySelector('.el-scrollbar__wrap')
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll);
    }
  })
  // 移除滚动事件
  onUnmounted(() => {
    const scrollContainer = scrollRef.value?.$el.querySelector('.el-scrollbar__wrap')
    if (scrollContainer) {
      scrollContainer.removeEventListener('scroll', handleScroll);
    }
  });
</script>
<style lang="scss">
  .withdrawal-detail{
    dl{
      font-size:14px;
      padding-bottom:16px;
      dt{
        @include color(tc-secondary);
      }
      dd{
        @include color(tc-primary);
      }
    }
    p{
      @include color(tc-secondary);
    }
  }
  @import url('@/assets/style/orders/deposit-withdrawal.scss');
</style>
