<template>
  <div class="uc-auth-container">
    <template v-if="step === 1">
      <el-form ref="kycForm1Ref" :model="kycForm1" :rules="kycRules1" class="kyc-form">
        <el-form-item :label="$t('选择国家')" prop="country">
          <el-select v-model="kycForm1.country" :placeholder="$t('请选择国家')" clearable filterable>
            <el-option v-for="(item, index) in countries" :key="index" :label="item[locale] || item.en" :value="item.code">{{ item[locale] || item.en }}</el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('选择证件类型')" prop="type">
          <el-select v-model="kycForm1.type" filterable :disabled="kycForm1.country === ''" clearable>
            <el-option v-for="(item, index) in identityTypeList" v-show="item.show" :key="index + 'identity'" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="kycForm1.country !== 'CN'" :label="$t('姓氏')" prop="first_name">
          <el-input v-model="kycForm1.first_name" />
        </el-form-item>
        <el-form-item :label="kycForm1.country === 'CN' || (kycForm1.type === '' && locale.code === 'zh') ? $t('姓名') : $t('名字')" prop="real_name">
          <el-input v-model="kycForm1.real_name" />
        </el-form-item>
        <el-form-item :label="(kycForm1.type === '' && locale.code === 'zh') || kycForm1.type === 1 ? $t('身份证件号') : $t('证件 ID 号码')" prop="identity">
          <el-input v-model="kycForm1.identity" />
        </el-form-item>
        <!-- <div class="line-box"></div> -->
        <div class="kyc-btn mg-t28 first">
          <el-button type="primary" @click="nextFun(kycForm1Ref)">{{ $t('下一步') }}</el-button>
        </div>
      </el-form>
    </template>
    <template v-if="step === 2">
      <div class="kyc-form-info-container">
        <h2>{{ $t('身份信息') }}</h2>
        <ul class="flex-box">
          <li class="flex-1">
            <p class="ts-14 fit-tc-secondary">{{ $t('国籍') }}</p>
            <h3 class="ts-16 tw-6 fit-tc-primary mg-t8">{{ curCountryName }}</h3>
          </li>
          <li class="flex-1">
            <p class="ts-14 fit-tc-secondary">{{ $t('姓名') }}</p>
            <h3 class="ts-16 tw-6 fit-tc-primary mg-t8">{{ kycForm1.country === 'CN' ? kycForm1.real_name : `${kycForm1.real_name} ${kycForm1.first_name}` }}</h3>
          </li>
          <li class="flex-1">
            <p class="ts-14 fit-tc-secondary">{{ $t('证件号') }}</p>
            <h3 class="ts-16 tw-6 fit-tc-primary mg-t8">{{ kycForm1.identity }}</h3>
          </li>
        </ul>
      </div>
      <div class="step2-info-box">
        <dl class="flex-box align-start">
          <dt class="ts-12 fit-tc-primary" :class="{'enTxt': locale.code !== 'zh' || locale.code !== 'ko'}">{{ kycForm1.type === 1 || (kycForm1.type === '' && locale.code === 'zh') ? $t('身份证人像照') : $t('证件封面') }}:</dt>
          <dd class="ts-12 fit-tc-secondary flex-1">{{ $t('证件需在有效期内') }}</dd>
        </dl>
        <dl class="mg-t16 flex-box align-start">
          <dt class="ts-12 fit-tc-primary" :class="{'enTxt': locale.code !== 'zh' || locale.code !== 'ko'}">{{ kycForm1.type === 1 || (kycForm1.type === '' && locale.code === 'zh') ? $t('手持本人证件信息正面页照片') : $t('手持本人证件信息正面页照片') }}:</dt>
          <dd class="ts-12 fit-tc-secondary flex-1">
            <p v-html="$t('请您上传一张手持护照信息页和个人签字的照片')"></p>
            <ul class="flex-box n-font-p-sty">
                <li class="flex-box">
                  <i></i>
                  {{ $t('人像清晰') }}
                </li>
                <li class="flex-box">
                  <i></i>
                  {{ $t('证件号码清晰') }}
                </li>
                <li class="flex-box">
                  <i></i>
                  {{ $t('手写文字清晰') }}
                </li>
                <li class="flex-box">
                  <i></i>
                  {{ $t('手写文字包含当前信息') }}
                </li>
              </ul>
          </dd>
        </dl>
      </div>
      <el-form ref="kycForm2Ref" :model="kycForm2" :rules="kycRules2" class="kyc-form kyc-step2">
        <el-form-item class="n-font-sty" :label="kycForm1.type === 1 || (kycForm1.type === '' && locale.code === 'zh') ? $t('身份证人像照') : $t('证件封面')" prop="identity_frond_side">
          <div>
            <div class="img-cont-box">
              <img-upload
                class="card-img"
                v-model="kycForm2.identity_frond_side"
                :class-type-driver="kycForm1.country !== 'CN' && kycForm1.type === 3"
                :class-type-passport="kycForm1.type === 2"
                :class-type-en="kycForm1.country !== 'CN' && kycForm1.type === 1"
                :class-type="(kycForm1.type === 1 && kycForm1.country === 'CN') || (kycForm1.type === '' && locale.code === 'zh')"
                :class-name="`card-before`"
              />
              <!-- <div class="sl-box">
                <div class="fixed-dialog n-fixed-fialog-text" @click="showMask = true; stepInfo = 1">
                  <i>{{ $t('查看示例') }}</i>
                </div>
                <div
                  class="img-cont card-before"
                  :class="{'id-card': (kycForm1.type === 1 && kycForm1.country === 'CN') || (kycForm1.type === '' && locale.code === 'zh'), 'id-card-en': kycForm1.country !== 'CN' && kycForm1.type === 1, 'id-card-passport': kycForm1.country !== 'CN' && kycForm1.type === 2, 'id-card-driver': kycForm1.country !== 'CN' && kycForm1.type === 3}"
                >
                </div>
              </div> -->
            </div>
          </div>
        </el-form-item>
        <el-form-item class="n-font-sty" :label="kycForm1.type === 1 || (kycForm1.type === '' && locale.code === 'zh') ? $t('身份证国徽照') : $t('证件个人信息页')" prop="identity_other_side">
          <div class="img-cont-box">
            <img-upload
              class="card-img"
              v-model="kycForm2.identity_other_side"
              :class-type-driver="kycForm1.country !== 'CN' && kycForm1.type === 3"
              :class-type-passport="kycForm1.type === 2"
              :class-type-en="kycForm1.country !== 'CN' && kycForm1.type === 1"
              :class-type="(kycForm1.type === 1 && kycForm1.country === 'CN') || (kycForm1.type === '' && locale.code === 'zh')"
              :class-name="`card-back`"
            />
            <!-- <div class="sl-box">
              <div class="fixed-dialog n-fixed-fialog-text" @click="showMask = true; stepInfo = 2">
                <i>{{ $t('查看示例') }}</i>
              </div>
              <div class="img-cont card-back" :class="{'id-card': (kycForm1.type === 1 && kycForm1.country === 'CN') || (kycForm1.type === '' && locale.code === 'zh'), 'id-card-en': kycForm1.country !== 'CN' && kycForm1.type === 1, 'id-card-passport': kycForm1.country !== 'CN' && kycForm1.type === 2, 'id-card-driver': kycForm1.country !== 'CN' && kycForm1.type === 3}">
              </div>
            </div>-->
          </div>
        </el-form-item>
        <el-form-item class="n-font-lh-sty" :label="kycForm1.type === 1 || (kycForm1.type === '' && locale.code === 'zh') ? $t('手持本人证件信息正面页照片') : $t('手持本人证件信息正面页照片')" prop="identity_in_hand">
          <div>
            <div class="img-cont-box">
              <img-upload
                class="card-img"
                v-model="kycForm2.identity_in_hand"
                :class-type-driver="kycForm1.country !== 'CN' && kycForm1.type === 3"
                :class-type-passport="kycForm1.type === 2"
                :class-type-en="kycForm1.country !== 'CN' && kycForm1.type === 1"
                :class-type="(kycForm1.type === 1 && kycForm1.country === 'CN') || (kycForm1.type === '' && locale.code === 'zh')"
                :class-name="`card-selfie`"
              />
              <!-- <div class="sl-box">
                <div class="fixed-dialog n-fixed-fialog-text" @click="showMask = true; stepInfo = 3">
                  <i>{{ $t('查看示例') }}</i>
                </div>
                <div
                  class="img-cont card-selfie"
                  :class="{'id-card': (kycForm1.type === 1 && kycForm1.country === 'CN') || (kycForm1.type === '' && locale.code === 'zh'), 'id-card-en': kycForm1.country !== 'CN' && kycForm1.type === 1, 'id-card-passport': kycForm1.country !== 'CN' && kycForm1.type === 2, 'id-card-driver': kycForm1.country !== 'CN' && kycForm1.type === 3}"
                >
                </div>
              </div> -->
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div class="line-box"></div>
      <div class="kyc-btn flex-box space-center">
        <el-button type="default" @click="preFun()">{{ $t('上一步') }}</el-button>
        <el-button type="primary"  @click="submitFun(kycForm2Ref)">{{ $t('提交') }}</el-button>
      </div>
    </template>
  </div>
  <BoxScript id="gt" ref="gt" append-to-body src="https://res.ktx.com/common/js/gt4.js" />
</template>
<script lang="ts" setup>
  import { ElButton, ElForm, ElFormItem, ElSelect, ElOption, ElInput, ElDialog } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import { valideIdentitynNum, getAttr } from '~/utils/index'
  import imgUpload from '~/components/my/account/kyc/img-upload'
  import countries from '~/utils/countries'
  import { saveRealName } from '~/api/user'
  import Verify from '~/utils/verify'
  import { useCommonData } from '~/composables/index'
  const useCommon = useCommonData()
  const { locale, t } = useI18n()
  const router = useRouter()
  const props = defineProps({
    user: {
      type: Object,
      default () {
        return {}
      }
    },
    useUser: {
      type: Object,
      default () {
        return {}
      }
    }
  })
  interface KycForm1{
    country: String,
    type: Number,
    first_name: String,
    real_name: String,
    identity: String
  }
  interface KycForm2{
    identity_frond_side: String,
    identity_other_side: String,
    identity_in_hand: String
  }
  const step = ref(1)
  const stepInfo = ref(1)
  const kycForm1Ref = ref<FormInstance>()
  const kycForm2Ref = ref<FormInstance>()
  const kycForm1 = reactive<KycForm1>({
    country: '',
    type: '',
    first_name: '',
    real_name: '',
    identity: ''
  })
  const kycForm2 = reactive<KycForm2>({
    identity_frond_side: '',
    identity_other_side: '',
    identity_in_hand: ''
  })
  const curCountryName = ref('')
  const valideRealName = (rule, value, callback) => {
    if (value === '') {
      const error = kycForm1.country === 'CN' || (kycForm1.type === '' && locale.value === 'zh') ? t('请填写姓名') : t('请填写名字')
      return callback(new Error(error))
    } else {
      return callback()
    }
  }
  const valideIdentity = (rule, value, callback) => {
      if (value === '') {
        const error = kycForm1.country === 'CN' || (kycForm1.type === '' && locale.value === 'zh') ? t('请填写身份证件号') : t('请填写证件 ID 号')
        return callback(new Error(error))
      } if (value && !valideIdentitynNum(value) && kycForm1.type === 1 && kycForm1.country === 'CN') {
        return callback(new Error(t('请填写正确的身份证号码')))
      } else {
        return callback()
      }
    }
    const valideIdentityFrontSide = (rule, value, callback) => {
      if (value === '') {
        const error = kycForm1.type === 1 || (kycForm1.type === '' && locale.code === 'zh') ? t('请上传身份证人像照') : t('请上传证件封面')
        return callback(new Error(error))
      } else {
        return callback()
      }
    }
    const valideIdentityBackSide = (rule, value, callback) => {
      if (value === '') {
        const error = kycForm1.type === 1 || (kycForm1.type === '' && locale.code === 'zh') ? t('请上传身份证国徽照') : t('请上传证件个人信息页')
        return callback(new Error(error))
      } else {
        return callback()
      }
    }
    const valideIdentityInHand = (rule, value, callback) => {
      if (value === '') {
        const error = kycForm1.type === 1 || (kycForm1.type === '' && locale.code === 'zh') ? t('请上传手持本人身份证信息正面页照片') : t('请上传手持本人护照信息正面页照片')
        return callback(new Error(error))
      } else {
        return callback()
      }
    }
  const kycRules1 = reactive<FormRules<KycForm1>>({
    country: [{
      required: true, message: t('请选择国家'), trigger: 'change'
    }],
    type: [{
      required: true, message: t('请选择证件类型'), trigger: 'change'
    }],
    first_name: [{
      required: true, message: t('请填写姓氏'), trigger: 'blur'
    }],
    real_name: [{ validator: valideRealName, trigger: 'blur' }],
    identity: [{ validator: valideIdentity, trigger: 'blur' }]
  })
  const kycRules2 = reactive<FormRules<KycForm2>>({
    identity_frond_side: [{ validator: valideIdentityFrontSide, trigger: 'change' }],
    identity_other_side: [{ validator: valideIdentityBackSide, trigger: 'change' }],
    identity_in_hand: [{ validator: valideIdentityInHand, trigger: 'change' }]
  })
  const selectedCountryInfo = computed(() => {
    return getAttr(countries, kycForm1.country) || {}
  })
  const identityTypeList = computed(() => {
    return [{
        value: 1,
        label: t('身份证'),
        show: selectedCountryInfo.value.isIDCard
      }, {
        value: 2,
        label: t('护照'),
        show: selectedCountryInfo.value.isPassport
      }, {
        value: 3,
        label: t('驾驶证'),
        show: selectedCountryInfo.value.isLicense
    }]
  })
  watch(() => kycForm1.country, (val) => {
    kycForm1.type = ''
    const typeArray = identityTypeList.value.filter((item) => {
      return item.show
    })
    curCountryName.value = Object.values(countries).filter((item) => {
      return item.code === val
    })[0][locale.value === 'zh' ? 'zh' : 'en']
    if (typeArray.length > 0) {
      kycForm1.type = typeArray[0].value
    } else if (typeArray.length === 0 && kycForm1.country !== '') {
      kycForm1.type = ''
    }
  })
  const nextFun = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async(valid, fields) => {
      if (valid) {
        step.value = 2
        stepInfo.value = 1
      } else {
        console.info('error submit!', fields)
      }
    })
  }
  const preFun = () => {
    step.value = 1
    setTimeout(() => {
      kycForm1Ref.value.clearValidate()
    }, 10)
  }
  const verifyCode = ref(null)
  const submitData = async(err, res) => {
    if (err) {
      console.info(err)
      return
    }
    console.info(kycForm1, kycForm2, 'kycForm2')
    if (res.type === 'success' || res.type === 'resend') {
      const { data, error } = await saveRealName({
        ...res.param,
        ...kycForm1,
        ...kycForm2
      })
      if (data) {
        useCommon.showMsg('success', t('提交成功，工作人员将在第一时间进行审核'))
        await props.useUser.getUserInfoAction()
        router.push(`/${locale.value}/my/kyc/result`)
      } else {
        useCommon.showMsg('error', useCommon.err(error.code, error))
      }
    }
  }
  const submitFun = async(formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async(valid, fields) => {
      if (valid) {
        verifyCode.value = new Verify(submitData)
        verifyCode.value.verify()
      } else {
        console.info('error submit!', fields)
      }
    })
  }
  onMounted(() => {
    if (props.user.is_real_name === 3 || props.user.is_real_name === 1) {
      router.replace(`/${locale.value}/my/kyc/result`)
    }
  })
</script>
<style lang="scss">
  @import url('@/assets/style/my/kyc.scss');
  .n-font-sty {
    .el-form-item__label {
      font-size: 12px;
    }
  }
  .n-font-lh-sty {
    .el-form-item__label {
      font-size: 12px;
      line-height: 24px;
      padding-top: 7px;
    }
  }
  .n-font-p-sty {
    font-size: 12px;
  }
  .n-fixed-fialog-text {
    i {
      font-size: 12px !important;
    }
  }
  .kyc_abroad {
    &_qr {
      .qr_code {
        width: 140px;
        height: 140px;
        margin: 24px 0 6px;
      }
    }
    &_info {
      min-width: 520px;
      max-width: 600px;
      ;
      padding: 24px;
      margin-left: 90px;
      [lang="ar"] & {
        margin-left: 0px;
        margin-right: 90px;
      }
    }
  }
</style>