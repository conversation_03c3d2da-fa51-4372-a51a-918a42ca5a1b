<template>
  <div class="result-container">
    <div class="result-wrapper">
      <img v-if="kycData.icon === 'icon-VerifyBlueFailed'" src="~/assets/images/my/kyc/icon-VerifyBlueFailed.png" style="width:98px;" />
      <img v-if="kycData.icon === 'icon-VerifyBlueReviewing'" src="~/assets/images/my/kyc/icon-VerifyBlueReviewing.png" style="width:98px;" />
      <img v-if="kycData.icon === 'icon-VerifyBlueSuccess'" src="~/assets/images/my/kyc/icon-VerifyBlueSuccess.png" style="width:98px;" />
      <div class="result-title ts-20 fit-tc-primary">
        <span>{{ kycData.title }}</span>
      </div>
      <div class="result-info ts-14 fit-tc-primary" :class="{'fit-error': user.is_real_name === 2}">{{ kycData.info }}</div>
      <div v-if="user.is_real_name === 2" class="btn-box" @click="router.push(`/${locale}/my/kyc`)">
        <el-button type="primary">{{ $t('重新认证') }}</el-button>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ElButton } from 'element-plus'
  const router = useRouter()
  const props = defineProps({
    user: {
      type: Object,
      default () {
        return {}
      }
    },
    useUser: {
      type: Object,
      default () {
        return {}
      }
    }
  })
  const { locale, t } = useI18n()
  const loading = ref(true)
  const kycData = computed(() => {
    const isRealName = props.user.is_real_name
    if (isRealName === 1) {
      return {
        icon: 'icon-VerifyBlueReviewing',
        info: t('已上传身份信息, 请等待审核结果'),
        title: `${t('审核中')}…`
      }
    } else if (isRealName === 2) {
      return {
        icon: 'icon-VerifyBlueFailed',
        info: props.user.real_name_deny,
        title: t('认证失败')
      }
    } else if (isRealName === 3) {
      return {
        icon: 'icon-VerifyBlueSuccess',
        info: '',
        title: t('您已通过身份认认证。')
      }
    }
    return {
      icon: '',
      info: '--',
      title: '--'
    }
  })
  watch(() => props.user, (val) => {
    if (JSON.stringify(val) !== '{}') {
      if (!props.user.is_real_name) {
        router.replace(`/${locale.value}/my/kyc`)
      }
    }
  }, {
    immediate: true
  })
  onBeforeMount(() => {
    props.useUser.getUserInfoAction()
  })
</script>
<style lang="scss">
  @import url('@/assets/style/my/kyc.scss');
</style>