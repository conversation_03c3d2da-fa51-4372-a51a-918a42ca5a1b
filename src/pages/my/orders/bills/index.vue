<template>
  <div class="orders-tab-content">
    <div class="orders-nav">
      <ul class="flex-box">
        <li v-for="(item, index) in navArray" :key="index" :class="{'active': navIndex === item.index}" @click="navRequest(item)">
          {{ $t(item.label) }}
        </li>
      </ul>
    </div>
    <div class="orders-search-container">
      <div v-if="!isMobile && isLoading" class="orders-search-wrapper-pc">
        <DropdownSelect v-model="tabIndex" :list="tabArray" label="bill_info_alias" val="id" :labelName="$t('账户')" @change="tabIndexChange" />
        <DropdownSelect v-if="billTypeList.length" v-model="billType" :list="billTypeList"  label="bill_info_alias" val="id" :labelName="thirdName" @change="billTypeChange" />
        <DropdownSelect isSearch v-model="search.symbol" :list="filterCoinList" :labelName="$t('币种')" wid="200" @change="symbolChange">
          <div class="orders-search-box-input">
            <el-input v-model="searchInput" clearable>
              <template #prepend>
                <MonoSearch size="16" />
              </template>
            </el-input>
          </div>
        </DropdownSelect>
        <DataSelect :isReset="isResetDate" @resetFun="isResetDate = false" @change="changeDate" />
        <el-button type="primary" @click="resetFun()">{{ $t('重置') }}</el-button>
      </div>
      <div v-if="isMobile && isLoading" class="orders-search-wrapper-m flex-box space-between isBill">
        <div class="flex-box">
          <div class="select-wrap flex-box mg-r16" :class="{'fit-tc-primary': search.symbol}" @click="isShowCoinList = true">
            {{ search.symbol ? search.symbol : $t('所有交易币') }}
            <MonoDownArrowMin :size="12" class="fit-tc-secondary mg-l4" :class="{'fit-tc-primary': search.symbol}" />
          </div>
          <div class="select-wrap flex-box" :class="{'fit-tc-primary': !(billType * 1 === 32 || billType * 1 === 246)}" @click="isShowBillTypeList = true">
            {{ billType * 1 === 32 || billType * 1 === 246 ? thirdName : billTypeName }}
            <MonoDownArrowMin :size="12" class="fit-tc-secondary mg-l4" :class="{'fit-tc-primary': !(billType * 1 === 32 || billType * 1 === 246)}" />
          </div>
        </div>
        <div class="tag-icon select-icon-m" @click="showParamsDialog"></div>
      </div>
    </div>
  </div>
  <div class="orders-bill-container" :style="isBillLoading || currentList.length === 0 ? 'height:500px;' : 'height:auto'">
    <OrdersBillTable :isLoading="isBillLoading" :currentList="currentList" :tabIndex="tabIndex" :tabMap="tabMap" />
  </div>
  <OrderParmasSelect
    v-if="isShowParamsSelect"
    :isBill="true"
    :isDate="true"
    :defaultSearch="{
      'accountType': tabIndex,
      'type': search.type,
      'start': search.startDate,
      'end': search.endDate
    }"
    :isShowSlectParamsCont="isShowSlectParamsCont"
    :accountTypeList="tabArray"
    @closeDialog="closeParamsDialog"
    @confirmParams="changeParams"
    @cloaseDialog="isShowParamsSelect = false"
    @change="searchInput = ''"
  />
  <SelectDialog
    v-if="isShowCoinList"
    :isShowDialog="isShowCoinList"
    :title="$t('交易币')"
    :isShowInput="true"
    :defaultValue="search.symbol"
    :list="filterCoinList"
    @close="isShowCoinList = false"
    @changeItem="symbolChange"
    >
    <div>
      <el-input v-model="searchInput" clearable>
        <template #prepend>
          <MonoSearch size="16" />
        </template>
      </el-input>
    </div>
  </SelectDialog>
  <SelectDialog
    v-if="isShowBillTypeList"
    :isShowDialog="isShowBillTypeList"
    :title="thirdName"
    :defaultValue="billType"
    :list="billTypeList"
    label="bill_info_alias"
    val="id"
    @close="isShowBillTypeList = false"
    @changeItem="billTypeChange"
  />
  <OrderDateSelect :isShowSlectDateCont="isShowSlectDateCont" :isShowDateSelect="isShowDateSelect" v-if="isShowDateSelect" @confirmChange="changeDate" @closeDialog="closeDateDialog()" />
</template>
<script lang="ts" setup>
  import { cookies } from '~/utils/cookies'
  import { ElInput, ElButton } from 'element-plus'
  import { ORDER_TYPE_SIGN_MAP } from '~/config'
  import OrdersBillTable from '~/components/my/orders/BillTable.vue'
  import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
  import DropdownSelect from '~/components/common/DropdownSelect'
  import DataSelect from '~/components/common/DataSelect.vue'
  import OrderParmasSelect from '~/components/common/OrderParmasSelect.vue'
  import OrderDateSelect from '~/components/common/OrderDateSelect.vue'
  import SelectDialog from '~/components/common/SelectDialog.vue'
  import { timeFormat } from '~/utils'
  import { tarderBillsChange, getTreeNewApi, getBillsDetailsApi } from '~/api/tf'
  import { commonStore } from '~/stores/commonStore'
  const store = commonStore()
  const { getCoinList } = store
  const { coinList } = storeToRefs(store)
  const { locale, t } = useI18n()
  const router = useRouter()
  const searchInput = ref('')
  const isShowDateSelect = ref(false)
  const isShowSlectDateCont = ref(false)
  const isShowParamsSelect = ref(false)
  const isShowSlectParamsCont = ref(false)
  const isResetDate = ref(false)
  const isShowCoinList = ref(false)
  const isShowBillTypeList = ref(false)
  const search = ref({
    symbol: '',
    type: 0,
    startDate: '',
    endDate: ''
  })
  const navArray = ref([
    {
      label: '全部账单',
      index: 0
    },
    {
      label: '收入',
      index: 1
    },
    {
      label: '支出',
      index: 2
    }
  ])
  const navIndex = ref(0)
  const tabIndex = ref('')
  const billType = ref('')
  const tabArray = ref([])
  const navTypeList = ref([])
  const billTypeList = ref([])
  const selectTypeList = ref([])
  const selectType = ref('')
  const thirdName = computed(() => {
    return navTypeList.value[navIndex.value].opt_menu_text
  })
  const isShowAd = ref(0)
  const navRequest = (item) => {
    navIndex.value = item.index
    billTypeList.value = navTypeList.value[navIndex.value] ? navTypeList.value[navIndex.value].children : []
    billType.value = billTypeList.value[0] ? billTypeList.value[0].id : ''
    selectTypeList.value = billTypeList.value[0] && !billTypeList.value[0].children_is_hide && billTypeList.value[0].children ? billTypeList.value[0].children : []
    selectType.value = selectTypeList.value[0] ? selectTypeList.value[0].id : ''
    getBillsList()
  }
  const showParamsDialog = () => {
    isShowParamsSelect.value = true
    setTimeout(() => {
      isShowSlectParamsCont.value = true
    })
  }
  const closeParamsDialog = () => {
    isShowSlectParamsCont.value = false
    setTimeout(() => {
      isShowParamsSelect.value = false
    })
  }
  const showDateDialog = () => {
    isShowDateSelect.value = true
    setTimeout(() => {
      isShowSlectDateCont.value = true
    })
  }
  const closeDateDialog = () => {
    isShowSlectDateCont.value = false
    setTimeout(() => {
      isShowDateSelect.value = false
    })
  }
  const filterCoinList = computed(() => {
    let arr = [{
      value: '',
      label: t('全部')
    }]
    coinList.value.forEach((item) => {
      arr.push({
        value: item.general_name,
        label: item.general_name
      })
    })
    if (searchInput.value !== '') {
      return arr.filter(v => {
        return v.value.includes(searchInput.value.toUpperCase())
      })
    } else {
      return arr
    }
  })
  const isLoading = ref(false)
  const isMobile = ref(false)
  const screenWidth = ref(0)
  const changeRequest = (item) => {
    search.value.pair = item
  }
  const resetFun = () => {
    isResetDate.value = true
    search.value = {
      symbol: ''
    }
  }
  const changeParams = (item) => {
    search.value.type = item.type,
    search.value.startDate = item.start,
    search.value.endDate = item.end
    tabIndexChange(item.accountType)
  }
  const updateScreenWidth = () => {
    screenWidth.value = window.innerWidth
    isMobile.value = screenWidth.value <= 768
  }
  const subtractFromCurrentDate = (type, amount) => {
    const currentDate = new Date()
    let newDate = new Date(currentDate)
    if (type === 'months') {
        // 设置月份时，需要注意JavaScript中的月份是从0开始的
        newDate.setMonth(newDate.getMonth() - amount)
    } else if (type === 'days') {
        newDate.setDate(newDate.getDate() - amount)
    } else if(type === 'years') {
      newDate.setDate(newDate.getYear() - amount)
    } else {
        throw new Error('Invalid type. Expected "months" or "days".')
    }
    return newDate
  }
  const initParams = () => {
    const start = timeFormat(subtractFromCurrentDate('days', 7), 'yyyy-MM-dd')
    const end = timeFormat(new Date(), 'yyyy-MM-dd')
    search.value = {
      symbol: '',
      type: 7,
      startDate: start,
      endDate: end
    }
    getBillsList()
  }
  const isLoadingData = ref(false)
  const currentList = ref([])
  const changeDate = (item) => {
    search.value.startDate = new Date(item.start)
    search.value.endDate = new Date(item.end)
    getBillsList()
  }
  const routerType = computed(() => {
    return router.currentRoute.value.query.type || ''
  })
  const changeBillTypeFun = (item) => {
    billType.value = item.id
  }
  const getTreeNew = async() => {
    const { data, error } = await getTreeNewApi({
      lang: locale.value
    })
    if (data) {
      tabArray.value = data
      tabIndex.value = (routerType.value ==='wallet' || routerType.value === '') ? data[0].id : data[1].id
      navTypeList.value = (routerType.value ==='wallet' || routerType.value === '') ? data[0].children : data[1].children
      billTypeList.value = navTypeList.value[navIndex.value] ? navTypeList.value[navIndex.value].children : []
      billType.value = billTypeList.value[0] ? billTypeList.value[0].id : ''
      selectTypeList.value = billTypeList.value[0] && !!billTypeList.value[0].children_is_hide && billTypeList.value[0].children ? billTypeList.value[0].children : []
      selectType.value = selectTypeList.value[0] ? selectTypeList.value[0].id : ''
      console.info(data, 'getTreeNewApi')
      deepMap(data)
    }
  }
  const tabMap = ref({})
  const deepMap = (arr) => {
    const tabMapP = {}
    for (let i = 0; i < arr.length; i++) {
      const item = arr[i]
      tabMapP[item.id] = recursion(item.children)
    }
    tabMap.value = tabMapP
  }
  const recursion = (arr, obj = {}) => {
    for (let i = 0; i < arr.length; i++) {
      const item = arr[i]
      if (item.bill_type >= 0) {
        obj[item.bill_type] = item.bill_info_comment ? item.bill_info_comment : item.bill_info_alias
      }
      if (item.children && item.children.length) {
        recursion(item.children, obj)
      }
    }
    return obj
  }
  const isBillLoading = ref(true)
  const in_or_out = computed(() => {
    if (navIndex.value === 0) {
      return 0
    } else if (navIndex.value === 1) {
      return 1
    } else {
      return -1
    }
  })
  const getBillsList = async() => {
    isBillLoading.value = true
    const { data, error } = await getBillsDetailsApi({
      account_type: tabIndex.value,
      tree_id: selectType.value || billType.value || tabIndex.value,
      from_time: search.value.startDate,
      to_time: search.value.endDate,
      in_or_out: in_or_out.value,
      symbol: search.value.symbol,
      page: 1,
      size: 1000
    })
    if (data) {
      isBillLoading.value = false
      currentList.value = data.rows
    } else {
      isBillLoading.value = false
    }
  }
  const tabIndexChange = (v) => {
    const list = tabArray.value.filter(item => {
      return item.id === v
    })
    tabIndex.value = v
    navTypeList.value = list[0] ? list[0].children : []
    billTypeList.value = navTypeList.value[navIndex.value] ? navTypeList.value[navIndex.value].children : []
    billType.value = billTypeList.value[0] ? billTypeList.value[0].id : ''
    selectTypeList.value = billTypeList.value[0] && !billTypeList.value[0].children_is_hide && billTypeList.value[0].children ? billTypeList.value[0].children : []
    selectType.value = selectTypeList.value[0] ? selectTypeList.value[0].id : ''
    search.value.symbol = ''
    getBillsList()
  }
  const billTypeName = ref('')
  const billTypeChange = (v) => {
    if (isMobile.value) {
      billTypeName.value = v.bill_info_alias
      billType.value = v.id
    }
    const list = billTypeList.value.filter(item => {
      return item.id === v
    })
   selectTypeList.value = list[0] && !list[0].children_is_hide && list[0].children ? list[0].children : []
   selectType.value =selectTypeList.value[0] ?selectTypeList.value[0].id : ''
   getBillsList()
  }
  const symbolChange = (v) => {
    if (isMobile.value) {
      search.value.symbol = v.value
    }
    searchInput.value = ''
    getBillsList()
  }
  const requestAnimationFrameInterval = ref(null)
  const socketDateAnimation = () => {
    isShowAd.value = cookies.get('showMessage')
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  }
  onMounted(async() => {
    socketDateAnimation()
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
    await getTreeNew()
    getCoinList()
    initParams()
    isLoading.value = true
    updateScreenWidth()
    window.addEventListener('resize', updateScreenWidth)
    await getBillsList()
  })
</script>
<style lang="scss" scoped>
@import url('@/assets/style/orders/index.scss');
</style>