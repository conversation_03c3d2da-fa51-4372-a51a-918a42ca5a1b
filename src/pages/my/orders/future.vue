<template>
  <div class="orders-tab-content">
    <div class="orders-nav">
      <ul class="flex-box">
        <li :class="{'active': curUrl === `/${locale}/my/orders/future/current`}">
          <NuxtLink :to="`/${locale}/my/orders/future/current`">
            {{ $t('当前委托') }}
            <span v-if="curListLen > 0"> · {{ curListLen }}</span>
          </NuxtLink>
        </li>
        <li :class="{'active': curUrl === `/${locale}/my/orders/future/history`}">
          <NuxtLink :to="`/${locale}/my/orders/future/history`">{{ $t('历史委托') }}</NuxtLink>
        </li>
        <li :class="{'active': curUrl === `/${locale}/my/orders/future/deals`}">
          <NuxtLink :to="`/${locale}/my/orders/future/deals`">{{ $t('成交记录') }}</NuxtLink>
        </li>
      </ul>
    </div>
  </div>
  <div class="orders-container-content">
    <NuxtPage @getLen="getLenFun" />
  </div>
</template>
<script lang="ts" setup>
  import { ElInput, ElButton, ElCheckbox } from 'element-plus'
  import { ORDER_TYPE_SIGN_MAP } from '~/config'
  import DropdownSelect from '~/components/common/DropdownSelect'
  import DataSelect from '~/components/common/DataSelect.vue'
  import OrderParmasSelect from '~/components/common/OrderParmasSelect.vue'
  import OrderDateSelect from '~/components/common/OrderDateSelect.vue'
  import { timeFormat } from '~/utils'
  import { commonStore } from '~/stores/commonStore'
  import { useCommonData } from '~/composables/index'
  const useCommon = useCommonData()
  const store = commonStore()
  const { getAllPairList } = store
  const { allPairList } = storeToRefs(store)
  const { locale, t } = useI18n()
  const router = useRouter()
  const nuxtpageRef= ref(null)
  const searchInput = ref('')
  const isShowDateSelect = ref(false)
  const isShowSlectDateCont = ref(false)
  const isShowParamsSelect = ref(false)
  const isShowSlectParamsCont = ref(false)
  const curUrl = computed(() => {
    return router.currentRoute.value.path
  })
  const data = {
    name: 'hhhhhhs'
  }
  const isResetDate = ref(false)
  const showParamsDialog = () => {
    isShowParamsSelect.value = true
    setTimeout(() => {
      isShowSlectParamsCont.value = true
    })
  }
  const closeParamsDialog = () => {
    isShowSlectParamsCont.value = false
    setTimeout(() => {
      isShowParamsSelect.value = false
    })
  }
  const showDateDialog = () => {
    isShowDateSelect.value = true
    setTimeout(() => {
      isShowSlectDateCont.value = true
    })
  }
  const closeDateDialog = () => {
    isShowSlectDateCont.value = false
    setTimeout(() => {
      isShowDateSelect.value = false
    })
  }
  const filterCoinList = computed(() => {
    const arr = useCommon.formatPairList(allPairList.value).filter((v) => {
      return !v.value.includes('_SWAP')
    })
    arr.unshift({
      label: t('全部'), value: ''
    })
    if (searchInput.value !== '') {
      return arr.filter(v => {
        return v.value.includes(searchInput.value.toUpperCase())
      })
    } else {
      return arr
    }
  })
  const isLoading = ref(false)
  const isMobile = ref(false)
  const screenWidth = ref(0)
  const changeRequest = (item) => {
    searchInput.value = ''
  }
  const updateScreenWidth = () => {
    screenWidth.value = window.innerWidth
    isMobile.value = screenWidth.value <= 768
  }
  const subtractFromCurrentDate = (type, amount) => {
    const currentDate = new Date()
    let newDate = new Date(currentDate)
    if (type === 'months') {
        // 设置月份时，需要注意JavaScript中的月份是从0开始的
        newDate.setMonth(newDate.getMonth() - amount)
    } else if (type === 'days') {
        newDate.setDate(newDate.getDate() - amount)
    } else if(type === 'years') {
      newDate.setDate(newDate.getYear() - amount)
    } else {
        throw new Error('Invalid type. Expected "months" or "days".')
    }
    return newDate
  }
  const curListLen = ref(0)
  const getLenFun = (len) => {
    curListLen.value = len
  }
  onMounted(() => {
    isLoading.value = true
    updateScreenWidth()
    window.addEventListener('resize', updateScreenWidth)
    getAllPairList()
  })
</script>
<style lang="scss" scoped>
@import url('@/assets/style/orders/index.scss');
</style>