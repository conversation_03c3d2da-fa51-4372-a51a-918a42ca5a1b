<template>
  <div class="rel-cont">
    <OrdersParams
      :isType="true"
      :isFuture="true"
      :isSide="true"
      :isMode="true"
      @request="requestFun" />
      <el-button v-if="times >= 2" type="primary" class="piliang-btn" @click="isShowCancellOrder = true">{{ $t('批量撤单') }}</el-button>
  </div>
  <div :style="isLoading || currentList.length === 0 ? 'height:500px;' : 'height:auto'">
    <FutureOrdersCurrent
      :isLogin="true"
      :isLoading="isLoading"
      :defaultCurType="0"
      :currentList="currentList"
      @getOrderList="getOrderList(curParmas)"
    />
  </div>
  <BatchOrderDialog
    v-if="isShowCancellOrder"
    :dialogVisible="isShowCancellOrder"
    :times="times"
    :openLongCount="openLongCount"
    :openShortCount="openShortCount"
    :closeLongCount="closeLongCount"
    :closeShortCount="closeShortCount"
    @close="isShowCancellOrder = false"
    @successBatch="getOrderList(curParmas)"
  />
</template>
<script lang="ts" setup>
  import OrdersParams from '~/components/exchange/orders/ordersParams.vue'
  import FutureOrdersCurrent from '~/components/future/orders/current.vue'
  import BatchOrderDialog from '~/components/future/orders/BatchOrderDialog.vue'
  import { getCurrenOrderList }  from '~/api/order'
  const isLoading = ref(false)
  const currentList = ref([])
  const curParmas = ref({})
  const requestFun = (row) => {
    curParmas.value = row
    getOrderList(row)
  }
  const emit = defineEmits(['getLen'])
  const isShowCancellOrder = ref(false)
  const times = ref(0)
  const openLongCount = ref(0)
  const openShortCount = ref(0)
  const closeLongCount = ref(0)
  const closeShortCount = ref(0)
  const getOrderList = async(row) => {
    isLoading.value = true
    const { data } = await getCurrenOrderList({
      status: 'unsettled',
      type: row ? row.type : '',
      side: row ? row.side : '',
      margin_method: row ? row.margin_method : '',
      symbol: row ? row.coin_symbol : '',
      market: 'lpc',
    })
    if (data) {
      let ol = 0
      let os = 0
      let cl = 0
      let cs = 0
      data.forEach((item) => {
        if (!item.close && item.positionMerge === 'short') {
          os++
        } else if (!item.close && item.positionMerge === 'long') {
          ol++
        } else if (item.close && item.positionMerge === 'short') {
          cs++
        } else if (item.close && item.positionMerge === 'long') {
          cl++
        }
      })
      times.value = data.length
      openLongCount.value = ol
      openShortCount.value = os
      closeLongCount.value = cl
      closeShortCount.value = cs
      isLoading.value = false
      currentList.value = data
      emit('getLen', data.length)
    }
  }
  onMounted(() => {
    getOrderList()
  })
</script>