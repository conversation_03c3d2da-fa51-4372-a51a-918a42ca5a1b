<template>
  <OrdersParams
    :isFuture="true"
    :isSide="true"
    :isDeal="true"
    :isDate="true"
    @request="requestFun" />
  <div :style="isLoading || currentList.length === 0 ? 'height:500px;' : 'height:auto'">
    <FutureOrdersDeals 
    :isLogin="true"
    :isLoading="isLoading"
    :currentList="currentList"
    />
   </div>
</template>
<script lang="ts" setup>
  import OrdersParams from '~/components/exchange/orders/ordersParams.vue'
  import FutureOrdersDeals from '~/components/future/orders/deals.vue'
  import { getMyDealList }  from '~/api/order'
  const isLoading = ref(false)
  const currentList = ref([])
  const requestFun = (row) => {
    getOrderList(row)
  }
  const getOrderList = async(row) => {
    isLoading.value = true
    const { data } = await getMyDealList({
      symbol: row ? row.coin_symbol : '',
      market: 'lpc',
      side: row ? row.side : '',
      start_time: new Date(row.startDate + ' 00:00:00').getTime(),
      end_time: new Date(row.endDate + ' 23:59:59').getTime()
    })
    if (data) {
      isLoading.value = false
      currentList.value = data
    }
  }
  onMounted(() => {
    // getOrderList()
  })
</script>