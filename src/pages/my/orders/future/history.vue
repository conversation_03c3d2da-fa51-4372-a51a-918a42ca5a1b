<template>
  <OrdersParams
    :isFuture="true"
    :isMode="true"
    :isSide="true"
    :isType="true"
    :isStatus="true"
    :isDate="true"
    @request="requestFun" />
  <div :style="isLoading || currentList.length === 0 ? 'height:500px;' : 'height:auto'">
    <FutureOrdersHistory 
      :isLogin="true"
      :isLoading="isLoading"
      :currentList="currentList"
      @goDealList="router.push(`/${locale}/my/orders/future/deals`)"
    />
   </div>
</template>
<script lang="ts" setup>
  import OrdersParams from '~/components/exchange/orders/ordersParams.vue'
  import FutureOrdersHistory from '~/components/future/orders/history.vue'
  import { getCurrenOrderList }  from '~/api/order'
  const router = useRouter()
  const { locale, t } = useI18n()
  const isLoading = ref(false)
  const currentList = ref([])
  const requestFun = (row) => {
    getOrderList(row)
  }
  const getOrderList = async(row) => {
    console.log(row.startDate, 'ddhdhueh<PERSON>h<PERSON><PERSON><PERSON><PERSON><PERSON>')
    isLoading.value = true
    const { data } = await getCurrenOrderList({
      status: 'settled',
      symbol: row ? row.coin_symbol : '',
      type: row ? row.type : '',
      side: row ? row.side : '',
      margin_method: row ? row.margin_method : '',
      order_status: row ? row.order_status : '',
      market: 'lpc',
      start_time: new Date(row.startDate + ' 00:00:00').getTime(),
      end_time: new Date(row.endDate + ' 23:59:59').getTime()
    })
    if (data) {
      isLoading.value = false
      currentList.value = data.filter((item) => {
        return Number(item.quantity) * 1 !== 0
      })
    }
  }
</script>