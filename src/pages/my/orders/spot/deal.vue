<template>
  <OrdersParams :isSide="true" :isDate="true" @request="requestFun" />
  <div :style="isLoading || currentList.length === 0 ? 'height:500px;' : 'height:auto'">
    <ExchangeDealOrders
      :list="currentList"
      :isLoading="isLoading"
      :currentList="currentList"
      :isLogin="true"
    />
  </div>
  <!-- <ElPagination v-if="!isMobile"
    class="flex-box space-center order-pagination"
    background
    layout="prev, pager, next"
    center
    :total="1000">
  </ElPagination> -->
</template>
<script lang="ts" setup>
import { ElPagination } from 'element-plus'
import OrdersParams from '~/components/exchange/orders/ordersParams.vue'
import ExchangeDealOrders from '~/components/exchange/orders/DealOrders.vue'
import { getMyDealList }  from '~/api/order'
const props = defineProps({
  isMobile: {
    type: Boolean,
    default: false
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  currenParams: {
    type: Object,
    default () {
      return {}
    }
  }
})
console.info(props.currenParams, 'currenParamscurrenParams')
const currentList = ref([])
const isLoading = ref(false)
const requestFun = (row) => {
  getDealsListFun(row)
}
const getDealsListFun = async(row) => {
  isLoading.value = true
  const { data } = await getMyDealList({
    symbol: row ? row.coin_symbol : '',
    side: row ? row.side : '',
    start_time: new Date(row.startDate + ' 00:00:00').getTime(),
    end_time: new Date(row.endDate + ' 23:59:59').getTime()
  })
  if (data) {
    isLoading.value = false
    currentList.value = data
  } else {
    isLoading.value = false
    currentList.value = []
  }
}
onMounted(() => {
  // getDealsListFun()
})
</script>
<style labg="scss">
.order-pagination {
  padding:20px 0;
}
</style>