<template>
  <OrdersParams :isType="true" :isSide="true" :isStatus="true" :isDate="true" @request="requestFun" />
  <div :style="isLoading || currentList.length === 0 ? 'height:500px;' : 'height:auto'">
    <ExchangeHistoryOrders
      :list="currentList"
      :isLoading="isLoading"
      :currentList="currentList"
      :isLogin="true"
      @goDealList="router.push(`/${locale}/my/orders/spot/deal`)"
    />
   </div>
</template>
<script lang="ts" setup>
import OrdersParams from '~/components/exchange/orders/ordersParams.vue'
import ExchangeHistoryOrders from '~/components/exchange/orders/HistoryOrders.vue'
import { getCurrenOrderList }  from '~/api/order'
const router = useRouter()
const { locale, t } = useI18n()
const props = defineProps({
  currenParams: {
    type: Object,
    default () {
      return {}
    }
  }
})
const currentList = ref([])
const isLoading = ref(false)
const requestFun = (row) => {
  isLoading.value = true
  getOrderList(row)
}
const getOrderList = async(row) => {
  const { data } = await getCurrenOrderList({
    status: 'settled',
    symbol: row ? row.coin_symbol : '',
    type: row ? row.type : '',
    side: row ? row.side : '',
    order_status: row ? row.order_status : '',
    start_time: new Date(row.startDate + ' 00:00:00').getTime(),
    end_time: new Date(row.endDate + ' 23:59:59').getTime()
  })
  if (data) {
    isLoading.value = false
    currentList.value = data
  }
}
onMounted(() => {
  isLoading.value = true
})
</script>
<style labg="scss">
.order-pagination {
  padding:20px 0;
}
</style>