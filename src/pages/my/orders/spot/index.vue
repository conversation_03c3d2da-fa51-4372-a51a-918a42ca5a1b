<template>
  <div class="rel-cont">
    <OrdersParams :isType="true" :isSide="true" @request="requestFun" />
    <el-button v-if="times >= 2" type="primary" class="piliang-btn" @click="isShowCancellOrder = true">{{ $t('批量撤单') }}</el-button>
  </div>
  <div :style="isLoading || currentList.length === 0 ? 'height:500px;' : 'height:auto'">
    <ExchangeCurrentOrders 
      :list="currentList"
      :isLoading="isLoading"
      :currentList="currentList"
      :isLogin="true"
      @getOrderList="getOrderList" />
  </div>
  <BatchOrderDialog
    v-if="isShowCancellOrder"
    :dialogVisible="isShowCancellOrder"
    :times="times"
    :buyTimes="buyTimes"
    :sellTimes="sellTimes"
    @close="isShowCancellOrder = false"
    @successBatch="getOrderList"
  />
</template>
<script lang="ts" setup>
import { ElButton } from 'element-plus'
import OrdersParams from '~/components/exchange/orders/ordersParams.vue'
import ExchangeCurrentOrders from '~/components/exchange/orders/CurrentOrders.vue'
import BatchOrderDialog from '~/components/exchange/orders/BatchOrderDialog.vue'
import { getCurrenOrderList }  from '~/api/order'
const props = defineProps({
  currenParams: {
    type: Object,
    default () {
      return {}
    }
  }
})
const currentList = ref([])
const isShowCancellOrder = ref(false)
const times = ref(0)
const buyTimes = ref(0)
const sellTimes = ref(0)
const isLoading = ref(false)
const requestFun = (row) => {
  isLoading.value = true
  getOrderList(row)
}
const getOrderList = async(row) => {
  const { data } = await getCurrenOrderList({
    status: 'unsettled',
    type: row ? row.type : '',
    side: row ? row.side : '',
    symbol: row ? row.coin_symbol : ''
  })
  if (data) {
    let sell = 0
    let buy = 0
    data.forEach((item) => {
      if (item.side === 'buy') {
        buy++
      } else {
        sell++
      }
    })
    times.value = data.length
    buyTimes.value = buy
    sellTimes.value = sell
    currentList.value = data
    isLoading.value = false
  }
}
watch(() => props.currenParams, (val) => {
  getOrderList()
})
onMounted(() => {
  isLoading.value = true
})
</script>
<style lang="scss">
.order-pagination {
  padding:20px 0;
}
.rel-cont{
  position: relative;
  .piliang-btn{
    position: absolute;
    bottom:14px;
    right:20px;
  }
}
@include mb {
  .rel-cont{
    position: relative;
    .piliang-btn{
      position: absolute;
      bottom:0;
      right:44px;
    }
  }
}

</style>