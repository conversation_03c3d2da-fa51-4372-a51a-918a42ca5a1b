<template>
  <div class="sub-account-api-container">
    <h2 class="font-size-18 fit-tc-primary sub-account-title">{{ $t('子账户API管理') }}</h2>
    <div v-loading="isLoading" class="sub-account-api-wrapper noTop">
      <div v-for="(item, index) in apisList" :key="index" class="api-item-box">
        <div class="api-item-pd">
          <div class="api-title">{{ $t('子账户') }}：{{ item.son_user_id }}</div>
          <dl class="flex-box align-start">
            <dt>
              <BoxQrcode :size="110" :value="item.api_key" />
            </dt>
            <dd>
              <div class="li-item">
                <div class="item-left">Access Key</div>
                <div class="item-right">{{ item.api_key }}</div>
              </div>
              <div class="li-item">
                <div class="item-left">Option</div>
                <div class="item-right">
                  <el-checkbox v-model="item.readInfoChecked" :disabled="editId !== item.id" @change="checkBoxReadInfoStatus($event, item)">{{ $t('读取信息') }}</el-checkbox>
                  <el-checkbox v-model="item.rechargeChecked" :disabled="editId !== item.id"@change="checkBoxRechargeStatus($event, item)">{{ $t('开放交易') }}</el-checkbox>
                </div>
              </div>
              <div class="li-item">
                <div class="item-left">{{ $t('授权信任的IP地址') }}</div>
                <div class="item-right">
                  <template v-if="!item.ipStr && item.id !== editId">
                    <div class="flex-box fit-warn font-size-14">
                       {{ $t('建议您添加可信的 IP 地址访问 API Key') }}
                      <el-button class="fit-theme mg-l16 font-size-14 cursor-pointer" @click="editId = item.id">{{
                        $t('添加')
                      }}</el-button>
                    </div>
                  </template>
                  <div v-if="item.ipStr && item.id !== editId" class="ip-item">{{ item.ipStr }}</div>
                  <template v-if="item.id === editId">
                    <el-input type="text" v-model="item.ipStr" :placeholder="$t('请输入IP地址')" />
                    <p>{{ $t('* 如果使用多个IP, 请使用逗号分隔, 最多添加20个') }}</p>
                  </template>
                </div>
              </div>
            </dd>
          </dl>
          <div class="btn-box flex-box">
            <template v-if="item.id !== editId">
              <el-button @click="deletePre(item)">{{ $t('删除') }}</el-button>
              <el-button type="primary" @click="editId = item.id">{{ $t('编辑') }}</el-button>
            </template>
            <template v-else>
              <el-button @click="cancelItemFun(item)">{{ $t('取消') }}</el-button>
              <el-button type="primary" @click="savePre(item)">{{ $t('保存') }}</el-button>
            </template>
          </div>
        </div>
      </div>
      <div v-if="apisList.length === 0 && !isLoading" style="height:500px;">
        <BoxNoData :text="$t('*API 密钥可以让您借助第三方网站或移动应用使用 KTX 的各类交易服务。')">
        </BoxNoData>
      </div>
    </div>
  </div>
  <CreateApiDialog v-if="isShowCreate" :dialogVisible="isShowCreate" @close="isShowCreate = false" @confirm="createConfirm" />
  <DeleteApiDialog v-if="isShowDelete" :data="currentData" :dialogVisible="isShowDelete" @close="isShowDelete = false" @confirm="deleteConfirm" />
  <AccountVerifyCodeDialog
    v-if="showEditVerify"
    :dialogVisible="showEditVerify"
    @request="saveItemFun"
    @handleClose="showEditVerify = false"
  />
  <CreateApiInfoDialog v-if="isShowAdd" :createdData="createdData" :dialogVisible="isShowAdd" @close="isShowAdd = false" />
</template>
<script lang="ts" setup>
  import { isApp } from '~/utils'
  import { cookies } from '~/utils/cookies'
  import { ElCheckbox, ElInput, ElMessageBox } from 'element-plus'
  import BoxNoData from '~/components/common/BoxNoData.vue'
  import CreateApiDialog from '~/components/my/subAccount/CreateApiDialog.vue'
  import DeleteApiDialog from '~/components/my/subAccount/DeleteApiDialog.vue'
  import { useCommonData } from '~/composables/index'
  import { queryAllSonApiKey, addSonApiKey, deleteSonApiKey, updateSonApiKey } from '~/api/user'
  import AccountVerifyCodeDialog from '~/components/common/AccountVerifyCodeDialog.vue'
  import CreateApiInfoDialog from '~/components/my/subAccount/CreateApiInfoDialog.vue'
  const useCommon = useCommonData()
  const router = useRouter()
  const { locale, t } = useI18n()
  const isShowCreate = ref(false)
  const isShowDelete = ref(false)
  const showEditVerify = ref(false)
  const apisList = ref([])
  const cacheData = ref({})
  const createApiFun = () => {
    isShowCreate.value = true
  }
  const isLoading = ref(true)
  const pager = ref({
    page: 1,
    size: 100
  })
  const pagerTotal = ref(0)
  const moreTxt = ref('')
  const getApiList = async() => {
    const { data, error } = await queryAllSonApiKey(pager.value)
    if (data) {
      data.rows.map((v) => {
        v.readInfo = v.api_flag & 1 ? 1 : 0
        v.readInfoChecked = !!(v.api_flag & 1)
        v.recharge = v.api_flag & 2 ? 1 : 0
        v.rechargeChecked = !!(v.api_flag & 2)
        v.withdrawal = v.api_flag & 4 ? 1 : 0
        v.ipStr = v.allow_ips.join(',') === '0.0.0.0' ? '' : v.allow_ips.join(',')
        return v
      })
      if (pager.value.page * 1 === 1) {
        pagerTotal.value = data.count
        cacheData.value = JSON.parse(JSON.stringify(data.rows))
        apisList.value = data.rows
      } else {
        cacheData.value = [...cacheData.value, ...JSON.parse(JSON.stringify(data.rows))]
        apisList.value = [...apisList.value, ...data.rows]
      }
      if (pagerTotal.value > pager.value.page * pager.value.size) {
        moreTxt.value = t('加载中...')
      } else {
        moreTxt.value = t('-没有更多了哦-')
      }
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
    isLoading.value = false
  }
  const isShowAdd = ref(false)
  const createdData = ref({})
  const createConfirm = async(params) => {
    const { data, error } = await addSonApiKey({
      name: params.name,
      son_id: router.currentRoute.value.params.id,
      allow_ips_array: params.ids,
      allow_ips: params.ids !== '' ? params.ids.split(',') : [],
      code: params.code
    })
    if (data) {
      isShowCreate.value = false
      isShowAdd.value = true
      createdData.value = data
      useCommon.showMsg('success', t('新增成功！'))
      getApiList()
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const editId = ref('')
  const deleteId = ref('')
  const currentData = ref({})
  const checkBoxReadInfoStatus = (value, item) => {
    item.readInfo = value ? 1 : 0
    return value
  }
  const checkBoxRechargeStatus = (value, item) => {
    item.recharge = value ? 1 : 0
    return value
  }
  const deletePre = (item) => {
    ElMessageBox.confirm(t('您确定要删除这条API吗？'), t('删除API'), {
      distinguishCancelAndClose: true,
      confirmButtonText: t('确认'),
      cancelButtonText: t('取消'),
    }).then(() => {
      currentData.value = item
      deleteId.value = item.id
      isShowDelete.value = true
    })
  }
  const cancelItemFun = (item) => {
    editId.value = ''
    apisList.value = cacheData.value.map((v) => {
      v.readInfo = v.api_flag & 1 ? 1 : 0
      v.readInfoChecked = !!(v.api_flag & 1)
      v.recharge = v.api_flag & 2 ? 1 : 0
      v.rechargeChecked = !!(v.api_flag & 2)
      v.withdrawal = v.api_flag & 4 ? 1 : 0
      v.ipStr = v.allow_ips.join(',') === '0.0.0.0' ? '' : v.allow_ips.join(',')
      return v
    })
    getApiList()
  }
  const validatorIds = (value) => {
    // IPv4正则（允许0-255）
    const regIPv4 = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    // IPv6正则（支持完整和缩写格式）
    const regIPv6 = /^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::([0-9a-fA-F]{1,4}:){0,6}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:))$/;
    // 处理输入：替换中文逗号，分割并去除空格
    const ips = value.replace(/，/g, ',').split(',').map(ip => ip.trim());
    // 检查每个IP是否匹配IPv4或IPv6
    const allValid = ips.every(ip => regIPv4.test(ip) || regIPv6.test(ip));
    if (!allValid) {
      return true
    } else {
      return false
    }
  }
  const savePre = (item) => {
    editId.value = item.id
    if (validatorIds(item.ipStr)) {
      useCommon.showMsg('error', t('IP地址输入有误'))
      return false
    }
    currentData.value = item
    showEditVerify.value = true
  }
  const saveItemFun = async(code) => {
    const allowIps = []
    currentData.value.ipStr.replace('，', ',').split(',').forEach(v => {
      if (!allowIps.includes(v)) {
        allowIps.push(v)
      }
    })
    const params = {
      name: currentData.value.name,
      code,
      id: currentData.value.id,
      query: currentData.value.readInfo,
      trade: currentData.value.recharge,
      withdraw: currentData.value.withdrawal,
      allow_ips_g: currentData.value.ipStr,
      allow_ips: allowIps.filter(v => v) || []
    }
    console.info(params, 'paramsparamsparams')
    const { data, error } = await updateSonApiKey(params)
    if (data) {
      editId.value = ''
      useCommon.showMsg('success', t('更新成功！'))
      getApiList()
      showEditVerify.value = false
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const deleteConfirm = async(code) => {
    const {data, error} = await deleteSonApiKey({
      code,
      id: deleteId.value
    })
    if (data) {
      useCommon.showMsg('success', t('删除API成功'))
      isShowDelete.value = false
      getApiList()
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const handleScroll = async() => {
    const scrollTop = window.scrollY || window.pageYOffset || document.documentElement.scrollTop
    const scrollHeight = document.documentElement.scrollHeight
    const clientHeight = window.innerHeight
    if (pagerTotal.value > pager.value.page * pager.value.size  && scrollHeight - (scrollTop + clientHeight) < 50) {
      pager.value.page += 1
      await getApiList();
    }
  }
  const isShowAd = ref(0)
  const requestAnimationFrameInterval = ref(null)
  const socketDateAnimation = () => {
    isShowAd.value = cookies.get('showMessage')
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  }
  onMounted(() => {
    window.addEventListener('scroll', handleScroll)
    getApiList()
  })
  onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll)
  })
</script>
<style lang="scss" scoped>
  @import url('@/assets/style/my/api.scss');
</style>