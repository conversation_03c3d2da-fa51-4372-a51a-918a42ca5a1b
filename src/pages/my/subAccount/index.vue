<template>
  <div class="account-container">
    <div class="account-wrapper">
      <div class="account-title flex-box space-between">
        <div>
          <h2>{{ $t('子账户') }}</h2>
          <p>
            {{ $t('* 创建和管理子账户') }}
            <a :href="`${useCommon.zendeskUrl(locale)}/articles/*************`" target="_blank" class="fit-theme">{{ $t('了解更多>>') }}</a>
          </p>
        </div>
        <div class="flex-box">
          <el-button :disabled="currentList.length >= 20" @click="creatSubAccount(false)">{{ $t('快捷子账户') }}</el-button>
          <el-button type="primary" :disabled="currentList.length >= 20" @click="creatSubAccount(true)">{{ $t('授权子账户') }}</el-button>
        </div>
      </div>
      <div class="account-body-container">
        <div class="account-body-wrapper">
          <div class="subAccount-cont">
            <div class="subAccount-wrap">
              <div class="subAccount-title">
                {{ $t('* 你总共可以创建{minusLen}个子账户，当前已创建{len}个。', { len: currentList.length, minusLen: 20  }) }}
                <a :href="`${useCommon.zendeskUrl(locale)}/articles/*************`" target="_blank" class="fit-theme">{{ $t('了解更多>>') }}</a>
              </div>
              <div style="height:400px;">
                <OrderstableBox :isLoading="isLoadingAccount" :mSortList="mSortList" :headers="headersList" :list="currentList">
                  <template #desc="{ data }">
                    <div class="flex-box sub-edit-input">
                      <template v-if="!(rowID[data.user_id] || {}).block">
                        <div class="cursor-pointer" :class="{'disabled-txt': data.user_id * 1 < 0}" @click="changeUserInfo(data)">
                          {{ data.desc }}
                        </div>
                        <a class="edit-btn cursor-pointer" :class="{'disabled-txt': data.user_id * 1 < 0}" @click.stop="getCurrenRow(data)">{{ $t('编辑') }}</a>
                      </template>
                      <template v-else>
                        <el-input v-model="(rowID[data.user_id] || {}).newDesc" maxlength="6" type="text" class="flex-1">
                        </el-input>
                        <a class="edit-btn cursor-pointer" @click.stop="closeCurrent(data)">{{ $t('取消') }}</a>
                        <a class="edit-btn cursor-pointer" @click.stop="updateDesc(data)">{{ $t('保存') }}</a>
                      </template>
                    </div>
                  </template>
                  <template #type="{ data }">
                    <div>{{ data.name ? $t('可授权') : $t('快捷') }}</div>
                  </template>
                  <template #name="{ data }">
                    <div>{{ data.name || '--' }}</div>
                  </template>
                  <template #is_switch="{ data }">
                    <div class="cursor-pointer fit-theme font-size-14" :class="{'disabled-txt': data.user_id * 1 < 0}" @click.stop="changeUserInfo(data)">{{ $t('切换') }}</div>
                  </template>
                  <template #api="{ data }">
                    <NuxtLink :to="`/${locale}/my/subAccount/apiDetail/${data.user_id}_${data.name || data.desc}`" class="fit-theme font-size-14" :class="{'disabled-txt': data.user_id * 1 < 0}">{{ $t('API管理') }}</NuxtLink>
                  </template>
                  <template #is_lock="{ data }">
                    <span class="cursor-pointer fit-theme" :class="{'disabled-txt': data.user_id * 1 < 0}" @click.stop="showStatusFun(data)">{{ $t(listStatus[data.is_lock * 1] || '') }}</span>
                  </template>
                  <template #user_id="{ data }">
                    <div class="user-id-info-box flex-box">
                      <div class="fit-theme cursor-pointer" :class="{'disabled-txt': data.user_id * 1 < 0}" @click.stop="curClickType = 'assets'; getSonAssetListFun(data)">{{ $t('资产') }}</div>
                      <div class="border-asset"></div>
                      <a class="fit-theme font-size-14 mg-r12 cursor-pointer" :class="{'disabled-txt': data.user_id * 1 < 0}" @click.stop="transferSub(data)">{{ $t('资金划转') }}</a>
                      <el-dropdown v-if="data.name" trigger="hover" popper-class="caozuo-box" @command="(v) => choisePercent(v, data)">
                        <div class="flex-box cursor-pointer fit-theme" :class="{'disabled-txt': data.user_id * 1 < 0}" @click.stop="clickCZfun()">
                          {{ $t('操作') }}
                          <MonoDownArrowMin :size="14" class="fit-theme mg-l4" />
                        </div>
                        <template v-if="data.user_id * 1 >= 0" #dropdown>
                          <el-dropdown-menu :style="locale === 'zh' || locale === 'zh-Hant'? 'width:136px;' : 'width:200px;'">
                            <el-dropdown-item :command="0" :class="{'disabled-txt': data.user_id * 1 < 0}">
                              {{ data.is_bind_totp ? $t('重置谷歌') : $t('重置谷歌') }}
                            </el-dropdown-item>
                            <el-dropdown-item :command="1" :class="{'disabled-txt': data.user_id * 1 < 0}">
                              {{ $t('重置登录密码') }}
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                      <NuxtLink v-else @click.stop="creatSubAccount(true, data)" class="fit-theme font-size-14 cursor-pointer" :class="{'disabled-txt': data.user_id * 1 < 0}">{{ $t('设置') }}</NuxtLink>
                    </div>
                  </template>
                </OrderstableBox>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <subAccountCreateDialog v-if="isShowSonCreate" :isShow="isShowSonCreate" :len="currentList.length" :currentItem="currentItem" :isAuthorize="isAuthorize" @close="isShowSonCreate = false" @request="getSonListFun()" />
  <el-dialog v-if="isShowFreeze" v-model="isShowFreeze" width="360" @close="isShowFreeze = false">
    <div class="freeze-info-box flex-box flex-column space-center align-center">
      <ColorChildAccountBg />
      <h3>{{ currentItem.is_lock ? $t('启用子账户') : $t('禁用子账户') }}</h3>
      <h4>{{ currentItem.user_id }}</h4>
      <p>{{ currentItem.is_lock ? $t('您确定要启用这个子账户') : $t('您确定要禁用这个子账户') }}</p>
      <el-button type="primary" @click="!currentItem.is_lock ? confirm() : showVertify = true">{{ $t('确认') }}</el-button>
    </div>
  </el-dialog>
  <AccountVerifyCodeDialog
    v-if="showVertify"
    :dialogVisible="showVertify"
    @request="confirm"
    @handleClose="showVertify = false"
  />
  <AccountVerifyCodeDialog
    v-if="showSetGoogleVertify"
    :dialogVisible="showSetGoogleVertify"
    @request="getGoogleInfo"
    @handleClose="showSetGoogleVertify = false"
  />
  <GoogleSetting v-if="isShowGoogle" :googleData="googleData" :currentItem="currentItem" :dialogVisible="isShowGoogle" @close="isShowGoogle = false" @confirm="getSonListFun()" />
  <PasswordSetting v-if="isShowSetPwd" :dialogVisible="isShowSetPwd" :currentItem="currentItem" @close="isShowSetPwd = false" @confirm="getSonListFun()" />
  <TransferDialog v-if="isShowTransfer" :visibleDialog="isShowTransfer" :transferType="2" :toUserId="currentItem.user_id" @close="isShowTransfer = false" />
  <ViewAssetsDialog v-if="isShowAssets" :isShow="isShowAssets" :currentItem="currentItem" :curAssetItem="curAssetItem" @close="isShowAssets = false" />
  <CommonVerifyIsHasGoogleDialog v-if="isShowVerify" :isShow="isShowVerify" @close="isShowVerify = false" />
</template>
<script lang="ts" setup>
  import { ElDialog, ElInput, ElSwitch, ElMessageBox, ElDropdownMenu, ElDropdownItem, ElTooltip } from 'element-plus'
  import MonoMoreDot from '~/components/common/icon-svg/MonoMoreDot.vue'
  import OrderstableBox from '~/components/common/OrderstableBox.vue'
  import { getSonList, updateSonDesc, freeze, unfreeze, updateIsSwitch, switchAccount, getSonGoolgKey } from '~/api/user'
  import { useCommonData } from '~/composables/index'
  import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
  import { cookies } from '~/utils'
  import AccountVerifyCodeDialog from '~/components/common/AccountVerifyCodeDialog.vue'
  import ColorChildAccountBg from '~/components/common/icon-svg/ColorChildAccountBg.vue'
  import GoogleSetting from '~/components/my/subAccount/GoogleSetting.vue'
  import PasswordSetting from '~/components/my/subAccount/PasswordSetting.vue'
  import TransferDialog from '~/components/common/TransferDialog.vue'
  import ViewAssetsDialog from '~/components/my/subAccount/ViewAssets.vue'
  import CommonVerifyIsHasGoogleDialog from '~/components/common/CommonVerifyIsHasGoogleDialog.vue'
  import subAccountCreateDialog from '~/components/my/subAccount/CreateSonDialog.vue'
  import { useUserStore } from '~/stores/useUserStore'
  import { getSonAssetsAll } from '~/api/tf.ts'
  import { commonStore } from '~/stores/commonStore'
  const store = commonStore()
  const { getCoinList, getAssetsByCoin } = store
  const { coinList } = storeToRefs(store)
  const useUser = useUserStore()
  const { userInfo } = storeToRefs(useUser)
  const { locale, t } = useI18n()
  const useCommon = useCommonData()
  const router = useRouter()
  const currentList = ref([])
  const isLoadingAccount = ref(true)
  const headersList = ref([
    { text: t('备注'), key: 'desc', align: 'left', wid: 'flex-2', style: 'auto'},
    { text: t('子账户名称'), key: 'name', align: 'left', wid: '', style: 'auto'},
    { text: t('类型'), key: 'type', align: 'center', wid: '', style: 'auto'},
    { text: t('切换'), key: 'is_switch', align: 'center', wid: '', style: 'auto'},
    { text: t('禁用/启用'), key: 'is_lock', align: 'center', wid: '', style: 'auto'},
    { text: t('API'), key: 'api', align: 'center', wid: '', style: 'auto'},
    { text: t('操作'), key: 'user_id', align: 'right', wid: 'flex-2', style: 'auto'}
  ])
  const mSortList = ref([
    { text: t('备注'), key: 'desc', align: 'left', wid: '', style: 'auto'},
    { text: t('类型'), key: 'type', align: 'center', wid: '', style: 'auto'},
    { text: t('子账户名称'), key: 'name', align: 'left', wid: 'flex-2', style: 'auto'},
    { text: t('切换'), key: 'is_switch', align: 'right', wid: '', style: 'auto'},
    { text: t('禁用/启用'), key: 'is_lock', align: '', wid: '', style: 'auto'},
    { text: t('API'), key: 'api', align: 'right', wid: '', style: 'auto'},
    { text: t('操作'), key: 'user_id', align: 'right', wid: 'flex-2', style: 'auto'}
  ])
  const clickCZfun = () => {
    return false
  }
  const goSetFun = (data) => {
    if (data.user_id * 1 < 0) {
      return false
    }
    router.push(`/${locale.value}/my/subAccount/create?len=${currentList.value.length}&desc=${data.name ? data.name : data.desc}&sonId=${data.user_id}`)
  }
  const rowID = reactive({})
  const switchRow = ref({})
  const listStatus = computed(() => {
    return {
      0: t('禁用'),
      1: t('启用')
    }
  })
  const getCurrenRow = (data) => {
    if (data.user_id * 1 < 0) {
      return false
    }
    rowID[data.user_id] = reactive({
      block: true,
      data,
      newDesc: data.desc
    })
  }
  const closeCurrent = (data) => {
    rowID[data.user_id] = {
      block: false,
      data
    }
    getSonListFun()
  }
  const updateDesc = async(params) => {
    if (!(rowID[params.user_id] || {}).newDesc) {
      return
    }
    if ((rowID[params.user_id] || {}).newDesc.length > 6) {
      useCommon.showMsg('error', t('最多6个字符，支持大小写字母、数字，特殊字符'))
      return
    }
    const { data, error } = await updateSonDesc({
      son_id: params.user_id,
      desc: (rowID[params.user_id] || {}).newDesc
    })
    if (data) {
      closeCurrent(params)
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const getSonListFun = async() => {
    const { data, error } = await getSonList()
    console.info(data, error, 'getSonListFun')
    if (data) {
      currentList.value = data
      data.forEach((item) => {
        switchRow.value[item.user_id] = {
          isSwitch: item.is_switch * 1 ? true : false,
          isLoading: false
        }
      })
      isLoadingAccount.value = false
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
      isLoadingAccount.value = false
    }
  }
  const showVertify = ref(false)
  const currentItem = ref({})
  const isShowFreeze = ref(false)
  const showStatusFun = (params) => {
    if (params.user_id * 1 < 0) {
      return false
    }
    currentItem.value = params
    isShowFreeze.value = true
  }
  const confirm = async(code) => {
    const { data, error } = currentItem.value.is_lock ? await unfreeze({
      son_id: currentItem.value.user_id,
      code
    }) : await freeze({
      son_id: currentItem.value.user_id
    })
    if (data) {
      isShowFreeze.value = false
      showVertify.value = false
      getSonListFun()
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const updateSwitch = async(params) => {
    const { data, error } = await updateIsSwitch({
      is_switch: params.is_switch * 1 ? 0 : 1,
      son_id: params.user_id
    })
    if (data) {
      switchRow.value[data.user_id] = {
        isSwitch: data.is_switch * 1 ? true : false,
        isLoading: false
      }
      getSonListFun()
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const changeSwitch = (params) => {
    switchRow.value[params.user_id] = {
      isSwitch: params.is_switch * 1 ? true : false,
      isLoading: true
    }
    if(!params.is_bind_totp) {
      useCommon.showMsg('error', t('请先激活当前子账户登录状态'))
      return false
    }
    updateSwitch(params)
  }
  const changeUserInfo = async(params) => {
    if (params.user_id * 1 < 0) {
      return false
    }
    const { data, error } = await switchAccount({
      user_id: params.user_id
    })
    if (data) {
      if (data.session_id) {
        cookies.set('session_id', data.session_id)
      }
      await useUser.getUserInfoAction()
      useCommon.showMsg('success', t('切换账户成功！'))
      router.replace(`/${locale.value}/my/dashboard`)
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const isShowVerify = ref(false)
  const isShowTransfer = ref(false)
  const isShowGoogle = ref(false)
  const isShowSetPwd = ref(false)
  const showSetGoogleVertify = ref(false)
  const curClickType = ref('')
  const curAssetItem = ref({})
  const isShowAssets = ref(false)
  const getSonAssetListFun = async(row) => {
    if (row.user_id * 1 < 0) {
      return false
    }
    let CoinAssetObj = {}
    let mainAssetObj = {}
    let tradeAssetObj = {}
    currentItem.value = row
    const { data, error } = await getSonAssetsAll({
      son_user_id: row.user_id
    })
    console.info(data, 'dhdheudeu')
    if (data) {
      data.arr.forEach((item) => {
        CoinAssetObj[item.asset] = item
      })
      data.main.forEach((item) => {
        mainAssetObj[item.asset] = item
      })
      data.trade.forEach((item) => {
        tradeAssetObj[item.asset] = item
      })
      curAssetItem.value = {
        'all': data.eq,
        'main': data.mainEq,
        'trade': data.tradeEq,
        'coinAssets': CoinAssetObj,
        'mainAssets': mainAssetObj,
        'tradeAssets': tradeAssetObj
      }
      if (curClickType.value === 'assets') {
        isShowAssets.value = true
      } else if (curClickType.value === 'transfer') {
        isShowTransfer.value = true
      }
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const choisePercent = (key, item) => {
    if (item.user_id * 1 < 0) {
      return false
    }
    currentItem.value = item
    if (key === 0) {
      showSetGoogleVertify.value = true
    } else if (key === 1) {
      isShowSetPwd.value = true
    }
  }
  const transferSub = (item) => {
    if (item.user_id * 1 < 0) {
      return false
    }
    currentItem.value = item
    curClickType.value = 'transfer'
    console.log(item, 'djeidjiejdiejdiejiejie')
    getAssetsByCoin()
    getSonAssetListFun(item)
  }
  const googleData = ref({})
  const getGoogleInfo = async(code) => {
    const { data, error } = await getSonGoolgKey({
      son_id: currentItem.value.user_id,
      code,
    })
    if (data) {
      googleData.value = data
      isShowGoogle.value = true
      showSetGoogleVertify.value = false
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const isShowSonCreate = ref(false)
  const isAuthorize = ref(false)
  const creatSubAccount = (type, row) => {
    if (!userInfo.value.is_bind_totp) {
      isShowVerify.value = true
      return false
    }
    currentItem.value = row
    isAuthorize.value = type
    isShowSonCreate.value = true
  }
  onMounted(() => {
    getCoinList()
    getSonListFun()
  })
</script>
<style lang="scss" scoped>
  @import url('@/assets/style/my/account.scss');
  .disabled-txt{
    cursor:default;
    opacity:0.5;
  }
  .sub-edit-input{
    .edit-btn{
      font-size:14px;
      cursor:pointer;
      margin-left:8px;
      @include color(theme);
      &.disabled-txt{
        cursor:default;
        opacity:0.5;
      }
    }
    .el-input{
    }
  }
  .user-id-info-box{
    .text-info{
      font-size:14px;
      cursor:pointer;
      margin-right:14px;
      @include color(theme);
    }
  }
  .border-asset{
    width:1px;
    height:14px;
    margin:0 8px;
    @include bg-color(border);
  }
  .freeze-info-box{
    svg{
      font-size:58px !important;
    }
    h3{
      padding-top:20px;
      font-size:16px;
      @include color(tc-primary);
    }
    h4{
      font-size:16px;
      padding-top:20px;
      padding-bottom:14px;
      @include color(tc-primary);
    }
    p{
      font-size:14px;
      @include color(tc-secondary);
    }
    .el-button{
      margin-top:20px;
    }
  }
</style>