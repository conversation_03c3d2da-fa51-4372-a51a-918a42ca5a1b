<template>
  <div v-loadding="isLoading" class="my-nft-content">
    <div class="my-nft-asset">{{ $t('我的NFT') }} <span>{{ myAssets.total }}</span></div>
    <div class="my-nft-nav-cont flex-box space-between">
      <div class="nft-nav-left">
        <el-select v-model="curActive" class="select-h5-cont">
          <el-option v-for="(item, index) in Object.keys(myNftInfo)" :key="index" :value="item">
            {{ item }}
          </el-option>
        </el-select>
        <ul class="flex-box">
          <li :class="{'active': item === curActive}" v-for="(item, index) in Object.keys(myNftInfo)" :key="index" @click="curActive = item">{{ item }}</li>
        </ul>
      </div>
      <div class="nft-nav-right flex-box">
        <el-input v-model="search" class="search-input" clearable :placeholder="$t('输入NFT名或空投币名检索锁仓项目')">
          <template #prepend>
            <MonoSearch class="fit-tc-secondary" :size="20" />
          </template>
        </el-input>
        <!-- <el-checkbox v-model="isAllChecked">{{ isAllChecked ? $t('取消全选') : $t('全选') }}</el-checkbox> -->
      </div>
    </div>
    <div v-if="nftList.length > 0" class="nft-check-box-container">
      <el-checkbox-group v-model="checkedList" class="flex-box space-start" @change="handleChange">
        <div v-for="(item, index) in nftList" :key="index" class="check-item">
          <div class="check-item-pd">
            <div class="check-item-title text-center">
              <p>{{ item.name }}</p>
              <p>#{{ item.sequence }}#</p>
            </div>
            <div class="check-item-img">
              <img :src="item.icon_url" />
            </div>
            <!-- <el-checkbox :value="item.id" :key="item.id" :disabled="item.status * 1 === 2">{{ $t('选择') }}</el-checkbox> -->
          </div>
        </div>
      </el-checkbox-group>
    </div>
    <div v-if="nftList.length === 0 && !isLoading" style="height:400px;">
      <BoxNoData :text="$t('暂无数据')" />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ElInput, ElCheckbox, ElCheckboxGroup, ElSelect, ElOption } from 'element-plus'
  import { getNftAssets } from '~/api/tf.ts'
  const emit = defineEmits(['getNftLen'])
  const checkedList = ref([])
  const search = ref('')
  const handleChange = () => {
  }
  const myAssets = ref({})
  const myNftInfo = ref({})
  const curActive = ref('')
  const isLoading = ref(true)
  const nftList = computed(() => {
    const arr = myNftInfo.value[curActive.value] || []
    return arr.filter((item) => {
      return item.name.toLowerCase().includes(search.value.toLowerCase())
    })
  })
  watch(() => curActive.value, (val) => {
    checkedList.value = []
    emit('getNftLen', checkedList.value)
  })
  const isAllChecked = ref(false)
  watch(() => isAllChecked.value, (val) => {
    if (val) {
      checkedList.value = nftList.value.filter((it) => {
        return it.status * 1 !== 2
      }).map(item => item.id)
    } else {
      checkedList.value = []
    }
  })
  watch(() =>checkedList.value, (val) => {
    emit('getNftLen', val)
  })
  const getnftList = async() => {
    isLoading.value = true
    const { data } = await getNftAssets()
    if (data) {
      myAssets.value = data.bal
      myNftInfo.value = data.data
      curActive.value = Object.keys(data.data)[0]
    }
    isLoading.value = false
  }
  onBeforeMount(() => {
    getnftList()
  })
</script>
<style lang="scss">
  .nft-assets-content{
    .my-nft-content{
      .my-nft-asset{
        padding-bottom:8px;
        font-size:14px;
        @include color(tc-secondary);
        span{
          font-size:16px;
          margin-left:4px;
          @include color(tc-primary);
        }
      }
      .my-nft-nav-cont{
        .nft-nav-left{
          padding:8px 0;
          .select-h5-cont{
            display:none;
          }
          ul{
            li{
              font-size:14px;
              padding:10px 20px;
              border-radius:20px;
              cursor:pointer;
              margin-right:12px;
              @include color(tc-secondary);
              @include bg-color(bg-quaternary);
              &.active{
                background:rgba(240, 185, 11, 0.1) !important;
                @include color(theme);
              }
            }
          }
        }
        .nft-nav-right{
          .el-input{
            &.search-input{
              width:300px;
              margin-right:20px;
              @include bg-color(bg-quaternary);
              border-radius:20px;
              .el-input__wrapper{
                border-radius:20px;
              }
            }
          }
          .el-input-group--prepend > .el-input__wrapper{
            border-bottom-left-radius: 20px !important;
            border-top-left-radius: 20px !important;
          }
        }
      }
    }
  }
  .el-dialog{
    &.deposit-withdawl-dialog{
      border-radius:20px !important;
    }
  }
  @include mb{
    .nft-assets-content{
      .my-nft-content{
        .my-nft-nav-cont{
          &.flex-box{
            display:block;
          }
          .nft-nav-left{
            padding:0px 0 8px;
            .select-h5-cont{
              display:block;
            }
            ul{
              display:none;
            }
          }
          .nft-nav-right{
            .el-input{
              &.search-input{
                width:100%;
                margin-right:0px;
                @include bg-color(bg-primary);
                border-radius:4px;
                .el-input__wrapper{
                  border-radius:4px;
                }
              }
            }
            .el-input-group--prepend > .el-input__wrapper{
              border-bottom-left-radius: 8px !important;
              border-top-left-radius: 8px !important;
            }
          }
        }
      }
    }
  }
</style>
<style lang="scss" scoped>
  @import '@/assets/style/nft/assets.scss';
</style>
