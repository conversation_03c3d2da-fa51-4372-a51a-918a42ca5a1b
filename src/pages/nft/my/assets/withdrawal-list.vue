<template>
  <div class="nft-cont-list">
    <WithdrawalList />
  </div>
</template>
<script lang="ts" setup>
  import WithdrawalList from '~/components/nft/my/withdrawal-list'
  const emit = defineEmits(['getNftLen'])
  onBeforeMount(() => {
    emit('getNftLen', [])
  })
</script>
<style lang="scss" scoped>
  .nft-cont-list{
    border:1px solid;
    border-radius:12px;
    height:500px;
    overflow:hidden;
    @include border-color(border);
    margin-bottom:40px;
  }
  @include mb{
    .nft-cont-list{
      border:0;
      border-top:1px solid;
      border-radius:0;
      min-height:calc(100vh - 222px);
      overflow:hidden;
      @include border-color(border);
      margin-bottom:0;
      margin-top:-8px;
    }
  }
</style>
