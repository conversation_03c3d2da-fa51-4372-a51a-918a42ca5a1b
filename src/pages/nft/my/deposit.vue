<template>
  <Header />
  <div class="nft-assets-container">
    <div class="nft-h5-cont flex-box">
      <NuxtLink :to="`/${locale}/nft/my/assets/nft-list`" class="fit-tc-secondary">{{ $t('我的NFT') }}</NuxtLink>
      <MonoRightArrowShort :size="16" class="fit-tc-secondary mg-lr4" />
      <span class="active">{{ $t('充值NFT') }}</span>
    </div>
    <div class="nft-assets-wrapper">
      <NFTDeposit />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import NFTDeposit from '~/components/nft/my/deposit.vue'
  const { locale, t } = useI18n()
</script>
<style lang="scss" scoped>
  @import '@/assets/style/nft/assets.scss';
</style>

