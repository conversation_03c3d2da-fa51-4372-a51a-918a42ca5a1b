<template>
  <Header />
  <div class="nft-assets-container">
    <div class="nft-h5-cont flex-box">
      <NuxtLink :to="`/${locale}/nft/my/assets/nft-list`" class="fit-tc-secondary">{{ $t('我的NFT') }}</NuxtLink>
      <MonoRightArrowShort :size="16" class="fit-tc-secondary mg-lr4" />
      <span class="active">{{ $t('提现NFT') }}</span>
    </div>
    <div class="nft-assets-wrapper">
      <NFTWithdrawal />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import NFTWithdrawal from '~/components/nft/my/withdrawal.vue'
  const { locale, t } = useI18n()
</script>
<style lang="scss" scoped>
  @import '@/assets/style/nft/assets.scss';
</style>
