<template>
  <Header />
  <div class="nodePlan-apply-container">
    <div class="nodePlan-apply-banner-w100">
      <div class="nodePlan-apply-banner">
        <div class="banner-center flex-box flex-column space-center align-start">
          <h1>{{ $t('节点计划') }}</h1>
          <p class="font-size-32 fit-tc-secondary pd-t12">{{ $t('高额收益，快速扩展，全球增长') }}</p>
          <a href="#apply" class="flex-box">
            <el-button type="primary">{{ $t('立即申请') }}</el-button>
          </a>
        </div>
      </div>
    </div>
    <div class="nodePlan-apply-wrapper">
      <div class="apply-wrapper-center">
        <div class="apply-cont1">
          <h3>{{ $t('将您的影响力转化为收入') }}</h3>
          <p>{{ $t('加入我们的节点计划，通过推广 KTX 领先的加密货币交易平台，赚取高达 55% 的佣金！') }}</p>
        </div>
        <div class="apply-cont2">
          <h2>{{ $t('为什么选择与我们合作？') }}</h2>
          <div class="cont2-wrap flex-box flex-wrap align-start">
            <dl>
              <dt>{{ $t('具有竞争力的佣金') }}</dt>
              <dd>
                {{ $t('交易手续费返佣高达') }}<span class="fit-tc-primary">55%</span>
              </dd>
            </dl>
            <dl>
              <dt>{{ $t('实时数据追踪') }}</dt>
              <dd>
                {{ $t('通过直观的后台仪表板，随时监控邀请数据与收入') }}
              </dd>
            </dl>
            <dl>
              <dt>{{ $t('专属支持服务') }}</dt>
              <dd>
                {{ $t('获得一对一支持，助您优化策略') }}
              </dd>
            </dl>
            <dl>
              <dt>{{ $t('独家专属奖励') }}</dt>
              <dd>
                {{ $t('您的受邀用户将解锁新用户专属福利；您也可定制专属推广活动，高效扩展您的社区影响力') }}
              </dd>
            </dl>
          </div>
        </div>
        <div id="apply" class="apply-cont3">
          <h2>{{ $t('立即加入我们') }}</h2>
          <div class="cont3-wrap flex-box align-start space-between">
            <div class="cont3-left">
              <dl>
                <dt class="flex-box flex-column space-between">01</dt>
                <dd>
                  <h3>{{ $t('赚取佣金') }}</h3>
                  <p>{{ $t('所有通过您的链接注册并交易的用户所产生的手续费，您将获得高达55%的返佣！') }}</p>
                </dd>
              </dl>
              <dl>
                <dt class="flex-box flex-column space-between">02</dt>
                <dd>
                  <h3>{{ $t('提交申请') }}</h3>
                  <p>{{ $t('只需几分钟填写申请表，我们将在审核通过后发送您的专属链接。') }}</p>
                </dd>
              </dl>
              <dl>
                <dt class="flex-box flex-column space-between">03</dt>
                <dd>
                  <h3>{{ $t('分享您的链接') }}</h3>
                  <p>{{ $t('通过社交媒体、网站、邮件等渠道分享您的邀请链接。') }}</p>
                </dd>
              </dl>
            </div>
            <div class="cont3-right">
              <div v-if="isLogin" class="form-cont3-box">
                <el-form :model="nodeForm">
                  <el-form-item props="nick_name" :label="$t('称呼')">
                    <el-input v-model="nodeForm.nick_name" :placeholder="$t('请输入您的姓名')" />
                  </el-form-item>
                  <el-form-item props="country" :label="$t('国家')">
                    <el-input v-model="nodeForm.country" :placeholder="$t('请输入您的国家')" />
                  </el-form-item>
                  <el-form-item props="telegram" :label="$t('Telegram')">
                    <el-input v-model="nodeForm.telegram" :placeholder="$t('请输入您的Telegram')" />
                  </el-form-item>
                  <el-form-item props="email" :label="$t('邮箱地址')">
                    <el-input v-model="nodeForm.email" :placeholder="$t('请输入您的邮箱地址')" />
                  </el-form-item>
                  <el-form-item props="media_url" :label="$t('个人社媒地址')">
                    <el-input v-model="nodeForm.media_url" :placeholder="$t('请输入您的个人社媒地址')" />
                  </el-form-item>
                  <el-form-item props="describe" :label="$t('个人简介')">
                    <el-input v-model="nodeForm.describe" type="textarea" :placeholder="$t('请输入您的个人简介')" />
                  </el-form-item>
                </el-form>
                <el-button :disabled="Object.values(nodeForm).some(v => !v)" type="primary" :loading="confirmLoading" @click="submit()">{{ $t('提交') }}</el-button>
              </div>
              <div v-else class="no-login-box flex-box space-center flex-column pd-tb24">
                <div class="font-size-16 fit-tc-primary mg-b32">{{ $t('您还未登录，登录后即可提交') }}</div>
                  <div class="login-btn-box flex-box">
                    <el-button type="primary" @click="useCommon.openLogin()">{{ $t('登录') }}</el-button>
                    <p class="flex-box font-size-14 fit-tc-secondary mg-l8">
                      {{ $t('还没有账号?') }}
                      <a class="fit-theme mg-l4 font-size-14 cursor-pointer" @click="useCommon.openRegister()">{{ $t('注册') }}</a>
                    </p>
                  </div>
              </div>
            </div>
          </div>
        </div>
        <div class="apply-cont4">
          <p>{{ $t('立即加入我们，开启您的节点收益之旅！') }}</p>
          <p>{{ $t('如有任何问题，欢迎联系') }}：<span class="fit-theme"><EMAIL></span></p>
        </div>
      </div>
    </div>
  </div>
  <Footer />
</template>
<script lang="ts" setup>
  import { ElButton, ElForm, ElFormItem, ElInput } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import { useCommonData } from '~/composables/index'
  import { businessNodeApply } from '~/api/tt.ts'
  import { useUserStore } from '~/stores/useUserStore'
  const { locale, t } = useI18n()
  const useCommon = useCommonData()
  const useStore = useUserStore()
  const { isLogin } = storeToRefs(useStore)
  interface nodeForm{
    nick_name: String,
    country: String,
    telegram: String,
    email: String,
    media_url: String,
    describe: String
  }
  const nodeForm = reactive<nodeForm>({
    nick_name: '',
    country: '',
    telegram: '',
    email: '',
    media_url: '',
    describe: ''
  })
  const confirmLoading = ref(false)
  const submit = async() => {
    if (Object.values(nodeForm).some(v => !v)) {
      return false
    }
    confirmLoading.value = true
    const { data, error } = await businessNodeApply(nodeForm)
    if (data) {
      useCommon.showMsg('success', t('提交成功！'))
      confirmLoading.value = false
      nodeForm.nick_name = ''
      nodeForm.country = ''
      nodeForm.telegram = ''
      nodeForm.email = ''
      nodeForm.media_url = ''
      nodeForm.describe = ''
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
      confirmLoading.value = false
    }
  }
</script>
<style lang="scss">
  .nodePlan-apply-container{
    .nodePlan-apply-banner-w100{
      width:100%;
      height:auto;
      @include bg-color(bg-secondary);
      .nodePlan-apply-banner{
        max-width:1920px;
        margin:0 auto;
        height:320px;
        @include get-img('~/assets/images/join-us/banner-bg-light.png', '~/assets/images/join-us/banner-bg-dark.png');
        background-size: 320px auto;
        background-repeat:no-repeat;
        background-position:left center;
        @include bg-color(bg-secondary);
        .banner-center{
          max-width:1920px;
          margin:0 auto;
          height:320px;
          @include get-img('~/assets/images/join-us/apply-banner-img-light.png', '~/assets/images/join-us/apply-banner-img-dark.png');
          background-size: 40% auto;
          background-repeat:no-repeat;
          background-position:95% center;
          h1{
            font-size:60px;
            @include color(tc-primary);
            padding-left:24%;
          }
          p{
            padding-left:24%;
            width:780px;
          }
          a{
            margin-left:24%;
          }
          .el-button{
            height:40px;
            line-height:40px;
            border-radius:18px;
            padding:0 24px;
            margin-top:20px;
          }
        }
      }
    }
    .nodePlan-apply-wrapper{
      width:100%;
      height:auto;
      .apply-wrapper-center{
        margin:0 auto;
        width:90%;
        height:auto;
        .apply-cont1{
          padding:40px 0;
          @include color(tc-primary);
          h3{
            font-size:24px;
          }
          p{
            font-size:16px;
            padding-top:12px;
          }
        }
        .apply-cont2{
          padding:40px 0 0;
          @include color(tc-primary);
          h2{
            text-align:center;
            font-size:40px;
          }
          .cont2-wrap{
            padding-top:40px;
            dl{
              width:50%;
              padding-bottom:40px;
              dt{
                font-size:20px;
              }
              dd{
                padding-top:12px;
                font-size:16px;
                @include color(tc-secondary);
              }
            }
          }
        }
        .apply-cont3{
          padding:40px 0;
          width:100%;
          background: linear-gradient(180deg, rgba(240, 185, 11, 0.08) 0%, rgba(240, 185, 11, 0) 100%);
          h2{
            text-align:center;
            font-size:40px;
            @include color(tc-primary);
          }
          .cont3-wrap{
            padding: 40px 40px 0;
            .cont3-left{
              margin-right:10%;
              dl{
                display:flex;
                height: auto;
                align-items: stretch;
                &:last-child{
                  dt{
                    &:after{
                      display:none;
                    }
                  }
                }
                dt{
                  height:160px;
                  margin-right:20px;
                  font-size:40px;
                  @include color(theme);
                  &:after{
                    content:'';
                    display:block;
                    height:100%;
                    width:2px;
                    @include bg-color(theme);
                  }
                }
                dd{
                  height:160px;
                  h3{
                    font-size:20px;
                    @include color(tc-primary);
                  }
                  p{
                    padding-top:8px;
                    font-size:14px;
                    @include color(tc-secondary);
                  }
                }
              }
            }
            .cont3-right{
              width:480px;
              border-radius:12px;
              @include bg-color(bg-primary);
              .form-cont3-box{
                padding:20px;
                .el-form .el-form-item__label{
                  &:after{
                    content: '*';
                    display:block;
                    margin-top:2px;
                    margin-left:4px;
                    @include color(warn);
                  }
                }
                .el-button{
                  height:44px;
                  width:100%;
                  margin-top:20px;
                }
              }
            }
          }
        }
        .apply-cont4{
          text-align:center;
          padding-top:20px;
          padding-bottom:40px;
          @include color(tc-primary);
          p{
            font-size:20px;
            padding-top:20px;
          }
        }
      }
    }
  }
  @include mb{
    .nodePlan-apply-container{
      .nodePlan-apply-banner-w100{
        max-width:100%;
        .nodePlan-apply-banner{
          max-width:100%;
          width:100%;
          margin:0 auto;
          height:auto;
          background-size: 40% auto;
          background-repeat:no-repeat;
          background-position:left top;
          .banner-center{
            width:100%;
            margin:0 auto;
            height:auto;
            background-size: 70% auto;
            background-repeat:no-repeat;
            background-position:right top;
            padding-top:40%;
            h1{
              padding-left:0;
              font-size:28px;
              text-align:center;
              width:90%;
              margin:0 auto;
              @include color(tc-primary);
            }
            p{
              padding-left:0;
              text-align:center;
              width:90%;
              margin:0 auto;
              padding-bottom:16px;
              &.font-size-32{
                font-size:16px !important;
                line-height:24px !important;
              }
            }
            a{
              margin-left:0;
              margin:0 auto;
            }
            .el-button{
              margin-bottom:28px;
            }
          }
        }
      }
      .nodePlan-apply-wrapper{
        .apply-wrapper-center{
          margin:0 16px;
          width:auto;
          .apply-cont1{
            padding:28px 0;
            @include color(tc-primary);
            h3{
              font-size:20px;
            }
            p{
              font-size:14px;
            }
          }
          .apply-cont2{
            padding:28px 0 12px;
            h2{
              font-size:28px;
            }
            .cont2-wrap{
              padding-top:28px;
              dl{
                width:100%;
                padding-bottom:20px;
                dt{
                  font-size:16px;
                }
                dd{
                  padding-top:8px;
                  font-size:14px;
                }
              }
            }
          }
          .apply-cont3{
            padding:28px 0;
            h2{
              font-size:28px;
            }
            .cont3-wrap{
              padding: 20px 12px 0;
              &.flex-box{
                display:block;
              }
              .cont3-left{
                margin-right:0;
                dl{
                  display:flex;
                  height: auto;
                  align-items: stretch;
                  &:last-child{
                    dt{
                      &:after{
                        display:none;
                      }
                    }
                  }
                  dt{
                    height:110px;
                    margin-right:16px;
                    font-size:32px;
                    &:after{
                      content:'';
                      display:block;
                      height:100%;
                      width:2px;
                      @include bg-color(theme);
                    }
                  }
                  dd{
                    height:110px;
                    h3{
                      font-size:18px;
                    }
                  }
                }
              }
              .cont3-right{
                width:100%;
                .form-cont3-box{
                  padding:16px;
                }
              }
            }
          }
          .apply-cont4{
            padding-top:0px;
            padding-bottom:40px;
            @include color(tc-primary);
            p{
              font-size:14px;
              padding-top:12px;
            }
          }
        }
      }
    }
  }
</style>