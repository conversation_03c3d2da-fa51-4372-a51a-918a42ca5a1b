<template>
  <Header />
  <div class="official-container">
    <div class="official-wrap1">
      <div class="official-center">
        <div class="official-title">{{ $t('KTX官方验证通道') }}</div>
        <div class="official-subtitle">{{ $t('如果您想确认KTX相关域名或信息来源的真实性（例如电话号码，邮件，网络地址，社媒账号等），请在以下输入框进行查询') }}</div>
        <div class="input-box flex-box">
          <el-input v-model="infoText" :placeholder="$t('请输入完整的查询信息')" class="td-input" @keyup.enter.native="queryData" />
          <el-button type="primary" :loading="isBtnLoading" @click="queryData">{{ $t('搜索') }}</el-button>
        </div>
      </div>
    </div>
    <div class="official-wrap2">
      <div class="official-center">
        <div class="official-title">{{ $t('您的反馈对我们至关重要') }}</div>
        <div class="official-subtitle">{{ $t('如果您认为自己在任何网站、邮件、社交媒体或即时通讯类 App 中遇到了欺诈或伪装成 KTX 客服的钓鱼攻击，请立即举报。') }}</div>
        <el-button type="primary" class="jb-btn" @click="isShowJB = true">{{ $t('举报') }}</el-button>
      </div>
    </div>
  </div>
  <el-dialog v-model="isShowTips" width="480" class="tips-show-dialog" @close="knowFun()">
    <div class="tips-cont">
      <div class="tips-icon" :class="isFail ? 'fail-icon' : 'success-icon'"></div>
      <div class="tips-text">{{ isFail ? $t('非官方渠道') : $t('KTX官方渠道') }}</div>
      <div class="info-box mg-t24">{{ infoText }}</div>
      <p class="font-size-14 fit-tc-primary pd-t12">{{ isFail ? $t('您输入的{website}不属于KTX官方渠道信息。', {
        website: infoText
      }) : $t('您输入的{website}属于KTX官方{type}。', {
        website: infoText,
        type: $t(infoType)
      }) }}</p>
      <p class="font-size-14 fit-tc-secondary pd-t32">{{ $t('*请核对您输入的信息是否正确，并避免与非KTX官方渠道进行交互。') }}</p>
      <p v-if="!isFail" class="fit-theme cursor-pointer mg-t8" @click="isShowJB = true">{{ $t('*点击举报钓鱼攻击') }}</p>
      <el-button type="primary" class="mg-t24" @click="knowFun()">{{ $t('我知道了') }}</el-button>
    </div>
  </el-dialog>
  <el-dialog v-if="isShowJB" v-model="isShowJB" width="480" :title="$t('举报')" class="jb-show-dialog">
    <p class="font-size-14 fit-tc-secondary">{{ $t('感谢您为确保平台安全，避免钓鱼威胁而做出的努力。如果您认为自己在某些网站、邮件、社交媒体或即时通讯类 App 中遇到了欺诈活动或伪装成 KTX 客服的钓鱼攻击，请填写下表举报。') }}</p>
    <el-form class="pd-tb12" ref="FormRef" :model="FormInfo" :rules="formRules">
      <el-form-item :label="$t('类型')" prop="type">
        <el-select v-model="FormInfo.type">
          <el-option v-for="(item, index) in typeList" :value="item.value" :label="item.label">
            {{ item.label }}
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="FormInfo.type * 1 === 2" :label="$t('链接')" prop="url">
        <el-input v-model="FormInfo.url" :placeholder="$t('输入链接')" />
      </el-form-item>
      <el-form-item v-if="FormInfo.type * 1 === 3" :label="$t('平台')" prop="platform">
        <el-input v-model="FormInfo.platform" :placeholder="$t('输入平台')" />
      </el-form-item>
      <el-form-item v-if="FormInfo.type * 1 === 3" :label="$t('账号')" prop="account">
        <el-input v-model="FormInfo.account" :placeholder="$t('输入账号')" />
      </el-form-item>
      <el-form-item v-if="FormInfo.type * 1 === 1" :label="$t('来源')" prop="source">
        <el-input v-model="FormInfo.source" :placeholder="$t('输入来源')" />
      </el-form-item>
      <el-form-item :label="$t('说明（选填）')" prop="comment">
        <el-input v-model="FormInfo.comment" type="textarea" :placeholder="$t('其他信息（例如：来源的完整网站地址、钓鱼方式等）')" />
      </el-form-item>
    </el-form>
    <el-button type="primary" :loading="isSubmitLoading" @click="onSubmit(FormRef)">{{ $t('提交') }}</el-button>
    <p class="font-size-14 fit-tc-secondary pd-t8">
      {{ $t('您在举报中提交的信息将按照 KTX') }}
      <a :href="`${useCommon.zendeskUrl(locale)}/articles/*************`" target="_blank" class="fit-theme">{{ $t('使用条款') }}</a>
      {{ $t('和') }}
      <a :href="`${useCommon.zendeskUrl(locale)}/articles/*************`" target="_blank" class="fit-theme">{{ $t('隐私条款') }}</a>
      {{ $t('予以保留') }}
    </p>
  </el-dialog>
  <Footer />
</template>
<script lang="ts" setup>
  import { ElForm, ElFormItem, ElSelect, ElOption, ElInput, ElButton, ElDialog } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import { officialVerification, officialReport } from '~/api/tt'
  import { useCommonData } from '~/composables/index'
  const useCommon = useCommonData()
  const { locale, t } = useI18n()
  const infoText = ref('')
  const isShowTips = ref(false)
  const isShowJB = ref(false)
  const isBtnLoading = ref(false)
  const isSubmitLoading = ref(false)
  const isFail = ref(false)
  const typeList = ref([
    {
      value: '2',
      label: t('钓鱼网站')
    },
    {
      value: '3',
      label: t('社交媒体/即时通讯')
    },
    {
      value: '1',
      label: t('其他')
    }
  ])
  interface FormInfo{
    type: string,
    url: string,
    platform: string,
    account: string,
    source: string,
    comment: string
  }
  const FormRef = ref<FormInstance>()
  const FormInfo = reactive<FormInfo>({
    type: '2',
    url: '',
    platform: '',
    account: '',
    source: '',
    comment: ''
  })
  watch(() => FormInfo.type, (val) => {
    FormInfo.url = ''
    FormInfo.platform = ''
    FormInfo.account = ''
    FormInfo.source = ''
    FormInfo.comment = ''
  })
  const formRules = reactive<FormRules<LoginForm>>({
    type: [{ required: true, message: t('选择类型'), trigger: 'change' }],
    url: [{ required: true, message: t('输入链接'), trigger: 'change' }],
    platform: [{ required: true, message: t('输入平台'), trigger: 'change' }],
    account: [{ required: true, message: t('输入账号'), trigger: 'change' }],
    source: [{ required: true, message: t('输入来源'), trigger: 'change' }]
  })
  const onSubmit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async(valid, fields) => {
      if (valid) {
        await submitData()
      } else {
        console.info('error submit!', fields)
      }
    })
  }
  const submitData = async() => {
    isSubmitLoading.value = true
    const { data } = await officialReport(FormInfo)
    if (data) {
      useCommon.showMsg('success', t('提交成功！'))
      isShowJB.value = false
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
    isSubmitLoading.value = false
  }
  const infoType = ref('')
  const infoObj = ref({
    0: '微信',
    1: '电话',
    2: 'Telegram',
    3: '邮箱',
    4: 'Facebook',
    5: 'Twitter',
    6: 'QQ',
    7: '工作人员',
    8: '网站',
    9: '国内域名',
    10: '国外域名',
    11: 'LinkedIn'
  })
  const queryData = async() => {
    if (infoText.value === '') {
      useCommon.showMsg('error', t('请输入完整的查询信息'))
      return false
    }
    isBtnLoading.value = true
    const { data, error } = await officialVerification({
      account: infoText.value
    })
    if (data) {
      isFail.value = data.is_official * 1 === 0
      infoType.value = data.is_official * 1 === 0 ? '' : infoObj.value[data.type]
      isShowTips.value = true
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
    isBtnLoading.value = false
  }
  const knowFun = () => {
    infoText.value = ''
    isShowTips.value = false
  }
</script>
<style lang="scss">
  .official-container{
    .official-wrap1{
      @include bg-color(bg-secondary);
    }
    .official-center{
      max-width:900px;
      padding:60px 0;
      margin:0 auto;
      text-align:center;
      .official-title{
        font-size:40px;
        @include color(tc-primary);
      }
      .official-subtitle{
        font-size:16px;
        font-weight:400;
        margin-top:20px;
        @include color(tc-primary);
      }
      .input-box{
        width:600px;
        height:60px;
        margin:0 auto;
        border-radius:8px;
        padding:0 16px 0 8px;
        margin-top:60px;
        @include bg-color(bg-primary);
        .el-input{
          &.td-input{
            border:0;
            box-shadow:none;
            .el-input__wrapper{
              border:0;
              box-shadow:none;
            }
            .el-input__inner{
              border:0;
              box-shadow:none;
            }
          }
        }
        .el-button{
          margin-left:8px;
        }
      }
      .jb-btn{
        margin-top:60px;
        width:160px;
        height:40px;
      }
    }
  }
  .el-dialog{
    &.tips-show-dialog{
      .tips-cont{
        .tips-icon{
          width:70px;
          height:70px;
          margin:0 auto;
          &.success-icon{
            background:url('~/assets/images/about-us/success.png') no-repeat center;
            background-size:100% auto;
          }
          &.fail-icon{
            background:url('~/assets/images/about-us/fail.png') no-repeat center;
            background-size:100% auto;
          }
        }
        .tips-text{
          font-size:20px;
          margin-top:24px;
          text-align:center;
        }
        .info-box{
          padding:0 8px;
          height:36px;
          line-height:36px;
          font-size:14px;
          border-radius:4px;
          @include color(tc-primary);
          @include bg-color(bg-secondary);
        }
      }
    }
  }
  @include mb {
    .official-container{
      .official-center{
        padding:60px 0;
        .official-title{
          font-size:28px;
        }
        .official-subtitle{
          padding:0 16px;
          font-size:14px;
          margin-top:16px;
        }
        .input-box{
          width:auto;
          margin:0 16px;
          border-radius:8px;
          margin-top:32px;
        }
        .jb-btn{
          width:90%;
          height:40px;
          margin:0 auto;
          margin-top:32px;
        }
      }
    }
    .el-dialog{
      &.tips-show-dialog{
        .tips-cont{
          .tips-icon{
            width:70px;
            height:70px;
            margin:0 auto;
            &.success-icon{
              background:url('~/assets/images/about-us/success.png') no-repeat center;
              background-size:100% auto;
            }
            &.fail-icon{
              background:url('~/assets/images/about-us/fail.png') no-repeat center;
              background-size:100% auto;
            }
          }
          .tips-text{
            font-size:20px;
            margin-top:24px;
            text-align:center;
          }
          .info-box{
            padding:0 8px;
            height:36px;
            line-height:36px;
            font-size:14px;
            border-radius:4px;
            @include color(tc-primary);
            @include bg-color(bg-secondary);
          }
        }
      }
    }
  }
</style>