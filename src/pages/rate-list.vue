<template>
  <div class="rate-list-container">
    <Header />
    <div class="rate-list-wrapper">
      <div v-if="isLogin" class="rate-list-title">{{ $t('您当前费率和等级') }}</div>
      <div v-if="isLogin" class="rate-info-box mg-bauto">
        <RateInfo is-rate-list />
      </div>
      <div class="rate-list-title last">{{ $t('费率和等级') }}</div>
      <div class="level-table-box">
        <div class="level-table-wrap">
          <div class="level-tab">
            <ul class="flex-box">
              <li :class="{'active': type === 1}" @click.prevent="scrollToBottom(1, $event)">
                {{ $t('现货交易') }}
              </li>
              <li :class="{'active': type === 2}" @click.prevent="scrollToBottom(2, $event)">
                {{ $t('合约交易') }}
              </li>
            </ul>
          </div>
          <el-table class="level-table-box-tb" v-show="type * 1 === 1" :data="spotList" :row-class-name="({ row }) => ({ 'active': isLogin && row.level === userInfo.user_level })">
            <el-table-column :label="$t('等级')" :align="isLoading && isMobile ? 'left' : 'center'">
              <template #default="scope">
                {{ scope.row.level * 1 === 0 ? $t('VIP 0') : `VIP ${scope.row.level}` }}
              </template>
            </el-table-column>
            <el-table-column :label="`${$t('近30日交易量')}(USDT)`" align="center" prop="tradeAmount" width="120" />
            <el-table-column :label="$t('且/或')" align="center" prop="and" width="100" />
            <el-table-column :label="`${$t('总资产')}(USDT)`" align="center" prop="assetAmount" width="120" />
            <el-table-column :label="$t('Maker')" align="center" prop="maker" />
            <el-table-column :label="$t('Taker')" :align="isLoading && isMobile ? 'right' : 'center'" prop="taker" />
          </el-table>
          <el-table class="level-table-box-tb" v-show="type * 1 === 2" :data="contractList" :row-class-name="({ row }) => ({ 'active': isLogin && row.level === userInfo.contract_level })">
            <el-table-column :label="$t('等级')" :align="isLoading && isMobile ? 'left' : 'center'">
              <template #default="scope">
                {{ scope.row.level * 1 === 0 ? $t('VIP 0') : `VIP ${scope.row.level}` }}
              </template>
            </el-table-column>
            <el-table-column :label="`${$t('近30日交易量')}(USDT)`" align="center" prop="tradeAmount" width="120" />
            <el-table-column :label="$t('Maker')" align="center" prop="maker" />
            <el-table-column :label="$t('Taker')" align="center" prop="taker" :align="isLoading && isMobile ? 'right' : 'center'" />
          </el-table>
          <div id="bottom"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ElTable, ElTableColumn } from 'element-plus'
  import RateInfo from '~/components/my/account/RateInfo.vue'
  import { useUserStore } from '~/stores/useUserStore'
  const store = useUserStore()
  const { userInfo, isLogin } = storeToRefs(store)
  const { locale, t } = useI18n()
  const type = ref(1)
  const spotList = ref([
    {
      level: 0,
      tradeAmount: '<500,000',
      assetAmount: '<5,000',
      and: t('或'),
      maker: '0.100%',
      taker: '0.200%'
    },
    {
      level: 1,
      tradeAmount: '≥500,000',
      assetAmount: '≥5,000',
      and: t('或'),
      maker: '0.090%',
      taker: '0.200%'
    },
    {
      level: 2,
      tradeAmount: '≥1,000,000',
      assetAmount: '≥10,000',
      and: t('或'),
      maker: '0.080%',
      taker: '0.150%'
    },
    {
      level: 3,
      tradeAmount: '≥3,000,000',
      assetAmount: '≥30,000',
      and: t('或'),
      maker: '0.065%',
      taker: '0.100%'
    },
    {
      level: 4,
      tradeAmount: '≥8,000,000',
      assetAmount: '≥100,000',
      and: t('或'),
      maker: '0.050%',
      taker: '0.095%'
    },
    {
      level: 5,
      tradeAmount: '≥15,000,000',
      assetAmount: '≥300,000',
      and: t('或'),
      maker: '0.040%',
      taker: '0.080%'
    },
    {
      level: 6,
      tradeAmount: '≥30,000,000',
      assetAmount: '≥1,000,000',
      and: t('或'),
      maker: '0.030%',
      taker: '0.070%'
    },
    {
      level: 7,
      tradeAmount: '≥50,000,000',
      assetAmount: '≥2,000,000',
      and: t('或'),
      maker: '0.025%',
      taker: '0.060%'
    },
    {
      level: 8,
      tradeAmount: '≥100,000,000',
      assetAmount: '≥5,000,000',
      and: t('或'),
      maker: '0.020%',
      taker: '0.050%'
    }
  ])
  const contractList = ref([
    {
      level: 0,
      tradeAmount: '<5,000,000',
      maker: '0.040%',
      taker: '0.060%'
    },
    {
      level: 1,
      tradeAmount: '≥5,000,000',
      maker: '0.020%',
      taker: '0.055%'
    },
    {
      level: 2,
      tradeAmount: '≥20,000,000',
      maker: '0.018%',
      taker: '0.045%'
    },
    {
      level: 3,
      tradeAmount: '≥50,000,000',
      maker: '0.015%',
      taker: '0.040%'
    },
    {
      level: 4,
      tradeAmount: '≥100,000,000',
      maker: '0.012%',
      taker: '0.035%'
    },
    {
      level: 5,
      tradeAmount: '≥200,000,000',
      maker: '0.008%',
      taker: '0.032%'
    },
    {
      level: 6,
      tradeAmount: '≥500,000,000',
      maker: '0.005%',
      taker: '0.030%'
    },
    {
      level: 7,
      tradeAmount: '≥1000,000,000',
      maker: '0.000%',
      taker: '0.028%'
    },
    {
      level: 8,
      tradeAmount: '≥5000,000,000',
      maker: '-0.002%',
      taker: '0.026%'
    }
  ])
  const screenWidth = ref(0)
  const isMobile = ref(false)
  const isLoading = ref(false)
  const updateScreenWidth = () => {
    screenWidth.value = window.innerWidth
    isMobile.value = screenWidth.value <= 1024
  }
  const scrollToBottom = (num, event) => {
    event.preventDefault(); // 阻止默认行为
    event.stopPropagation(); // 阻止事件冒泡
    type.value = num
    // 手动控制滚动位置
    const currentScrollPosition = window.scrollY;
    window.scrollTo(0, currentScrollPosition);
  }
  onMounted(() => {
    isLoading.value = true
    updateScreenWidth()
    window.addEventListener('resize', updateScreenWidth)
  })
</script>
<style lang="scss" scoped>
.rate-list-container{
  .rate-list-wrapper{
    padding:0 20px 40px;
    .rate-list-title{
      font-size:20px;
      line-height:28px;
      padding-top:20px;
      @include color(tc-primary);
    }
    .rate-info-box{
      &.mg-bauto{
        margin-bottom:-40px;
      }
    }
    .level-table-box{
      margin-top:20px;
      border:1px solid;
      border-radius:12px;
      @include border-color(border);
      .level-table-wrap{
        .level-tab{
          ul{
            height:60px;
            line-height:60px;
            border-bottom:1px;
            padding:0 24px;
            @include border-color(border);
            li{
              height:58px;
              line-height:58px;
              margin-right:24px;
              position:relative;
              font-size:16px;
              cursor:pointer;
              @include color(tc-secondary);
              &.active{
                @include color(tc-primary);
                &:after{
                  position:absolute;
                  content: '';
                  display:block;
                  width:14px;
                  height:2px;
                  left:50%;
                  margin-left:-7px;
                  @include bg-color(theme);
                }
              }
            }
          }
        }
      }
    }
  }
}
@include mb{
  .rate-list-container{
    .rate-list-wrapper{
      padding:0 16px 40px;
      .rate-list-title{
        font-size:16px;
        line-height:46px;
        padding-top:0;
        &.last{
          display:none;
        }
      }
      .rate-info-box{
        &.mg-bauto{
          margin-top:-20px;
          margin-bottom:-40px;
        }
      }
      .level-table-box{
        margin-top:20px;
        border:0;
        border-radius:0;
        .level-table-wrap{
          .level-tab{
            ul{
              height:44px;
              line-height:44px;
              border-bottom:1px solid;
              padding:0;
              @include border-color(border);
              li{
                height:40px;
                line-height:40px;
                margin-right:16px;
                font-size:14px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="scss">
  .el-table{
    &.level-table-box-tb{
      .el-table__row{
        &.active{
          @include color(theme);
          td.el-table__cell{
            @include bg-color(bg-quaternary);
          }
        }
      }
    }
  }
</style>