<template>
  <div v-loading="isLoading" class="red-paper-wrapper">
    <div v-if="!isLoading && !isShowList" class="red-paper-receive">
      <div class="receive-logo">
        <NuxtLink :to="`/${locale}`" class="logo-wrap">
          <img src="~/assets/images/common/logo.png" />
        </NuxtLink>
      </div>
      <div class="receive-content flex-box flex-column">
        <img src="" />
        <div class="text-center name color-y">Madex</div>
        <div class="text-center subName color-y">{{ packetDetail.nickname }}</div>
        <div class="text-center subName color-y">{{ $t('给你发送了超级大红包') }}</div>
        <div class="text-center font-size-18 color-y desc">{{ packetDetail.desc }}</div>
        <div class="redPaper-input-box">
          <el-input type="text" v-model="account" :placeholder="$t('请输入邮箱地址领取红包')" />
        </div>
        <div class="open-btn" @click="openRedPaper"></div>
      </div>
    </div>
    <RedReceiveResult
      :random="router.currentRoute.value.params.random"
      :account="account"
      :inviteCode="inviteCode"
      v-if="!isLoading && isShowList"
    />
  </div>
  <BoxScript id="gt" ref="gt" append-to-body src="https://res.ktx.com/common/js/gt4.js" />
</template>
<script lang="ts" setup>
  import { ElInput } from 'element-plus'
  import { packetOutQueryAPI, joinPacketAPI } from '~/api/tt'
  import { useCommonData } from '~/composables/index'
  import { isValidEmail } from '~/utils'
  import RedReceiveResult from '~/components/redPaper/ReceiveResult.vue'
  import Verify from '~/utils/verify'
  const { locale, t } = useI18n()
  const useCommon = useCommonData()
  const router = useRouter()
  const account = ref('')
  const packetDetail = ref({})
  const isLoading = ref(true)
  const verifyEmailCode = ref(null)
  const isShowList = ref(false)
  const inviteCode = computed(() => {
    return router.currentRoute.value.query.invite_code
  })
  const getPackerDetail = async() => {
    const random = router.currentRoute.value.params.random
    const { data, error } = await packetOutQueryAPI({
      random
    })
    console.log(data, error, 'dhdhduehdu')
    if (data) {
      isLoading.value = false
      packetDetail.value = data
    } else if (Number(error.code) === 7019) {
      const localAccount = localStorage.getItem('redPaperAccount')
      if (!localAccount || ((account.value !== localAccount) && account.value)) {
        localStorage.setItem('redPaperDetail', JSON.stringify({
          myredpacket: {},
          redpacketout: {},
          isEnd: true
        }))
      }
      isLoading.value = false
      isShowList.value = true
    } else {
      isLoading.value = false
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const validate = () => {
    if (account.value === '') {
      useCommon.showMsg('error', t('请输入邮箱地址领取红包'))
      return false
    }
    if (!isValidEmail(account.value)) {
      useCommon.showMsg('error', t('请输入正确的邮箱地址'))
      return false
    }
    return true
  }
  const openRedPaper = () => {
    if (!validate()) {
      return false
    }
    verifyEmailCode.value = new Verify(receiveFun)
    verifyEmailCode.value.verify()
  }
  const receiveFun = async(err, res) => {
    if (err) {
      return
    }
    if (res.type === 'success' || res.type === 'resend') {
      const params = {
        ...res.param,
        random: router.currentRoute.value.params.random,
        account: account.value
      }
      const { data, error } = await joinPacketAPI(params)
      if (data) {
        isShowList.value = true
        localStorage.setItem('redPaperDetail', JSON.stringify(data))
        localStorage.setItem('redPaperAccount', account.value)
      } else {
        account.value = ''
        useCommon.showMsg('error', useCommon.err(error.code, error))
      }
    }
  }
  onMounted(() => {
    getPackerDetail()
  })
</script>
<style lang="scss" scoped>
  .red-paper-wrapper{
    min-height:100vh;
    .color-y{
      color:#ffeac2;
    }
    .red-paper-receive{
      width:100%;
      height:100vh;
      background: getSingleColor(bg-primary, 1, dark);
      .receive-logo{
        padding: 20px;
        .logo-wrap{
          display:block;
          width:80px;
          height:auto;
        }
      }
      .receive-content{
        height:448px;
        margin: 20px;
        border-radius:12px;
        overflow:hidden;
        background: url('@/assets/images/redPaper/redPaper-bg.jpg') repeat top;
        background-size:auto 100%;
        .name{
          font-size:16px;
          padding-top:20px;
        }
        .subName{
          font-size:14px;
          padding-top:14px;
        }
        .desc{
          height:32px;
          line-height:32px;
        }
        .open-btn{
          margin-top:80px;
          width:90px;
          height:90px;
          cursor:pointer;
          background: url('@/assets/images/redPaper/open-bg.png') no-repeat center;
          background-size:100% auto;
        }
      }
    }
  }
</style>
<style lang="scss">
  .redPaper-input-box{
    padding-top:40px;
    width:90%;
    .el-input{
      .el-input__wrapper{
        background:#fff5e0!important;
      }
    }
  }
</style>