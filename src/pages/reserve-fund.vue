<template>
  <Header />
  <div class="reserve-fund-container">
    <div class="reserve-fund-banner">
      <div class="banner-center flex-box flex-column space-center align-start">
        <h1>{{ $t('储备金率超 100%') }}</h1>
        <p class="font-size-18 fit-tc-secondary pd-t24">{{ $t('打造下一代加密货币交易生态') }}</p>
        <a href="https://blockchair.com/bitcoin/address/**************************************************************" target="_blank">{{ $t('个人报告') }}</a>
      </div>
    </div>
    <div class="reserve-fund-cont1">
      <div class="reserve-fund-list flex-box space-center">
        <dl class="flex-1">
          <dt>
            <img v-show="colorMode.preference === 'light'" src="~@/assets/images/about-us/fund-icon1-light.png" />
            <img v-show="colorMode.preference === 'dark'" src="~@/assets/images/about-us/fund-icon1-dark.png" />
          </dt>
          <dd>
            <h3>{{ $t('储备金率超 100%') }}</h3>
            <p>{{ $t('资产安全透明，您不用担心挤兑风险') }}</p>
          </dd>
        </dl>
        <dl class="flex-1">
          <dt>
            <img v-show="colorMode.preference === 'light'" src="~@/assets/images/about-us/fund-icon2-light.png" />
            <img v-show="colorMode.preference === 'dark'" src="~@/assets/images/about-us/fund-icon2-dark.png" />
          </dt>
          <dd>
            <h3>{{ $t('资产安全储存') }}</h3>
            <p>{{ $t('结合冷存储与热钱包策略，确保您的资产存储安全') }}</p>
          </dd>
        </dl>
        <dl class="flex-1">
          <dt>
            <img v-show="colorMode.preference === 'light'" src="~@/assets/images/about-us/fund-icon3-light.png" />
            <img v-show="colorMode.preference === 'dark'" src="~@/assets/images/about-us/fund-icon3-dark.png" />
          </dt>
          <dd>
            <h3>{{ $t('合约保险基金') }}</h3>
            <p>{{ $t('补偿超过保证金的亏损，让您可以放心交易') }}</p>
          </dd>
        </dl>
      </div>
      <div class="reserve-fund-title">
        <h2>{{ $t('KTX储备金率') }}</h2>
        <p>{{ $t('基于{time} 验证报告', { time: '2025/02/28 23:59:59 UTC+8' }) }}</p>
      </div>
      <div class="reserve-fund-wrap">
        <div class="reserve-fund-center">
          <div class="asset-title flex-box space-between align-start">
            <dl class="flex-box">
              <dt>
                <BoxCoinIcon icon="/appimg/BTC.png" class="icon-box" />
              </dt>
              <dd>
                <p class="font-size-16">{{ $t('BTC储备金率') }}</p>
                <p class="font-size-32">{{ $t('126%') }}</p>
              </dd>
            </dl>
            <div class="fit-tc-primary font-size-16 flex-box">
              {{ $t('储备充足') }}
              <div class="fund-right-icon flex-box space-center mg-l8">
                <MonoRigthChecked size="14" class="fit-bg-primary" />
              </div>
            </div>
          </div>
          <div class="pd-t20">
            <p class="fit-tc-secondary font-size-16">{{ $t('KTX钱包资产') }}</p>
            <p class="fit-tc-primary font-size-32">89 BTC (7,514,804.00 USD)</p>
          </div>
          <div class="pd-t20">
            <p class="fit-tc-secondary font-size-16">{{ $t('验证地址') }}</p>
            <div class="flex-box m-hideText">
              <a href="https://blockchair.com/bitcoin/address/**************************************************************" target="_blank" class="fit-tc-primary font-size-16 flex-box">
                **************************************************************
              </a>
              <a href="https://blockchair.com/bitcoin/address/**************************************************************" target="_blank" class="fit-tc-primary font-size-16 flex-box">
                <MonoLinkUrl size="20" class="fit-tc-primary mg-l8 cursor-pointer" />
              </a>
              <MonoCopy size="20" class="fit-tc-primary mg-l8 cursor-pointer" @click.stop="useCommon.copy('**************************************************************', $t('复制成功！'))" />
            </div>
            <!-- <MonoCopy size="20" class="fit-tc-primary mg-l12 cursor-pointer" @click="useCommon.copy('**************************************************************', $t('复制成功！'))" /> -->
          </div>
          <div class="assets-bottom font-size-16 text-center fit-tc-secondary">
            {{ $t('储备比例=平台储备金/用户资产。当储备比例≥100%时，表明平台拥有充足资金。') }}
          </div>
        </div>
      </div>
    </div>
    <div class="reserve-fund-cont2">
      <div class="reserve-fund-title">
        <h2>{{ $t('资产安全储存') }}</h2>
        <p>{{ $t('KTX结合冷存储与热钱包策略，确保您的资金存储安全') }}</p>
      </div>
      <div class="reserve-fund-wallet">
        <div class="wallet-item">
          <div class="item-center">
            <div class="tt">
              <h2 class="font-size-28 fit-tc-primary">{{ $t('冷钱包') }}</h2>
              <p class="font-size-16 fit-tc-secondary pd-t8">{{ $t('离线存储您的关键资产') }}</p>
            </div>
            <div class="pd-t20 flex-box flex-column" style="text-align:left;">
              <p class="font-size-16 fit-tc-primary flex-box">
                <MonoReseveIconFour size="18" class="mg-r4" />
                {{ $t('离线存储，防止网络攻击') }}
              </p>
              <p class="font-size-16 fit-tc-primary pd-t8 flex-box">
                <MonoReseveIconOne size="18" class="mg-r4" />
                {{ $t('异地备份，分散资金风险') }}
              </p>
            </div>
          </div>
        </div>
        <div class="wallet-item">
          <div class="item-center">
            <div class="tt">
              <h2 class="font-size-28 fit-tc-primary">{{ $t('热钱包') }}</h2>
              <p class="font-size-16 fit-tc-secondary pd-t8">{{ $t('保证您的即时交易体验') }}</p>
            </div>
            <div class="pd-t20 flex-box flex-column" style="text-align:left;">
              <p class="font-size-16 fit-tc-primary flex-box">
                <MonoReseveIconTwo size="18" class="mg-r4" />
                {{ $t('备用私钥，应对突发情况') }}
              </p>
              <p class="font-size-16 fit-tc-primary pd-t8 flex-box">
                 <MonoReseveIconThree size="18" class="mg-r4" />
                {{ $t('大数据风控，风险管理自动化') }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="reserve-fund-cont3">
      <div class="reserve-fund-title">
        <h2>{{ $t('默克尔树') }}</h2>
        <p>{{ $t('在储备金证明的场景中，默克尔树用于验证交易所的总储备金，确保其能够覆盖所有用户的资产，同时保护用户隐私。以每个哈希后的用户UID和余额联结形成底层数据块，最终以所有用户数据生成默克尔树。叶节点中的任意账户ID或余额变动，都会引起默克尔根也产生变化。每个用户都可以验证自己的资产是否被包含在叶节点中。') }}</p>
      </div>
      <div class="tree-wrap-cont">
        <div class="tree-cont first-child">
          <div class="tree-item ">Hash zx234</div>
        </div>
        <div class="tree-cont">
          <div class="tree-item">
            Hash ac1234
          </div>
          <div class="tree-item">
            Hash bx1234
          </div>
        </div>
        <div class="tree-cont">
          <div class="tree-item">
            Hash bx1234
          </div>
          <div class="tree-item">
            Hash ax1234
          </div>
          <div class="tree-item">
            Hash fc1234
          </div>
          <div class="tree-item">
            Hash wc1234
          </div>
        </div>
        <div class="tree-cont">
          <div class="tree-item">
            Hash gv1234
          </div>
          <div class="tree-item">
            Hash fx1234
          </div>
          <div class="tree-item">
            Hash rv1234
          </div>
          <div class="tree-item">
            Hash nv1234
          </div>
        </div>
      </div>
    </div>
  </div>
  <Footer />
</template>
<script lang="ts" setup>
  import { ElButton } from 'element-plus'
  import MonoCopy from '~/components/common/icon-svg/MonoCopy.vue'
  import { useCommonData } from '~/composables/index'
  import MonoRigthChecked from '~/components/common/icon-svg/MonoRigthChecked.vue'
  import MonoLinkUrl from '~/components/common/icon-svg/MonoLinkUrl.vue'
  import MonoReseveIconFour from '~/components/common/icon-svg/MonoReseveIconFour.vue'
  import MonoReseveIconOne from '~/components/common/icon-svg/MonoReseveIconOne.vue'
  import MonoReseveIconTwo from '~/components/common/icon-svg/MonoReseveIconTwo.vue'
  import MonoReseveIconThree from '~/components/common/icon-svg/MonoReseveIconThree.vue'
  const useCommon = useCommonData()
  const colorMode = useColorMode()
</script>
<style lang="scss" scoped>
  .reserve-fund-container{
    .reserve-fund-banner{
      max-width:1920px;
      width:100%;
      margin:0 auto;
      height:320px;
      @include get-img('~/assets/images/about-us/banner-bg-light.png', '~/assets/images/about-us/banner-bg-dark.png');
      background-size: 320px auto;
      background-repeat:no-repeat;
      background-position:left center;
      @include bg-color(bg-primary);
      .banner-center{
        width:100%;
        margin:0 auto;
        height:320px;
        @include get-img('~/assets/images/about-us/fund-img-light.png', '~/assets/images/about-us/fund-img-dark.png');
        background-size: 300px auto;
        background-repeat:no-repeat;
        background-position:90% center;
        h1{
          padding-left:24%;
          font-size:40px;
          @include color(tc-primary);
        }
        p{
          padding-left:24%;
        }
        a{
          margin-left:24%;
          margin-top:14px;
          display:block;
          width:200px;
          height:50px;
          line-height:50px;
          border-radius:25px;
          text-align:center;
          @include bg-color(theme);
          @include color(tc-primary);
        }
      }
    }
    .fund-right-icon{
      width:20px;
      height:20px;
      border-radius:50%;
      @include bg-color(rise);
      text-align:center;
    }
    .reserve-fund-cont1{
      padding-bottom:60px;
      @include bg-color(bg-quaternary);
      .reserve-fund-list{
        max-width:1920px;
        margin:0 auto;
        padding:60px 0;
        dl{
          dt{
            width:80px;
            height:80px;
            margin:0 auto;
            img{
              width:100%;
              height:auto;
            }
          }
          dd{
            text-align:center;
            padding-top:20px;
            h3{
              font-size:20px;
              @include color(tc-primary);
            }
            p{
              padding-top:12px;
              font-size:14px;
              @include color(tc-secondary);
            }
          }
        }
      }
      .reserve-fund-wrap{
        width:760px;
        height:auto;
        margin:0 auto;
        border-radius:20px;
        border:1px solid;
        margin-top:60px;
        @include border-color(border);
        @include bg-color(bg-primary);
        .reserve-fund-center{
          padding:50px 40px 40px;
          .asset-title{
            padding-bottom:20px;
            border-bottom:1px dashed;
            @include border-color(border);
            dl{
              dt{
                .icon-box{
                  width:74px;
                  height:74px;
                }
              }
              dd{
                padding-left:12px;
                p{
                  @include color(tc-primary);
                }
              }
            }
          }
          .assets-bottom{
            margin-top:20px;
            padding-top:20px;
            border-top:1px dashed;
            @include border-color(border);
          }
        }
      }
    }
    .reserve-fund-title{
      text-align:center;
      padding-top:60px;
      max-width:1180px;
      margin:0 auto;
      h2{
        font-size:32px;
        @include color(tc-primary);
      }
      p{
        padding-top:10px;
        font-size:16px;
        @include color(tc-secondary);
      }
    }
    .reserve-fund-cont2{
      .reserve-fund-wallet{
        width:1180px;
        margin:0 auto;
        display:flex;
        margin-top:40px;
        justify-content: space-between;
        .wallet-item{
          flex:1;
          height:256px;
          border-radius:20px;
          background: linear-gradient(180deg, rgba(240, 185, 11, 0.08) 0%, rgba(240, 185, 11, 0) 100%);
          padding:40px;
          margin:0 10px;
          .tt{
            text-align:center;
            padding-bottom:20px;
            border-bottom:1px solid;
            @include border-color(border);
          }
        }
      }
    }
    .reserve-fund-cont3{
      padding-bottom:60px;
      .tree-wrap-cont{
        width:700px;
        margin:0 auto;
        .tree-cont{
          width:100%;
          display:flex;
          margin:0 auto;
          &.first-child{
            width:80%;
            .tree-item{
              @include color(bg-primary);
              @include bg-color(tc-primary);
              &:before{
                display:none;
              }
            }
          }
          .tree-item{
            flex:1;
            border-radius:6px;
            text-align:center;
            height:54px;
            margin:60px 10px 0;
            line-height:54px;
            position:relative;
            @include color(tc-primary);
            @include bg-color(bg-quaternary);
            &:before{
              position:absolute;
              content: '';
              width:10px;
              height:49px;
              top:-49px;
              left:50%;
              margin-left:-5px;
              background-size:100% auto;
              @include get-img('~/assets/images/about-us/icon-jt-light.png', '~/assets/images/about-us/icon-jt-dark.png')
            }
          }
        }
      }
    }
  }
  @include mb {
    .reserve-fund-container{
      .reserve-fund-banner{
        max-width:100%;
        height:auto;
        background-size: 112px auto;
        background-repeat:no-repeat;
        background-position:left 40px;
        @include bg-color(bg-primary);
        .banner-center{
          width:100%;
          margin:0 auto;
          height:auto;
          background-size: 50% auto;
          background-repeat:no-repeat;
          background-position:right 0px;
          padding-top:170px;
          h1{
            padding-left:0;
            font-size:28px;
            text-align:center;
            width:90%;
            margin:0 auto;
            @include color(tc-primary);
          }
          p{
            padding-left:0;
            text-align:center;
            width:90%;
            margin:0 auto;
            &.pd-t24{
              padding-top:12px !important;
            }
          }
          a{
            margin-left:0;
            margin-top:14px;
            display:block;
            width:90%;
            height:46px;
            line-height:46px;
            border-radius:25px;
            text-align:center;
            margin:12px auto 24px;
          }
        }
      }
      .fund-right-icon{
        width:20px;
        height:20px;
        border-radius:50%;
        @include bg-color(rise);
        text-align:center;
      }
      .reserve-fund-cont1{
        padding-bottom:40px;
        @include bg-color(bg-quaternary);
        .reserve-fund-list{
          max-width:auto;
          padding:40px 16px;
          flex-direction: column;
          dl{
            margin-bottom:24px;
            dd{
              text-align:center;
              padding-top:20px;
            }
          }
        }
        .reserve-fund-wrap{
          width:100%;
          overflow:hidden;
          border-radius:0;
          border:0;
          margin-top:24px;
          .m-hideText{
            a{
              &:first-child{
                display:block;
                width:80%;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
            }
          }
          .font-size-32{
            font-size:20px !important;
          }
          .reserve-fund-center{
            padding:24px 16px;
            .asset-title{
              dl{
                dt{
                  .icon-box{
                    width:40px;
                    height:40px;
                  }
                }
                dd{
                  padding-left:12px;
                  p{
                    @include color(tc-primary);
                    &.font-size-32{
                      font-size:20px !important;
                      line-height:28px !important;
                    }
                  }
                }
              }
            }
            .assets-bottom{
              margin-top:20px;
              padding-top:20px;
              border-top:1px dashed;
              @include border-color(border);
            }
          }
        }
      }
      .reserve-fund-title{
        text-align:center;
        padding-top:20px;
        max-width:auto;
        margin:0 16px;
        h2{
          font-size:24px;
        }
        p{
          font-size:14px;
        }
      }
      .reserve-fund-cont2{
        .reserve-fund-wallet{
          width:auto;
          margin:0 16px;
          display:flex;
          margin-top:24px;
          flex-direction: column;
          .wallet-item{
            flex:1;
            height:auto;
            padding:12px;
          }
        }
      }
      .reserve-fund-cont3{
        padding-top:32px;
        padding-bottom:60px;
        .tree-wrap-cont{
          width:auto;
          margin:0 6px;
          .tree-cont{
            width:100%;
            display:flex;
            margin:0 auto;
            &.first-child{
              width:80%;
              .tree-item{
                @include color(bg-primary);
                @include bg-color(tc-primary);
                &:before{
                  display:none;
                }
              }
            }
            .tree-item{
              flex:1;
              border-radius:6px;
              text-align:center;
              height:54px;
              margin:60px 10px 0;
              line-height:54px;
              position:relative;
              @include color(tc-primary);
              @include bg-color(bg-quaternary);
              &:before{
                position:absolute;
                content: '';
                width:10px;
                height:49px;
                top:-49px;
                left:50%;
                margin-left:-5px;
                background-size:100% auto;
                @include get-img('~/assets/images/about-us/icon-jt-light.png', '~/assets/images/about-us/icon-jt-dark.png')
              }
            }
          }
        }
      }
    }
  }
</style>
