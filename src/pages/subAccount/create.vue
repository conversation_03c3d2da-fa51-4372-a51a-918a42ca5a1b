<template>
  <div class="son-create-page">
    <h2>{{ isAuthorize ? $t('授权子账号信息') : $t('新建快捷子账号') }}</h2>
    <CreateView :isAuthorize="isAuthorize" :len="len" :currentItem="currentItem" @close="goSubAccount" />
  </div>
</template>
<script lang="ts" setup>
  import { isApp } from '~/utils'
  import CreateView from '~/components/my/subAccount/createView.vue'
  const router = useRouter()
  const isAuthorize = computed(() => {
    return router.currentRoute.value.query.sonType * 1 === 2
  })
  const len = computed(() => {
    return router.currentRoute.value.query.len * 1
  })
  const currentItem = computed(() => {
    return {
      'user_id': router.currentRoute.value.query.sonId || '',
      'desc': router.currentRoute.value.query.desc || ''
    }
  })
  const goSubAccount = () => {
    if (isApp()) {
      useCommonData().jsAppBridge('goSubAccounts')
    }
  }
</script>
<style lang="scss">
  .son-create-page{
    padding:0 !important;
    .son-tips, .son-tips-ul{
      margin-top:-12px;
      font-size:14px;
      padding-bottom:20px;
      @include color(tc-secondary);
      li{
        padding-left:16px;
        padding-bottom:8px;
        position:relative;
        &:after{
          content:'';
          display:block;
          position:absolute;
          top:8px;
          left:2px;
          width:4px;
          height:4px;
          border-radius:50%;
          @include bg-color(tc-secondary);
        }
      }
    }
    .text-info{
      width:100%;
      height:44px;
      line-height:44px;
      border-radius:4px;
      border:1px solid;
      font-size:14px;
      padding:0 12px;
      @include color(tc-primary);
      @include border-color(border);
    }
    .info-tips{
      font-size:14px;
      @include color(tc-secondary);
      span{
        margin-right:4px;
        cursor:pointer;
        text-decoration: underline;
      }
    }
  }
  @include mb{
    .son-create-page{
      position:fixed;
      top:0;
      left:16px;
      right:16px;
      bottom:0;
      margin:0 !important;
      padding:0 !important;
      h2{
        font-size:24px;
        padding: 20px 0;
        @include color(tc-primary);
      }
      .el-dialog__header{
        padding:24px 16px;
      }
      .son-tips, .son-tips-ul{
        margin-top:0;
      }
      .text-info{
        width:100%;
        height:44px;
        line-height:44px;
        border-radius:4px;
        border:1px solid;
        font-size:14px;
        padding:0 12px;
        @include color(tc-primary);
        @include border-color(border);
      }
      .info-tips{
        font-size:14px;
        @include color(tc-secondary);
        span{
          margin-right:4px;
          cursor:pointer;
          text-decoration: underline;
        }
      }
      .btn-box{
        position:fixed;
        bottom:0;
        left:0;
        right:0;
        padding:12px 16px;
        @include bg-color(bg-primary);
        .el-button{
          width:100%;
          height:40px;
        }
      }
    }
  }
</style>