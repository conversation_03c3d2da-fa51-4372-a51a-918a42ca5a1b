<template>
  <div id="setLoadMoreDataCallback-chart" style="height: 400px"></div>
</template>

<script lang="ts" setup>
  import { init } from 'klinecharts';
  import { getKlinesApi } from '~/api/order';

  const transChartData = (obj: any) => {
    const keys = ['timestamp', 'open', 'high', 'low', 'close', 'volume'];
    return keys.reduce((finalItem, key, i) => {
      finalItem[key] = Number(obj[i]);
      return finalItem;
    }, { time: Number(obj[0]) });
  };

  const request = async (end?: number) => {
    const { data } = await getKlinesApi({
      symbol: 'BTC_USDT_SWAP',
      market: 'lpc',
      time_frame: '1m',
      before: end,
      limit: 300
    });
    if (data) {
      const klineData = data.e.map((item: any) => transChartData(item));
      return klineData
    } else {
      return [];
    }
  };

  onMounted(async () => {
    const chart = init('setLoadMoreDataCallback-chart');
    chart.setLoadMoreDataCallback(async ({ type, data, callback }) => {
      if (type === 'forward') {
        const end = data ? data.timestamp : Date.now();
        const klineData = await request(end);
        console.log('Forward data:', data, klineData);
        callback(klineData, klineData.length === 300);
      } else {
        callback([], false);
      }
    });

    const { data } = await getKlinesApi({
      symbol: 'BTC_USDT_SWAP',
      market: 'lpc',
      time_frame: '1m',
      limit: 300
    });

    if (data) {
      const klineData = data.e.map((item: any) => transChartData(item));
      chart.applyNewData(klineData, true);
    }
  });
</script>
