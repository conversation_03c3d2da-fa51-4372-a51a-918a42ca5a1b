<template>
  <Header />
  <div class="symbol-apply-container">
    <div class="symbol-apply-wrapper">
      <div class="symbol-apply-center">
        <h2>{{ $t('申请上币') }}</h2>
        <div class="symbol-apply-step-nav" :class="{'en': locale === 'en', 'ja': locale === 'ja' }">
          <el-steps :active="curActive">
            <el-step>
              <template #title>
                <div class="title-step-cont first-step-cont">{{ $t('联系方式') }}</div>
              </template>
            </el-step>
            <el-step>
              <template #title>
                <div class="title-step-cont second-step-cont">{{ $t('币种信息') }}</div>
              </template>
            </el-step>
            <el-step>
              <template #title>
                <div class="title-step-cont third-step-cont">{{ $t('链上信息') }}</div>
              </template>
            </el-step>
          </el-steps>
        </div>
        <div class="symbol-apply-form-cont">
          <div class="symbol-apply-form">
            <el-form v-if="curActive * 1 === 0">
              <el-form-item prop="nick_name" :label="$t('姓名')">
                <el-input v-model="step1Form.nick_name" />
              </el-form-item>
              <el-form-item prop="position" :label="$t('职位/头衔')">
                <el-input v-model="step1Form.position" />
              </el-form-item>
              <el-form-item prop="telegram" label="Telegram">
                <el-input v-model="step1Form.telegram" />
              </el-form-item>
              <el-form-item prop="email" :label="$t('邮箱地址')">
                <el-input v-model="step1Form.email" />
              </el-form-item>
              <div class="btn-box-cont">
                <el-button :disabled="Object.values(step1Form).some(v => !v)" type="primary" @click="nextFun(0)">{{ $t('下一步') }}</el-button>
              </div>
            </el-form>
            <el-form v-if="curActive * 1 === 1">
              <el-form-item prop="name" :label="$t('币种名称')">
                <el-input v-model="step2Form.name" />
              </el-form-item>
              <el-form-item prop="general_name" :label="$t('币种简称')">
                <el-input v-model="step2Form.general_name" />
              </el-form-item>
              <el-form-item prop="icon_url" :label="$t('币种图标（上传）')">
                <BoxImgUpload v-model="step2Form.icon_url" />
              </el-form-item>
              <el-form-item prop="describe_url" :label="$t('币种简介')">
                <el-input v-model="step2Form.describe_url" />
              </el-form-item>
              <el-form-item prop="website_url" :label="$t('官网地址')">
                <el-input v-model="step2Form.website_url" />
              </el-form-item>
              <el-form-item prop="white_paper_url" :label="$t('白皮书地址')">
                <el-input v-model="step2Form.white_paper_url" />
              </el-form-item>
              <div class="btn-box-cont">
                <el-button @click="prevFun(1)">{{ $t('上一步') }}</el-button>
                <el-button :disabled="Object.values(step2Form).some(v => !v)" type="primary" @click="nextFun(1)">{{ $t('下一步') }}</el-button>
              </div>
            </el-form>
            <el-form v-if="curActive * 1 === 2">
              <el-form-item prop="chain_type" :label="$t('主链信息')">
                <el-input v-model="step3Form.chain_type" />
              </el-form-item>
              <el-form-item prop="contract" :label="$t('Erc20链地址')">
                <el-input v-model="step3Form.contract" />
              </el-form-item>
              <el-form-item prop="explor_url" :label="$t('区块链浏览器')">
                <el-input v-model="step3Form.explor_url" />
              </el-form-item>
              <el-form-item prop="total_amount" :label="$t('发行总量')">
                <el-input v-model="step3Form.total_amount" />
              </el-form-item>
              <el-form-item prop="supply_amount" :label="$t('流通总量')">
                <el-input v-model="step3Form.supply_amount" />
              </el-form-item>
              <el-form-item prop="price" :label="$t('发行价格')">
                <el-input v-model="step3Form.price" />
              </el-form-item>
              <el-form-item prop="supply_time" :label="$t('发行时间')">
                <el-input v-model="step3Form.supply_time" />
              </el-form-item>
              <el-form-item prop="circulation_rate" :label="$t('流通率')">
                <el-input v-model="step3Form.circulation_rate" />
              </el-form-item>
              <el-form-item prop="deflation_rate" :label="$t('通缩率')">
                <el-input v-model="step3Form.deflation_rate" />
              </el-form-item>
              <div class="btn-box-cont">
                <el-button @click="prevFun(2)">{{ $t('上一步') }}</el-button>
                <el-button :disabled="Object.values(step3Form).some(v => !v)" type="primary" @click="submit()">{{ $t('提交') }}</el-button>
              </div>
            </el-form>
            <div class="info-txt">
              {{ $t('如有任何问题，欢迎联系') }}:
              <span class="fit-theme"><EMAIL></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <Footer />
</template>
<script lang="ts" setup>
  import { ElSteps, ElStep, ElForm, ElFormItem, ElInput, ElButton, ElUpload } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import BoxImgUpload from '~/components/common/BoxImgUpload.vue'
  import { useCommonData } from '~/composables/index'
  import { businessCoinApply, businessCoinCheck } from '~/api/tt.ts'
  import { useUserStore } from '~/stores/useUserStore'
  const useCommon = useCommonData()
  const { locale, t } = useI18n()
  const curActive = ref(0)
  const useStore = useUserStore()
  const { isLogin } = storeToRefs(useStore)
  interface step1Form{
    nick_name: string,
    position: string,
    telegram: string,
    email: string
  }
  const step1FormRef = ref<FormInstance>()
  const step1Form = reactive<step1Form>({
    nick_name: '',
    position: '',
    telegram: '',
    email: ''
  })
  interface step2Form{
    name: String,
    general_name: String,
    icon_url: String,
    describe_url: String,
    website_url: String,
    white_paper_url: String
  }
  const step2FormRef = ref<FormInstance>()
  const step2Form = reactive<step2Form>({
    name: '',
    general_name: '',
    icon_url: '',
    describe_url: '',
    website_url: '',
    white_paper_url: ''
  })
  interface step3Form{
    chain_type: String,
    contract: String,
    explor_url: String,
    total_amount: String,
    supply_amount: String,
    price: String,
    supply_time: String,
    circulation_rate: String,
    deflation_rate: String
  }
  const step3Form = reactive<step3Form>({
    chain_type: '',
    contract: '',
    explor_url: '',
    total_amount: '',
    supply_amount: '',
    price: '',
    supply_time: '',
    circulation_rate: '',
    deflation_rate: ''
  })
  const nextFun = (num) => {
    if (num * 1 === 1) {
      checkCoin(num)
      return false
    }
    curActive.value = num + 1
  }
  const prevFun = (num) => {
    curActive.value = num - 1
  }
  const checkCoin = async(num) => {
    const { data, error } = await businessCoinCheck({
      name: step2Form.name
    })
    if (data) {
      if (data.exist) {
        step2Form.name = ''
        useCommon.showMsg('error', t('币种名称重复，请更改名称'))
      } else {
        curActive.value = num + 1
      }
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const submit = async() => {
    const obj = Object.assign({}, step1Form, step2Form, step3Form)
    const { data, error } = await businessCoinApply(obj)
    if (data) {
      useCommon.showMsg('success', t('项目申请提交成功！'))
      curActive.value = 0
      window.scrollTo(0, 0)
      Object.assign(step1Form, {
        nick_name: '',
        position: '',
        telegram: '',
        email: ''
      })
      Object.assign(step2Form, {
        name: '',
        general_name: '',
        icon_url: '',
        describe_url: '',
        website_url: '',
        white_paper_url: ''
      })
      Object.assign(step3Form, {
        chain_type: '',
        contract: '',
        explor_url: '',
        total_amount: '',
        supply_amount: '',
        price: '',
        supply_time: '',
        circulation_rate: '',
        deflation_rate: ''
      })
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  watch(() => isLogin.value, async(val) => {
    if (!val) {
      useCommon.openLogin()
      return false
    }
  })
  onMounted(async() => {
    if (!isLogin.value) {
      useCommon.openLogin()
      return false
    }
  })
</script>
<style lang="scss">
  .symbol-apply-container{
    .symbol-apply-wrapper{
      .symbol-apply-center{
        width:500px;
        margin:0 auto;
        h2{
          font-size:40px;
          padding:26px 0 40px;
          text-align:center;
          @include color(tc-primary);
        }
        .symbol-apply-step-nav{
          width:500px;
          .title-step-cont{
            font-size:16px;
            margin-top:8px;
            white-space: nowrap;
            display:inline-block;
          }
          .first-step-cont{
            transform: translateX(-38%);
          }
          .second-step-cont{
            transform: translateX(-34%);
          }
          .third-step-cont{
            transform: translateX(-30%);
          }
        }
        .symbol-apply-form-cont{
          .symbol-apply-form{
            padding:40px 0 60px;
            .el-form-item__label{
              &:after{
                content: '*';
                font-size:14px;
                margin-left:4px;
                margin-top:2px;
                @include color(warn);
              }
            }
          }
        }
        .btn-box-cont{
          display:flex;
          .el-button{
            display:block;
            width:100%;
            height:44px;
          }
        }
        .info-txt{
          padding-top:40px;
          font-size:14px;
          text-align:center;
          @include color(tc-primary);
        }
      }
    }
  }
  @include mb {
    .symbol-apply-container{
      .symbol-apply-wrapper{
        .symbol-apply-center{
          width:auto;
          margin:0 16px;
          h2{
            font-size:32px;
            padding:26px 0 30px;
          }
          .symbol-apply-step-nav{
            width:88%;
            margin-left:6%;
            &.en{
              .title-step-cont{
                white-space:break-spaces;
              }
            }
            &.ja{
              width:70%;
            }
            .title-step-cont{
              font-size:14px;
              margin-top:8px;
              display:inline-block;
            }
            .first-step-cont{
              transform: translateX(-38%);
            }
            .second-step-cont{
              transform: translateX(-34%);
            }
            .third-step-cont{
              transform: translateX(-30%);
            }
          }
          .symbol-apply-form-cont{
            .symbol-apply-form{
              padding:30px 0 40px;
            }
          }
          .info-txt{
            padding-top:40px;
            font-size:14px;
            text-align:center;
            @include color(tc-primary);
          }
        }
      }
    }
  }
</style>

