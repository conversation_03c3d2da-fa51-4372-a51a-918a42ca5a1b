<template>
  <Header />
  <p class="fit-tc-primary">搜索组件</p>
  <ElInput type="text" v-model="searchText" clearable>
    <template #prepend>
      <MonoSun size="20" />
    </template>
  </ElInput>
  <ElInput type="text" v-model="searchText" clearable>
    <template #append>
      <MonoSun size="20" />
    </template>
  </ElInput>
  <ElInput type="text" v-model="text" placeholder="搜索" clearable />
  <p class="fit-tc-primary">普通输入框</p>
  <ElInput type="text" v-model="text2" placeholder="请输入文字" clearable />
  <p class="fit-tc-primary">谷歌验证码输入框</p>
  <p class="fit-tc-primary">按钮组件</p>
  <ElButton type="primary">有背景</ElButton>
  <ElButton type="success">有背景</ElButton>
  <ElButton type="warning">有背景</ElButton>
  <ElButton type="danger">有背景</ElButton>
  <ElButton>无背景</ElButton>
  <p class="fit-tc-primary">弹框</p>
  <ElButton type="primary" @click="dialogFormVisible = true">打开弹层</ElButton>
  <ElDialog v-model="dialogFormVisible" title="Shipping address" width="800">
  </ElDialog>
  <p class="fit-tc-primary">表格</p>
  <ElTable :data="tableData">
    <ElTableColumn prop="name" label="名称" />
    <ElTableColumn prop="date" label="最新价" />
    <ElTableColumn prop="address" label="涨跌幅" />
    <ElTableColumn prop="date" label="最高价" />
  </ElTable>
  <p class="fit-tc-primary">轮播图片</p>
  <p class="fit-tc-primary">svg图标</p>
  <CoinHkd />
  <ColorC2Api />
  <MonoSun color="#f00" />
  <IntlCh />
  <MediaTwitter color="#f00" />
  <Footer />
</template>
<script lang="ts" setup>
  import Header from '~/components/header/index.vue'
  import Footer from '~/components/footer/index.vue'
  import { ElInput, ElCalendar, ElButton, ElDialog, ElTable } from 'element-plus'
  const colorMode = useColorMode()
  const setColorMode = () => {
    colorMode.preference = colorMode.preference === 'dark' ? 'light' : 'dark'
    localStorage.setItem('nuxt-color-mode', colorMode.preference)
    console.info(colorMode)
  }
  const searchText = ref<string|number>('')
  const text = ref<string|number>('')
  const text2 = ref<string|number>('')
  const dialogFormVisible = ref(false)
  const tableData = [
    {
      date: '2016-05-03',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
    },
    {
      date: '2016-05-02',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
    },
    {
      date: '2016-05-04',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
    },
    {
      date: '2016-05-01',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
    },
    {
      date: '2016-05-08',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
    },
    {
      date: '2016-05-06',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
    },
    {
      date: '2016-05-07',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
    },
  ]
</script>