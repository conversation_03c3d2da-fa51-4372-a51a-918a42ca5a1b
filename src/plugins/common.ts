import VueLazyload from 'vue-lazyload-next'
import BoxQrcode from '~/components/common/BoxQrcode.vue'
import MonoSun from '~/components/common/icon-svg/MonoSun.vue'
import MonoMoon from '~/components/common/icon-svg/MonoMoon.vue'
import MonoLanguage from '~/components/common/icon-svg/MonoLanguage.vue'
import MonoDownload from '~/components/common/icon-svg/MonoDownload.vue'
import MonoSearch from '~/components/common/icon-svg/MonoSearch.vue'
import MonoUserInfo from '~/components/common/icon-svg/MonoUserInfo.vue'
import MonoMenuIcon from '~/components/common/icon-svg/MonoMenuIcon.vue'
import MonoTwitter from '~/components/common/icon-svg/MonoTwitter.vue'
import BoxCoinIcon from '~/components/common/BoxCoinIcon.vue'
import BoxLoading from '~/components/common/BoxLoading.vue'
import BoxNoData from '~/components/common/BoxNoData.vue'
import MonoRightArrowShort from '~/components/common/icon-svg/MonoRightArrowShort.vue'
import MonoCollect from '~/components/common/icon-svg/MonoCollect.vue'
import MonoCollected from '~/components/common/icon-svg/MonoCollected.vue'
import MonoClose from '~/components/common/icon-svg/MonoClose.vue'
import MonoDelete from '~/components/common/icon-svg/MonoDelete.vue'
import BoxScript from '~/components/common/BoxScript.vue'
export default defineNuxtPlugin((nuxtApp) => {
  // console.info(nuxtApp.vueApp, 'nuxtApp.vueAppnuxtApp.vueApp')
  nuxtApp.vueApp.component('MonoSun', MonoSun)
  nuxtApp.vueApp.component('MonoMoon', MonoMoon)
  nuxtApp.vueApp.component('MonoLanguage', MonoLanguage)
  nuxtApp.vueApp.component('MonoDownload', MonoDownload)
  nuxtApp.vueApp.component('MonoSearch', MonoSearch)
  nuxtApp.vueApp.component('MonoUserInfo', MonoUserInfo)
  nuxtApp.vueApp.component('MonoMenuIcon', MonoMenuIcon)
  nuxtApp.vueApp.component('MonoTwitter', MonoTwitter)
  nuxtApp.vueApp.component('MonoRightArrowShort', MonoRightArrowShort)
  nuxtApp.vueApp.component('MonoCollect', MonoCollect)
  nuxtApp.vueApp.component('MonoCollected', MonoCollected)
  nuxtApp.vueApp.component('MonoDelete', MonoDelete)
  nuxtApp.vueApp.component('MonoClose', MonoClose)
  nuxtApp.vueApp.component('BoxQrcode', BoxQrcode)
  nuxtApp.vueApp.component('BoxCoinIcon', BoxCoinIcon)
  nuxtApp.vueApp.component('BoxLoading', BoxLoading)
  nuxtApp.vueApp.component('BoxNoData', BoxNoData)
  nuxtApp.vueApp.component('BoxScript', BoxScript)
  // 配置选项
  const options = {
    preLoad: 1.3,
    // error: 'dist/error.png',
    // loading: 'dist/loading.gif',
    attempt: 1
  }
  nuxtApp.vueApp.use(VueLazyload, options)
})