import { useCookieUniversal } from '~/composables/useCookieUniversal'
import {initializeCookies} from '~/utils'
import ElementPlus from 'element-plus'
import zhLocale from 'element-plus/es/locale/lang/zh-cn'
import enLocale from 'element-plus/es/locale/lang/en'
import jaLocale from 'element-plus/es/locale/lang/ja'
import vantUI from 'vant'
export default defineNuxtPlugin(() => {
  const nuxtApp = useNuxtApp()
  const { ssrContext } = nuxtApp
  const req = ssrContext?.event.node.req
  const res = ssrContext?.event.node.res
  const cookies = useCookieUniversal(req, res)
  initializeCookies(cookies)
  const localeList = ref({
    'zh': zhLocale,
    'en': enLocale,
    'ja': jaLocale
  })
  const exchangeRateList = ref({
    'zh': {
      rate: 'CNY',
      symbol: '¥'
    },
    'en': {
      rate: 'USD',
      symbol: '$'
    },
    'ja': {
      rate: 'JPY',
      symbol: '¥'
    }
  })
  nuxtApp.$i18n.setLocaleCookie(nuxtApp.$i18n.locale.value)
  // cookies.set('exchangeRates', JSON.stringify(exchangeRateList.value[nuxtApp.$i18n.locale.value]))
  nuxtApp.vueApp.use(vantUI)
  nuxtApp.vueApp.use(ElementPlus, {
    locale: localeList.value[nuxtApp.$i18n.locale.value],
  })
  return {
    provide: {
      cookies
    }
  }
})