export default defineNuxtPlugin(() => {
  if (process.client) {
    const preloadResources = () => {
      const criticalResources = [
        { href: 'https://code.highcharts.com/highcharts.js', as: 'script', crossorigin: 'anonymous' }
      ]

      criticalResources.forEach(resource => {
        try {
          const existingLink = document.querySelector(`link[href="${resource.href}"]`)
          if (!existingLink) {
            const link = document.createElement('link')
            link.rel = 'preload'
            link.href = resource.href
            link.as = resource.as
            if (resource.crossorigin) {
              link.crossOrigin = resource.crossorigin
            }
            link.onerror = () => {}
            document.head.appendChild(link)
          }
        } catch (e) {}
      })
    }

    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', preloadResources)
    } else {
      preloadResources()
    }
  }
})
