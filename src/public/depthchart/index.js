export default {
  depthChart: function (t, e) {
    var i, o;
    (i = HTMLCanvasElement.prototype).getContext = (o = i.getContext,
    function(t) {
        var e, i, r = o.call(this, t);
        return "2d" === t && (e = r.backingStorePixelRatio || r.webkitBackingStorePixelRatio || r.mozBackingStorePixelRatio || r.msBackingStorePixelRatio || r.oBackingStorePixelRatio || r.backingStorePixelRatio || 1,
        (i = (window.devicePixelRatio || 1) / e) > 1 && (this.style.height = this.height + "px",
        this.style.width = this.width + "px",
        this.width *= i,
        this.height *= i)),
        r
    }
    );
    var n = {
        "dark": {
            bidsLineColor: e && e.bidsLineColor || "rgba(0, 141, 115, 1)",
            asksLineColor: e && e.asksLineColor || "rgba(236, 49, 75, 1)",
            bidsFillColor: e && e.bidsFillColor || "rgba(0, 141, 115, .2)",
            asksFillColor: e && e.asksFillColor || "rgba(236, 49, 75, .2)",
            axisColor: e && e.axisColor || "rgba(97, 104, 138, .3)",
            color: e && e.color || "rgba(81, 128, 159, .8)",
            bgColor: e && e.bgColor || "rgba(23, 54, 72, .95)",
            dotColor: "rgba(0 , 126, 224, 1)",
            tipColor: e && e.tipColor || "#C7CCE6",
            tipShadow: 4
        },
        "light": {
            bidsLineColor: e && e.bidsLineColor || "rgba(3, 192, 135, 0)",
            asksLineColor: e && e.asksLineColor || "rgba(231, 109, 66, 0)",
            bidsFillColor: e && e.bidsFillColor || "rgba(3, 192, 135, .1)",
            asksFillColor: e && e.asksFillColor || "rgba(231, 109, 66, .1)",
            axisColor: e && e.axisColor || "rgba(180, 188, 227, .3)",
            color: e && e.color || "#232A4A",
            bgColor: e && e.bgColor || "#ffffff",
            dotColor: "rgba(0 , 126, 224, 1)",
            tipColor: e && e.tipColor || "#9CA9B5",
            tipShadow: 4
        }
    }
      , a = {
        theme: e && e.theme || "dark",
        ruleHeight: e && e.ruleHeight || 30,
        ruleWidth: e && e.ruleWidth || 54,
        priceFix: e && e.priceFix || 2,
        amountFix: e && e.amountFix || 0,
        paddingTop: e && e.paddingTop || 15,
        noAmountTick: e && e.noAmountTick || 500,
        lang: e && e.lang || "en",
        langMap: e && e.langMap
    };
    function l(t) {
        var e = t || a.theme;
        Object.keys(n["light"]).forEach((function(t) {
            a[t] = n[e][t]
        }
        ))
    }
    l();
    var s, h, c, d = Object.assign({
        "zh-cn": {
            "委托价": "委托价",
            "累计": "累计"
        },
        "en-us": {
            "委托价": "Price",
            "累计": "Sum"
        }
    }, a.langMap || {}), u = [], g = [], p = 0, f = "string" == typeof t ? document.querySelector("#" + t.replace("#", "")) : t || document.querySelector("#chart"), b = document.createElement("canvas"), C = f.offsetWidth, m = f.offsetHeight, x = document.createElement("canvas"), y = !0, v = b.getContext("2d"), k = x.getContext("2d"), P = (c = v,
    (window.devicePixelRatio || 1) / (c.backingStorePixelRatio || c.webkitBackingStorePixelRatio || c.mozBackingStorePixelRatio || c.msBackingStorePixelRatio || c.oBackingStorePixelRatio || c.backingStorePixelRatio || 1)), T = C - a.ruleWidth, w = m - a.ruleHeight, S = ~~(m * a.paddingTop / 100), O = S * P, F = w - S, L = F * P, j = T * P, D = w * P;
    function E(t, e) {
        if (t = t || h)
            if (h = t,
            U(k),
            y = !1,
            t > j - P)
                y = !0;
            else {
                for (var i = v.getImageData(t, 0, 1, D - 1 * P), o = 0; o < i.height; o++) {
                    var r = i.data[4 * o * i.width]
                      , n = i.data[4 * o * i.width + 1];
                    if (r || n)
                        return B(t, o, t > j / 2 ? "asks" : "bids"),
                        void (y = !0)
                }
                y = !0
            }
    }
    function M(t) {
        var e = (t + "").split(".");
        // return r()(e, 1)[0].length
        return e[0].length
    }
    b.width = x.width = C * P,
    b.height = x.height = m * P,
    b.style.position = x.style.position = "absolute",
    b.style.width = x.style.width = C + "px",
    b.style.height = x.style.height = m + "px",
    f.style.position = "relative",
    f.appendChild(b),
    f.appendChild(x);
    var A = {
        1e3: ["K", "M", "B"],
        1e4: ["万", "亿", "兆"]
    };
    function R(t, e, i) {
        a.dotColor = "bids" === i ? a.bidsLineColor : a.asksLineColor,
        k.beginPath(),
        k.arc(t, e, 10 * P, 0, 2 * Math.PI),
        k.closePath(),
        k.fillStyle = a.dotColor.replace("1)", ".3)"),
        k.fill(),
        k.beginPath(),
        k.arc(t, e, 5 * P, 0, 2 * Math.PI),
        k.closePath(),
        k.fillStyle = a.dotColor,
        k.fill(),
        function(t, e, i) {
            k.beginPath(),
            k.strokeStyle = a[i + "LineColor"],
            k.lineWidth = ~~(1 * P),
            k.setLineDash([5 * P, 5 * P]),
            k.moveTo(t, Math.min(e + 10 * P, D)),
            k.lineTo(t, D),
            k.stroke(),
            k.closePath()
        }(t, e, i)
    }
    function B(t, e, i) {
        R(t, e, i),
        CanvasRenderingContext2D.prototype.roundRect = function(t, e, i, o, r) {
            var n = Math.min(i, o);
            return r > n / 2 && (r = n / 2),
            this.beginPath(),
            this.moveTo(t + r, e),
            this.arcTo(t + i, e, t + i, e + o, r),
            this.arcTo(t + i, e + o, t, e + o, r),
            this.arcTo(t, e + o, t, e, r),
            this.arcTo(t, e, t + i, e, r),
            this.closePath(),
            this
        }
        ;
        var o = (6 * (p ? Math.max(p.toFixed(a.amountFix).toString().length - 9, 0) : 0) + 140) * P
          , r = 170 * P
          , n = 18 * P;
        k.shadowBlur = a.tipShadow * P,
        k.shadowOffsetY = a.tipShadow * P;
        var l = T - t > o ? t + 4 : t - o - 2
          , h = e - r - n > k.shadowBlur ? e - r - n : e + n
          , c = e - r - n > k.shadowBlur ? e - r - n : e + n
          , f = e - r - n > k.shadowBlur ? e - n : e + r + n
          , b = $()
          , C = b.pTick * t + b.pMin
          , m = function(t, e) {
            if ("bids" === e) {
                var i = g.map((function(e) {
                    return Math.abs(e[0] - t)
                }
                ));
                // console.log(i.indexOf(Math.min.apply(null, i)) > -1 && (s = g[i.indexOf(Math.min.apply(null, i))][1]),
                // s, 'dhdhdhhdhdhdhdhdhdhhdhdhdhdh')
                return i.indexOf(Math.min.apply(null, i)) > -1 && (s = g[i.indexOf(Math.min.apply(null, i))][1]),
                s
            }
            var o = u.map((function(e) {
                return Math.abs(e[0] - t)
            }
            ));
            return o.indexOf(Math.min.apply(null, o)) > -1 && (s = u[o.indexOf(Math.min.apply(null, o))][1]),
            s
        }(C, i);
        k.beginPath(),
        k.moveTo(_(t), _(c)),
        k.lineTo(_(t), _(f)),
        k.closePath(),
        k.lineWidth = ~~(4 * P),
        k.strokeStyle = "bids" === i ? a.bidsLineColor : a.asksLineColor,
        k.stroke(),
        k.fillStyle = a.bgColor,
        k.roundRect(l, h, o, r, 3 * P),
        k.fill(),
        k.shadowBlur = 0,
        k.shadowOffsetY = 0,
        k.fillStyle = a.tipColor,
        k.font = 12 * P + "px Arial",
        k.fillText(d[a.lang]["委托价"], l + 16 * P, h + 20 * P),
        k.fillText(C.toFixed(a.priceFix), l + 16 * P, h + 36 * P),
        k.fillText(d[a.lang]["累计"], l + 16 * P, h + 60 * P),
        k.fillText(m.toFixed(a.amountFix), l + 16 * P, h + 76 * P);
        var x = (C - ((g[g.length - 1][0] + u[0][0]) / 2).toFixed(a.priceFix)).toFixed(a.priceFix);
        k.fillText(d[a.lang]["价差"], l + 16 * P, h + 100 * P),
        k.fillStyle = x < 0 ? a.asksLineColor : a.bidsLineColor,
        k.fillText(x < 0 ? "".concat(x) : "+".concat(x), l + 16 * P, h + 116 * P);
        var y = (100 * (C / ((g[g.length - 1][0] + u[0][0]) / 2).toFixed(a.priceFix) - 1)).toFixed(2);
        k.fillStyle = a.tipColor,
        k.fillText(d[a.lang]["涨跌幅"], l + 16 * P, h + 140 * P),
        k.fillStyle = y < 0 ? a.asksLineColor : a.bidsLineColor,
        k.fillText(y < 0 ? "".concat(y, "%") : "+".concat(y, "%"), l + 16 * P, h + 156 * P)
    }
    function W() {
        v.strokeStyle = a.axisColor,
        v.lineWidth = ~~(1 * P),
        v.beginPath(),
        v.moveTo(_(0), _(D)),
        v.lineTo(_(j), _(D)),
        v.lineTo(_(j), _(0)),
        v.stroke(),
        v.closePath()
    }
    function z() {
        v.strokeStyle = a.bidsLineColor,
        v.lineWidth = ~~(1 * P),
        v.beginPath();
        for (var t = g.sort((function(t, e) {
            return t[0] - e[0]
        }
        )), e = j / t.length / 2, i = 0; i < t.length; i++)
            0 === i && v.moveTo(_(i * e), _(G(t[i][1]))),
            v.lineTo(_(i * e), _(G(t[i][1]))),
            i === t.length - 1 && v.lineTo(_(i * e), _(D - P));
        v.stroke(),
        v.lineTo(_(0), _(D)),
        v.lineTo(_(0), _(0)),
        v.closePath(),
        v.fillStyle = a.bidsFillColor,
        v.fill()
    }
    function H() {
        v.strokeStyle = a.asksLineColor,
        v.beginPath();
        for (var t = u.sort((function(t, e) {
            return t[0] - e[0]
        }
        )), e = j / t.length / 2, i = j / 2 + 2 * P, o = 0; o < t.length; o++)
            0 === o && v.lineTo(_(i), _(D - P)),
            v.lineTo(_(o * e + i), _(G(t[o][1]))),
            o === t.length - 1 && v.lineTo(_(j), _(G(t[o][1])));
        v.stroke(),
        v.lineTo(_(j), _(D - P)),
        v.lineTo(_(i), _(D - P)),
        v.closePath(),
        v.fillStyle = a.asksFillColor,
        v.fill()
    }
    function I() {
        var t = 32 * P;
        var fixedTickCount = 5; // 固定5个刻度
        var i = (j - 2 * t) / (fixedTickCount - 1); // 等分间距
        
        // 其余代码保持不变...
        for (var r = [], n = [], l = [], s = [], h = 0, c = [], d = [], u = $(), g = t; g < j; g += i) {
            r.push(g);
            n.push(u.pMin + g * u.pTick);
        }
        // Y轴部分保持原样（自动计算）
        var e = 1 + ~~(w / 100); 
        var o = (D - 2 * (16 * P)) / e;
        for (var f = D - P; f > 0; f -= o)
            l.push(f),
            s.push((D - P - f) * u.aTick);
        s.forEach((function(t, e) {
            h += t,
            c.push(a.noAmountTick * e),
            d.push(e)
        }
        )),
        p < 5 && 0 !== h && (s = d,
        p = (L - o * e - 1) / o + 5),
        0 === h && (s = c),
        s[0] = 0,
        V(r, n, "x"),
        V(l, s, "y")
    }
    function V(t, e, i) {
        v.lineWidth = ~~(1 * P),
        v.strokeStyle = a.axisColor,
        v.font = 12 * P + "px Arial",
        v.fillStyle = a.color,
        v.textAlign = "x" === i ? "center" : "left";
        var o = P;
        "x" === i ? t.forEach((function(t, i) {
            v.beginPath(),
            v.lineTo(_(t), D + o),
            v.lineTo(_(t), (w + 4) * P + o),
            v.stroke(),
            v.closePath(),
            v.fillText(e[i].toFixed(a.priceFix), _(t), (w + 20) * P + o)
        }
        )) : t.forEach((function(t, i) {
            var r = e[i] <= 1e3 ? e[i].toFixed(0) : function(t, e) {
                var i = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 1e3
                  , o = A[i].reduce((function(t, e) {
                    return t[0] > i && (t[0] /= i,
                    t[1] = e),
                    t
                }
                ), [t, ""])
                  , r = e;
                return M(o[0]) === M(i) - 1 && (r -= 1),
                o[0] = o[0].toFixed(r),
                o.join("")
            }(e[i], 2, 1e3);
            v.beginPath(),
            v.lineTo(j + o, _(t + o)),
            v.lineTo((T + 4) * P + o, _(t + o)),
            v.stroke(),
            v.fillText(r, (T + 8) * P + o, _(t + 4 * P)),
            v.closePath()
        }
        ))
    }
    function _(t) {
        return .5 + ~~t
    }
    function G(t) {
        if (0 === t)
            return D - P;
        var e = L - L * t / p + O;
        return e - D < ~~(v.lineWidth * P) ? e - ~~(v.lineWidth * P) : e
    }
    function U(t) {
        t.clearRect(0, 0, C * P, m * P)
    }
    function $() {
        var t = g[0] && g[0][0] || 0
          , e = u[u.length - 1] && u[u.length - 1][0] || 0;
        return {
            pMin: 1 * t,
            pMax: 1 * e,
            pTick: (e - t) / j,
            aTick: p / L
        }
    }
    function q() {
        U(v),
        W(),
        I(),
        z(),
        H()
    }
    return x.addEventListener("mousemove", (function(t) {
        var e = function(t) {
            var e = b.getBoundingClientRect();
            return {
                x: (t.clientX - e.left) * P,
                y: (t.clientY - e.top) * P
            }
        }(t);
        y && E(e.x, e.y)
    }
    ), !1),
    x.addEventListener("mouseout", (function(t) {
        setTimeout((function() {
            return U(k)
        }
        ), 500),
        h = null
    }
    ), !1),
    {
        update: q,
        putData: function(t) {
            U(v),
            U(k),
            function(t) {
                var e = []
                  , i = [];
                t.asks.forEach((function(t, i) {
                    var o = [];
                    o.push(t[0]),
                    i - 1 > -1 ? o.push(1 * t[1] + 1 * e[i - 1][1]) : o.push(t[1]),
                    e.push(o)
                }
                ));
                var o = e[e.length - 1] ? e[e.length - 1][1] : 0;
                t.bids.forEach((function(t, e) {
                    var o = [];
                    o.push(t[0]),
                    e - 1 > -1 ? o.push(1 * t[1] + 1 * i[e - 1][1]) : o.push(t[1]),
                    i.push(o)
                }
                ));
                var r = i[i.length - 1] ? i[i.length - 1][1] : 0;
                p = Math.max(r, o),
                u = e,
                g = i.reverse()
            }(t),
            W(),
            I(),
            z(),
            H(),
            E()
        },
        forceUpdate: function() {
            C = f.offsetWidth,
            m = f.offsetHeight,
            b.width = x.width = C * P,
            b.height = x.height = m * P,
            b.style.width = x.style.width = C + "px",
            b.style.height = x.style.height = m + "px",
            T = C - a.ruleWidth,
            w = m - a.ruleHeight,
            S = ~~(m * a.paddingTop / 100),
            O = S * P,
            L = (F = w - S) * P,
            j = T * P,
            D = w * P,
            y = !0,
            U(k),
            q()
        },
        initTheme: l,
        reload: function(t) {
            Object.assign(a, t)
        }
    }
  }
}