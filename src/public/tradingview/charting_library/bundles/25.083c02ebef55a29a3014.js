(window.webpackJsonp=window.webpackJsonp||[]).push([[25],{"1Kfe":function(e,t,o){e.exports={container:"container-TZggBcGZ",sectionTitle:"sectionTitle-TZggBcGZ",separator:"separator-TZggBcGZ",customButton:"customButton-TZggBcGZ"}},"9dlw":function(e,t,o){"use strict";o.d(t,"a",(function(){return d}));var n=o("mrSG"),s=o("q1tI"),i=o.n(s),r=o("i8i4"),a=o.n(r),c=(o("EsMY"),o("AiMB")),l=o("DTHj"),u=o("X0gx"),h=o("8Rai");function d(e){const{controller:t,children:o,isOpened:r,closeOnClickOutside:d=!0,doNotCloseOn:p,onClickOutside:m,onClose:v}=e,f=Object(n.a)(e,["controller","children","isOpened","closeOnClickOutside","doNotCloseOn","onClickOutside","onClose"]),g=Object(s.useContext)(u.a),b=Object(h.a)({handler:function(e){m&&m(e);if(!d)return;if(p&&e.target instanceof Node){const t=a.a.findDOMNode(p);if(t instanceof Node&&t.contains(e.target))return}v()},mouseDown:!0,touchStart:!0});return r?i.a.createElement(c.a,{top:"0",left:"0",right:"0",bottom:"0",pointerEvents:"none"},i.a.createElement("span",{ref:b,style:{pointerEvents:"auto"}},i.a.createElement(l.b,Object.assign({},f,{isOpened:r,onClose:v,onScroll:function(t){const{onScroll:o}=e;o&&o(t);t.stopPropagation()},customCloseDelegate:g,ref:t}),o))):null}},DXuF:function(e,t,o){e.exports={swatches:"swatches-vBKBthtD",swatch:"swatch-vBKBthtD",hover:"hover-vBKBthtD",empty:"empty-vBKBthtD",white:"white-vBKBthtD",selected:"selected-vBKBthtD",contextItem:"contextItem-vBKBthtD"}},N5tr:function(e,t,o){"use strict";o.d(t,"a",(function(){return h})),o.d(t,"b",(function(){return m}));var n=o("mrSG"),s=o("q1tI"),i=o.n(s),r=o("TSYQ"),a=o("tWVy"),c=o("JWMC"),l=o("ijHL"),u=o("v1bN");const h=u;function d(e){const{reference:t}=e,o=Object(n.a)(e,["reference"]),s=Object.assign(Object.assign({},o),{ref:t});return i.a.createElement(e.href?"a":"div",s)}function p(e){e.stopPropagation()}function m(e){const{id:t,role:o,"aria-selected":n,className:h,title:m,labelRowClassName:v,labelClassName:f,shortcut:g,forceShowShortcuts:b,icon:C,isActive:_,isDisabled:w,isHovered:E,appearAsDisabled:y,label:O,link:S,showToolboxOnHover:N,target:k,toolbox:T,reference:j,onMouseOut:I,onMouseOver:P,theme:x=u}=e,M=Object(l.b)(e),D=Object(s.useRef)(null);return i.a.createElement(d,Object.assign({},M,{id:t,role:o,"aria-selected":n,className:r(h,x.item,C&&x.withIcon,{[x.isActive]:_,[x.isDisabled]:w||y,[x.hovered]:E}),title:m,href:S,target:k,reference:function(e){D.current=e,"function"==typeof j&&j(e);"object"==typeof j&&(j.current=e)},onClick:function(t){const{dontClosePopup:o,onClick:n,onClickArg:s,trackEventObject:i}=e;if(w)return;i&&Object(c.trackEvent)(i.category,i.event,i.label);n&&n(s,t);o||Object(a.b)()},onContextMenu:function(t){const{trackEventObject:o,trackRightClick:n}=e;o&&n&&Object(c.trackEvent)(o.category,o.event,o.label+"_rightClick")},onMouseUp:function(t){const{trackEventObject:o,trackMouseWheelClick:n}=e;if(1===t.button&&S&&o){let e=o.label;n&&(e+="_mouseWheelClick"),Object(c.trackEvent)(o.category,o.event,e)}},onMouseOver:P,onMouseOut:I}),void 0!==C&&i.a.createElement("div",{className:x.icon,
dangerouslySetInnerHTML:{__html:C}}),i.a.createElement("div",{className:r(x.labelRow,v)},i.a.createElement("div",{className:r(x.label,f)},O)),(void 0!==g||b)&&i.a.createElement("div",{className:x.shortcut},(B=g)&&B.split("+").join(" + ")),void 0!==T&&i.a.createElement("div",{onClick:p,className:r(x.toolbox,{[x.showOnHover]:N})},T));var B}},Oqo1:function(e,t,o){e.exports={opacity:"opacity-2UqCUhku",opacitySlider:"opacitySlider-2UqCUhku",opacitySliderGradient:"opacitySliderGradient-2UqCUhku",pointer:"pointer-2UqCUhku",dragged:"dragged-2UqCUhku",opacityPointerWrap:"opacityPointerWrap-2UqCUhku",opacityInputWrap:"opacityInputWrap-2UqCUhku",opacityInput:"opacityInput-2UqCUhku",opacityInputPercent:"opacityInputPercent-2UqCUhku"}},"R4+T":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 8" width="16" height="8"><path fill="currentColor" d="M0 1.475l7.396 6.04.596.485.593-.49L16 1.39 14.807 0 7.393 6.122 8.58 6.12 1.186.08z"/></svg>'},U1eG:function(e,t,o){"use strict";o.d(t,"a",(function(){return c}));var n=o("q1tI"),s=o("TSYQ"),i=o("Eyy1"),r=o("Hr11"),a=o("Oqo1");class c extends n.PureComponent{constructor(e){super(e),this._container=null,this._pointer=null,this._raf=null,this._refContainer=e=>{this._container=e},this._refPointer=e=>{this._pointer=e},this._handlePosition=e=>{null===this._raf&&(this._raf=requestAnimationFrame(()=>{const t=Object(i.ensureNotNull)(this._container),o=Object(i.ensureNotNull)(this._pointer),n=t.getBoundingClientRect(),s=o.offsetWidth,a=e.clientX-s/2-n.left,c=Object(r.clamp)(a/(n.width-s),0,1);this.setState({inputOpacity:Math.round(100*c).toString()}),this.props.onChange(c),this._raf=null}))},this._onSliderClick=e=>{this._handlePosition(e.nativeEvent),this._dragSubscribe()},this._mouseUp=e=>{this.setState({isPointerDragged:!1}),this._dragUnsubscribe(),this._handlePosition(e)},this._mouseMove=e=>{this.setState({isPointerDragged:!0}),this._handlePosition(e)},this._onTouchStart=e=>{this._handlePosition(e.nativeEvent.touches[0])},this._handleTouch=e=>{this.setState({isPointerDragged:!0}),this._handlePosition(e.nativeEvent.touches[0])},this._handleTouchEnd=()=>{this.setState({isPointerDragged:!1})},this._handleInput=e=>{const t=e.currentTarget.value,o=Number(t)/100;this.setState({inputOpacity:t}),Number.isNaN(o)||o>1||this.props.onChange(o)},this.state={inputOpacity:Math.round(100*e.opacity).toString(),isPointerDragged:!1}}componentWillUnmount(){null!==this._raf&&(cancelAnimationFrame(this._raf),this._raf=null),this._dragUnsubscribe()}render(){const{color:e,opacity:t,hideInput:o}=this.props,{inputOpacity:i,isPointerDragged:r}=this.state,c={color:e||void 0};return n.createElement("div",{className:a.opacity},n.createElement("div",{className:a.opacitySlider,style:c,ref:this._refContainer,onMouseDown:this._onSliderClick,onTouchStart:this._onTouchStart,onTouchMove:this._handleTouch,onTouchEnd:this._handleTouchEnd},n.createElement("div",{className:a.opacitySliderGradient,style:{backgroundImage:`linear-gradient(90deg, transparent, ${e})`}}),n.createElement("div",{
className:a.opacityPointerWrap},n.createElement("div",{className:s(a.pointer,r&&a.dragged),style:{left:100*t+"%"},ref:this._refPointer}))),!o&&n.createElement("div",{className:a.opacityInputWrap},n.createElement("input",{type:"text",className:a.opacityInput,value:i,onChange:this._handleInput}),n.createElement("span",{className:a.opacityInputPercent},"%")))}_dragSubscribe(){const e=Object(i.ensureNotNull)(this._container).ownerDocument;e&&(e.addEventListener("mouseup",this._mouseUp),e.addEventListener("mousemove",this._mouseMove))}_dragUnsubscribe(){const e=Object(i.ensureNotNull)(this._container).ownerDocument;e&&(e.removeEventListener("mousemove",this._mouseMove),e.removeEventListener("mouseup",this._mouseUp))}}},V3OP:function(e,t,o){"use strict";var n=o("q1tI"),s=o("Vdly"),i=o("FQhm");function r(e,t){Object(n.useEffect)(()=>(i.subscribe(e,t,null),()=>{i.unsubscribe(e,t,null)}),[e,t])}var a=o("eJTA");function c(){const[e,t]=Object(n.useState)(Object(s.getJSON)("pickerCustomColors",[]));r("add_new_custom_color",o=>t(l(o,e))),r("remove_custom_color",o=>t(u(o,e)));const o=Object(n.useCallback)(t=>{const o=t?Object(a.parseRgb)(t):null;e.some(e=>null!==e&&null!==o&&Object(a.areEqualRgb)(Object(a.parseRgb)(e),o))||(i.emit("add_new_custom_color",t),Object(s.setJSON)("pickerCustomColors",l(t,e)))},[e]),c=Object(n.useCallback)(t=>{(t>=0||t<e.length)&&(i.emit("remove_custom_color",t),Object(s.setJSON)("pickerCustomColors",u(t,e)))},[e]);return[e,o,c]}function l(e,t){const o=t.slice();return o.push(e),o.length>29&&o.shift(),o}function u(e,t){return t.filter((t,o)=>e!==o)}o.d(t,"a",(function(){return c}))},htM8:function(e,t,o){"use strict";var n=o("YFKU"),s=o("q1tI"),i=o.n(s),r=o("TSYQ"),a=o.n(r),c=o("eJTA"),l=o("Eyy1"),u=o("qFKp"),h=o("9dlw"),d=o("N5tr");const p=s.createContext(void 0);var m=o("wLjq"),v=o("aVjL"),f=o("DXuF");function g(e){const{index:t,color:o,selected:a,onSelect:c}=e,[g,b]=Object(s.useState)(!1),C=Object(s.useContext)(p),_=Object(s.useRef)(null),w=Boolean(C)&&!u.CheckMobile.any();return i.a.createElement(i.a.Fragment,null,i.a.createElement("div",{ref:_,style:o?{color:o}:void 0,className:r(f.swatch,g&&f.hover,a&&f.selected,!o&&f.empty,String(o).toLowerCase()===m.c&&f.white),onClick:function(){c(o)},onContextMenu:w?E:void 0}),w&&i.a.createElement(h.a,{isOpened:g,onClose:E,position:function(){const e=Object(l.ensureNotNull)(_.current).getBoundingClientRect();return{x:e.left,y:e.top+e.height+4}},onClickOutside:E},i.a.createElement(d.b,{className:f.contextItem,label:Object(n.t)("Remove color"),icon:v,onClick:function(){E(),Object(l.ensureDefined)(C)(t)},dontClosePopup:!0})));function E(){b(!g)}}class b extends s.PureComponent{constructor(){super(...arguments),this._onSelect=e=>{const{onSelect:t}=this.props;t&&t(e)}}render(){const{colors:e,color:t,children:o}=this.props;if(!e)return null;const n=t?Object(c.parseRgb)(String(t)):void 0;return s.createElement("div",{className:f.swatches},e.map((e,t)=>s.createElement(g,{key:String(e)+t,index:t,color:e,selected:n&&Object(c.areEqualRgb)(n,Object(c.parseRgb)(String(e))),
onSelect:this._onSelect})),o)}}var C=o("U1eG"),_=o("mwqF");function w(e){const t="Invalid RGB color: "+e;if(null===e)throw new Error(t);const o=e.match(/^#?([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})$/i);if(null===o)throw new Error(t);const[,n,s,i]=o;if(!n||!s||!i)throw new Error(t);const r=parseInt(n,16)/255,a=parseInt(s,16)/255,c=parseInt(i,16)/255,l=Math.max(r,a,c),u=Math.min(r,a,c);let h;const d=l,p=l-u,m=0===l?0:p/l;if(l===u)h=0;else{switch(l){case r:h=(a-c)/p+(a<c?6:0);break;case a:h=(c-r)/p+2;break;case c:h=(r-a)/p+4;break;default:h=0}h/=6}return{h,s:m,v:d}}var E=o("UXvI"),y=o("lY1a");class O extends s.PureComponent{constructor(){super(...arguments),this._container=null,this._refContainer=e=>{this._container=e},this._handlePosition=e=>{const{hsv:{h:t},onChange:o}=this.props;if(!o)return;const n=Object(l.ensureNotNull)(this._container).getBoundingClientRect(),s=e.clientX-n.left,i=e.clientY-n.top;let r=s/n.width;r<0?r=0:r>1&&(r=1);let a=1-i/n.height;a<0?a=0:a>1&&(a=1),o({h:t,s:r,v:a})},this._mouseDown=e=>{window.addEventListener("mouseup",this._mouseUp),window.addEventListener("mousemove",this._mouseMove)},this._mouseUp=e=>{window.removeEventListener("mousemove",this._mouseMove),window.removeEventListener("mouseup",this._mouseUp),this._handlePosition(e)},this._mouseMove=Object(E.default)(this._handlePosition,100),this._handleTouch=e=>{this._handlePosition(e.nativeEvent.touches[0])}}render(){const{className:e,hsv:{h:t,s:o,v:n}}=this.props,i=`hsl(${360*t}, 100%, 50%)`;return s.createElement("div",{className:a()(y.saturation,e),style:{backgroundColor:i},ref:this._refContainer,onMouseDown:this._mouseDown,onTouchStart:this._handleTouch,onTouchMove:this._handleTouch},s.createElement("div",{className:y.pointer,style:{left:100*o+"%",top:100*(1-n)+"%"}}))}}var S=o("jpE+");class N extends s.PureComponent{constructor(){super(...arguments),this._container=null,this._refContainer=e=>{this._container=e},this._handlePosition=e=>{const{hsv:{s:t,v:o},onChange:n}=this.props;if(!n)return;const s=Object(l.ensureNotNull)(this._container).getBoundingClientRect();let i=(e.clientY-s.top)/s.height;i<0?i=0:i>1&&(i=1),n({h:i,s:t,v:o})},this._mouseDown=e=>{window.addEventListener("mouseup",this._mouseUp),window.addEventListener("mousemove",this._mouseMove)},this._mouseUp=e=>{window.removeEventListener("mousemove",this._mouseMove),window.removeEventListener("mouseup",this._mouseUp),this._handlePosition(e)},this._mouseMove=Object(E.default)(this._handlePosition,100),this._handleTouch=e=>{this._handlePosition(e.nativeEvent.touches[0])}}render(){const{className:e,hsv:{h:t}}=this.props;return s.createElement("div",{className:a()(S.hue,e)},s.createElement("div",{className:S.pointerContainer,ref:this._refContainer,onMouseDown:this._mouseDown,onTouchStart:this._handleTouch,onTouchMove:this._handleTouch},s.createElement("div",{className:S.pointer,style:{top:100*t+"%"}})))}}var k=o("uJfL");const T=window.t("Add",{context:"Color Picker"});class j extends s.PureComponent{constructor(e){super(e),this._handleHSV=e=>{const t=function(e){const{h:t,s:o,v:n}=e
;let s,i,r;const a=Math.floor(6*t),c=6*t-a,l=n*(1-o),u=n*(1-c*o),h=n*(1-(1-c)*o);switch(a%6){case 0:s=n,i=h,r=l;break;case 1:s=u,i=n,r=l;break;case 2:s=l,i=n,r=h;break;case 3:s=l,i=u,r=n;break;case 4:s=h,i=l,r=n;break;case 5:s=n,i=l,r=u;break;default:s=0,i=0,r=0}return"#"+[255*s,255*i,255*r].map(e=>("0"+Math.round(e).toString(16)).replace(/.+?([a-f0-9]{2})$/i,"$1")).join("")}(e)||"#000000";this.setState({color:t,inputColor:t.replace(/^#/,""),hsv:e}),this.props.onSelect(t)},this._handleInput=e=>{const t=e.currentTarget.value;try{const e=w(t),o="#"+t;this.setState({color:o,inputColor:t,hsv:e}),this.props.onSelect(o)}catch(o){this.setState({inputColor:t})}},this._handleAddColor=()=>this.props.onAdd(this.state.color);const t=e.color||"#000000";this.state={color:t,inputColor:t.replace(/^#/,""),hsv:w(t)}}render(){const{color:e,hsv:t,inputColor:o}=this.state;return s.createElement("div",{className:k.container},s.createElement("div",{className:k.form},s.createElement("div",{className:k.swatch,style:{backgroundColor:e}}),s.createElement("div",{className:k.inputWrap},s.createElement("span",{className:k.inputHash},"#"),s.createElement("input",{type:"text",className:k.input,value:o,onChange:this._handleInput})),s.createElement("div",{className:k.buttonWrap},s.createElement(_.a,{size:"s",onClick:this._handleAddColor},T))),s.createElement("div",{className:k.hueSaturationWrap},s.createElement(O,{className:k.saturation,hsv:t,onChange:this._handleHSV}),s.createElement(N,{className:k.hue,hsv:t,onChange:this._handleHSV})))}}var I=o("1Kfe");o.d(t,"a",(function(){return M}));const P=window.t("Add Custom Color",{context:"Color Picker"}),x=window.t("Opacity",{context:"Color Picker"});class M extends s.PureComponent{constructor(e){super(e),this._handleAddColor=e=>{this.setState({isCustom:!1}),this._onToggleCustom(!1);const{onAddColor:t}=this.props;t&&t(e)},this._handleSelectColor=e=>{const{onColorChange:t}=this.props,{isCustom:o}=this.state;t&&t(e,o)},this._handleCustomClick=()=>{this.setState({isCustom:!0}),this._onToggleCustom(!0)},this._handleOpacity=e=>{const{onOpacityChange:t}=this.props;t&&t(e)},this.state={isCustom:!1}}componentDidUpdate(e,t){e.selectOpacity!==this.props.selectOpacity&&this.props.menu&&this.props.menu.update()}render(){const{color:e,opacity:t,selectCustom:o,selectOpacity:n,customColors:i,onRemoveCustomColor:r}=this.props,{isCustom:c}=this.state,l="number"==typeof t?t:1;return c?s.createElement(j,{color:e,onSelect:this._handleSelectColor,onAdd:this._handleAddColor}):s.createElement("div",{className:I.container},s.createElement(b,{colors:m.a,color:e,onSelect:this._handleSelectColor}),s.createElement(b,{colors:m.b,color:e,onSelect:this._handleSelectColor}),s.createElement("div",{className:I.separator}),s.createElement(p.Provider,{value:r},s.createElement(b,{colors:i,color:e,onSelect:this._handleSelectColor},o&&s.createElement("div",{className:a()(I.customButton,"apply-common-tooltip"),onClick:this._handleCustomClick,title:P}))),n&&s.createElement(s.Fragment,null,s.createElement("div",{className:I.sectionTitle
},x),s.createElement(C.a,{color:e,opacity:l,onChange:this._handleOpacity})))}_onToggleCustom(e){const{onToggleCustom:t}=this.props;t&&t(e)}}},"jpE+":function(e,t,o){e.exports={hue:"hue-1Mi0KyO-",pointer:"pointer-1Mi0KyO-",pointerContainer:"pointerContainer-1Mi0KyO-"}},lY1a:function(e,t,o){e.exports={saturation:"saturation-2uNV-KY0",pointer:"pointer-2uNV-KY0"}},uJfL:function(e,t,o){e.exports={container:"container-1r82-bI2",form:"form-1r82-bI2",swatch:"swatch-1r82-bI2",inputWrap:"inputWrap-1r82-bI2",inputHash:"inputHash-1r82-bI2",input:"input-1r82-bI2",buttonWrap:"buttonWrap-1r82-bI2",hueSaturationWrap:"hueSaturationWrap-1r82-bI2",saturation:"saturation-1r82-bI2",hue:"hue-1r82-bI2"}},v1bN:function(e,t,o){e.exports={"tablet-small-breakpoint":"screen and (max-width: 428px)",item:"item-2IihgTnv",hovered:"hovered-2IihgTnv",isDisabled:"isDisabled-2IihgTnv",isActive:"isActive-2IihgTnv",shortcut:"shortcut-2IihgTnv",toolbox:"toolbox-2IihgTnv",withIcon:"withIcon-2IihgTnv",icon:"icon-2IihgTnv",labelRow:"labelRow-2IihgTnv",label:"label-2IihgTnv",showOnHover:"showOnHover-2IihgTnv"}},wLjq:function(e,t,o){"use strict";o.d(t,"c",(function(){return s})),o.d(t,"a",(function(){return a})),o.d(t,"b",(function(){return l}));var n=o("HGP3");const s=n.a["color-white"],i=["ripe-red","tan-orange","banana-yellow","iguana-green","minty-green","sky-blue","tv-blue","deep-blue","grapes-purple","berry-pink"],r=[200,300,400,500,600,700,800,900].map(e=>"color-cold-gray-"+e);r.unshift("color-white"),r.push("color-black"),i.forEach(e=>{r.push(`color-${e}-500`)});const a=r.map(e=>n.a[e]),c=[];[100,200,300,400,700,900].forEach(e=>{i.forEach(t=>{c.push(`color-${t}-${e}`)})});const l=c.map(e=>n.a[e])},"x0D+":function(e,t,o){var n,s,i;s=[t],void 0===(i="function"==typeof(n=function(e){"use strict";function t(e){if(Array.isArray(e)){for(var t=0,o=Array(e.length);t<e.length;t++)o[t]=e[t];return o}return Array.from(e)}Object.defineProperty(e,"__esModule",{value:!0});var o=!1;if("undefined"!=typeof window){var n={get passive(){o=!0}};window.addEventListener("testPassive",null,n),window.removeEventListener("testPassive",null,n)}var s="undefined"!=typeof window&&window.navigator&&window.navigator.platform&&/iP(ad|hone|od)/.test(window.navigator.platform),i=[],r=!1,a=-1,c=void 0,l=void 0,u=function(e){return i.some((function(t){return!(!t.options.allowTouchMove||!t.options.allowTouchMove(e))}))},h=function(e){var t=e||window.event;return!!u(t.target)||1<t.touches.length||(t.preventDefault&&t.preventDefault(),!1)},d=function(){setTimeout((function(){void 0!==l&&(document.body.style.paddingRight=l,l=void 0),void 0!==c&&(document.body.style.overflow=c,c=void 0)}))};e.disableBodyScroll=function(e,n){if(s){if(!e)return void console.error("disableBodyScroll unsuccessful - targetElement must be provided when calling disableBodyScroll on IOS devices.");if(e&&!i.some((function(t){return t.targetElement===e}))){var d={targetElement:e,options:n||{}};i=[].concat(t(i),[d]),e.ontouchstart=function(e){1===e.targetTouches.length&&(a=e.targetTouches[0].clientY)},
e.ontouchmove=function(t){var o,n,s,i;1===t.targetTouches.length&&(n=e,i=(o=t).targetTouches[0].clientY-a,!u(o.target)&&(n&&0===n.scrollTop&&0<i||(s=n)&&s.scrollHeight-s.scrollTop<=s.clientHeight&&i<0?h(o):o.stopPropagation()))},r||(document.addEventListener("touchmove",h,o?{passive:!1}:void 0),r=!0)}}else{m=n,setTimeout((function(){if(void 0===l){var e=!!m&&!0===m.reserveScrollBarGap,t=window.innerWidth-document.documentElement.clientWidth;e&&0<t&&(l=document.body.style.paddingRight,document.body.style.paddingRight=t+"px")}void 0===c&&(c=document.body.style.overflow,document.body.style.overflow="hidden")}));var p={targetElement:e,options:n||{}};i=[].concat(t(i),[p])}var m},e.clearAllBodyScrollLocks=function(){s?(i.forEach((function(e){e.targetElement.ontouchstart=null,e.targetElement.ontouchmove=null})),r&&(document.removeEventListener("touchmove",h,o?{passive:!1}:void 0),r=!1),i=[],a=-1):(d(),i=[])},e.enableBodyScroll=function(e){if(s){if(!e)return void console.error("enableBodyScroll unsuccessful - targetElement must be provided when calling enableBodyScroll on IOS devices.");e.ontouchstart=null,e.ontouchmove=null,i=i.filter((function(t){return t.targetElement!==e})),r&&0===i.length&&(document.removeEventListener("touchmove",h,o?{passive:!1}:void 0),r=!1)}else 1===i.length&&i[0].targetElement===e?(d(),i=[]):i=i.filter((function(t){return t.targetElement!==e}))}})?n.apply(t,s):n)||(e.exports=i)}}]);