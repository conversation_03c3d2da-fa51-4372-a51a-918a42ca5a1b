(window.webpackJsonp=window.webpackJsonp||[]).push([[51],{"+ByK":function(e,t,n){e.exports={itemWrap:"itemWrap-3FEBD9eP",item:"item-3FEBD9eP",icon:"icon-3FEBD9eP",selected:"selected-3FEBD9eP",label:"label-3FEBD9eP"}},"0lS6":function(e,t,n){e.exports={wrapper:"wrapper-2ESZuAbX",isActive:"isActive-2ESZuAbX"}},"4Fxa":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M12.143 20l1.714-12H12V7h5v1h-2.143l-1.714 12H15v1h-5v-1h2.143z"/></svg>'},"6w4h":function(e,t,n){e.exports={row:"row-NcPpqR9x",wrap:"wrap-NcPpqR9x",breakpointNormal:"breakpointNormal-NcPpqR9x",breakpointMedium:"breakpointMedium-NcPpqR9x",breakpointSmall:"breakpointSmall-NcPpqR9x"}},"7EmB":function(e,t,n){e.exports={range:"range-2PdPYA_J",valueInput:"valueInput-2PdPYA_J",rangeSlider:"rangeSlider-2PdPYA_J",input:"input-2PdPYA_J"}},"7Y2P":function(e,t,n){e.exports={wrapper:"wrapper-1MlnSDA4",focused:"focused-1MlnSDA4",readonly:"readonly-1MlnSDA4",disabled:"disabled-1MlnSDA4","size-small":"size-small-1MlnSDA4","size-medium":"size-medium-1MlnSDA4","size-large":"size-large-1MlnSDA4","font-size-small":"font-size-small-1MlnSDA4","font-size-medium":"font-size-medium-1MlnSDA4","font-size-large":"font-size-large-1MlnSDA4","border-none":"border-none-1MlnSDA4",shadow:"shadow-1MlnSDA4","border-thin":"border-thin-1MlnSDA4","border-thick":"border-thick-1MlnSDA4","intent-default":"intent-default-1MlnSDA4","intent-success":"intent-success-1MlnSDA4","intent-warning":"intent-warning-1MlnSDA4","intent-danger":"intent-danger-1MlnSDA4","intent-primary":"intent-primary-1MlnSDA4","corner-top-left":"corner-top-left-1MlnSDA4","corner-top-right":"corner-top-right-1MlnSDA4","corner-bottom-right":"corner-bottom-right-1MlnSDA4","corner-bottom-left":"corner-bottom-left-1MlnSDA4",childrenContainer:"childrenContainer-1MlnSDA4"}},"8XTa":function(e,t,n){e.exports={lineEndSelect:"lineEndSelect-1x0HNmOc",right:"right-1x0HNmOc"}},"9UfQ":function(e,t,n){e.exports={wrapper:"wrapper-2F4fv0AC",checkbox:"checkbox-2F4fv0AC",colorSelect:"colorSelect-2F4fv0AC"}},"9gev":function(e,t,n){e.exports={dropdown:"dropdown-3Z3YBwWw",normal:"normal-3Z3YBwWw",big:"big-3Z3YBwWw",dropdownMenu:"dropdownMenu-3Z3YBwWw"}},"9pqQ":function(e,t,n){e.exports={wrapper:"wrapper-3G6q-JGM"}},A3oJ:function(e,t){
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M22.87 6.44c.09-.78-.53-1.4-1.3-1.31-1.43.15-3.43.48-5.42 1.2a11.8 11.8 0 0 0-5.23 3.44L9.86 11.9l6.24 6.24 2.13-1.06a11.8 11.8 0 0 0 3.44-5.23c.72-1.99 1.05-4 1.2-5.41zm-4.93 11.9l-1.72.86-.04.02h-.04l-2.2.67v.01a19.68 19.68 0 0 0-.13 3.33c.01.14.08.22.17.26.08.04.2.05.32-.03a18.83 18.83 0 0 0 2.79-2.26 8.18 8.18 0 0 0 .44-1.1c.16-.51.33-1.12.41-1.76zm-.44 3.16l.35.35-.01.02-.05.05a16.85 16.85 0 0 1-.83.76c-.54.47-1.3 1.08-2.1 1.61a1.3 1.3 0 0 1-2.05-.98 16.46 16.46 0 0 1 .09-3.08l-.16.05a1.5 1.5 0 0 1-1.53-.36l-3.13-3.13c-.4-.4-.54-1-.36-1.53l.05-.16-.36.04c-.7.06-1.62.11-2.54.06a1.3 1.3 0 0 1-1.13-.8c-.18-.42-.13-.94.17-1.35a87.55 87.55 0 0 1 2.15-2.8l.04-.04v-.02l.4.31-.22-.45.03-.01a5.93 5.93 0 0 1 .34-.16c.23-.1.55-.22.94-.35A9.77 9.77 0 0 1 10.26 9a12.9 12.9 0 0 1 5.55-3.61c2.09-.76 4.18-1.1 5.65-1.26 1.41-.15 2.56 1 2.4 2.41a24.04 24.04 0 0 1-1.25 5.65A12.9 12.9 0 0 1 19 17.74a9.77 9.77 0 0 1-.88 3.61 9.18 9.18 0 0 1-.16.34v.03h-.01l-.45-.22zm0 0l.45.22-.04.08-.06.05-.35-.35zm-11-11l-.4-.31.08-.09.1-.05.22.45zm3.16-.44a9.61 9.61 0 0 0-2.84.84l-.13.16a109.83 109.83 0 0 0-1.97 2.58.4.4 0 0 0-.06.38c.04.1.12.17.27.18a16.05 16.05 0 0 0 3.18-.15l.66-2.2.01-.03.02-.04.86-1.72zm5.4 8.45l-5.57-5.56-.51 1.7-.31.92a.5.5 0 0 0 .12.51l3.13 3.13a.5.5 0 0 0 .5.12l.92-.3h.02l1.7-.52zm-10.91.64l2-2 .7.7-2 2-.7-.7zm0 4l4-4 .7.7-4 4-.7-.7zm4 0l2-2 .7.7-2 2-.7-.7zM16 10.5a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0zM17.5 8a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5z"/></svg>'},Bbdy:function(e,t,n){e.exports={wrapper:"wrapper-30HPnwk9"}},CHgb:function(e,t,n){"use strict";n.d(t,"c",(function(){return u})),n.d(t,"a",(function(){return p})),n.d(t,"b",(function(){return m}));var a=n("mrSG"),i=n("q1tI"),o=n.n(i),r=n("TSYQ"),l=n.n(r),c=n("PECq"),s=n("Iivm"),d=n("+ByK");function u(e){const{menuItemClassName:t}=e,n=Object(a.a)(e,["menuItemClassName"]);return o.a.createElement(c.a,Object.assign({},n,{menuItemClassName:l()(t,d.itemWrap)}))}function p(e){return o.a.createElement("div",{className:l()(d.item,d.selected)},o.a.createElement(s.a,{className:d.icon,icon:e.icon}))}function m(e){return o.a.createElement("div",{className:d.item},o.a.createElement(s.a,{className:l()(d.icon,e.iconClassName),icon:e.icon}),o.a.createElement("div",{className:d.label},e.label))}},CaTF:function(e,t,n){e.exports={colorPicker:"colorPicker-zLgQJ6Yh",fontStyleButton:"fontStyleButton-zLgQJ6Yh",dropdown:"dropdown-zLgQJ6Yh",dropdownMenu:"dropdownMenu-zLgQJ6Yh"}},EJl2:function(e,t,n){e.exports={input:"input-1y54fm74",control:"control-1y54fm74",item:"item-1y54fm74",cell:"cell-1y54fm74",fragmentCell:"fragmentCell-1y54fm74",withTitle:"withTitle-1y54fm74",title:"title-1y54fm74"}},FIOl:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M8.5 13.5a2 2 0 1 1-4 0 2 2 0 0 1 4 0zm0 0H24"/></svg>'},G7lD:function(e,t,n){e.exports={range:"range-31GwrUpb",disabled:"disabled-31GwrUpb",
rangeSlider:"rangeSlider-31GwrUpb",rangeSliderMiddleWrap:"rangeSliderMiddleWrap-31GwrUpb",rangeSliderMiddle:"rangeSliderMiddle-31GwrUpb",dragged:"dragged-31GwrUpb",pointer:"pointer-31GwrUpb",rangePointerWrap:"rangePointerWrap-31GwrUpb"}},HWhk:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path fill="currentColor" fillRule="evenodd" clipRule="evenodd" d="M7.5 13a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM5 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0zm9.5-1.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM12 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0zm9.5-1.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM19 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0z"/></svg>'},Iksw:function(e,t,n){"use strict";n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return i})),n.d(t,"d",(function(){return o})),n.d(t,"b",(function(){return r})),n.d(t,"e",(function(){return s}));var a,i,o,r,l=n("Eyy1");!function(e){e[e.Top=0]="Top",e[e.Bottom=1]="Bottom"}(a||(a={})),function(e){e[e.Left=0]="Left",e[e.Right=1]="Right"}(i||(i={})),function(e){e[e.FromTopToBottom=0]="FromTopToBottom",e[e.FromBottomToTop=1]="FromBottomToTop"}(o||(o={})),function(e){e[e.FromLeftToRight=0]="FromLeftToRight",e[e.FromRightToLeft=1]="FromRightToLeft"}(r||(r={}));const c={verticalAttachEdge:a.Bottom,horizontalAttachEdge:i.Left,verticalDropDirection:o.FromTopToBottom,horizontalDropDirection:r.FromLeftToRight,verticalMargin:0,horizontalMargin:0};function s(e,t){return(n,s)=>{const d=Object(l.ensureNotNull)(e).getBoundingClientRect(),{verticalAttachEdge:u=c.verticalAttachEdge,verticalDropDirection:p=c.verticalDropDirection,horizontalAttachEdge:m=c.horizontalAttachEdge,horizontalDropDirection:h=c.horizontalDropDirection,horizontalMargin:f=c.horizontalMargin,verticalMargin:b=c.verticalMargin}=t,v=u===a.Top?-1*b:b,g=m===i.Right?d.right:d.left,w=u===a.Top?d.top:d.bottom;return{x:g-(h===r.FromRightToLeft?n:0)+f,y:w-(p===o.FromBottomToTop?s:0)+v}}}},J4oI:function(e,t,n){e.exports={lineStyleSelect:"lineStyleSelect-3KjU7hI0"}},JoYF:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M6 14.5C6 9.78 9.78 6 14.5 6c4.72 0 8.5 3.78 8.5 8.5 0 4.72-3.78 8.5-8.5 8.5A8.46 8.46 0 0 1 6 14.5zM14.5 5A9.46 9.46 0 0 0 5 14.5c0 5.28 4.22 9.5 9.5 9.5s9.5-4.22 9.5-9.5S19.78 5 14.5 5zM14 16V9h1v6h4v1h-5z"/></svg>'},"K+KL":function(e,t,n){"use strict";n.d(t,"a",(function(){return h}));var a=n("mrSG"),i=n("q1tI"),o=n.n(i),r=n("ECWH"),l=n("RMU6"),c=n("/3z9"),s=n("AnDN"),d=n("GQPI"),u=n("zS+2"),p=n("UmON");const m={role:"listbox"},h=o.a.forwardRef((e,t)=>{const{id:n,tabIndex:i=0,listboxTabIndex:h=-1,disabled:f,highlight:b,intent:v,children:g,onClick:w,onFocus:E,onBlur:j,listboxAria:O=m}=e,y=Object(a.a)(e,["id","tabIndex","listboxTabIndex","disabled","highlight","intent","children","onClick","onFocus","onBlur","listboxAria"]),{isOpened:S,isFocused:x,highlight:C,intent:N,onOpen:z,close:k,toggle:M,buttonFocusBindings:_,onButtonClick:V,buttonRef:A,listboxRef:T}=Object(u.a)({disabled:f,intent:v,
highlight:b,onFocus:E,onBlur:j,onClick:w}),D=void 0!==n?Object(l.a)(n,"listbox"):void 0,F=Object(d.c)(M),B=Object(d.a)(S,k);return o.a.createElement(s.a,Object.assign({},y,_,{id:n,role:"button",tabIndex:f?-1:i,disabled:f,isOpened:S,isFocused:x,ref:Object(r.a)([A,t]),highlight:C,intent:N,onClose:k,onOpen:z,onClick:V,onKeyDown:function(e){const t=Object(c.hashFromEvent)(e);if(F(t)||B(t))return void e.preventDefault()},listboxAria:O,listboxId:D,listboxTabIndex:S?0:h,listboxReference:T,onListboxKeyDown:function(e){S&&27===Object(c.hashFromEvent)(e)&&(e.stopPropagation(),k())}}),g,o.a.createElement("span",{className:p.invisibleFocusHandler,tabIndex:0,"aria-hidden":!0,onFocus:k}))});h.displayName="DisclosureMenu"},K5B3:function(e,t,n){e.exports={input:"input-1Mm_e7ms"}},KacW:function(e,t,n){"use strict";n.d(t,"a",(function(){return h}));n("YFKU");var a=n("q1tI"),i=n.n(a),o=n("TSYQ"),r=n.n(o),l=n("8Uy/"),c=n("CHgb"),s=n("bQEj"),d=n("UXdH"),u=n("ZSM+"),p=n("J4oI");const m=[{type:l.LINESTYLE_SOLID,icon:s,label:window.t("Line")},{type:l.LINESTYLE_DASHED,icon:d,label:window.t("Dashed Line")},{type:l.LINESTYLE_DOTTED,icon:u,label:window.t("Dotted Line")}];class h extends i.a.PureComponent{render(){const{id:e,lineStyle:t,className:n,lineStyleChange:a,disabled:o,additionalItems:l,allowedLineStyles:s}=this.props;let d=function(e){let t=[...m];return void 0!==e&&(t=t.filter(t=>e.includes(t.type))),t.map(e=>({value:e.type,selectedContent:i.a.createElement(c.a,{icon:e.icon}),content:i.a.createElement(c.b,{icon:e.icon,label:e.label})}))}(s);return l&&(d=[{id:"additional",readonly:!0,content:l},...d]),i.a.createElement(c.c,{id:e,disabled:o,className:r()(p.lineStyleSelect,n),hideArrowButton:!0,items:d,value:t,onChange:a,"data-name":"line-style-select"})}}},ORlR:function(e){e.exports=JSON.parse('{"textarea-container":"textarea-container-1vKcpneM","change-highlight":"change-highlight-3ZF5dCHp","focused":"focused-tusi7NC8","resize-vertical":"resize-vertical-1ddEqhTL","resize-horizontal":"resize-horizontal-2_PnoCKN","resize-both":"resize-both-3tZsc84l","textarea":"textarea-387rVPmq"}')},Px4x:function(e,t,n){},STR1:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M6 14.5C6 9.78 9.78 6 14.5 6c4.72 0 8.5 3.78 8.5 8.5 0 4.72-3.78 8.5-8.5 8.5A8.46 8.46 0 0 1 6 14.5zM14.5 5A9.46 9.46 0 0 0 5 14.5c0 5.28 4.22 9.5 9.5 9.5s9.5-4.22 9.5-9.5S19.78 5 14.5 5zM12 12a1 1 0 1 0 0 2 1 1 0 0 0 0-2zm4 1a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-6 4l-.43.26v.01l.03.03a3.55 3.55 0 0 0 .3.4 5.7 5.7 0 0 0 9.22 0 5.42 5.42 0 0 0 .28-.4l.02-.03v-.01L19 17l-.43-.26v.02a2.45 2.45 0 0 1-.24.32c-.17.21-.43.5-.78.79a4.71 4.71 0 0 1-6.88-.8 4.32 4.32 0 0 1-.23-.31l-.01-.02L10 17z"/></svg>'},Sn4D:function(e,t,n){"use strict";n.d(t,"a",(function(){return h}));var a=n("q1tI"),i=n.n(a),o=n("Eyy1"),r=n("TSYQ"),l=n("x0D+"),c=n("Nkvk"),s=n("AiMB"),d=n("mkWe"),u=n("qFKp"),p=n("X0gx"),m=n("sHQ4");function h(e){
const{position:t,onClose:n,children:h,className:f,theme:b=m}=e,v=Object(o.ensureNotNull)(Object(a.useContext)(d.a)),[g,w]=Object(a.useState)(0),E=Object(a.useRef)(null),j=Object(a.useContext)(p.a);return Object(a.useEffect)(()=>{var e;return null===(e=E.current)||void 0===e||e.focus({preventScroll:!0}),j.subscribe(v,n),Object(c.setFixedBodyState)(!0),u.CheckMobile.iOS()&&Object(l.disableBodyScroll)(Object(o.ensureNotNull)(E.current)),w(v.addDrawer()),()=>{j.unsubscribe(v,n);const e=v.removeDrawer();u.CheckMobile.iOS()&&Object(l.enableBodyScroll)(Object(o.ensureNotNull)(E.current)),0===e&&Object(c.setFixedBodyState)(!1)}},[]),i.a.createElement(s.a,null,i.a.createElement("div",{className:r(m.wrap,m["position"+t])},g===v.currentDrawer&&i.a.createElement("div",{className:m.backdrop,onClick:n}),i.a.createElement("div",{className:r(m.drawer,b.drawer,m["position"+t],f),ref:function(e){E.current=e},tabIndex:-1,onScroll:function(e){e.stopPropagation()},"data-name":e["data-name"]},h)))}},To8B:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"><path fill="currentColor" d="M9.707 9l4.647-4.646-.707-.708L9 8.293 4.354 3.646l-.708.708L8.293 9l-4.647 4.646.708.708L9 9.707l4.646 4.647.708-.707L9.707 9z"/></svg>'},UXdH:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" d="M4 13h5v1H4v-1zM12 13h5v1h-5v-1zM20 13h5v1h-5v-1z"/></svg>'},UXjO:function(e,t,n){"use strict";n.d(t,"a",(function(){return d}));var a=n("q1tI"),i=n.n(a),o=n("TSYQ"),r=n.n(o),l=n("PECq"),c=n("ijHL"),s=n("z1Uu");function d(e){const{id:t,fontSize:n,fontSizes:a=[],className:o,disabled:d,fontSizeChange:u}=e;return i.a.createElement(l.a,Object.assign({id:t,disabled:d,className:r()(o,s.defaultSelect),menuClassName:s.defaultSelect,items:(p=a,p.map(e=>({value:e.value,content:e.title}))),value:n,onChange:u},Object(c.b)(e)));var p}},YV34:function(e,t,n){e.exports={desktopSize:"desktopSize--UxMOnMB",drawer:"drawer--UxMOnMB",menuBox:"menuBox--UxMOnMB"}},ZRxn:function(e,t,n){e.exports={unit:"unit-3YVf8t1O",input:"input-3YVf8t1O",normal:"normal-3YVf8t1O",big:"big-3YVf8t1O",dropdown:"dropdown-3YVf8t1O",dropdownMenu:"dropdownMenu-3YVf8t1O"}},"ZSM+":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="currentColor"><circle cx="9" cy="14" r="1"/><circle cx="4" cy="14" r="1"/><circle cx="14" cy="14" r="1"/><circle cx="19" cy="14" r="1"/><circle cx="24" cy="14" r="1"/></svg>'},ZcEB:function(e,t,n){e.exports={dropdown:"dropdown-T1V4i3sE",menu:"menu-T1V4i3sE"}},aSdR:function(e,t,n){e.exports={coordinates:"coordinates-28UK1YDt",input:"input-28UK1YDt"}},aw5J:function(e,t,n){e.exports={container:"container-1zlYw2UK",active:"active-1zlYw2UK",disabled:"disabled-1zlYw2UK",icon:"icon-1zlYw2UK"}},bQEj:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M4 13.5h20"/></svg>'},bvfV:function(e,t,n){"use strict";var a=n("q1tI"),i=n.n(a),o=n("HSjo"),r=n("++uw"),l=n("Q+1u"),c=n("fktV")
;function s(e){const{id:t,offset:n,disabled:a,checked:o,title:r,children:s}=e;return i.a.createElement(l.a.Row,null,i.a.createElement(l.a.Cell,{placement:"first",verticalAlign:"adaptive",offset:n,"data-section-name":t,colSpan:Boolean(s)?void 0:2,checkableTitle:!0},i.a.createElement(c.a,{name:"is-enabled-"+t,title:r,disabled:a,property:o})),Boolean(s)&&i.a.createElement(l.a.Cell,{placement:"last","data-section-name":t},s))}function d(e){const{definition:{id:t,properties:{checked:n,disabled:a},title:o},offset:l}=e,[c]=Object(r.a)({property:a,defaultValue:!1});return i.a.createElement(s,{id:t,offset:l,checked:n,title:o,disabled:e.disabled||c})}var u=n("TSYQ"),p=n.n(u),m=n("RMU6"),h=n("KacW");function f(e){const{property:t}=e,[n,a]=Object(r.a)({property:t});return i.a.createElement(h.a,Object.assign({},e,{lineStyle:n,lineStyleChange:a}))}var b=n("PECq"),v=n("kJwE");const g=[1,2,3,4];function w(e){const{id:t,value:n,items:a=g,disabled:o,onChange:r}=e;return i.a.createElement(b.a,{id:t,disabled:o,hideArrowButton:!0,className:v.lineWidthSelect,items:(l=a,l.map(e=>({value:e,selectedContent:c(e,!0),content:c(e)}))),value:n,onChange:r,"data-name":"line-width-select"});var l;function c(e,t){const a={borderTopWidth:e};return i.a.createElement("div",{className:v.item},i.a.createElement("div",{className:u(v.bar,{[v.isActive]:e===n&&!t}),style:a}," "))}}function E(e){const{property:t}=e,[n,a]=Object(r.a)({property:t});return i.a.createElement(w,Object.assign({},e,{value:n,onChange:a}))}var j=n("mrSG"),O=n("nc0P"),y=n("Eyy1");function S(e,t,n){const[i,o]=Object(a.useState)(e),r=Object(a.useRef)(i);return Object(a.useEffect)(()=>{o(e)},[e,n]),[i,function(e){r.current=e,o(e)},function(){t(r.current)},function(){r.current=e,o(e)}]}var x=n("/3z9"),C=n("WboT"),N=n("Hr11"),z=n("zXvd"),k=n("qFKp");function M(e){const{property:t}=e,n=Object(j.a)(e,["property"]),[o,l]=Object(a.useState)(performance.now()),[c,s]=Object(r.a)({property:t,handler:()=>l(performance.now())}),d=S(c,s,o);return i.a.createElement(_,Object.assign({},n,{valueHash:o,sharedBuffer:d}))}function _(e){const{sharedBuffer:t,min:n,max:o,step:r}=e,l=Object(j.a)(e,["sharedBuffer","min","max","step"]),[c,s,d,u]=t,p=Object(a.useRef)(null),m=Object(a.useRef)(null),h={flushed:!1};return i.a.createElement(A,Object.assign({},l,{ref:m,onValueChange:function(e,t){s(e),"step"!==t||h.flushed||(d(),h.flushed=!0)},onKeyDown:function(e){if(e.defaultPrevented||h.flushed)return;switch(Object(x.hashFromEvent)(e.nativeEvent)){case 27:u(),h.flushed=!0;break;case 13:e.preventDefault();const t=Object(y.ensureNotNull)(m.current).getClampedValue();null!==t&&(s(t),d(),h.flushed=!0)}},onBlur:function(e){const t=Object(y.ensureNotNull)(p.current);if(!t.contains(document.activeElement)&&!t.contains(e.relatedTarget)){const e=Object(y.ensureNotNull)(m.current).getClampedValue();null===e||h.flushed||(s(e),d(),h.flushed=!0)}},value:c,roundByStep:!1,containerReference:function(e){p.current=e},inputMode:k.CheckMobile.iOS()?void 0:"numeric",min:n,max:o,step:r,stretch:!1}))}const V={mode:"float",
min:-Number.MAX_VALUE,max:Number.MAX_VALUE,step:1,precision:0,inheritPrecisionFromStep:!0};class A extends i.a.PureComponent{constructor(e){super(e),this._selection=null,this._restoreSelection=!1,this._input=null,this._handleSelectionChange=()=>{this._restoreSelection||document.activeElement!==Object(y.ensureNotNull)(this._input)||this._saveSelection(Object(y.ensureNotNull)(this._input))},this._handleInputReference=e=>{this._input=e,this.props.inputReference&&this.props.inputReference(e)},this._onFocus=e=>{this._saveSelection(Object(y.ensureNotNull)(this._input)),this.setState({focused:!0}),this.props.onFocus&&this.props.onFocus(e)},this._onBlur=e=>{this._selection=null,this.setState({displayValue:F(this.props,this.props.value,B(this.props)),focused:!1}),this.props.onBlur&&this.props.onBlur(e)},this._onValueChange=e=>{const t=e.currentTarget,n=t.value,a=function(e,t,n){switch(n){case"integer":return T.test(t)?t:e;case"float":return t=t.replace(/,/g,"."),D.test(t)?t:e}}(this.state.displayValue,n,this.props.mode),i=P(a),o=this._checkValueBoundaries(i);var r,l;this.setState({displayValue:a}),a!==n&&(r=this.state.displayValue,l=(l=a).replace(/,/g,"."),(r=r.replace(/,/g,".")).includes(".")||!l.includes("."))?(this._restoreSelection=!0,this.forceUpdate()):this._saveSelection(t),o.value&&F(this.props,i)===a&&this.props.onValueChange(i,"input")},this._onValueByStepChange=e=>{const{roundByStep:t=!0,step:n=1}=this.props,a=P(this.state.displayValue);if(isNaN(a))return;const i=new O.Big(a),o=new O.Big(n),r=i.mod(o);let l=i.plus(e*n);!r.eq(0)&&t&&(l=l.plus((e>0?0:1)*n).minus(r));const c=Number(l);this._checkValueBoundaries(c).value&&(this.setState({displayValue:F(this.props,c,B(this.props))}),this.props.onValueChange(c,"step"))};const t=I(this.props.value);this.state={value:t,displayValue:F(this.props,t,B(this.props)),focused:!1,valueHash:this.props.valueHash}}componentDidMount(){document.addEventListener("selectionchange",this._handleSelectionChange)}componentWillUnmount(){document.removeEventListener("selectionchange",this._handleSelectionChange)}componentDidUpdate(){const e=Object(y.ensureNotNull)(this._input),t=this._selection;if(null!==t&&this._restoreSelection&&document.activeElement===e){const{start:n,end:a,direction:i}=t;e.setSelectionRange(n,a,i)}this._restoreSelection=!1}render(){return i.a.createElement(C.a,{type:"text",inputMode:this.props.inputMode,name:this.props.name,fontSizeStyle:"medium",value:this.state.displayValue,className:this.props.className,placeholder:this.props.placeholder,disabled:this.props.disabled,stretch:this.props.stretch,onValueChange:this._onValueChange,onValueByStepChange:this._onValueByStepChange,containerReference:this.props.containerReference,inputReference:this._handleInputReference,onClick:this.props.onClick,onFocus:this._onFocus,onBlur:this._onBlur,onKeyDown:this.props.onKeyDown})}getClampedValue(){const{min:e,max:t}=this.props,n=P(this.state.displayValue);return isNaN(n)?null:Object(N.clamp)(n,e,t)}static getDerivedStateFromProps(e,t){const{valueHash:n}=e,a=I(e.value)
;if(t.value!==a||t.valueHash!==n){return{value:a,valueHash:n,displayValue:F(e,a,t.focused&&t.valueHash===n?void 0:B(e))}}return null}_saveSelection(e){const{selectionStart:t,selectionEnd:n,selectionDirection:a}=e;null!==t&&null!==n&&null!==a&&(this._selection={start:t,end:n,direction:a})}_checkValueBoundaries(e){const{min:t,max:n}=this.props;return{value:function(e,t,n){const a=e>=t,i=e<=n;return{passMin:a,passMax:i,pass:a&&i,clamped:Object(N.clamp)(e,t,n)}}(e,t,n).pass}}}A.defaultProps=V;const T=/^-?[0-9]*$/,D=/^(-?([0-9]+\.?[0-9]*)|(-?[0-9]*))$/;function F(e,t,n){return null!==(t=I(t))&&void 0!==n&&(n=Math.max(R(t),n)),function(e,t){if(null===e)return"";return new z.NumericFormatter(t).format(e)}(t,n)}function B(e){let t=0;return e.inheritPrecisionFromStep&&e.step<=1&&(t=R(e.step)),Math.max(e.precision,t)||void 0}function R(e){const t=Math.trunc(e).toString();return Object(N.clamp)(z.NumericFormatter.formatNoE(e).length-t.length-1,0,15)}function P(e,t){return new z.NumericFormatter(t).parse(e)}function I(e){return"number"==typeof e&&Number.isFinite(e)?e:null}var L=n("eJTA"),U=n("7MId"),H=n("Tmoa");function Y(e){const{color:t,thickness:n,thicknessItems:a,noAlpha:o}=e,[l,c]=Object(r.a)({property:t}),[s,d]=Object(r.a)(n?{property:n}:{defaultValue:void 0});return i.a.createElement(U.a,Object.assign({},e,{color:function(){if(!l)return null;return Object(L.rgbToHexString)(Object(L.parseRgb)(l))}(),onColorChange:function(e){const t=l?Object(H.alphaToTransparency)(Object(L.parseRgba)(l)[3]):0;c(Object(H.generateColor)(String(e),t,!0))},thickness:s,thicknessItems:a,onThicknessChange:d,opacity:o?void 0:l?Object(L.parseRgba)(l)[3]:void 0,onOpacityChange:o?void 0:function(e){c(Object(H.generateColor)(l,Object(H.alphaToTransparency)(e),!0))}}))}var W=n("YFKU"),q=n("a7Ha"),J=n("CHgb"),K=n("ijHL"),G=n("FIOl"),X=n("jAqK"),Q=n("8XTa");const Z=[{type:q.LineEnd.Normal,icon:G,label:window.t("Normal")},{type:q.LineEnd.Arrow,icon:X,label:window.t("Arrow")}];class $ extends i.a.PureComponent{constructor(e){super(e),this._items=[],this._items=Z.map(t=>({value:t.type,selectedContent:i.a.createElement(J.a,{icon:t.icon}),content:i.a.createElement(J.b,{icon:t.icon,iconClassName:p()(e.isRight&&Q.right),label:t.label})}))}render(){const{id:e,lineEnd:t,className:n,lineEndChange:a,isRight:o,disabled:r}=this.props;return i.a.createElement(J.c,Object.assign({id:e,disabled:r,className:p()(Q.lineEndSelect,o&&Q.right,n),items:this._items,value:t,onChange:a,hideArrowButton:!0},Object(K.b)(this.props)))}}function ee(e){const{property:t}=e,[n,a]=Object(r.a)({property:t});return i.a.createElement($,Object.assign({},e,{lineEnd:n,lineEndChange:a}))}var te=n("xpzh"),ne=n("6w4h");function ae(e){const{children:t,className:n,breakPoint:i="Normal"}=e;return a.createElement(te.a,{className:u(ne.wrap,n,ne["breakpoint"+i])},a.Children.map(t,e=>a.isValidElement(e)?a.createElement("span",{key:null===e.key?void 0:e.key,className:ne.row},e):e))}const ie={1:"float",0:"integer"};var oe=n("vqb8"),re=n("eU7S");function le(e){
const{definition:{id:t,properties:{checked:n,disabled:o,leftEnd:l,rightEnd:c,value:d,extendLeft:p,extendRight:h},title:b,valueMin:v,valueMax:g,valueStep:w,valueUnit:j,extendLeftTitle:O,extendRightTitle:y},offset:S}=e,[x]=Object(r.a)({property:n,defaultValue:!0}),[C]=Object(r.a)({property:o,defaultValue:!1}),N=Object(oe.a)({watchedValue:v,defaultValue:void 0}),z=Object(oe.a)({watchedValue:g,defaultValue:void 0}),k=Object(oe.a)({watchedValue:w,defaultValue:void 0}),_=Object(oe.a)({watchedValue:j,defaultValue:void 0}),V=e.disabled||!x;return i.a.createElement(a.Fragment,null,i.a.createElement(s,{id:t,offset:S,checked:n,title:b,disabled:e.disabled||C},i.a.createElement(ae,{className:re.line,breakPoint:"Small"},i.a.createElement(a.Fragment,null,function(){const{definition:{properties:{color:n,width:a},widthValues:o}}=e;if(n)return i.a.createElement("span",{className:re.control},i.a.createElement(Y,{color:n,thickness:a,disabled:V,thicknessItems:o}));return a&&i.a.createElement("span",{className:re.control},i.a.createElement(E,{id:Object(m.a)(t,"line-width-select"),items:o,property:a,disabled:V}))}(),function(){const{definition:{properties:{style:n}}}=e;return n&&i.a.createElement("span",{className:re.control},i.a.createElement(f,{id:Object(m.a)(t,"line-style-select"),property:n,disabled:V}))}()),(l||c||d)&&i.a.createElement(a.Fragment,null,i.a.createElement(a.Fragment,null,l&&i.a.createElement(ee,{id:Object(m.a)(t,"left-end-select"),"data-name":"left-end-select",className:re.control,property:l,disabled:V}),c&&i.a.createElement(ee,{id:Object(m.a)(t,"right-end-select"),"data-name":"right-end-select",className:re.control,property:c,disabled:V,isRight:!0})),function(){const{definition:{valueType:t}}=e;return d&&i.a.createElement("span",{className:u(re.valueInput,re.control)},i.a.createElement(M,{className:re.input,property:d,min:N,max:z,step:k,disabled:V,mode:void 0!==t?ie[t]:void 0,name:"line-value-input"}),i.a.createElement("span",{className:re.valueUnit},_))}()))),p&&i.a.createElement(s,{id:t+"ExtendLeft",offset:S,checked:p,title:O,disabled:e.disabled||C}),h&&i.a.createElement(s,{id:t+"ExtendRight",offset:S,checked:h,title:y,disabled:e.disabled||C}))}var ce=n("4vW/"),se=n("gla1");function de(e){const{property:t,options:n}=e,o=Object(j.a)(e,["property","options"]),[l,c]=Object(r.a)({property:t}),s=Object(se.a)();return Object(a.useEffect)(()=>{const e=()=>s();return Array.isArray(n)||n.subscribe(e),()=>{Array.isArray(n)||n.unsubscribe(e)}},[]),i.a.createElement(b.a,Object.assign({},o,{onChange:c,value:l,items:(Array.isArray(n)?n:n.value()).map(e=>({content:e.title,value:e.value,id:e.id}))}))}var ue=n("ioCK");const pe=[{title:Object(W.t)("Solid"),value:ce.ColorType.Solid},{title:Object(W.t)("Gradient"),value:ce.ColorType.Gradient}];function me(e){const{id:t,disabled:n,noAlpha:a,properties:o}=e,{color:l,gradientColor1:c,gradientColor2:s,type:d}=o,[u]=Object(r.a)({property:d,defaultValue:ce.ColorType.Solid});return i.a.createElement(ae,null,i.a.createElement(de,{id:Object(m.a)(t,"background-type-options-dropdown"),
"data-name":"background-type-options-dropdown",className:ue.dropdown,menuClassName:ue.dropdownMenu,disabled:n,property:d,options:pe}),u===ce.ColorType.Solid?i.a.createElement(Y,{color:l,disabled:n,noAlpha:a}):i.a.createElement(i.a.Fragment,null,i.a.createElement(Y,{className:ue.firstColorPicker,color:c,disabled:n,noAlpha:a}),i.a.createElement(Y,{color:s,disabled:n,noAlpha:a})))}function he(e){const{definition:{id:t,properties:n,title:a,noAlpha:o},offset:l}=e,{color:c,checked:d,disabled:u}=n,[p]=Object(r.a)({property:d,defaultValue:!0}),[m]=Object(r.a)({property:u,defaultValue:!1}),h=e.disabled||!p;return i.a.createElement(s,{id:t,offset:l,checked:d,title:a,disabled:e.disabled||m},i.a.createElement(te.a,null,n.hasOwnProperty("type")?i.a.createElement(me,{id:t,properties:n,disabled:h,noAlpha:o}):i.a.createElement(Y,{color:c,disabled:h,noAlpha:o})))}var fe=n("U1eG"),be=n("HGP3"),ve=n("lB1i");function ge(e){const{value:t,disabled:n,onChange:a}=e;return i.a.createElement("div",{className:u(ve.wrap,{[ve.disabled]:n})},i.a.createElement(fe.a,{hideInput:!0,color:be.a["color-tv-blue-500"],opacity:1-t/100,onChange:function(e){n||a(100-100*e)}}))}function we(e){const{property:t}=e,n=Object(j.a)(e,["property"]),[i,o]=Object(r.a)({property:t});return a.createElement(ge,Object.assign({},n,{value:i,onChange:o}))}function Ee(e){const{definition:{id:t,properties:{transparency:n,checked:a,disabled:o},title:l},offset:c}=e,[d]=Object(r.a)({property:a,defaultValue:!0}),[u]=Object(r.a)({property:o,defaultValue:!1}),p=e.disabled||!d;return i.a.createElement(s,{id:t,offset:c,checked:a,title:l,disabled:e.disabled||u},i.a.createElement(te.a,null,i.a.createElement(we,{property:n,disabled:p})))}var je=n("oWdB");function Oe(e){const{definition:{id:t,properties:{color1:n,color2:a,checked:o,disabled:l},title:c,noAlpha1:d,noAlpha2:u},offset:p}=e,[m]=Object(r.a)({property:o,defaultValue:!0}),[h]=Object(r.a)({property:l,defaultValue:!1}),f=e.disabled||!m||h;return i.a.createElement(s,{id:t,offset:p,checked:o,title:c,disabled:e.disabled||h},i.a.createElement(te.a,{className:je.twoColors},b(n,d),b(a,u)));function b(e,t){return i.a.createElement("span",{className:je.colorPicker},i.a.createElement(Y,{color:e,disabled:f,noAlpha:t}))}}var ye=n("ybVX"),Se=n("ZRxn");function xe(e){const{definition:{id:t,properties:{checked:n,value:o,unitOptionsValue:l,disabled:c},min:d,max:p,step:h,title:f,unit:b,unitOptions:v,type:g},offset:w}=e,[E]=Object(r.a)({property:n,defaultValue:!0}),[j]=Object(r.a)({property:c,defaultValue:!1}),O=Object(oe.a)({watchedValue:d,defaultValue:void 0}),S=Object(oe.a)({watchedValue:p,defaultValue:void 0}),x=Object(oe.a)({watchedValue:h,defaultValue:void 0}),C=Object(oe.a)({watchedValue:b,defaultValue:void 0}),N=Object(a.useContext)(ye.b),z=e.disabled||!E;return i.a.createElement(s,{id:t,offset:w,checked:n,title:f,disabled:e.disabled||j},i.a.createElement(te.a,null,i.a.createElement(ae,null,i.a.createElement(M,{className:u(Se.input,N[t]&&Se[N[t]]),property:o,min:O,max:S,step:x,disabled:z,mode:ie[g],name:"number-input"
}),l&&i.a.createElement(de,{id:Object(m.a)(t,"unit-options-dropdown"),"data-name":"unit-options-dropdown",className:Se.dropdown,menuClassName:Se.dropdownMenu,disabled:z,property:l,options:Object(y.ensureDefined)(v)})),i.a.createElement("span",{className:Se.unit},C)))}function Ce(e){const{definition:{id:t,properties:{checked:n,disabled:a},childrenDefinitions:o,title:l},offset:c}=e,[d]=Object(r.a)({property:n,defaultValue:!0}),[u]=Object(r.a)({property:a,defaultValue:!1}),p=e.disabled||!d;return i.a.createElement(i.a.Fragment,null,i.a.createElement(s,{id:t,offset:c,checked:n,title:l,disabled:e.disabled||u}),o.map(e=>i.a.createElement(gn,{key:e.id,disabled:p,definition:e,offset:!0})))}var Ne=n("UXjO");function ze(e){const{property:t}=e,[n,a]=Object(r.a)({property:t});return i.a.createElement(Ne.a,Object.assign({},e,{fontSize:n,fontSizeChange:a,"data-name":"font-size-select"}))}var ke=n("Iivm"),Me=n("aw5J");function _e(e){const{className:t,checked:n,icon:a,disabled:o,onClick:r}=e;return i.a.createElement("div",Object.assign({className:p()(t,Me.container,n&&!o&&Me.active,o&&Me.disabled),onClick:o?void 0:r,"data-role":"button"},Object(K.b)(e)),i.a.createElement(ke.a,{className:Me.icon,icon:a}))}function Ve(e){const{icon:t,className:n,property:i,disabled:o}=e,[l,c]=Object(r.a)({property:i});return a.createElement(_e,Object.assign({className:n,icon:t,checked:l,onClick:function(){c(!l)},disabled:o},Object(K.b)(e)))}var Ae,Te=n("ldG2"),De=n("ECWH"),Fe=n("SpAO"),Be=n("Bcy+"),Re=n("3F0O"),Pe=n("xADF"),Ie=n("ORlR");n("Px4x");!function(e){e.None="none",e.Vertical="vertical",e.Horizontal="horizontal",e.Both="both"}(Ae||(Ae={}));const Le=i.a.forwardRef((e,t)=>{const{id:n,title:a,tabIndex:o,containerTabIndex:r,role:l,inputClassName:c,autoComplete:s,autoFocus:d,cols:p,disabled:m,isFocused:h,form:f,maxLength:b,minLength:v,name:g,placeholder:w,readonly:E,required:O,rows:y,value:S,defaultValue:x,wrap:C,containerReference:N,onChange:z,onSelect:k,onFocus:M,onContainerFocus:_,onBlur:V}=e,A=Object(j.a)(e,["id","title","tabIndex","containerTabIndex","role","inputClassName","autoComplete","autoFocus","cols","disabled","isFocused","form","maxLength","minLength","name","placeholder","readonly","required","rows","value","defaultValue","wrap","containerReference","onChange","onSelect","onFocus","onContainerFocus","onBlur"]),T={id:n,title:a,tabIndex:o,role:l,autoComplete:s,autoFocus:d,cols:p,disabled:m,form:f,maxLength:b,minLength:v,name:g,placeholder:w,readOnly:E,required:O,rows:y,value:S,defaultValue:x,wrap:C,onChange:z,onSelect:k,onFocus:M,onBlur:V};return i.a.createElement(Te.a,Object.assign({},A,{tabIndex:r,disabled:m,readonly:E,isFocused:h,ref:N,onFocus:_,middleSlot:i.a.createElement(Pe.c,null,i.a.createElement("textarea",Object.assign({},T,{className:u(Ie.textarea,c),ref:t})))}))});Le.displayName="TextareaView";const Ue=i.a.forwardRef((e,t)=>{e=Object(Be.a)(e)
;const{className:n,disabled:o,tabIndex:r=0,borderStyle:l,highlight:c,resize:s,containerReference:d=null,onFocus:p,onBlur:m}=e,h=Object(j.a)(e,["className","disabled","tabIndex","borderStyle","highlight","resize","containerReference","onFocus","onBlur"]),f=Object(a.useRef)(null),b=Object(a.useRef)(null),[v,g]=Object(Fe.a)(),w=Object(Re.a)(g.onFocus,p),E=Object(Re.a)(g.onBlur,m),O=o?void 0:v?-1:r,y=o?void 0:v?r:-1,S=void 0!==s&&s!==Ae.None,x=null!=l?l:S?c?"thick":"thin":void 0,C=null!=c?c:!S&&void 0;return i.a.createElement(Le,Object.assign({},h,{className:u(Ie["textarea-container"],S&&Ie["change-highlight"],s&&s!==Ae.None&&Ie["resize-"+s],v&&Ie.focused,n),disabled:o,isFocused:v,containerTabIndex:O,tabIndex:y,borderStyle:x,highlight:C,onContainerFocus:function(e){b.current===e.target&&null!==f.current&&f.current.focus()},onFocus:w,onBlur:E,ref:function(e){f.current=e,"function"==typeof t?t(e):t&&(t.current=e)},containerReference:Object(De.a)([d,b])}))});function He(e){const{property:t}=e,n=Object(j.a)(e,["property"]),[o,l]=Object(r.a)({property:t}),c=Object(a.useCallback)(e=>l(e.target.value),[l]);return i.a.createElement(Ue,Object.assign({},n,{value:o,onChange:c}))}Ue.displayName="Textarea";var Ye=n("rRJX"),We=n("4Fxa"),qe=n("CaTF");const Je=e=>({content:e.title,title:e.title,value:e.value,id:e.id}),Ke=e=>({content:e.title,title:e.title,value:e.value,id:e.id});function Ge(e){const{definition:{id:t,properties:{color:n,size:o,checked:c,disabled:d,bold:u,italic:p,text:h,alignmentHorizontal:f,alignmentVertical:v,orientation:g,backgroundVisible:w,backgroundColor:E,borderVisible:j,borderColor:O,borderWidth:y,wrap:S},title:x,sizeItems:C,alignmentTitle:N,alignmentHorizontalItems:z,alignmentVerticalItems:k,orientationTitle:M,orientationItems:_,backgroundTitle:V,borderTitle:A,borderWidthItems:T,wrapTitle:D},offset:F}=e,B=Object(a.useContext)(ye.a),[R]=Object(r.a)({property:c,defaultValue:!0}),[P]=Object(r.a)({property:d,defaultValue:!1}),[I,L]=Object(r.a)({property:v,defaultValue:void 0}),[U,H]=Object(r.a)({property:g,defaultValue:"horizontal"}),[W,q]=Object(r.a)({property:f,defaultValue:void 0}),[J]=Object(r.a)({property:w,defaultValue:!1}),[K]=Object(r.a)({property:j,defaultValue:!1}),G=e.disabled||!R;return i.a.createElement(a.Fragment,null,function(){if(x)return i.a.createElement(s,{id:t,offset:F,checked:c,title:x,disabled:e.disabled||P},i.a.createElement(ae,{breakPoint:"Small"},Z(),$()));return i.a.createElement(l.a.Row,null,i.a.createElement(l.a.Cell,{placement:"first",colSpan:2,offset:F,"data-section-name":t},Z(),$()))}(),h&&i.a.createElement(l.a.Row,null,i.a.createElement(l.a.Cell,{placement:"first",colSpan:2,offset:F,"data-section-name":t},i.a.createElement(He,{className:Te.b.FontSizeMedium,rows:(X=B[t],"big"===X?9:5),stretch:!0,property:h,disabled:G,onFocus:function(e){e.target.select()},name:"text-input"}))),(f||v)&&i.a.createElement(l.a.Row,null,i.a.createElement(l.a.Cell,{placement:"first",verticalAlign:"adaptive",offset:F,"data-section-name":t},i.a.createElement(te.a,null,N)),i.a.createElement(l.a.Cell,{
placement:"last",verticalAlign:"adaptive","data-section-name":t},i.a.createElement(ae,{breakPoint:"Small"},void 0!==I&&void 0!==k&&i.a.createElement(b.a,{id:Object(m.a)(t,"alignment-vertical-select"),"data-name":"alignment-vertical-select",className:qe.dropdown,menuClassName:qe.dropdownMenu,disabled:G,value:I,items:k.map(Je),onChange:L}),void 0!==W&&void 0!==z&&i.a.createElement(b.a,{id:Object(m.a)(t,"alignment-horizontal-select"),"data-name":"alignment-horizontal-select",className:qe.dropdown,menuClassName:qe.dropdownMenu,disabled:G,value:W,items:z.map(Je),onChange:q})))),void 0!==g&&void 0!==_&&i.a.createElement(l.a.Row,null,i.a.createElement(l.a.Cell,{placement:"first",verticalAlign:"adaptive",offset:F,"data-section-name":t},i.a.createElement(te.a,null,M)),i.a.createElement(l.a.Cell,{placement:"last",verticalAlign:"adaptive","data-section-name":t},i.a.createElement(ae,{breakPoint:"Small"},i.a.createElement(b.a,{id:Object(m.a)(t,"orientation-select"),"data-name":"orientation-select",className:qe.dropdown,menuClassName:qe.dropdownMenu,disabled:G,value:U,items:_.map(Ke),onChange:H})))),ee(V,w,E,!!w&&!J),ee(A,j,O,!!j&&!K,y,T),S&&i.a.createElement(s,{id:t+"Wrap",offset:F,checked:S,title:D,disabled:e.disabled||P}));var X;function Q(e,t,n){return e?i.a.createElement(Ve,{className:qe.fontStyleButton,icon:t,property:e,disabled:G,"data-name":n}):null}function Z(){return i.a.createElement(a.Fragment,null,n&&i.a.createElement("div",{className:qe.colorPicker},i.a.createElement(Y,{color:n,disabled:G})),o&&C&&i.a.createElement(ze,{id:Object(m.a)(t,"font-size-select"),property:o,fontSizes:C,disabled:G}))}function $(){return i.a.createElement(a.Fragment,null,Q(u,Ye,"toggle-bold"),Q(p,We,"toggle-italic"))}function ee(n,a,o,r,l,c){return o?i.a.createElement(s,{id:t+"ColorSelect",offset:F,checked:a,title:n,disabled:e.disabled||P},i.a.createElement(Y,{color:o,thickness:l,thicknessItems:c,disabled:G||r})):null}}var Xe=n("wHCJ"),Qe=n("jAh7"),Ze=n("pZll"),$e=n("RgaO"),et=n("e3/o"),tt=n("+EG+"),nt=n("K5B3");class at extends a.PureComponent{constructor(e){super(e),this._symbolSearch=null,this._input=null,this._popup=null,this._uuid=Object(et.guid)(),this._updateSymbolName=()=>{const{propType:e,properties:t}=this.props.definition,n=t[e];this._symbolSearch&&(Object(y.ensureNotNull)(this._input).value=n.value(),this._symbolSearch.acceptTypeIn())},this._onSetSymbol=e=>{const{propType:t,properties:n}=this.props.definition;n[t].setValue(e)},this._handleOutsideClick=e=>{null!==this._input&&document.activeElement===this._input&&e.target instanceof Node&&null!==this._popup&&!this._popup.contains(e.target)&&this._input.blur()},this._refInput=e=>{this._input=e},this.state={expanded:!1}}componentDidMount(){const{properties:e,propType:t}=this.props.definition;e[t].subscribe(this,this._updateSymbolName);const n=this.context||Object(Qe.getRootOverlapManager)();Object(Ze.symbolSearchUIService)().bindToInput(Object(y.ensureNotNull)(this._input),{syncWithChartWidget:!1,syncOnBlur:!0,callback:this._onSetSymbol,onPopupOpen:e=>{
this._popup=n.ensureWindow(this._uuid),e.appendTo(this._popup),this.setState({expanded:!0})},onPopupClose:()=>{this._popup=null,this.setState({expanded:!1}),n.removeWindow(this._uuid),this._input&&this._input.focus()},keepFocus:!0}).then(e=>this._symbolSearch=e)}componentWillUnmount(){const{properties:e,propType:t}=this.props.definition;e[t].unsubscribe(this,this._updateSymbolName)}render(){const{definition:{id:e,title:t=""}}=this.props,{expanded:n}=this.state,{propType:a,properties:o}=this.props.definition,r=o[a].value()||"";return i.a.createElement(s,{id:e,title:t},i.a.createElement(te.a,null,i.a.createElement($e.a,{mouseDown:!0,touchStart:!0,handler:this._handleOutsideClick},e=>i.a.createElement(Xe.a,{className:nt.input,reference:this._refInput,containerReference:e,defaultValue:r,"data-haspopup":!0,"data-expanded":n}))))}}function it(e){const t=at;return i.a.createElement(t,Object.assign({},e))}at.contextType=tt.b;var ot=n("aSdR");function rt(e){const{definition:{properties:{x:t,y:n,disabled:a},id:o,minX:r,maxX:c,stepX:s,minY:d,maxY:u,stepY:p,title:m,typeX:h,typeY:f},offset:b}=e,v=a&&a.value()||e.disabled,g=Object(oe.a)({watchedValue:r,defaultValue:void 0}),w=Object(oe.a)({watchedValue:c,defaultValue:void 0}),E=Object(oe.a)({watchedValue:s,defaultValue:void 0}),j=Object(oe.a)({watchedValue:d,defaultValue:void 0}),O=Object(oe.a)({watchedValue:u,defaultValue:void 0}),y=Object(oe.a)({watchedValue:p,defaultValue:void 0});return i.a.createElement(l.a.Row,null,i.a.createElement(l.a.Cell,{verticalAlign:"adaptive",placement:"first",offset:b,"data-section-name":o},i.a.createElement("span",{className:ot.coordinates},m)),(t||n)&&i.a.createElement(l.a.Cell,{placement:"last",offset:b,"data-section-name":o},i.a.createElement(ae,{breakPoint:"Medium"},n&&i.a.createElement(M,{className:ot.input,property:n,min:j,max:O,step:y,disabled:v,name:"y-input",mode:void 0!==f?ie[f]:"integer"}),t&&i.a.createElement(M,{className:ot.input,property:t,min:g,max:w,step:E,disabled:v,name:"x-input",mode:void 0!==h?ie[h]:"integer"}))))}var lt=n("9gev");function ct(e){const{definition:{id:t,properties:{checked:n,option:o,disabled:l},title:c,options:d},offset:u}=e,[h]=Object(r.a)({property:n,defaultValue:!0}),[f]=Object(r.a)({property:l,defaultValue:!1}),b=Object(a.useContext)(ye.b),v=e.disabled||!h;return i.a.createElement(s,{id:t,offset:u,checked:n,title:c,disabled:e.disabled||f},i.a.createElement(te.a,null,i.a.createElement(de,{id:Object(m.a)(t,"options-dropdown"),"data-name":"options-dropdown",className:p()(lt.dropdown,b[t]&&lt[b[t]]),menuClassName:p()(lt.dropdownMenu,b[t]&&lt[b[t]]),disabled:v,property:o,options:d})))}var st=n("yqnI");var dt=n("Ialn"),ut=n("G7lD");class pt extends a.PureComponent{constructor(e){super(e),this._container=null,this._pointer=null,this._rafPosition=null,this._rafDragStop=null,this._refContainer=e=>{this._container=e},this._refPointer=e=>{this._pointer=e},this._handlePosition=e=>{null!==this._rafPosition||this.props.disabled||(this._rafPosition=requestAnimationFrame(()=>{
const{from:t,to:n,min:a,max:i}=this.props,o=this._getNewPosition(e),r=1===this._detectPointerMode(e),l=r?Object(N.clamp)(o,a,n):t,c=r?n:Object(N.clamp)(o,t,i);l<=c&&this._handleChange(l,c),this._rafPosition=null}))},this._handleDragStop=()=>{null!==this._rafDragStop||this.props.disabled||(this._rafDragStop=requestAnimationFrame(()=>{this.setState({pointerDragMode:0}),this._rafDragStop=null,this.props.onCommit()}))},this._onSliderClick=e=>{k.CheckMobile.any()||(this._handlePosition(e.nativeEvent),this._dragSubscribe())},this._mouseUp=e=>{this._dragUnsubscribe(),this._handlePosition(e),this._handleDragStop()},this._mouseMove=e=>{this._handlePosition(e)},this._onTouchStart=e=>{this._handlePosition(e.nativeEvent.touches[0])},this._handleTouch=e=>{this._handlePosition(e.nativeEvent.touches[0])},this._handleTouchEnd=()=>{this._handleDragStop()},this.state={pointerDragMode:0}}componentWillUnmount(){null!==this._rafPosition&&(cancelAnimationFrame(this._rafPosition),this._rafPosition=null),null!==this._rafDragStop&&(cancelAnimationFrame(this._rafDragStop),this._rafDragStop=null),this._dragUnsubscribe()}render(){const{className:e,disabled:t,from:n,to:i,min:o,max:r}=this.props,{pointerDragMode:l}=this.state,c=0!==l,s=r-o,d=(n-o)/s,p=(i-o)/s,m=Object(dt.isRtl)()?"right":"left";return a.createElement("div",{className:u(e,ut.range,t&&ut.disabled)},a.createElement("div",{className:ut.rangeSlider,ref:this._refContainer,onMouseDown:this._onSliderClick,onTouchStart:this._onTouchStart,onTouchMove:this._handleTouch,onTouchEnd:this._handleTouchEnd},a.createElement("div",{className:ut.rangeSliderMiddleWrap},a.createElement("div",{className:u(ut.rangeSliderMiddle,c&&ut.dragged),style:{[m]:100*d+"%",width:100*(p-d)+"%"}})),a.createElement("div",{className:ut.rangePointerWrap},a.createElement("div",{className:u(ut.pointer,c&&ut.dragged),style:{[m]:100*d+"%"},ref:this._refPointer})),a.createElement("div",{className:ut.rangePointerWrap},a.createElement("div",{className:u(ut.pointer,c&&ut.dragged),style:{[m]:100*p+"%"}}))))}_dragSubscribe(){const e=Object(y.ensureNotNull)(this._container).ownerDocument;e&&(e.addEventListener("mouseup",this._mouseUp),e.addEventListener("mousemove",this._mouseMove))}_dragUnsubscribe(){const e=Object(y.ensureNotNull)(this._container).ownerDocument;e&&(e.removeEventListener("mousemove",this._mouseMove),e.removeEventListener("mouseup",this._mouseUp))}_getNewPosition(e){const{min:t,max:n}=this.props,a=n-t,i=Object(y.ensureNotNull)(this._container),o=Object(y.ensureNotNull)(this._pointer),r=i.getBoundingClientRect(),l=o.offsetWidth;let c=e.clientX-l/2-r.left;return Object(dt.isRtl)()&&(c=r.width-c-l),Object(N.clamp)(c/(r.width-l),0,1)*a+t}_detectPointerMode(e){const{from:t,to:n}=this.props,{pointerDragMode:a}=this.state;if(0!==a)return a;const i=this._getNewPosition(e),o=Math.abs(t-i),r=Math.abs(n-i),l=o===r?i<t?1:2:o<r?1:2;return this.setState({pointerDragMode:l}),l}_handleChange(e,t){const{from:n,to:a,onChange:i}=this.props;e===n&&t===a||i(e,t)}}var mt=n("/KDZ"),ht=n("7EmB");function ft(e){
const{definition:{id:t,properties:{checked:n,disabled:a,from:o,to:l},title:c,max:d,min:u},offset:p}=e,[m]=Object(r.a)({property:n,defaultValue:!0}),[h]=Object(r.a)({property:a,defaultValue:!1}),f=Object(oe.a)({watchedValue:u,defaultValue:void 0}),b=Object(oe.a)({watchedValue:d,defaultValue:void 0}),[v,g]=Object(r.a)({property:o}),w=S(v,g),[E,j,O]=w,[y,x]=Object(r.a)({property:l}),C=S(y,x),[N,z,k]=C,M=e.disabled||!m,V={flushed:!1};return i.a.createElement(s,{id:t,offset:p,checked:n,title:c,disabled:e.disabled||h},i.a.createElement(te.a,{className:ht.range},function(){if(!f||!b)return null;return i.a.createElement(mt.a,{rule:"screen and (max-width: 460px)"},e=>i.a.createElement(ae,{breakPoint:"Medium"},i.a.createElement(i.a.Fragment,null,i.a.createElement("span",{className:ht.valueInput},i.a.createElement(_,{className:ht.input,sharedBuffer:w,min:f,max:N,step:1,disabled:M,name:"from-input",mode:"integer"}),e?i.a.createElement("span",{className:ht.rangeSlider},"—"):i.a.createElement(pt,{className:ht.rangeSlider,from:E,to:N,min:f,max:b,onChange:A,onCommit:T,disabled:M}))),i.a.createElement(i.a.Fragment,null,i.a.createElement("span",{className:ht.valueInput},i.a.createElement(_,{className:ht.input,sharedBuffer:C,min:E,max:b,step:1,disabled:M,name:"to-input",mode:"integer"})))))}()));function A(e,t){j(Math.round(e)),z(Math.round(t))}function T(){V.flushed||(O(),k(),V.flushed=!0)}}var bt=n("07LS"),vt=n("EJl2");function gt(e){const{definitions:t,name:n,offset:a}=e;return i.a.createElement(l.a.Row,null,i.a.createElement(l.a.Cell,{className:p()(vt.cell,vt.fragmentCell),offset:a,placement:"first",verticalAlign:"adaptive",colSpan:2,"data-section-name":n,checkableTitle:!0},t.map(e=>i.a.createElement("div",{className:vt.item,key:e.id,"data-section-name":e.id},i.a.createElement(Et,{definition:e})))))}function wt(e){const{definition:t,offset:n}=e;return i.a.createElement(l.a.Row,null,i.a.createElement(l.a.Cell,{className:vt.cell,offset:n,placement:"first",verticalAlign:"adaptive",colSpan:2,checkableTitle:!0},i.a.createElement(Et,{definition:t})))}function Et(e){const{definition:{id:t,properties:{disabled:n,checked:a,color:o,level:l,width:s,style:d},title:u,widthValues:h,styleValues:b}}=e,[v]=Object(r.a)({property:a,defaultValue:!0}),[g]=Object(r.a)({property:n,defaultValue:!1}),w=g||!v;return i.a.createElement(i.a.Fragment,null,i.a.createElement(c.a,{name:"is-enabled-"+t,className:p()(u&&vt.withTitle),title:u&&i.a.createElement("span",{className:vt.title},u),property:a,disabled:g}),l&&i.a.createElement(M,{className:p()(vt.input,vt.control),property:l,disabled:w}),o&&i.a.createElement(Y,{className:vt.control,disabled:w,color:o,thickness:s,thicknessItems:h}),d&&i.a.createElement(f,{id:Object(m.a)(t,"leveled-line-style-select"),className:vt.control,property:d,disabled:w,allowedLineStyles:b}))}var jt=n("ZcEB");function Ot(e){const{definition:{id:t,properties:{option1:n,option2:a,checked:o,disabled:l},title:c,optionsItems1:d,optionsItems2:u},offset:p}=e,[h]=Object(r.a)({property:o,defaultValue:!0}),[f]=Object(r.a)({property:l,
defaultValue:!1}),b=e.disabled||!h;return i.a.createElement(s,{id:t,offset:p,checked:o,title:c,disabled:e.disabled||f},i.a.createElement(ae,{className:jt.twoOptions},i.a.createElement(de,{id:Object(m.a)(t,"two-options-dropdown-1"),"data-name":"two-options-dropdown-1",className:jt.dropdown,menuClassName:jt.menu,property:n,disabled:b,options:d}),i.a.createElement(de,{id:Object(m.a)(t,"two-options-dropdown-2"),"data-name":"two-options-dropdown-2",className:jt.dropdown,menuClassName:jt.menu,property:a,disabled:b,options:u})))}var yt=n("fV0y"),St=n("Vdly"),xt=n("MjAr");var Ct=n("0lS6");function Nt(e){const{tab:t,icon:n,isActive:a,onTabClick:o}=e;return i.a.createElement("div",{className:p()(Ct.wrapper,a&&Ct.isActive),onClick:function(){o(t)}},i.a.createElement(ke.a,{icon:n}))}var zt=n("Bbdy");function kt(e){const{activeTab:t,emojis:n,onTabClick:a}=e;return i.a.createElement("div",{className:zt.wrapper},n.map(({title:e,icon:n})=>i.a.createElement(Nt,{key:e,tab:e,icon:n,isActive:t===e,onTabClick:a})))}var Mt=n("iR1w"),_t=n("j0Er");function Vt(e){const{title:t}=e;return i.a.createElement("div",{className:_t.wrapper},t)}var At=n("+FzY"),Tt=n("e/Lx");const Dt=34;function Ft(e){const{className:t,emoji:n,size:a=Dt,onClick:o}=e,r=Object(At.a)(n,"png");return i.a.createElement("div",{className:p()(Tt.wrapper,t),style:{width:a,height:a},onClick:function(){o(n)}},i.a.createElement("img",{className:Tt.emoji,src:r,decoding:"async",width:"24",height:"24",alt:"",draggable:!1,onContextMenu:function(e){e.preventDefault()}}))}var Bt=n("tQXF");const Rt=i.a.memo(e=>{const{emojis:t,itemSize:n,onEmojiClick:a}=e;return i.a.createElement("div",{className:Bt.wrapper},t.map(e=>i.a.createElement(Ft,{key:e,className:Bt.emojiItem,emoji:e,size:n,onClick:a})))});var Pt=n("tCqQ"),It=n("iy5y");const Lt=i.a.createContext(null);function Ut(e){const{listRef:t,emojiGroups:n,emojiSize:o,onSelect:r,onContentRendered:l}=e;Object(a.useEffect)(()=>{var e;return null===(e=t.current)||void 0===e?void 0:e.resetAfterIndex(0,!0)},[n]);const c=Object(a.useCallback)(e=>"title"===n[e].type?30:38,[n]),s=Object(a.useCallback)(({visibleStartIndex:e})=>{const{relatedTitle:t}=n[e];l(t)},[n,l]);return i.a.createElement(Lt.Provider,{value:Object(a.useMemo)(()=>({size:o,onSelect:r}),[o,r])},i.a.createElement(Mt.b,{className:It.list,ref:t,width:"100%",height:Math.min(330,window.innerHeight-60),itemData:n,itemCount:n.length,children:Ht,onItemsRendered:s,itemSize:c}))}const Ht=i.a.memo(e=>{const{style:t,index:n,data:a}=e,o=a[n],{size:r,onSelect:l}=Object(Pt.a)(Lt);return"title"===o.type?i.a.createElement("div",{style:t},i.a.createElement(Vt,{title:o.relatedTitle})):i.a.createElement("div",{style:t},i.a.createElement(Rt,{emojis:o.content,itemSize:r,onEmojiClick:l}))});var Yt=n("9pqQ");function Wt(e){var t;const{className:n,emojis:o,onSelect:r}=e,l=Object(a.useRef)(null),[c,s]=Object(a.useState)(0),d=Object(a.useMemo)(()=>function(e,t){if(0===t)return[];const n=[];return e.forEach(({title:e,emojis:a})=>{n.push({type:"title",relatedTitle:e,content:[e]});let i=[]
;for(const o of a)i.length<t?i.push(o):(n.push({type:"emojiRow",relatedTitle:e,content:i}),i=[o]);i.length&&n.push({type:"emojiRow",relatedTitle:e,content:i})}),n}(o,c),[o,c]),u=function(e,t=[]){const n=Object(a.useRef)(null),i=Object(a.useRef)(null),o=Object(a.useRef)(e);o.current=e;const r=Object(a.useCallback)(e=>{n.current=e,null!==i.current&&(i.current.disconnect(),null!==e&&i.current.observe(e))},[n,i]);return Object(a.useEffect)(()=>(i.current=new xt.default((e,t)=>o.current(e,t)),n.current&&r(n.current),()=>{var e;null===(e=i.current)||void 0===e||e.disconnect()}),[n,...t]),r}((function(e){const[t]=e,{width:n}=t.contentRect,a=Math.floor((n-12)/38);s(a)})),[m,h]=Object(a.useState)((null===(t=d[0])||void 0===t?void 0:t.relatedTitle)||"");return i.a.createElement("div",{className:p()(Yt.wrapper,n)},i.a.createElement(kt,{emojis:o,activeTab:m,onTabClick:function(e){!function(e){var t;null===(t=l.current)||void 0===t||t.scrollToItem(e,"start"),requestAnimationFrame(()=>{var t;return null===(t=l.current)||void 0===t?void 0:t.scrollToItem(e,"start")})}(function(e){return d.findIndex(({relatedTitle:t,type:n})=>"title"===n&&t===e)}(e))}}),i.a.createElement("div",{ref:u},i.a.createElement(Ut,{listRef:l,emojiGroups:d,emojiSize:38,onSelect:r,onContentRendered:h})))}var qt=n("JoYF"),Jt=n("STR1"),Kt=n("tJif"),Gt=n("gd+L"),Xt=n("gX9w"),Qt=n("A3oJ"),Zt=n("hreg"),$t=n("r6bn"),en=n("mt7N");const tn=[{title:Object(W.t)("recently used",{context:"emoji_group"}),emojis:[],icon:qt},{title:Object(W.t)("smiles & people",{context:"emoji_group"}),
emojis:["😀","😃","😄","😁","😆","😅","😂","🤣","☺️","😊","😇","🙂","🙃","😉","😌","😍","🥰","😘","😗","😙","😚","😋","😛","😝","😜","🤪","🤨","🧐","🤓","😎","🤩","🥳","😏","😒","😞","😔","😟","😕","🙁","☹️","😣","😖","😫","😩","🥺","😢","😭","😤","😠","😡","🤬","🤯","😳","🥵","🥶","😱","😨","😰","😥","😓","🤗","🤔","🤭","🤫","🤥","😶","😐","😑","😬","🙄","😯","😦","😧","😮","😲","🥱","😴","🤤","😪","😵","🤐","🥴","🤢","🤮","🤧","😷","🤒","🤕","🤑","🤠","😈","👿","👹","👺","🤡","💩","👻","💀","☠️","👽","👾","🤖","🎃","😺","😸","😹","😻","😼","😽","🙀","😿","😾","👋","🤚","🖐","✋","🖖","👌","🤏","✌️","🤞","🤟","🤘","🤙","👈","👉","👆","🖕","👇","☝️","👍","👎","✊","👊","🤛","🤜","👏","🙌","👐","🤲","🤝","🙏","✍️","💅","🤳","💪","🦾","🦵","🦿","🦶","👂","🦻","👃","🧠","🦷","🦴","👀","👁","👅","👄","💋","🩸","👶","🧒","👦","👧","🧑","👱","👨","🧔","👨‍🦰","👨‍🦱","👨‍🦳","👨‍🦲","👩","👩‍🦰","🧑‍🦰","👩‍🦱","🧑‍🦱","👩‍🦳","🧑‍🦳","👩‍🦲","🧑‍🦲","👱‍♀️","👱‍♂️","🧓","👴","👵","🙍","🙍‍♂️","🙍‍♀️","🙎","🙎‍♂️","🙎‍♀️","🙅","🙅‍♂️","🙅‍♀️","🙆","🙆‍♂️","🙆‍♀️","💁","💁‍♂️","💁‍♀️","🙋","🙋‍♂️","🙋‍♀️","🧏","🧏‍♂️","🧏‍♀️","🙇","🙇‍♂️","🙇‍♀️","🤦","🤦‍♂️","🤦‍♀️","🤷","🤷‍♂️","🤷‍♀️","🧑‍⚕️","👨‍⚕️","👩‍⚕️","🧑‍🎓","👨‍🎓","👩‍🎓","🧑‍🏫","👨‍🏫","👩‍🏫","🧑‍⚖️","👨‍⚖️","👩‍⚖️","🧑‍🌾","👨‍🌾","👩‍🌾","🧑‍🍳","👨‍🍳","👩‍🍳","🧑‍🔧","👨‍🔧","👩‍🔧","🧑‍🏭","👨‍🏭","👩‍🏭","🧑‍💼","👨‍💼","👩‍💼","🧑‍🔬","👨‍🔬","👩‍🔬","🧑‍💻","👨‍💻","👩‍💻","🧑‍🎤","👨‍🎤","👩‍🎤","🧑‍🎨","👨‍🎨","👩‍🎨","🧑‍✈️","👨‍✈️","👩‍✈️","🧑‍🚀","👨‍🚀","👩‍🚀","🧑‍🚒","👨‍🚒","👩‍🚒","👮","👮‍♂️","👮‍♀️","🕵","🕵️‍♂️","🕵️‍♀️","💂","💂‍♂️","💂‍♀️","👷","👷‍♂️","👷‍♀️","🤴","👸","👳","👳‍♂️","👳‍♀️","👲","🧕","🤵","👰","🤰","🤱","👼","🎅","🤶","🦸","🦸‍♂️","🦸‍♀️","🦹","🦹‍♂️","🦹‍♀️","🧙","🧙‍♂️","🧙‍♀️","🧚","🧚‍♂️","🧚‍♀️","🧛","🧛‍♂️","🧛‍♀️","🧜","🧜‍♂️","🧜‍♀️","🧝","🧝‍♂️","🧝‍♀️","🧞","🧞‍♂️","🧞‍♀️","🧟","🧟‍♂️","🧟‍♀️","💆","💆‍♂️","💆‍♀️","💇","💇‍♂️","💇‍♀️","🚶","🚶‍♂️","🚶‍♀️","🧍","🧍‍♂️","🧍‍♀️","🧎","🧎‍♂️","🧎‍♀️","🧑‍🦯","👨‍🦯","👩‍🦯","🧑‍🦼","👨‍🦼","👩‍🦼","🧑‍🦽","👨‍🦽","👩‍🦽","🏃","🏃‍♂️","🏃‍♀️","💃","🕺","🕴","👯","👯‍♂️","👯‍♀️","🧖","🧖‍♂️","🧖‍♀️","🧑‍🤝‍🧑","👭","👫","👬","💏","👨‍❤️‍💋‍👨","👩‍❤️‍💋‍👩","💑","👨‍❤️‍👨","👩‍❤️‍👩","👪","👨‍👩‍👦","👨‍👩‍👧","👨‍👩‍👧‍👦","👨‍👩‍👦‍👦","👨‍👩‍👧‍👧","👨‍👨‍👦","👨‍👨‍👧","👨‍👨‍👧‍👦","👨‍👨‍👦‍👦","👨‍👨‍👧‍👧","👩‍👩‍👦","👩‍👩‍👧","👩‍👩‍👧‍👦","👩‍👩‍👦‍👦","👩‍👩‍👧‍👧","👨‍👦","👨‍👦‍👦","👨‍👧","👨‍👧‍👦","👨‍👧‍👧","👩‍👦","👩‍👦‍👦","👩‍👧","👩‍👧‍👦","👩‍👧‍👧","🗣","👤","👥","👣"],icon:Jt},{title:Object(W.t)("animals & nature",{context:"emoji_group"}),
emojis:["🐶","🐱","🐭","🐹","🐰","🦊","🐻","🐼","🐨","🐯","🦁","🐮","🐷","🐽","🐸","🐵","🙈","🙉","🙊","🐒","🐔","🐧","🐦","🐤","🐣","🐥","🦆","🦅","🦉","🦇","🐺","🐗","🐴","🦄","🐝","🐛","🦋","🐌","🐞","🐜","🦟","🦗","🕷","🕸","🦂","🐢","🐍","🦎","🦖","🦕","🐙","🦑","🦐","🦞","🦀","🐡","🐠","🐟","🐬","🐳","🐋","🦈","🐊","🐅","🐆","🦓","🦍","🦧","🐘","🦛","🦏","🐪","🐫","🦒","🦘","🐃","🐂","🐄","🐎","🐖","🐏","🐑","🦙","🐐","🦌","🐕","🐩","🦮","🐕‍🦺","🐈","🐓","🦃","🦚","🦜","🦢","🦩","🕊","🐇","🦝","🦨","🦡","🦦","🦥","🐁","🐀","🐿","🦔","🐾","🐉","🐲","🌵","🎄","🌲","🌳","🌴","🌱","🌿","☘️","🍀","🎍","🎋","🍃","🍂","🍁","🍄","🐚","🌾","💐","🌷","🌹","🥀","🌺","🌸","🌼","🌻","🌞","🌝","🌛","🌜","🌚","🌕","🌖","🌗","🌘","🌑","🌒","🌓","🌔","🌙","🌎","🌍","🌏","🪐","💫","⭐️","🌟","✨","⚡️","☄️","💥","🔥","🌪","🌈","☀️","🌤","⛅️","🌥","☁️","🌦","🌧","⛈","🌩","🌨","❄️","☃️","⛄️","🌬","💨","💧","💦","☔️","🌊","🌫"],icon:Kt},{title:Object(W.t)("food & drink",{context:"emoji_group"}),emojis:["🍏","🍎","🍐","🍊","🍋","🍌","🍉","🍇","🍓","🍈","🍒","🍑","🥭","🍍","🥥","🥝","🍅","🍆","🥑","🥦","🥬","🥒","🌶","🌽","🥕","🧄","🧅","🥔","🍠","🥐","🥯","🍞","🥖","🥨","🧀","🥚","🍳","🧈","🥞","🧇","🥓","🥩","🍗","🍖","🌭","🍔","🍟","🍕","🥪","🥙","🧆","🌮","🌯","🥗","🥘","🥫","🍝","🍜","🍲","🍛","🍣","🍱","🥟","🦪","🍤","🍙","🍚","🍘","🍥","🥠","🥮","🍢","🍡","🍧","🍨","🍦","🥧","🧁","🍰","🎂","🍮","🍭","🍬","🍫","🍿","🍩","🍪","🌰","🥜","🍯","🥛","🍼","☕️","🍵","🧃","🥤","🍶","🍺","🍻","🥂","🍷","🥃","🍸","🍹","🧉","🍾","🧊","🥄","🍴","🍽","🥣","🥡","🥢","🧂"],icon:Gt},{title:Object(W.t)("activity",{context:"emoji_group"}),emojis:["⚽️","🏀","🏈","⚾️","🥎","🎾","🏐","🏉","🥏","🎱","🪀","🏓","🏸","🏒","🏑","🥍","🏏","🥅","⛳️","🪁","🏹","🎣","🤿","🥊","🥋","🎽","🛹","🛷","⛸","🥌","🎿","⛷","🏂","🪂","🏋️","🏋️‍♂️","🏋️‍♀️","🤼","🤼‍♂️","🤼‍♀️","🤸‍♀️","🤸","🤸‍♂️","⛹️","⛹️‍♂️","⛹️‍♀️","🤺","🤾","🤾‍♂️","🤾‍♀️","🏌️","🏌️‍♂️","🏌️‍♀️","🏇","🧘","🧘‍♂️","🧘‍♀️","🏄","🏄‍♂️","🏄‍♀️","🏊","🏊‍♂️","🏊‍♀️","🤽","🤽‍♂️","🤽‍♀️","🚣","🚣‍♂️","🚣‍♀️","🧗","🧗‍♂️","🧗‍♀️","🚵","🚵‍♂️","🚵‍♀️","🚴","🚴‍♂️","🚴‍♀️","🏆","🥇","🥈","🥉","🏅","🎖","🏵","🎗","🎫","🎟","🎪","🤹","🤹‍♂️","🤹‍♀️","🎭","🎨","🎬","🎤","🎧","🎼","🎹","🥁","🎷","🎺","🎸","🪕","🎻","🎲","🎯","🎳","🎮","🎰","🧩"],icon:Xt},{title:Object(W.t)("travel & places",{context:"emoji_group"}),emojis:["🚗","🚕","🚙","🚌","🚎","🏎","🚓","🚑","🚒","🚐","🚚","🚛","🚜","🦯","🦽","🦼","🛴","🚲","🛵","🏍","🛺","🚨","🚔","🚍","🚘","🚖","🚡","🚠","🚟","🚃","🚋","🚞","🚝","🚄","🚅","🚈","🚂","🚆","🚇","🚊","🚉","✈️","🛫","🛬","🛩","💺","🛰","🚀","🛸","🚁","🛶","⛵️","🚤","🛥","🛳","⛴","🚢","⚓️","⛽️","🚧","🚦","🚥","🚏","🗺","🗿","🗽","🗼","🏰","🏯","🏟","🎡","🎢","🎠","⛲️","⛱","🏖","🏝","🏜","🌋","⛰","🏔","🗻","🏕","⛺️","🏠","🏡","🏘","🏚","🏗","🏭","🏢","🏬","🏣","🏤","🏥","🏦","🏨","🏪","🏫","🏩","💒","🏛","⛪️","🕌","🕍","🛕","🕋","⛩","🛤","🛣","🗾","🎑","🏞","🌅","🌄","🌠","🎇","🎆","🌇","🌆","🏙","🌃","🌌","🌉","🌁"],icon:Qt},{title:Object(W.t)("objects",{context:"emoji_group"}),
emojis:["⌚️","📱","📲","💻","⌨️","🖥","🖨","🖱","🖲","🕹","🗜","💽","💾","💿","📀","📼","📷","📸","📹","🎥","📽","🎞","📞","☎️","📟","📠","📺","📻","🎙","🎚","🎛","🧭","⏱","⏲","⏰","🕰","⌛️","⏳","📡","🔋","🔌","💡","🔦","🕯","🪔","🧯","🛢","💸","💵","💴","💶","💷","💰","💳","💎","⚖️","🧰","🔧","🔨","⚒","🛠","⛏","🔩","⚙️","🧱","⛓","🧲","🔫","💣","🧨","🪓","🔪","🗡","⚔️","🛡","🚬","⚰️","⚱️","🏺","🔮","📿","🧿","💈","⚗️","🔭","🔬","🕳","🩹","🩺","💊","💉","🧬","🦠","🧫","🧪","🌡","🧹","🧺","🧻","🚽","🚰","🚿","🛁","🛀","🧼","🪒","🧽","🧴","🛎","🔑","🗝","🚪","🪑","🛋","🛏","🛌","🧸","🖼","🛍","🛒","🎁","🎈","🎏","🎀","🎊","🎉","🎎","🏮","🎐","🧧","✉️","📩","📨","📧","💌","📥","📤","📦","🏷","📪","📫","📬","📭","📮","📯","📜","📃","📄","📑","🧾","📊","📈","📉","🗒","🗓","📆","📅","🗑","📇","🗃","🗳","🗄","📋","📁","📂","🗂","🗞","📰","📓","📔","📒","📕","📗","📘","📙","📚","📖","🔖","🧷","🔗","📎","🖇","📐","📏","🧮","📌","📍","✂️","🖊","🖋","✒️","🖌","🖍","📝","✏️","🔍","🔎","🔏","🔐","🔒","🔓","🧳","🌂","☂️","🧵","🧶","👓","🕶","🥽","🥼","🦺","👔","👕","👖","🧣","🧤","🧥","🧦","👗","👘","🥻","🩱","🩲","🩳","👙","👚","👛","👜","👝","🎒","👞","👟","🥾","🥿","👠","👡","🩰","👢","👑","👒","🎩","🎓","🧢","⛑","💄","💍","💼"],icon:Zt},{title:Object(W.t)("symbols",{context:"emoji_group"}),emojis:["❤️","🧡","💛","💚","💙","💜","🖤","🤍","🤎","💔","❣️","💕","💞","💓","💗","💖","💘","💝","💟","☮️","✝️","☪️","🕉","☸️","✡️","🔯","🕎","☯️","☦️","🛐","⛎","♈️","♉️","♊️","♋️","♌️","♍️","♎️","♏️","♐️","♑️","♒️","♓️","🆔","⚛️","🉑","☢️","☣️","📴","📳","🈶","🈚️","🈸","🈺","🈷️","✴️","🆚","💮","🉐","㊙️","㊗️","🈴","🈵","🈹","🈲","🅰️","🅱️","🆎","🆑","🅾️","🆘","❌","⭕️","🛑","⛔️","📛","🚫","💯","💢","♨️","🚷","🚯","🚳","🚱","🔞","📵","🚭","❗️","❕","❓","❔","‼️","⁉️","🔅","🔆","〽️","⚠️","🚸","🔱","⚜️","🔰","♻️","✅","🈯️","💹","❇️","✳️","❎","🌐","💠","Ⓜ️","🌀","💤","🏧","🚾","♿️","🅿️","🈳","🈂️","🛂","🛃","🛄","🛅","🚹","🚺","🚼","🚻","🚮","🎦","📶","🈁","🔣","ℹ️","🔤","🔡","🔠","🆖","🆗","🆙","🆒","🆕","🆓","0️⃣","1️⃣","2️⃣","3️⃣","4️⃣","5️⃣","6️⃣","7️⃣","8️⃣","9️⃣","🔟","🔢","#️⃣","*️⃣","⏏️","▶️","⏸","⏯","⏹","⏺","⏭","⏮","⏩","⏪","⏫","⏬","◀️","🔼","🔽","➡️","⬅️","⬆️","⬇️","↗️","↘️","↙️","↖️","↕️","↔️","↪️","↩️","⤴️","⤵️","🔀","🔁","🔂","🔄","🔃","🎵","🎶","➕","➖","➗","✖️","♾","💲","💱","™️","©️","®️","〰️","➰","➿","🔚","🔙","🔛","🔝","🔜","✔️","☑️","🔘","🔴","🟠","🟡","🟢","🔵","🟣","⚫️","⚪️","🟤","🔺","🔻","🔸","🔹","🔶","🔷","🔳","🔲","▪️","▫️","◾️","◽️","◼️","◻️","🟥","🟧","🟨","🟩","🟦","🟪","⬛️","⬜️","🟫","🔈","🔇","🔉","🔊","🔔","🔕","📣","📢","👁‍🗨","💬","💭","🗯","♠️","♣️","♥️","♦️","🃏","🎴","🀄️","🕐","🕑","🕒","🕓","🕔","🕕","🕖","🕗","🕘","🕙","🕚","🕛","🕜","🕝","🕞","🕟","🕠","🕡","🕢","🕣","🕤","🕥","🕦","🕧"],icon:$t},{title:Object(W.t)("flags",{context:"emoji_group"}),
emojis:["🏳️","🏴","🏁","🚩","🏳️‍🌈","🏴‍☠️","🇦🇫","🇦🇽","🇦🇱","🇩🇿","🇦🇸","🇦🇩","🇦🇴","🇦🇮","🇦🇶","🇦🇬","🇦🇷","🇦🇲","🇦🇼","🇦🇺","🇦🇹","🇦🇿","🇧🇸","🇧🇭","🇧🇩","🇧🇧","🇧🇾","🇧🇪","🇧🇿","🇧🇯","🇧🇲","🇧🇹","🇧🇴","🇧🇦","🇧🇼","🇧🇷","🇮🇴","🇻🇬","🇧🇳","🇧🇬","🇧🇫","🇧🇮","🇰🇭","🇨🇲","🇨🇦","🇮🇨","🇨🇻","🇧🇶","🇰🇾","🇨🇫","🇹🇩","🇨🇱","🇨🇳","🇨🇽","🇨🇨","🇨🇴","🇰🇲","🇨🇬","🇨🇩","🇨🇰","🇨🇷","🇨🇮","🇭🇷","🇨🇺","🇨🇼","🇨🇾","🇨🇿","🇩🇰","🇩🇯","🇩🇲","🇩🇴","🇪🇨","🇪🇬","🇸🇻","🇬🇶","🇪🇷","🇪🇪","🇪🇹","🇪🇺","🇫🇰","🇫🇴","🇫🇯","🇫🇮","🇫🇷","🇬🇫","🇵🇫","🇹🇫","🇬🇦","🇬🇲","🇬🇪","🇩🇪","🇬🇭","🇬🇮","🇬🇷","🇬🇱","🇬🇩","🇬🇵","🇬🇺","🇬🇹","🇬🇬","🇬🇳","🇬🇼","🇬🇾","🇭🇹","🇭🇳","🇭🇰","🇭🇺","🇮🇸","🇮🇳","🇮🇩","🇮🇷","🇮🇶","🇮🇪","🇮🇲","🇮🇱","🇮🇹","🇯🇲","🇯🇵","🎌","🇯🇪","🇯🇴","🇰🇿","🇰🇪","🇰🇮","🇽🇰","🇰🇼","🇰🇬","🇱🇦","🇱🇻","🇱🇧","🇱🇸","🇱🇷","🇱🇾","🇱🇮","🇱🇹","🇱🇺","🇲🇴","🇲🇰","🇲🇬","🇲🇼","🇲🇾","🇲🇻","🇲🇱","🇲🇹","🇲🇭","🇲🇶","🇲🇷","🇲🇺","🇾🇹","🇲🇽","🇫🇲","🇲🇩","🇲🇨","🇲🇳","🇲🇪","🇲🇸","🇲🇦","🇲🇿","🇲🇲","🇳🇦","🇳🇷","🇳🇵","🇳🇱","🇳🇨","🇳🇿","🇳🇮","🇳🇪","🇳🇬","🇳🇺","🇳🇫","🇰🇵","🇲🇵","🇳🇴","🇴🇲","🇵🇰","🇵🇼","🇵🇸","🇵🇦","🇵🇬","🇵🇾","🇵🇪","🇵🇭","🇵🇳","🇵🇱","🇵🇹","🇵🇷","🇶🇦","🇷🇪","🇷🇴","🇷🇺","🇷🇼","🇼🇸","🇸🇲","🇸🇦","🇸🇳","🇷🇸","🇸🇨","🇸🇱","🇸🇬","🇸🇽","🇸🇰","🇸🇮","🇬🇸","🇸🇧","🇸🇴","🇿🇦","🇰🇷","🇸🇸","🇪🇸","🇱🇰","🇧🇱","🇸🇭","🇰🇳","🇱🇨","🇵🇲","🇻🇨","🇸🇩","🇸🇷","🇸🇿","🇸🇪","🇨🇭","🇸🇾","🇹🇼","🇹🇯","🇹🇿","🇹🇭","🇹🇱","🇹🇬","🇹🇰","🇹🇴","🇹🇹","🇹🇳","🇹🇷","🇹🇲","🇹🇨","🇹🇻","🇻🇮","🇺🇬","🇺🇦","🇦🇪","🇬🇧","🏴󠁧󠁢󠁥󠁮󠁧󠁿","🏴󠁧󠁢󠁳󠁣󠁴󠁿","🏴󠁧󠁢󠁷󠁬󠁳󠁿","🇺🇳","🇺🇸","🇺🇾","🇺🇿","🇻🇺","🇻🇦","🇻🇪","🇻🇳","🇼🇫","🇪🇭","🇾🇪","🇿🇲","🇿🇼"],icon:en}];var nn=n("9dlw"),an=n("mkWe"),on=n("Sn4D"),rn=n("DTHj"),ln=n("nPPD");var cn=n("Iksw"),sn=n("uhCe"),dn=n("7Y2P");function un(e){const{children:t,highlight:n,disabled:a,reference:o}=e,r=n?"primary":"default";return i.a.createElement("div",{ref:o,className:p()(dn.wrapper,dn["intent-"+r],dn["border-thin"],dn["size-medium"],n&&dn.highlight,n&&dn.focused,a&&dn.disabled),"data-role":"button"},i.a.createElement("div",{className:p()(dn.childrenContainer,a&&dn.disabled)},t),n&&i.a.createElement("span",{className:dn.shadow}))}var pn=n("YV34");const mn=()=>null,hn=Object(ln.a)(rn.a,{menuBox:pn.menuBox});function fn(e){const{value:t,disabled:n,onSelect:o}=e,r=Object(a.useRef)(null),{current:l}=Object(a.useRef)(St.getJSON("RecentlyUsedEmojis",[t])),[c,s]=Object(a.useState)(l),[d,u]=Object(a.useState)(!1),p=Object(a.useCallback)(()=>u(!1),[]);var m;m=p,Object(a.useEffect)(()=>(document.addEventListener("scroll",m),()=>{document.removeEventListener("scroll",m)}),[m]);const h=Object(a.useCallback)(e=>{const t=Array.from(new Set([e,...c])).slice(0,18);St.setJSON("RecentlyUsedEmojis",t),s(t),o(e),p()},[c,o]),f=(b=c,Object(a.useMemo)(()=>(tn[0].emojis=b,[...tn]),[b]));var b
;return i.a.createElement(i.a.Fragment,null,i.a.createElement(un,{reference:r,highlight:d,disabled:n},i.a.createElement(Ft,{emoji:t,onClick:function(){n||u(!0)}})),i.a.createElement(mt.a,{rule:sn.a.TabletSmall},e=>d&&i.a.createElement(an.b,null,e?i.a.createElement(on.a,{className:pn.drawer,position:"Bottom",onClose:p},i.a.createElement(Wt,{emojis:f,onSelect:h})):i.a.createElement(nn.a,{theme:hn,isOpened:!0,position:Object(cn.e)(r.current,{horizontalDropDirection:cn.b.FromLeftToRight,horizontalAttachEdge:cn.a.Left}),onClickOutside:p,onClose:mn},i.a.createElement(Wt,{className:pn.desktopSize,emojis:f,onSelect:h})))))}var bn=n("9UfQ");function vn(e){const{definition:{title:t,properties:n}}=e,{checked:a,emoji:o,backgroundColor:l}=n,[c,s]=Object(r.a)({property:a,defaultValue:!1}),[d,u]=Object(r.a)({property:o,defaultValue:"🙂"}),[p,m]=Object(r.a)({property:l,defaultValue:be.a["color-tv-blue-a600"]});return i.a.createElement("div",{className:bn.wrapper},i.a.createElement(yt.a,{className:bn.checkbox,label:t,checked:c,onChange:function(){s(!c)}}),i.a.createElement(fn,{value:d,disabled:!c,onSelect:u}),i.a.createElement(U.a,{className:bn.colorSelect,disabled:!c,color:p,onColorChange:m}))}function gn(e){const{definition:t}=e;if(function(e){Object(a.useEffect)(()=>{if(void 0===e)return;const t=Object.assign({},e.properties);return Object.entries(t).forEach(([n,a])=>{void 0!==a&&a.subscribe(t,()=>st.a.logNormal(`Property "${n}" in definition "${e.id}" was updated to value "${a.value()}"`))}),()=>{Object.entries(t).forEach(([,e])=>{void 0!==e&&e.unsubscribeAll(t)})}},[e])}(Object(o.A)(t)?void 0:t),Object(o.A)(t)){const r=t.definitions;return i.a.createElement(a.Fragment,null,t.title&&i.a.createElement(bt.a,{title:t.title,name:t.id}),r&&(n=r.value(),n.reduce((e,t)=>{if(Object(o.A)(t)||"leveledLine"!==t.propType)e.push(t);else{const n=e[e.length-1];Array.isArray(n)?n.push(t):e.push([t])}return e},[])).map(n=>Array.isArray(n)?i.a.createElement(gt,{key:n[0].id,name:t.id,definitions:n}):i.a.createElement(gn,Object.assign({key:n.id},e,{definition:n}))),"general"===t.groupType&&i.a.createElement(l.a.GroupSeparator,{size:1}))}switch(t.propType){case"line":return i.a.createElement(le,Object.assign({},e,{definition:t}));case"checkable":return i.a.createElement(d,Object.assign({},e,{definition:t}));case"color":return i.a.createElement(he,Object.assign({},e,{definition:t}));case"transparency":return i.a.createElement(Ee,Object.assign({},e,{definition:t}));case"twoColors":return i.a.createElement(Oe,Object.assign({},e,{definition:t}));case"number":return i.a.createElement(xe,Object.assign({},e,{definition:t}));case"symbol":return i.a.createElement(it,Object.assign({},e,{definition:t}));case"text":return i.a.createElement(Ge,Object.assign({},e,{definition:t}));case"checkableSet":return i.a.createElement(Ce,Object.assign({},e,{definition:t}));case"options":return i.a.createElement(ct,Object.assign({},e,{definition:t}));case"range":return i.a.createElement(ft,Object.assign({},e,{definition:t}));case"coordinates":
return i.a.createElement(rt,Object.assign({},e,{definition:t}));case"twoOptions":return i.a.createElement(Ot,Object.assign({},e,{definition:t}));case"leveledLine":return i.a.createElement(wt,Object.assign({},e,{definition:t}));case"emoji":return i.a.createElement(vn,Object.assign({},e,{definition:t}));default:return null}var n}n.d(t,"a",(function(){return gn}))},"e/Lx":function(e,t,n){e.exports={wrapper:"wrapper-1-S6uQxU",emoji:"emoji-1-S6uQxU"}},eU7S:function(e,t,n){e.exports={line:"line-2EFUN2WR",control:"control-2EFUN2WR",valueInput:"valueInput-2EFUN2WR",valueUnit:"valueUnit-2EFUN2WR",input:"input-2EFUN2WR"}},gX9w:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M13.98 6.02L14.5 6c2.18 0 4.16.8 5.66 2.14l-5.66 5.65-2.31-2.3a8.43 8.43 0 0 0 1.55-3.64 14.01 14.01 0 0 0 .24-1.83zm-1.01.12a8.45 8.45 0 0 0-4.13 2l2.64 2.63a7.59 7.59 0 0 0 1.28-3.12c.12-.59.18-1.12.2-1.51zm-4.83 2.7a8.45 8.45 0 0 0-2 4.13c.39-.03.92-.1 1.51-.21a7.59 7.59 0 0 0 3.12-1.28L8.14 8.84zm-2.12 5.14a8.48 8.48 0 0 0 2.12 6.18l5.65-5.66-2.3-2.31a8.43 8.43 0 0 1-3.64 1.55 14.03 14.03 0 0 1-1.83.24zm2.82 6.88a8.46 8.46 0 0 0 5.13 2.12v-.07A8.95 8.95 0 0 1 16.3 17l-1.8-1.8-5.66 5.65zM14.97 23c2-.1 3.8-.9 5.19-2.13L17 17.72a7.94 7.94 0 0 0-2.04 5.27zm5.9-2.83a8.46 8.46 0 0 0 2.11-5.13h-.02a10.62 10.62 0 0 0-5.2 2l3.1 3.13zm2.12-6.13c-.1-2-.9-3.8-2.13-5.19l-5.65 5.66 1.83 1.83a11.6 11.6 0 0 1 5.95-2.3zM14.5 5A9.46 9.46 0 0 0 5 14.5c0 5.28 4.22 9.5 9.5 9.5s9.5-4.22 9.5-9.5S19.78 5 14.5 5z"/></svg>'},"gd+L":function(e,t){
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M12.5 8h1.36l-.85-3.38.98-.24.9 3.62h7.64a1.34 1.34 0 0 1 .2.02c.13.02.31.07.5.16.18.09.38.24.53.46.15.24.24.52.24.86 0 .34-.09.62-.24.86a1.38 1.38 0 0 1-.79.56L22 24.54l-.03.46H6.5c-1 0-1.64-.68-1.99-1.23a4.4 4.4 0 0 1-.38-.78l-.01-.04c-.1-.03-.22-.07-.34-.13a1.36 1.36 0 0 1-.54-.46A1.51 1.51 0 0 1 3 21.5c0-.34.09-.62.24-.86.15-.22.35-.37.54-.46.1-.05.2-.09.28-.11a6.6 6.6 0 0 1 .96-2.34C5.92 16.35 7.56 15 10.5 15c.72 0 1.36.08 1.93.22l-.4-4.3a1.38 1.38 0 0 1-.8-.57A1.51 1.51 0 0 1 11 9.5c0-.34.09-.62.24-.86.15-.22.35-.37.54-.46a1.73 1.73 0 0 1 .7-.18h.02v.5V8zm.96 7.57a5.73 5.73 0 0 1 2.52 2.16 6.86 6.86 0 0 1 .95 2.34 1.38 1.38 0 0 1 .82.58c.16.23.25.51.25.85 0 .34-.09.62-.24.86-.15.22-.35.37-.54.46-.12.06-.24.1-.34.13l-.01.04a4.4 4.4 0 0 1-.54 1.01h4.7l.93-13h-8.91l.41 4.57zM14.5 9h8a.73.73 0 0 1 .28.07c.06.04.11.08.15.13.03.05.07.14.07.3 0 .16-.04.25-.07.3a.38.38 0 0 1-.15.13.73.73 0 0 1-.27.07H12.5a.73.73 0 0 1-.28-.07.38.38 0 0 1-.15-.13.52.52 0 0 1-.07-.3c0-.16.04-.25.07-.3.04-.05.09-.1.15-.13A.73.73 0 0 1 12.5 9h2.01zm1.4 11a5.8 5.8 0 0 0-.76-1.73C14.41 17.15 13.06 16 10.5 16c-2.56 0-3.91 1.15-4.64 2.27A5.86 5.86 0 0 0 5.1 20h10.78zM4.5 21a.72.72 0 0 0-.28.07.38.38 0 0 0-.15.13.52.52 0 0 0-.07.3c0 .16.04.25.07.3.04.05.09.1.15.13a.73.73 0 0 0 .27.07H16.5a.72.72 0 0 0 .28-.07.38.38 0 0 0 .15-.13.52.52 0 0 0 .07-.3.52.52 0 0 0-.07-.3.38.38 0 0 0-.15-.13.73.73 0 0 0-.27-.07H4.5zm.73 2l.13.23c.28.45.65.77 1.14.77h8c.5 0 .86-.32 1.14-.77.05-.07.1-.15.13-.23H5.23zM11 17v1h-1v-1h1zm-3 1h1v1H8v-1zm4 1v-1h1v1h-1z"/></svg>'},gla1:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var a=n("q1tI");const i=()=>{const[,e]=Object(a.useReducer)((e,t)=>e+1,0);return e}},hreg:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M9.5 21H9h.5zm8 0H17h.5zm-6-10H11v1h.5v-1zm4 1h.5v-1h-.5v1zm2 7.5h.5-.5zm.29-1.59A7.97 7.97 0 0 0 21 11.5h-1a6.97 6.97 0 0 1-2.79 5.59l.58.82zM21 11.5A7.5 7.5 0 0 0 13.5 4v1a6.5 6.5 0 0 1 6.5 6.5h1zM13.5 4A7.5 7.5 0 0 0 6 11.5h1A6.5 6.5 0 0 1 13.5 5V4zM6 11.5a7.98 7.98 0 0 0 3.21 6.41l.57-.82A6.98 6.98 0 0 1 7 11.5H6zM9 21a1 1 0 0 0 1 1v-1H9zm8 1a1 1 0 0 0 1-1h-1v1zm-6-.5V23h1v-1.5h-1zm0 1.5a1 1 0 0 0 1 1v-1h-1zm1 1h3v-1h-3v1zm3 0a1 1 0 0 0 1-1h-1v1zm1-1v-1.5h-1V23h1zm-3-11.5v6h1v-6h-1zM9.5 20h8v-1h-8v1zM9 17.5v2h1v-2H9zm0 2V21h1v-1.5H9zm9 1.5v-1.5h-1V21h1zm0-1.5v-2h-1v2h1zM9.5 18h4v-1h-4v1zm4 0h4v-1h-4v1zm-2-6h2v-1h-2v1zm2 0h2v-1h-2v1zM10 22h1.5v-1H10v1zm1.5 0h4v-1h-4v1zm4 0H17v-1h-1.5v1z"/></svg>'},"i/MG":function(e,t,n){"use strict";n.d(t,"a",(function(){return d}));var a=n("mrSG"),i=(n("YFKU"),n("q1tI")),o=n("TSYQ"),r=n("Iivm"),l=n("To8B"),c=n("kXJy");const s={remove:window.t("Remove")};function d(e){
const{className:t,isActive:n,onClick:d,title:u,hidden:p,"data-name":m="remove-button"}=e,h=Object(a.a)(e,["className","isActive","onClick","title","hidden","data-name"]);return i.createElement(r.a,Object.assign({},h,{"data-name":m,className:o(c.button,"apply-common-tooltip",n&&c.active,p&&c.hidden,t),icon:l,onClick:d,title:u||s.remove}))}},ioCK:function(e,t,n){e.exports={dropdown:"dropdown-Y-VtYUMO",dropdownMenu:"dropdownMenu-Y-VtYUMO",firstColorPicker:"firstColorPicker-Y-VtYUMO"}},iy5y:function(e,t,n){e.exports={list:"list-tOao0Vo7"}},j0Er:function(e,t,n){e.exports={wrapper:"wrapper-1ecGiBuB"}},jAqK:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M4.5 13.5H24m-19.5 0L8 17m-3.5-3.5L8 10"/></svg>'},kJwE:function(e,t,n){e.exports={lineWidthSelect:"lineWidthSelect-3VlOWnM8",bar:"bar-3VlOWnM8",isActive:"isActive-3VlOWnM8",item:"item-3VlOWnM8"}},kXJy:function(e,t,n){e.exports={button:"button-3B9fDLtm",disabled:"disabled-3B9fDLtm",active:"active-3B9fDLtm",hidden:"hidden-3B9fDLtm"}},lB1i:function(e,t,n){e.exports={wrap:"wrap-3yqu-5zT",disabled:"disabled-3yqu-5zT"}},mkWe:function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"a",(function(){return r}));var a=n("q1tI"),i=n.n(a);class o extends i.a.PureComponent{constructor(e){super(e),this._addDrawer=()=>{const e=this.state.currentDrawer+1;return this.setState({currentDrawer:e}),e},this._removeDrawer=()=>{const e=this.state.currentDrawer-1;return this.setState({currentDrawer:e}),e},this.state={currentDrawer:0}}render(){return i.a.createElement(r.Provider,{value:{addDrawer:this._addDrawer,removeDrawer:this._removeDrawer,currentDrawer:this.state.currentDrawer}},this.props.children)}}const r=i.a.createContext(null)},mt7N:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" d="M7.5 24v-5.5m0 0s2.7-1.1 4.5-1c2.1.12 2.9 1.88 5 2 1.8.1 4.5-1 4.5-1v-6m-14 6v-6m0 0v-6s2.7-1.1 4.5-1c2.1.12 2.9 1.88 5 2 1.8.1 4.5-1 4.5-1v6m-14 0s2.7-1.1 4.5-1c2.1.12 2.9 1.88 5 2 1.8.1 4.5-1 4.5-1"/></svg>'},nPPD:function(e,t,n){"use strict";function a(e,t,n={}){const a=Object.assign({},t);for(const i of Object.keys(t)){const o=n[i]||i;o in e&&(a[i]=[e[o],t[i]].join(" "))}return a}function i(e,t,n={}){return Object.assign({},e,a(e,t,n))}n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return i}))},oWdB:function(e,t,n){e.exports={twoColors:"twoColors-1b_W-OHw",colorPicker:"colorPicker-1b_W-OHw"}},r6bn:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M5.6 15.43A6.19 6.19 0 0 1 14 6.36a6.19 6.19 0 0 1 8.4 9.08l-.03.02-7.3 7.31a1.5 1.5 0 0 1-2.13 0l-7.3-7.3-.03-.03m.71-.7v-.01a5.19 5.19 0 0 1 7.33-7.34v.01c.2.2.51.19.7 0a5.19 5.19 0 0 1 7.34 7.33l-.03.02-7.3 7.31a.5.5 0 0 1-.71 0l-7.3-7.3-.03-.02z"/></svg>'},rRJX:function(e,t){
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M14 21h-3a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h3c2 0 4 1 4 3 0 1 0 2-1.5 3 1.5.5 2.5 2 2.5 4 0 2.75-2.638 4-5 4zM12 9l.004 3c.39.026.82 0 1.25 0C14.908 12 16 11.743 16 10.5c0-1.1-.996-1.5-2.5-1.5-.397 0-.927-.033-1.5 0zm0 5v5h1.5c1.5 0 3.5-.5 3.5-2.5S15 14 13.5 14c-.5 0-.895-.02-1.5 0z"/></svg>'},sHQ4:function(e,t,n){e.exports={wrap:"wrap-164vy-kj",positionBottom:"positionBottom-164vy-kj",backdrop:"backdrop-164vy-kj",drawer:"drawer-164vy-kj",positionLeft:"positionLeft-164vy-kj"}},tCqQ:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var a=n("q1tI"),i=n("Eyy1");function o(e){return Object(i.ensureNotNull)(Object(a.useContext)(e))}},tJif:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M4.54 3.2l.78-.59 5.49 4.5 1.43 1.07a5.28 5.28 0 0 1 2.19-2.3 9.19 9.19 0 0 1 1.88-.85h.04l.01-.01.14.48.42-.28v.01l.01.02a3.14 3.14 0 0 1 .16.26l.37.72c.2.45.4 1.02.5 1.64a2.13 2.13 0 0 1 1.89.46l.18.16.03.02.18.16c.22.16.42.27.81.25a5.9 5.9 0 0 0 2.2-.86l.66-.36.09.75a5.98 5.98 0 0 1-1.7 5.1 6.87 6.87 0 0 1-1.7 1.23 19.97 19.97 0 0 1 .48 2.48c.25 1.73.42 4.08.06 6.5A1.46 1.46 0 0 1 19.68 25h-7.71a1.5 1.5 0 0 1-1.4-2.06l1-2.47c-.18.02-.37.03-.58.03a3 3 0 0 1-1.53-.4 6.84 6.84 0 0 1-1.6.64c-1.08.27-2.55.29-3.72-.89a4.06 4.06 0 0 1-.96-3 5.1 5.1 0 0 1 2-3.74 98.5 98.5 0 0 0 2.7-2.24L4.55 3.2zM16.5 5.5l-.14-.48.35-.1.2.3-.41.28zm-7.87 6.06a57.48 57.48 0 0 1-2.19 1.82l.49.26c.65.37 1.48.9 1.97 1.56a5.78 5.78 0 0 1 1.14 4.07l.06.03c.19.1.49.2.9.2.68 0 .95-.11 1.03-.16v-.03l.97.19h-.5.5v.03a.75.75 0 0 1-.01.1.74.74 0 0 1-.09.21l-1.39 3.47a.5.5 0 0 0 .47.69h7.71c.24 0 .43-.17.47-.38a22 22 0 0 0-.06-6.22 24.4 24.4 0 0 0-.56-2.71 11.35 11.35 0 0 0-.94-1.52 7.1 7.1 0 0 0-2.31-2.22l-.62-.31.49-.5A3.03 3.03 0 0 0 17 8.6a1.2 1.2 0 0 0 .01-.1c0-.65-.22-1.33-.46-1.86-.1-.21-.18-.4-.26-.54a8.07 8.07 0 0 0-1.34.64c-.9.54-1.74 1.32-1.95 2.36v.03l-.02.03L12.5 9l.47.16v.02a2.97 2.97 0 0 1-.1.26 5.9 5.9 0 0 1-.31.62c-.27.46-.7 1.07-1.34 1.39-.63.31-1.38.3-1.9.23a5.83 5.83 0 0 1-.7-.12zm3.26-2.39L10.2 7.9l-.02-.01L6.3 4.7l2.57 5.88h.01c.14.04.34.08.57.11.47.06.97.05 1.34-.14.36-.18.68-.57.91-.99.08-.14.15-.27.2-.39zm8.32 4.68a5.47 5.47 0 0 0 1.37-1.02 4.88 4.88 0 0 0 1.46-3.53c-.8.39-1.41.58-1.92.61-.7.05-1.14-.18-1.49-.45a5.6 5.6 0 0 1-.22-.19l-.03-.03-.17-.13a1.4 1.4 0 0 0-.33-.22c-.18-.07-.44-.12-.93 0l-.1.4c-.1.3-.28.69-.58 1.09.87.59 1.6 1.46 2.14 2.2a14.92 14.92 0 0 1 .8 1.27zM9.05 19.19v-.09a4.78 4.78 0 0 0-.96-3.3 5.56 5.56 0 0 0-1.65-1.29c-.3-.17-.6-.3-.8-.4l-.05-.03a4.05 4.05 0 0 0-1.4 2.82 3.1 3.1 0 0 0 .66 2.25c.83.82 1.86.84 2.78.62a5.71 5.71 0 0 0 1.42-.58zm4.26-5.87c-.3.24-.74.54-1.18.66-.37.1-.81.1-1.12.08a6.95 6.95 0 0 1-.54-.06h-.05l.08-.5.08-.5.03.01a5.02 5.02 0 0 0 1.26 0c.24-.06.54-.25.83-.47a6.1 6.1 0 0 0 .42-.37l.02-.02.36.35.35.36h-.01l-.03.04a6.09 6.09 0 0 1-.5.42zM6 17h1v-1H6v1z"/></svg>'},tQXF:function(e,t,n){
e.exports={wrapper:"wrapper-2uFJv5Fz",emojiItem:"emojiItem-2uFJv5Fz"}},vqb8:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var a=n("q1tI");const i=e=>{const t="watchedValue"in e?e.watchedValue:void 0,n="defaultValue"in e?e.defaultValue:e.watchedValue.value(),[i,o]=Object(a.useState)(t?t.value():n);return Object(a.useEffect)(()=>{if(t){o(t.value());const e=e=>o(e);return t.subscribe(e),()=>t.unsubscribe(e)}return()=>{}},[t]),i}},ybVX:function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"a",(function(){return r}));var a=n("q1tI"),i=n.n(a);const o=i.a.createContext({}),r=i.a.createContext({})},z1Uu:function(e,t,n){e.exports={defaultSelect:"defaultSelect-rvczD149"}}}]);