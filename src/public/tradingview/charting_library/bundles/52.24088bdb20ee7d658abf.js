(window.webpackJsonp=window.webpackJsonp||[]).push([[52],{"2dtg":function(o,e){o.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><g fill="currentColor" fill-rule="evenodd"><path fill-rule="nonzero" d="M23.002 23C23 23 23 18.003 23 18.003L15.998 18C16 18 16 22.997 16 22.997l7.002.003zM15 18.003A1 1 0 0 1 15.998 17h7.004c.551 0 .998.438.998 1.003v4.994A1 1 0 0 1 23.002 24h-7.004A.993.993 0 0 1 15 22.997v-4.994z"/><path d="M19 20h1v2h-1z"/><path fill-rule="nonzero" d="M22 14.5a2.5 2.5 0 0 0-5 0v3h1v-3a1.5 1.5 0 0 1 3 0v.5h1v-.5z"/><g fill-rule="nonzero"><path d="M3 14.707A1 1 0 0 1 3.293 14L14.439 2.854a1.5 1.5 0 0 1 2.122 0l2.585 2.585a1.5 1.5 0 0 1 0 2.122L8 18.707a1 1 0 0 1-.707.293H4a1 1 0 0 1-1-1v-3.293zm1 0V18h3.293L18.439 6.854a.5.5 0 0 0 0-.708l-2.585-2.585a.5.5 0 0 0-.708 0L4 14.707z"/><path d="M13.146 4.854l4 4 .708-.708-4-4zm-9 9l4 4 .708-.708-4-4z"/><path d="M15.146 6.146l-9 9 .708.708 9-9z"/></g></g></svg>'},"2lje":function(o,e){o.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 29 31" width="29" height="31"><g fill="currentColor" fill-rule="nonzero"><path d="M15.3 22l8.187-8.187c.394-.394.395-1.028.004-1.418l-4.243-4.243c-.394-.394-1.019-.395-1.407-.006l-11.325 11.325c-.383.383-.383 1.018.007 1.407l1.121 1.121h7.656zm-9.484-.414c-.781-.781-.779-2.049-.007-2.821l11.325-11.325c.777-.777 2.035-.78 2.821.006l4.243 4.243c.781.781.78 2.048-.004 2.832l-8.48 8.48h-8.484l-1.414-1.414z"/><path d="M13.011 22.999h7.999v-1h-7.999zM13.501 11.294l6.717 6.717.707-.707-6.717-6.717z"/></g></svg>'},"3s8f":function(o,e){o.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><g fill="currentColor" fill-rule="evenodd"><path fill-rule="nonzero" d="M14 10a2 2 0 0 0-2 2v11H6V12c0-4.416 3.584-8 8-8s8 3.584 8 8v11h-6V12a2 2 0 0 0-2-2zm-3 2a3 3 0 0 1 6 0v10h4V12c0-3.864-3.136-7-7-7s-7 3.136-7 7v10h4V12z"/><path d="M6.5 18h5v1h-5zm10 0h5v1h-5z"/></g></svg>'},"3zTq":function(o,e){
o.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" d="M16.47 3.7A8.32 8.32 0 0013 2.94c-3.12 0-5.5 1.75-7.06 3.44a16 16 0 00-2.38 3.38v.02h-.01L4 10l-.45-.22-.1.22.1.22L4 10l-.45.22.01.02a5.5 5.5 0 00.15.28 16 16 0 002.53 3.41l.7-.7-.27-.29a15 15 0 01-2.08-2.9L4.56 10l.03-.04a15 15 0 012.09-2.9c1.47-1.6 3.6-3.12 6.32-3.12.98 0 1.88.2 2.7.52l.77-.76zm-7.04 7.04l.93-.93a2.6 2.6 0 012.36-2.36l.95-.95a3.6 3.6 0 00-4.24 4.24zm.1 5.56c1.03.47 2.18.76 3.47.76 3.12 0 5.5-1.75 7.06-3.44a16 16 0 002.38-3.38v-.02h.01L22 10l.45.22.1-.22-.1-.22L22 10l.45-.22-.01-.02-.02-.03-.01-.03a9.5 9.5 0 00-.57-1 16 16 0 00-2.08-2.63l-.7.7.27.29a15.01 15.01 0 012.08 2.9l.03.04-.03.04a15 15 0 01-2.09 2.9c-1.47 1.6-3.6 3.12-6.32 3.12-.98 0-1.88-.2-2.7-.52l-.77.76zm2.8-2.8a3.6 3.6 0 004.24-4.24l-.93.93a2.6 2.6 0 01-2.36 2.36l-.95.95zm7.9 3.73c-.12.12-.23.35-.23.77v2h1v1h-1v2c0 .58-.14 1.1-.52 1.48-.38.38-.9.52-1.48.52s-1.1-.14-1.48-.52c-.38-.38-.52-.9-.52-1.48h1c0 .42.1.65.23.77.12.12.35.23.77.23.42 0 .65-.1.77-.23.12-.12.23-.35.23-.77v-2h-1v-1h1v-2c0-.58.14-1.1.52-1.48.38-.38.9-.52 1.48-.52s1.1.14 1.48.52c.38.38.52.9.52 1.48h-1c0-.42-.1-.65-.23-.77-.12-.12-.35-.23-.77-.23-.42 0-.65.1-.77.23zm2.56 6.27l-1.14-1.15.7-.7 1.15 1.14 1.15-1.14.7.7-1.14 1.15 1.14 1.15-.7.7-1.15-1.14-1.15 1.14-.7-.7 1.14-1.15zM21.2 2.5L5.5 18.2l-.7-.7L20.5 1.8l.7.7z"/></svg>'},"43BO":function(o,e){o.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M14 6a3 3 0 0 0-3 3v3h6V9a3 3 0 0 0-3-3zm4 6V9a4 4 0 0 0-8 0v3H8.5A2.5 2.5 0 0 0 6 14.5v7A2.5 2.5 0 0 0 8.5 24h11a2.5 2.5 0 0 0 2.5-2.5v-7a2.5 2.5 0 0 0-2.5-2.5H18zm-5 5a1 1 0 1 1 2 0v2a1 1 0 1 1-2 0v-2zm-6-2.5c0-.83.67-1.5 1.5-1.5h11c.83 0 1.5.67 1.5 1.5v7c0 .83-.67 1.5-1.5 1.5h-11A1.5 1.5 0 0 1 7 21.5v-7z"/></svg>'},"4rU7":function(o,e,l){"use strict";l.d(e,"a",(function(){return c}));var i=l("q1tI"),n=l("TSYQ"),a=l("Iivm"),t=l("ijHL"),s=l("gb5g");function c(o){const{id:e,activeClass:l,children:c,className:r,icon:T,isActive:d,isGrayed:L,isHidden:m,isTransparent:h,theme:z=s,onClick:v,title:w,buttonHotKey:g,tooltipPosition:u="vertical"}=o;return i.createElement("div",Object.assign({id:e,className:n(z.button,r,d&&l,{"apply-common-tooltip":Boolean(w),"common-tooltip-vertical":Boolean(w)&&"vertical"===u,[z.isActive]:d,[z.isGrayed]:L,[z.isHidden]:m,[z.isTransparent]:h}),onClick:v,title:w,"data-role":"button","data-tooltip-hotkey":g},Object(t.b)(o)),i.createElement("div",{className:z.bg},T&&("string"==typeof T?i.createElement(a.a,{className:z.icon,icon:T}):i.createElement("span",{className:z.icon},T)),c))}},"6oLA":function(o,e){
o.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M4.56 14a10.05 10.05 0 00.52.91c.41.69 1.04 1.6 1.85 2.5C8.58 19.25 10.95 21 14 21c3.05 0 5.42-1.76 7.07-3.58A17.18 17.18 0 0023.44 14a9.47 9.47 0 00-.52-.91c-.41-.69-1.04-1.6-1.85-2.5C19.42 8.75 17.05 7 14 7c-3.05 0-5.42 1.76-7.07 3.58A17.18 17.18 0 004.56 14zM24 14l.45-.21-.01-.03a7.03 7.03 0 00-.16-.32c-.11-.2-.28-.51-.5-.87-.44-.72-1.1-1.69-1.97-2.65C20.08 7.99 17.45 6 14 6c-3.45 0-6.08 2-7.8 3.92a18.18 18.18 0 00-2.64 3.84v.02h-.01L4 14l-.45-.21-.1.21.1.21L4 14l-.45.21.01.03a5.85 5.85 0 00.16.32c.**********.********** 1.1 1.69 1.97 2.65C7.92 20.01 10.55 22 14 22c3.45 0 6.08-2 7.8-3.92a18.18 18.18 0 002.64-3.84v-.02h.01L24 14zm0 0l.45.21.1-.21-.1-.21L24 14zm-10-3a3 3 0 100 6 3 3 0 000-6zm-4 3a4 4 0 118 0 4 4 0 01-8 0z"/></svg>'},ASyk:function(o,e,l){o.exports={"tablet-normal-breakpoint":"screen and (max-width: 768px)","small-height-breakpoint":"screen and (max-height: 360px)","tablet-small-breakpoint":"screen and (max-width: 428px)"}},Csdk:function(o,e){o.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><circle fill="currentColor" cx="14" cy="14" r="3"/></svg>'},Ex32:function(o,e){o.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M5 10.76a13.27 13.27 0 01-.41-.72L4.56 10l.03-.04a15 15 0 012.08-2.9c1.48-1.6 3.6-3.12 6.33-3.12s4.85 1.53 6.33 3.12a15.01 15.01 0 012.08 2.9l.03.04-.03.04a15 15 0 01-2.08 2.9c-1.48 1.6-3.6 3.12-6.33 3.12s-4.85-1.53-6.33-3.12a15 15 0 01-1.66-2.18zm17.45-.98L22 10l.45.22-.01.02a14.3 14.3 0 01-.6 1.05c-.4.64-1 1.48-1.78 2.33-1.56 1.7-3.94 3.44-7.06 3.44s-5.5-1.75-7.06-3.44a16 16 0 01-2.23-3.1 9.39 9.39 0 01-.15-.28v-.02h-.01L4 10l-.45-.22.01-.02a5.59 5.59 0 01.15-.28 16 16 0 012.23-3.1C7.5 4.69 9.87 2.94 13 2.94c3.12 0 5.5 1.75 7.06 3.44a16 16 0 012.23 3.1 9.5 9.5 0 01.15.28v.01l.01.01zM22 10l.45-.22.1.22-.1.22L22 10zM3.55 9.78L4 10l-.45.22-.1-.22.1-.22zm6.8.22A2.6 2.6 0 0113 7.44 2.6 2.6 0 0115.65 10 2.6 2.6 0 0113 12.56 2.6 2.6 0 0110.35 10zM13 6.44A3.6 3.6 0 009.35 10c0 1.98 1.65 3.56 3.65 3.56s3.65-1.58 3.65-3.56A3.6 3.6 0 0013 6.44zM20 18c0-.42.1-.65.23-.77.12-.13.35-.23.77-.23.42 0 .65.1.77.23.13.12.23.35.23.77h1c0-.58-.14-1.1-.52-1.48-.38-.38-.9-.52-1.48-.52s-1.1.14-1.48.52c-.37.38-.52.9-.52 1.48v2h-1v1h1v2c0 .42-.1.65-.23.77-.12.13-.35.23-.77.23-.42 0-.65-.1-.77-.23-.13-.12-.23-.35-.23-.77h-1c0 .58.14 1.1.52 1.48.38.37.9.52 1.48.52s1.1-.14 1.48-.52c.37-.38.52-.9.52-1.48v-2h1v-1h-1v-2zm1.65 4.35l1.14 1.15-1.14 1.15.7.7 1.15-1.14 1.15 1.14.7-.7-1.14-1.15 1.14-1.15-.7-.7-1.15 1.14-1.15-1.14-.7.7z"/></svg>'},FS6t:function(o,e){
o.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M5 10.76l-.41-.72-.03-.04.03-.04a15 15 0 012.09-2.9c1.47-1.6 3.6-3.12 6.32-3.12 2.73 0 4.85 1.53 6.33 3.12a15.01 15.01 0 012.08 2.9l.03.04-.03.04a15 15 0 01-2.09 2.9c-1.47 1.6-3.6 3.12-6.32 3.12-2.73 0-4.85-1.53-6.33-3.12a15 15 0 01-1.66-2.18zm17.45-.98L22 10l.45.22-.01.02a5.04 5.04 0 01-.15.28 16.01 16.01 0 01-2.23 3.1c-1.56 1.69-3.94 3.44-7.06 3.44-3.12 0-5.5-1.75-7.06-3.44a16 16 0 01-2.38-3.38v-.02h-.01L4 10l-.45-.22.01-.02a5.4 5.4 0 01.15-.28 16 16 0 012.23-3.1C7.5 4.69 9.88 2.94 13 2.94c3.12 0 5.5 1.75 7.06 3.44a16.01 16.01 0 012.38 3.38v.02h.01zM22 10l.45-.22.1.22-.1.22L22 10zM3.55 9.78L4 10l-.45.22-.1-.22.1-.22zm6.8.22A2.6 2.6 0 0113 7.44 2.6 2.6 0 0115.65 10 2.6 2.6 0 0113 12.56 2.6 2.6 0 0110.35 10zM13 6.44A3.6 3.6 0 009.35 10 3.6 3.6 0 0013 13.56c2 0 3.65-1.58 3.65-3.56A3.6 3.6 0 0013 6.44zm7.85 12l.8-.8.7.71-.79.8a.5.5 0 000 .7l.59.59c.2.2.5.2.7 0l1.8-1.8.7.71-1.79 1.8a1.5 1.5 0 01-2.12 0l-.59-.59a1.5 1.5 0 010-2.12zM16.5 21.5l-.35-.35a.5.5 0 00-.07.07l-1 1.5-1 1.5a.5.5 0 00.42.78h4a2.5 2.5 0 001.73-.77A2.5 2.5 0 0021 22.5a2.5 2.5 0 00-.77-1.73A2.5 2.5 0 0018.5 20a3.1 3.1 0 00-1.65.58 5.28 5.28 0 00-.69.55v.01h-.01l.35.36zm.39.32l-.97 1.46-.49.72h3.07c.34 0 .72-.17 1.02-.48.3-.3.48-.68.48-1.02 0-.34-.17-.72-.48-1.02-.3-.3-.68-.48-1.02-.48-.35 0-.75.18-1.1.42a4.27 4.27 0 00-.51.4z"/></svg>'},FVBd:function(o,e){o.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><g fill="currentColor" fill-rule="evenodd"><path fill-rule="nonzero" d="M23.002 23C23 23 23 18.003 23 18.003L15.998 18C16 18 16 22.997 16 22.997l7.002.003zM15 18.003A1 1 0 0 1 15.998 17h7.004c.551 0 .998.438.998 1.003v4.994A1 1 0 0 1 23.002 24h-7.004A.993.993 0 0 1 15 22.997v-4.994z"/><path d="M19 20h1v2h-1z"/><path fill-rule="nonzero" d="M22 17.5v-2a2.5 2.5 0 0 0-5 0v2h1v-2a1.5 1.5 0 0 1 3 0v2h1z"/><g fill-rule="nonzero"><path d="M3 14.707A1 1 0 0 1 3.293 14L14.439 2.854a1.5 1.5 0 0 1 2.122 0l2.585 2.585a1.5 1.5 0 0 1 0 2.122L8 18.707a1 1 0 0 1-.707.293H4a1 1 0 0 1-1-1v-3.293zm1 0V18h3.293L18.439 6.854a.5.5 0 0 0 0-.708l-2.585-2.585a.5.5 0 0 0-.708 0L4 14.707z"/><path d="M13.146 4.854l4 4 .708-.708-4-4zm-9 9l4 4 .708-.708-4-4z"/><path d="M15.146 6.146l-9 9 .708.708 9-9z"/></g></g></svg>'},G1jy:function(o,e){o.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><g fill="currentColor"><path fill-rule="nonzero" d="M15.039 5.969l-.019-.019-2.828 2.828.707.707 2.474-2.474c1.367-1.367 3.582-1.367 4.949 0s1.367 3.582 0 4.949l-2.474 2.474.707.707 2.828-2.828-.019-.019c1.415-1.767 1.304-4.352-.334-5.99-1.638-1.638-4.224-1.749-5.99-.334zM5.97 15.038l-.019-.019 2.828-2.828.707.707-2.475 2.475c-1.367 1.367-1.367 3.582 0 4.949s3.582 1.367 4.949 0l2.474-2.474.707.707-2.828 2.828-.019-.019c-1.767 1.415-4.352 1.304-5.99-.334-1.638-1.638-1.749-4.224-.334-5.99z"/><path d="M10.485 16.141l5.656-5.656.707.707-5.656 5.656z"/></g></svg>'},Ijvb:function(o,e,l){
"use strict";l.d(e,"a",(function(){return i}));const i={SyncDrawing:l("G1jy"),arrow:l("tceb"),cursor:l("WHEt"),dot:l("Csdk"),drawginmode:l("2dtg"),drawginmodeActive:l("FVBd"),eraser:l("2lje"),group:l("lZXH"),hideAllDrawings:l("6oLA"),hideAllDrawingsActive:l("dmHa"),hideAllIndicators:l("Ex32"),hideAllIndicatorsActive:l("3zTq"),hideAllDrawingTools:l("FS6t"),hideAllDrawingToolsActive:l("VToE"),lockAllDrawings:l("Uh5y"),lockAllDrawingsActive:l("43BO"),magnet:l("3s8f"),strongMagnet:l("xjKU"),measure:l("oCKS"),removeAllDrawingTools:l("aVjL"),showObjectTree:l("qQ3E"),zoom:l("kmdM"),"zoom-out":l("mbEK")}},"MP+M":function(o,e,l){"use strict";l.d(e,"a",(function(){return d}));l("YFKU");var i=l("+GxX"),n=l("/DW5"),a=(l("HbRj"),l("zxD0")),t=l("Ijvb"),s=l("0qMc");const c={keys:["Shift"],text:window.t("{0} — drawing a straight line at angles of 45")},r={keys:["Shift"],text:window.t("{0} — circle")},T={keys:["Shift"],text:window.t("{0} — square")},d={LineTool5PointsPattern:{icon:a.lineToolsIcons.LineTool5PointsPattern,localizedName:s.lineToolsLocalizedNames.LineTool5PointsPattern},LineToolABCD:{icon:a.lineToolsIcons.LineToolABCD,localizedName:s.lineToolsLocalizedNames.LineToolABCD},LineToolArc:{icon:a.lineToolsIcons.LineToolArc,localizedName:s.lineToolsLocalizedNames.LineToolArc},LineToolArrow:{icon:a.lineToolsIcons.LineToolArrow,localizedName:s.lineToolsLocalizedNames.LineToolArrow},LineToolArrowMarkDown:{icon:a.lineToolsIcons.LineToolArrowMarkDown,localizedName:s.lineToolsLocalizedNames.LineToolArrowMarkDown},LineToolArrowMarkLeft:{icon:a.lineToolsIcons.LineToolArrowMarkLeft,localizedName:s.lineToolsLocalizedNames.LineToolArrowMarkLeft},LineToolArrowMarkRight:{icon:a.lineToolsIcons.LineToolArrowMarkRight,localizedName:s.lineToolsLocalizedNames.LineToolArrowMarkRight},LineToolArrowMarkUp:{icon:a.lineToolsIcons.LineToolArrowMarkUp,localizedName:s.lineToolsLocalizedNames.LineToolArrowMarkUp},LineToolBalloon:{icon:a.lineToolsIcons.LineToolBalloon,localizedName:s.lineToolsLocalizedNames.LineToolBalloon},LineToolBarsPattern:{icon:a.lineToolsIcons.LineToolBarsPattern,localizedName:s.lineToolsLocalizedNames.LineToolBarsPattern},LineToolBezierCubic:{icon:a.lineToolsIcons.LineToolBezierCubic,localizedName:s.lineToolsLocalizedNames.LineToolBezierCubic},LineToolBezierQuadro:{icon:a.lineToolsIcons.LineToolBezierQuadro,localizedName:s.lineToolsLocalizedNames.LineToolBezierQuadro},LineToolBrush:{icon:a.lineToolsIcons.LineToolBrush,localizedName:s.lineToolsLocalizedNames.LineToolBrush},LineToolCallout:{icon:a.lineToolsIcons.LineToolCallout,localizedName:s.lineToolsLocalizedNames.LineToolCallout},LineToolCircleLines:{icon:a.lineToolsIcons.LineToolCircleLines,localizedName:s.lineToolsLocalizedNames.LineToolCircleLines},LineToolCypherPattern:{icon:a.lineToolsIcons.LineToolCypherPattern,localizedName:s.lineToolsLocalizedNames.LineToolCypherPattern},LineToolDateAndPriceRange:{icon:a.lineToolsIcons.LineToolDateAndPriceRange,localizedName:s.lineToolsLocalizedNames.LineToolDateAndPriceRange},LineToolDateRange:{icon:a.lineToolsIcons.LineToolDateRange,
localizedName:s.lineToolsLocalizedNames.LineToolDateRange},LineToolDisjointAngle:{icon:a.lineToolsIcons.LineToolDisjointAngle,localizedName:s.lineToolsLocalizedNames.LineToolDisjointAngle,hotKey:Object(n.b)(c)},LineToolElliottCorrection:{icon:a.lineToolsIcons.LineToolElliottCorrection,localizedName:s.lineToolsLocalizedNames.LineToolElliottCorrection},LineToolElliottDoubleCombo:{icon:a.lineToolsIcons.LineToolElliottDoubleCombo,localizedName:s.lineToolsLocalizedNames.LineToolElliottDoubleCombo},LineToolElliottImpulse:{icon:a.lineToolsIcons.LineToolElliottImpulse,localizedName:s.lineToolsLocalizedNames.LineToolElliottImpulse},LineToolElliottTriangle:{icon:a.lineToolsIcons.LineToolElliottTriangle,localizedName:s.lineToolsLocalizedNames.LineToolElliottTriangle},LineToolElliottTripleCombo:{icon:a.lineToolsIcons.LineToolElliottTripleCombo,localizedName:s.lineToolsLocalizedNames.LineToolElliottTripleCombo},LineToolEllipse:{icon:a.lineToolsIcons.LineToolEllipse,localizedName:s.lineToolsLocalizedNames.LineToolEllipse,hotKey:Object(n.b)(r)},LineToolExtended:{icon:a.lineToolsIcons.LineToolExtended,localizedName:s.lineToolsLocalizedNames.LineToolExtended},LineToolFibChannel:{icon:a.lineToolsIcons.LineToolFibChannel,localizedName:s.lineToolsLocalizedNames.LineToolFibChannel},LineToolFibCircles:{icon:a.lineToolsIcons.LineToolFibCircles,localizedName:s.lineToolsLocalizedNames.LineToolFibCircles,hotKey:Object(n.b)(r)},LineToolFibRetracement:{icon:a.lineToolsIcons.LineToolFibRetracement,localizedName:s.lineToolsLocalizedNames.LineToolFibRetracement},LineToolFibSpeedResistanceArcs:{icon:a.lineToolsIcons.LineToolFibSpeedResistanceArcs,localizedName:s.lineToolsLocalizedNames.LineToolFibSpeedResistanceArcs},LineToolFibSpeedResistanceFan:{icon:a.lineToolsIcons.LineToolFibSpeedResistanceFan,localizedName:s.lineToolsLocalizedNames.LineToolFibSpeedResistanceFan,hotKey:Object(n.b)(T)},LineToolFibSpiral:{icon:a.lineToolsIcons.LineToolFibSpiral,localizedName:s.lineToolsLocalizedNames.LineToolFibSpiral},LineToolFibTimeZone:{icon:a.lineToolsIcons.LineToolFibTimeZone,localizedName:s.lineToolsLocalizedNames.LineToolFibTimeZone},LineToolFibWedge:{icon:a.lineToolsIcons.LineToolFibWedge,localizedName:s.lineToolsLocalizedNames.LineToolFibWedge},LineToolFlagMark:{icon:a.lineToolsIcons.LineToolFlagMark,localizedName:s.lineToolsLocalizedNames.LineToolFlagMark},LineToolImage:{icon:"",localizedName:s.lineToolsLocalizedNames.LineToolImage},LineToolFlatBottom:{icon:a.lineToolsIcons.LineToolFlatBottom,localizedName:s.lineToolsLocalizedNames.LineToolFlatBottom,hotKey:Object(n.b)(c)},LineToolAnchoredVWAP:{icon:a.lineToolsIcons.LineToolAnchoredVWAP,localizedName:s.lineToolsLocalizedNames.LineToolAnchoredVWAP},LineToolGannComplex:{icon:a.lineToolsIcons.LineToolGannComplex,localizedName:s.lineToolsLocalizedNames.LineToolGannComplex},LineToolGannFixed:{icon:a.lineToolsIcons.LineToolGannFixed,localizedName:s.lineToolsLocalizedNames.LineToolGannFixed},LineToolGannFan:{icon:a.lineToolsIcons.LineToolGannFan,localizedName:s.lineToolsLocalizedNames.LineToolGannFan},
LineToolGannSquare:{icon:a.lineToolsIcons.LineToolGannSquare,localizedName:s.lineToolsLocalizedNames.LineToolGannSquare,hotKey:Object(n.b)({keys:["Shift"],text:window.t("{0} — fixed increments")})},LineToolHeadAndShoulders:{icon:a.lineToolsIcons.LineToolHeadAndShoulders,localizedName:s.lineToolsLocalizedNames.LineToolHeadAndShoulders},LineToolHorzLine:{icon:a.lineToolsIcons.LineToolHorzLine,localizedName:s.lineToolsLocalizedNames.LineToolHorzLine,hotKey:Object(n.b)({keys:["Alt","H"],text:"{0} + {1}"})},LineToolHorzRay:{icon:a.lineToolsIcons.LineToolHorzRay,localizedName:s.lineToolsLocalizedNames.LineToolHorzRay},LineToolIcon:{icon:a.lineToolsIcons.LineToolIcon,localizedName:s.lineToolsLocalizedNames.LineToolIcon},LineToolInsidePitchfork:{icon:a.lineToolsIcons.LineToolInsidePitchfork,localizedName:s.lineToolsLocalizedNames.LineToolInsidePitchfork},LineToolNote:{icon:a.lineToolsIcons.LineToolNote,localizedName:s.lineToolsLocalizedNames.LineToolNote},LineToolNoteAbsolute:{icon:a.lineToolsIcons.LineToolNoteAbsolute,localizedName:s.lineToolsLocalizedNames.LineToolNoteAbsolute},LineToolSignpost:{icon:a.lineToolsIcons.LineToolSignpost,localizedName:s.lineToolsLocalizedNames.LineToolSignpost},LineToolParallelChannel:{icon:a.lineToolsIcons.LineToolParallelChannel,localizedName:s.lineToolsLocalizedNames.LineToolParallelChannel,hotKey:Object(n.b)(c)},LineToolPitchfan:{icon:a.lineToolsIcons.LineToolPitchfan,localizedName:s.lineToolsLocalizedNames.LineToolPitchfan},LineToolPitchfork:{icon:a.lineToolsIcons.LineToolPitchfork,localizedName:s.lineToolsLocalizedNames.LineToolPitchfork},LineToolPolyline:{icon:a.lineToolsIcons.LineToolPolyline,localizedName:s.lineToolsLocalizedNames.LineToolPolyline},LineToolPath:{icon:a.lineToolsIcons.LineToolPath,localizedName:s.lineToolsLocalizedNames.LineToolPath},LineToolPrediction:{icon:a.lineToolsIcons.LineToolPrediction,localizedName:s.lineToolsLocalizedNames.LineToolPrediction},LineToolPriceLabel:{icon:a.lineToolsIcons.LineToolPriceLabel,localizedName:s.lineToolsLocalizedNames.LineToolPriceLabel},LineToolPriceNote:{icon:a.lineToolsIcons.LineToolPriceNote,localizedName:s.lineToolsLocalizedNames.LineToolPriceNote,hotKey:Object(n.b)(c)},LineToolArrowMarker:{icon:a.lineToolsIcons.LineToolArrowMarker,localizedName:s.lineToolsLocalizedNames.LineToolArrowMarker},LineToolPriceRange:{icon:a.lineToolsIcons.LineToolPriceRange,localizedName:s.lineToolsLocalizedNames.LineToolPriceRange},LineToolProjection:{icon:a.lineToolsIcons.LineToolProjection,localizedName:s.lineToolsLocalizedNames.LineToolProjection},LineToolRay:{icon:a.lineToolsIcons.LineToolRay,localizedName:s.lineToolsLocalizedNames.LineToolRay},LineToolRectangle:{icon:a.lineToolsIcons.LineToolRectangle,localizedName:s.lineToolsLocalizedNames.LineToolRectangle,hotKey:Object(n.b)({keys:["Shift"],text:window.t("{0} — square")})},LineToolRegressionTrend:{icon:a.lineToolsIcons.LineToolRegressionTrend,localizedName:s.lineToolsLocalizedNames.LineToolRegressionTrend},LineToolRiskRewardLong:{icon:a.lineToolsIcons.LineToolRiskRewardLong,
localizedName:s.lineToolsLocalizedNames.LineToolRiskRewardLong},LineToolRiskRewardShort:{icon:a.lineToolsIcons.LineToolRiskRewardShort,localizedName:s.lineToolsLocalizedNames.LineToolRiskRewardShort},LineToolFixedRangeVolumeProfile:{icon:a.lineToolsIcons.LineToolFixedRangeVolumeProfile,localizedName:s.lineToolsLocalizedNames.LineToolFixedRangeVolumeProfile},LineToolRotatedRectangle:{icon:a.lineToolsIcons.LineToolRotatedRectangle,localizedName:s.lineToolsLocalizedNames.LineToolRotatedRectangle,hotKey:Object(n.b)(c)},LineToolSchiffPitchfork:{icon:a.lineToolsIcons.LineToolSchiffPitchfork,localizedName:s.lineToolsLocalizedNames.LineToolSchiffPitchfork},LineToolSchiffPitchfork2:{icon:a.lineToolsIcons.LineToolSchiffPitchfork2,localizedName:s.lineToolsLocalizedNames.LineToolSchiffPitchfork2},LineToolSineLine:{icon:a.lineToolsIcons.LineToolSineLine,localizedName:s.lineToolsLocalizedNames.LineToolSineLine},LineToolText:{icon:a.lineToolsIcons.LineToolText,localizedName:s.lineToolsLocalizedNames.LineToolText},LineToolTextAbsolute:{icon:a.lineToolsIcons.LineToolTextAbsolute,localizedName:s.lineToolsLocalizedNames.LineToolTextAbsolute},LineToolThreeDrivers:{icon:a.lineToolsIcons.LineToolThreeDrivers,localizedName:s.lineToolsLocalizedNames.LineToolThreeDrivers},LineToolTimeCycles:{icon:a.lineToolsIcons.LineToolTimeCycles,localizedName:s.lineToolsLocalizedNames.LineToolTimeCycles},LineToolTrendAngle:{icon:a.lineToolsIcons.LineToolTrendAngle,localizedName:s.lineToolsLocalizedNames.LineToolTrendAngle,hotKey:Object(n.b)(c)},LineToolTrendBasedFibExtension:{icon:a.lineToolsIcons.LineToolTrendBasedFibExtension,localizedName:s.lineToolsLocalizedNames.LineToolTrendBasedFibExtension},LineToolTrendBasedFibTime:{icon:a.lineToolsIcons.LineToolTrendBasedFibTime,localizedName:s.lineToolsLocalizedNames.LineToolTrendBasedFibTime},LineToolTrendLine:{icon:a.lineToolsIcons.LineToolTrendLine,localizedName:s.lineToolsLocalizedNames.LineToolTrendLine,hotKey:Object(n.b)(c)},LineToolInfoLine:{icon:a.lineToolsIcons.LineToolInfoLine,localizedName:s.lineToolsLocalizedNames.LineToolInfoLine},LineToolTriangle:{icon:a.lineToolsIcons.LineToolTriangle,localizedName:s.lineToolsLocalizedNames.LineToolTriangle},LineToolTrianglePattern:{icon:a.lineToolsIcons.LineToolTrianglePattern,localizedName:s.lineToolsLocalizedNames.LineToolTrianglePattern},LineToolVertLine:{icon:a.lineToolsIcons.LineToolVertLine,localizedName:s.lineToolsLocalizedNames.LineToolVertLine,hotKey:Object(n.b)({keys:["Alt","V"],text:"{0} + {1}"})},LineToolCrossLine:{icon:a.lineToolsIcons.LineToolCrossLine,localizedName:s.lineToolsLocalizedNames.LineToolCrossLine},LineToolHighlighter:{icon:a.lineToolsIcons.LineToolHighlighter,localizedName:s.lineToolsLocalizedNames.LineToolHighlighter},SyncDrawing:{icon:t.a.SyncDrawing,iconActive:t.a.SyncDrawingActive,localizedName:window.t("New drawings are replicated to all charts in the layout and shown when the same ticker is selected")},arrow:{icon:t.a.arrow,localizedName:window.t("Arrow")},cursor:{icon:t.a.cursor,localizedName:window.t("Cross")},dot:{
icon:t.a.dot,localizedName:window.t("Dot")},drawginmode:{icon:t.a.drawginmode,iconActive:t.a.drawginmodeActive,localizedName:window.t("Stay in Drawing Mode")},eraser:{icon:t.a.eraser,localizedName:window.t("Eraser")},group:{icon:t.a.group,localizedName:window.t("Show Hidden Tools")},hideAllDrawings:{icon:t.a.hideAllDrawings,iconActive:t.a.hideAllDrawingsActive,localizedName:window.t("Hide All Drawing Tools"),hotKey:Object(n.b)({keys:["Ctrl","Alt","H"],text:"{0} + {1} + {2}"})},lockAllDrawings:{icon:t.a.lockAllDrawings,iconActive:t.a.lockAllDrawingsActive,localizedName:window.t("Lock All Drawing Tools")},magnet:{icon:t.a.magnet,localizedName:window.t("Magnet Mode snaps drawings placed near price bars to the closest OHLC value"),hotKey:Object(n.b)({keys:["Ctrl"],text:"{0}"})},measure:{icon:t.a.measure,localizedName:window.t("Measure"),hotKey:Object(n.b)({keys:["Shift"],text:window.t("{0} + Click on the chart")})},removeAllDrawingTools:{icon:t.a.removeAllDrawingTools,localizedName:window.t("Remove Drawings")},showObjectsTree:{icon:t.a.showObjectTree,localizedName:window.t("Show Object Tree")},zoom:{icon:t.a.zoom,localizedName:window.t("Zoom In")},"zoom-out":{icon:t.a["zoom-out"],localizedName:window.t("Zoom Out")}};Object(i.isFeatureEnabled)("remove-line-tool-ghost-feed")||(d.LineToolGhostFeed={icon:a.lineToolsIcons.LineToolGhostFeed,localizedName:s.lineToolsLocalizedNames.LineToolGhostFeed})},R5JZ:function(o,e,l){"use strict";function i(o,e,l,i,n){function a(n){if(o>n.timeStamp)return;const a=n.target;void 0!==l&&null!==e&&null!==a&&a.ownerDocument===i&&(e.contains(a)||l(n))}return n.click&&i.addEventListener("click",a,!1),n.mouseDown&&i.addEventListener("mousedown",a,!1),n.touchEnd&&i.addEventListener("touchend",a,!1),n.touchStart&&i.addEventListener("touchstart",a,!1),()=>{i.removeEventListener("click",a,!1),i.removeEventListener("mousedown",a,!1),i.removeEventListener("touchend",a,!1),i.removeEventListener("touchstart",a,!1)}}l.d(e,"a",(function(){return i}))},Uh5y:function(o,e){o.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M14 6a3 3 0 0 0-3 3v3h8.5a2.5 2.5 0 0 1 2.5 2.5v7a2.5 2.5 0 0 1-2.5 2.5h-11A2.5 2.5 0 0 1 6 21.5v-7A2.5 2.5 0 0 1 8.5 12H10V9a4 4 0 0 1 8 0h-1a3 3 0 0 0-3-3zm-1 11a1 1 0 1 1 2 0v2a1 1 0 1 1-2 0v-2zm-6-2.5c0-.83.67-1.5 1.5-1.5h11c.83 0 1.5.67 1.5 1.5v7c0 .83-.67 1.5-1.5 1.5h-11A1.5 1.5 0 0 1 7 21.5v-7z"/></svg>'},VToE:function(o,e){
o.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M19.76 6.07l-.7.7a13.4 13.4 0 011.93 2.47c.19.3.33.55.42.72l.03.04-.03.04a15 15 0 01-2.09 2.9c-1.47 1.6-3.6 3.12-6.32 3.12-.98 0-1.88-.2-2.7-.52l-.77.76c1.03.47 2.18.76 3.47.76 3.12 0 5.5-1.75 7.06-3.44a16 16 0 002.38-3.38v-.02h.01L22 10l.45.22.1-.22-.1-.22L22 10l.45-.22-.01-.02a5.1 5.1 0 00-.15-.28 16 16 0 00-2.53-3.41zM6.24 13.93l.7-.7-.27-.29a15 15 0 01-2.08-2.9L4.56 10l.03-.04a15 15 0 012.09-2.9c1.47-1.6 3.6-3.12 6.32-3.12.98 0 1.88.2 2.7.52l.77-.76A8.32 8.32 0 0013 2.94c-3.12 0-5.5 1.75-7.06 3.44a16 16 0 00-2.38 3.38v.02h-.01L4 10l-.45-.22-.1.22.1.22L4 10l-.45.22.01.02a5.5 5.5 0 00.15.28 16 16 0 002.53 3.41zm6.09-.43a3.6 3.6 0 004.24-4.24l-.93.93a2.6 2.6 0 01-2.36 2.36l-.95.95zm-1.97-3.69l-.93.93a3.6 3.6 0 014.24-4.24l-.95.95a2.6 2.6 0 00-2.36 2.36zm11.29 7.84l-.8.79a1.5 1.5 0 000 2.12l.59.59a1.5 1.5 0 002.12 0l1.8-1.8-.71-.7-1.8 1.79a.5.5 0 01-.7 0l-.59-.59a.5.5 0 010-.7l.8-.8-.71-.7zm-5.5 3.5l.35.35-.35-.35.01-.02.02-.02.02-.02a4.68 4.68 0 01.65-.5c.4-.27 1-.59 1.65-.59.66 0 1.28.33 1.73.77.44.45.77 1.07.77 1.73a2.5 2.5 0 01-.77 1.73 2.5 2.5 0 01-1.73.77h-4a.5.5 0 01-.42-.78l1-1.5 1-1.5a.5.5 0 01.07-.07zm.74.67a3.46 3.46 0 01.51-.4c.35-.24.75-.42 1.1-.42.34 0 .72.17 1.02.48.3.3.48.68.48 1.02 0 .34-.17.72-.48 1.02-.3.3-.68.48-1.02.48h-3.07l.49-.72.97-1.46zM21.2 2.5L5.5 18.2l-.7-.7L20.5 1.8l.7.7z"/></svg>'},WHEt:function(o,e){o.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><g fill="currentColor"><path d="M18 15h8v-1h-8z"/><path d="M14 18v8h1v-8zM14 3v8h1v-8zM3 15h8v-1h-8z"/></g></svg>'},b2d7:function(o,e,l){"use strict";l.d(e,"a",(function(){return n}));var i,n,a=l("aIyQ"),t=l.n(a),s=l("Vdly");!function(o){function e(){o.favorites=[];Object(s.getJSON)("chart.favoriteDrawings",[]).forEach(e=>{l(e.tool||e)&&o.favorites.push(e.tool||e)}),o.favoritesSynced.fire()}function l(o){return"string"==typeof o&&""!==o}o.favorites=[],o.favoritesSynced=new t.a,o.favoriteIndex=function(e){return o.favorites.indexOf(e)},o.isValidLineToolName=l,o.saveFavorites=function(e){Object(s.setJSON)("chart.favoriteDrawings",o.favorites,e)},e(),s.onSync.subscribe(null,e)}(i||(i={})),function(o){function e(o){return i.isValidLineToolName(o)}function l(){return i.favorites.length}function n(o){return-1!==i.favoriteIndex(o)}o.favoriteAdded=new t.a,o.favoriteRemoved=new t.a,o.favoriteMoved=new t.a,o.favoritesSynced=i.favoritesSynced,o.favorites=function(){return i.favorites.slice()},o.isValidLineToolName=e,o.favoritesCount=l,o.favorite=function(o){return o<0||o>=l()?"":i.favorites[o]},o.addFavorite=function(l,a){return!(n(l)||!e(l))&&(i.favorites.push(l),i.saveFavorites(a),o.favoriteAdded.fire(l),!0)},o.removeFavorite=function(e,l){const n=i.favoriteIndex(e);return-1!==n&&(i.favorites.splice(n,1),i.saveFavorites(l),o.favoriteRemoved.fire(e),!0)},o.isFavorite=n,o.moveFavorite=function(n,a,t){if(a<0||a>=l()||!e(n))return!1;const s=i.favoriteIndex(n)
;return-1!==s&&a!==s&&(i.favorites.splice(s,1),i.favorites.splice(a,0,n),i.saveFavorites(t),o.favoriteMoved.fire(n,s,a),!0)}}(n||(n={}))},gb5g:function(o,e,l){o.exports={button:"button-5-QHyx-s",hover:"hover-5-QHyx-s",bg:"bg-5-QHyx-s",icon:"icon-5-QHyx-s",isActive:"isActive-5-QHyx-s",isTransparent:"isTransparent-5-QHyx-s",isGrayed:"isGrayed-5-QHyx-s",isHidden:"isHidden-5-QHyx-s"}},hn2c:function(o,e){o.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 16" width="10" height="16"><path d="M.6 1.4l1.4-1.4 8 8-8 8-1.4-1.4 6.389-6.532-6.389-6.668z"/></svg>'},ijHL:function(o,e,l){"use strict";function i(o){return a(o,t)}function n(o){return a(o,s)}function a(o,e){const l=Object.entries(o).filter(e),i={};for(const[n,a]of l)i[n]=a;return i}function t(o){const[e,l]=o;return 0===e.indexOf("data-")&&"string"==typeof l}function s(o){return 0===o[0].indexOf("aria-")}l.d(e,"b",(function(){return i})),l.d(e,"a",(function(){return n}))},kmdM:function(o,e){o.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path d="M17.646 18.354l4 4 .708-.708-4-4z"/><path d="M12.5 21a8.5 8.5 0 1 1 0-17 8.5 8.5 0 0 1 0 17zm0-1a7.5 7.5 0 1 0 0-15 7.5 7.5 0 0 0 0 15z"/><path d="M9 13h7v-1H9z"/><path d="M13 16V9h-1v7z"/></svg>'},lZXH:function(o,e){o.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" width="30" height="30"><path fill="currentColor" d="M5.5 13A2.5 2.5 0 0 0 3 15.5 2.5 2.5 0 0 0 5.5 18 2.5 2.5 0 0 0 8 15.5 2.5 2.5 0 0 0 5.5 13zm9.5 0a2.5 2.5 0 0 0-2.5 2.5A2.5 2.5 0 0 0 15 18a2.5 2.5 0 0 0 2.5-2.5A2.5 2.5 0 0 0 15 13zm9.5 0a2.5 2.5 0 0 0-2.5 2.5 2.5 2.5 0 0 0 2.5 2.5 2.5 2.5 0 0 0 2.5-2.5 2.5 2.5 0 0 0-2.5-2.5z"/></svg>'},mbEK:function(o,e){o.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path d="M17.646 18.354l4 4 .708-.708-4-4z"/><path d="M12.5 21a8.5 8.5 0 1 1 0-17 8.5 8.5 0 0 1 0 17zm0-1a7.5 7.5 0 1 0 0-15 7.5 7.5 0 0 0 0 15z"/><path d="M9 13h7v-1H9z"/></svg>'},nPPD:function(o,e,l){"use strict";function i(o,e,l={}){const i=Object.assign({},e);for(const n of Object.keys(e)){const a=l[n]||n;a in o&&(i[n]=[o[a],e[n]].join(" "))}return i}function n(o,e,l={}){return Object.assign({},o,i(o,e,l))}l.d(e,"b",(function(){return i})),l.d(e,"a",(function(){return n}))},oCKS:function(o,e){o.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" d="M2 9.75a1.5 1.5 0 0 0-1.5 1.5v5.5a1.5 1.5 0 0 0 1.5 1.5h24a1.5 1.5 0 0 0 1.5-1.5v-5.5a1.5 1.5 0 0 0-1.5-1.5zm0 1h3v2.5h1v-2.5h3.25v3.9h1v-3.9h3.25v2.5h1v-2.5h3.25v3.9h1v-3.9H22v2.5h1v-2.5h3a.5.5 0 0 1 .5.5v5.5a.5.5 0 0 1-.5.5H2a.5.5 0 0 1-.5-.5v-5.5a.5.5 0 0 1 .5-.5z" transform="rotate(-45 14 14)"/></svg>'},qQ3E:function(o,e){
o.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><g fill="currentColor"><path fill-rule="nonzero" d="M14 18.634l-.307-.239-7.37-5.73-2.137-1.665 9.814-7.633 9.816 7.634-.509.394-1.639 1.269-7.667 5.969zm7.054-6.759l1.131-.876-8.184-6.366-8.186 6.367 1.123.875 7.063 5.491 7.054-5.492z"/><path d="M7 14.5l-1 .57 8 6.43 8-6.5-1-.5-7 5.5z"/><path d="M7 17.5l-1 .57 8 6.43 8-6.5-1-.5-7 5.5z"/></g></svg>'},tceb:function(o,e){o.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M11.682 16.09l3.504 6.068 1.732-1-3.497-6.057 3.595-2.1L8 7.74v10.512l3.682-2.163zm-.362 1.372L7 20V6l12 7-4.216 2.462 3.5 6.062-3.464 2-3.5-6.062z"/></svg>'},uhCe:function(o,e,l){"use strict";l.d(e,"a",(function(){return n}));var i=l("ASyk");const n={SmallHeight:i["small-height-breakpoint"],TabletSmall:i["tablet-small-breakpoint"],TabletNormal:i["tablet-normal-breakpoint"]}},xjKU:function(o,e){o.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="nonzero" d="M14 5a7 7 0 0 0-7 7v3h4v-3a3 3 0 1 1 6 0v3h4v-3a7 7 0 0 0-7-7zm7 11h-4v3h4v-3zm-10 0H7v3h4v-3zm-5-4a8 8 0 1 1 16 0v8h-6v-8a2 2 0 1 0-4 0v8H6v-8zm3.293 11.294l-1.222-2.037.858-.514 1.777 2.963-2 1 1.223 2.037-.858.514-1.778-2.963 2-1zm9.778-2.551l.858.514-1.223 2.037 2 1-1.777 2.963-.858-.514 1.223-2.037-2-1 1.777-2.963z"/></svg>'}}]);