.ui-slider{position:relative;text-align:left}.ui-slider .ui-slider-handle{position:absolute;z-index:2;width:1.2em;height:1.2em;cursor:default}.ui-slider .ui-slider-range{position:absolute;z-index:1;font-size:.7em;display:block;border:0;background-position:0 0}.ui-slider-horizontal{height:.8em}.ui-slider-horizontal .ui-slider-handle{top:-.3em;margin-left:-.6em}.ui-slider-horizontal .ui-slider-range{top:0;height:100%}.ui-slider-horizontal .ui-slider-range-min{left:0}.ui-slider-horizontal .ui-slider-range-max{right:0}.ui-slider-vertical{width:.8em;height:100px}.ui-slider-vertical .ui-slider-handle{left:-.3em;margin-left:0;margin-bottom:-.6em}.ui-slider-vertical .ui-slider-range{left:0;width:100%}.ui-slider-vertical .ui-slider-range-min{bottom:0}.ui-slider-vertical .ui-slider-range-max{top:0}.ui-helper-hidden{display:none}.ui-helper-hidden-accessible{position:absolute!important;clip:rect(1px 1px 1px 1px);clip:rect(1px,1px,1px,1px)}.ui-helper-reset{margin:0;padding:0;border:0;outline:0;line-height:1.3;text-decoration:none;font-size:100%;list-style:none}.ui-helper-clearfix:after{content:".";display:block;height:0;clear:both;visibility:hidden}.ui-helper-clearfix{display:inline-block}* html .ui-helper-clearfix{height:1%}.ui-helper-clearfix{display:block}.ui-helper-zfix{width:100%;height:100%;top:0;left:0;position:absolute;opacity:0;filter:alpha(opacity=0)}.ui-state-disabled{cursor:default!important}.ui-icon{display:block;text-indent:-99999px;overflow:hidden;background-repeat:no-repeat}.ui-widget-overlay{position:absolute;top:0;left:0;width:100%;height:100%}.ui-widget{font-family:Verdana,Arial,sans-serif;font-size:1.1em}.ui-widget .ui-widget{font-size:1em}.ui-widget button,.ui-widget input,.ui-widget select,.ui-widget textarea{font-family:Verdana,Arial,sans-serif;font-size:1em}.ui-widget-content{border:1px solid #aaa;background:#fff 50% 50% repeat-x;color:#6b6b6d}html.theme-dark .ui-widget-content{color:#2f3241;background:#1e222d 50% 50% repeat-x}.ui-widget-content a{color:#6b6b6d}html.theme-dark .ui-widget-content a{color:#2f3241}.ui-widget-header{border:1px solid;border-color:#c9cbcd;background:#fafafa;color:#6b6b6d}html.theme-dark .ui-widget-header{color:#2f3241;background:#2f3241;border-color:#2f3241}.ui-widget-header a{color:#6b6b6d}html.theme-dark .ui-widget-header a{color:#2f3241}.ui-state-default,.ui-widget-content .ui-state-default,.ui-widget-header .ui-state-default{border:1px solid #d3d3d3;background:#fafafa;font-weight:400;color:#555}html.theme-dark .ui-state-default,html.theme-dark .ui-widget-content .ui-state-default,html.theme-dark .ui-widget-header .ui-state-default{color:#8b8f95}.ui-state-default a,.ui-state-default a:link,.ui-state-default a:visited{color:#555;text-decoration:none}html.theme-dark .ui-state-default a,html.theme-dark .ui-state-default a:link,html.theme-dark .ui-state-default a:visited{color:#8b8f95}.ui-state-focus,.ui-state-hover,.ui-widget-content .ui-state-focus,.ui-widget-content .ui-state-hover,.ui-widget-header .ui-state-focus,.ui-widget-header .ui-state-hover{border:1px solid #999;background:#dadada;font-weight:400;color:#212121}.ui-state-hover a{color:#212121;text-decoration:none}@media (any-hover:hover),(min--moz-device-pixel-ratio:0){.ui-state-hover a:hover{color:#212121;text-decoration:none}}.ui-state-active,.ui-widget-content .ui-state-active,.ui-widget-header .ui-state-active{border:1px solid #aaa;background:#fff 50% 50% repeat-x;font-weight:400;color:#212121}.ui-state-active a,.ui-state-active a:link,.ui-state-active a:visited{color:#212121;text-decoration:none}.ui-widget :active{outline:none}.ui-state-highlight,.ui-widget-content .ui-state-highlight,.ui-widget-header .ui-state-highlight{border:1px solid #fde763;background:#fff8cf;color:#363636}.ui-state-highlight a,.ui-widget-content .ui-state-highlight a,.ui-widget-header .ui-state-highlight a{color:#363636}.ui-state-error,.ui-widget-content .ui-state-error,.ui-widget-header .ui-state-error{border:1px solid #cd0a0a;background:#fef1ec 50% 50% repeat-x;color:#cd0a0a}.ui-state-error-text,.ui-state-error a,.ui-widget-content .ui-state-error-text,.ui-widget-content .ui-state-error a,.ui-widget-header .ui-state-error-text,.ui-widget-header .ui-state-error a{color:#cd0a0a}.ui-priority-primary,.ui-widget-content .ui-priority-primary,.ui-widget-header .ui-priority-primary{font-weight:700}.ui-priority-secondary,.ui-widget-content .ui-priority-secondary,.ui-widget-header .ui-priority-secondary{opacity:.7;filter:alpha(opacity=70);font-weight:400}.ui-state-disabled,.ui-widget-content .ui-state-disabled,.ui-widget-header .ui-state-disabled{opacity:.35;filter:alpha(opacity=35);background-image:none}.ui-widget-overlay,.ui-widget-shadow{background:#aaa 50% 50% repeat-x;opacity:.3;filter:alpha(opacity=30)}.ui-widget-shadow{margin:-8px 0 0 -8px;padding:8px;border-radius:8px}.ui-resizable{position:relative}.ui-resizable-handle{position:absolute;font-size:.1px;z-index:99999;display:block}.ui-resizable-autohide .ui-resizable-handle,.ui-resizable-disabled .ui-resizable-handle{display:none}.ui-resizable-n{cursor:n-resize;height:7px;width:100%;top:-5px;left:0}.ui-resizable-s{cursor:s-resize;height:7px;width:100%;bottom:-5px;left:0}.ui-resizable-e{cursor:e-resize;width:7px;right:-5px;top:0;height:100%}.ui-resizable-w{cursor:w-resize;width:7px;left:-5px;top:0;height:100%}.ui-resizable-se{cursor:se-resize;width:12px;height:12px;right:1px;bottom:1px}.ui-resizable-sw{cursor:sw-resize;width:9px;height:9px;left:-5px;bottom:-5px}.ui-resizable-nw{cursor:nw-resize;width:9px;height:9px;left:-5px;top:-5px}.ui-resizable-ne{cursor:ne-resize;width:9px;height:9px;right:-5px;top:-5px}.ui-selectable-helper{position:absolute;z-index:100;border:1px dotted #000}.ui-autocomplete{position:absolute;cursor:default}* html .ui-autocomplete{width:1px}.ui-menu{list-style:none;padding:2px;margin:0;display:block;float:left}.ui-menu .ui-menu{margin-top:-3px}.ui-menu .ui-menu-item{margin:0;padding:0;zoom:1;float:left;clear:left;width:100%}.ui-menu .ui-menu-item a{text-decoration:none;display:block;padding:.2em .4em;line-height:1.5;zoom:1}.ui-menu .ui-menu-item a.ui-state-active,.ui-menu .ui-menu-item a.ui-state-hover{font-weight:400;margin:-1px}.ui-button{display:inline-block;position:relative;padding:0;margin-right:.1em;text-decoration:none!important;cursor:pointer;text-align:center;zoom:1;overflow:visible}.ui-button-icon-only{width:2.2em}button.ui-button-icon-only{width:2.4em}.ui-button-icons-only{width:3.4em}button.ui-button-icons-only{width:3.7em}.ui-button .ui-button-text{display:block;line-height:1.4}.ui-button-text-only .ui-button-text{padding:.4em 1em}.ui-button-icon-only .ui-button-text,.ui-button-icons-only .ui-button-text{padding:.4em;text-indent:-9999999px}.ui-button-text-icon-primary .ui-button-text,.ui-button-text-icons .ui-button-text{padding:.4em 1em .4em 2.1em}.ui-button-text-icon-secondary .ui-button-text,.ui-button-text-icons .ui-button-text{padding:.4em 2.1em .4em 1em}.ui-button-text-icons .ui-button-text{padding-left:2.1em;padding-right:2.1em}input.ui-button{padding:.4em 1em}.ui-button-icon-only .ui-icon,.ui-button-icons-only .ui-icon,.ui-button-text-icon-primary .ui-icon,.ui-button-text-icon-secondary .ui-icon,.ui-button-text-icons .ui-icon{position:absolute;top:50%;margin-top:-8px}.ui-button-icon-only .ui-icon{left:50%;margin-left:-8px}.ui-button-icons-only .ui-button-icon-primary,.ui-button-text-icon-primary .ui-button-icon-primary,.ui-button-text-icons .ui-button-icon-primary{left:.5em}.ui-button-icons-only .ui-button-icon-secondary,.ui-button-text-icon-secondary .ui-button-icon-secondary,.ui-button-text-icons .ui-button-icon-secondary{right:.5em}.ui-buttonset{margin-right:7px}.ui-buttonset .ui-button{margin-left:0;margin-right:-.3em}button.ui-button::-moz-focus-inner{border:0;padding:0}.ui-tabs .ui-tabs-nav{margin:0;padding:.2em .2em 0}.ui-tabs .ui-tabs-nav li{list-style:none;float:left;position:relative;top:1px;margin:0 .2em 1px 0;border-bottom:0!important;padding:0;white-space:nowrap}.ui-tabs .ui-tabs-nav li a{float:left;padding:.5em 1em;text-decoration:none}.ui-tabs .ui-tabs-nav li.ui-tabs-selected{margin-bottom:0;padding-bottom:1px}.ui-tabs .ui-tabs-nav li.ui-state-disabled a,.ui-tabs .ui-tabs-nav li.ui-state-processing a,.ui-tabs .ui-tabs-nav li.ui-tabs-selected a{cursor:text}.ui-tabs.ui-tabs-collapsible .ui-tabs-nav li.ui-tabs-selected a,.ui-tabs .ui-tabs-nav li a{cursor:pointer}.ui-tabs .ui-tabs-panel{display:block;border-width:0;margin:5px 0;background:none}.ui-tabs .ui-tabs-hide{position:absolute;left:-10000px}.ui-datepicker{padding:0;border:none;display:none}.ui-datepicker .ui-datepicker-header{position:relative;height:53px;line-height:53px;border:none;padding:0;background-color:#f0b90b;color:#fff}html.theme-dark .ui-datepicker .ui-datepicker-header{color:#c5cbce;background-color:#21384d}.ui-datepicker .ui-datepicker-header .ui-datepicker-next,.ui-datepicker .ui-datepicker-header .ui-datepicker-prev{opacity:.8;position:absolute;top:0;width:40px;height:100%;background:none;border:none;cursor:pointer}.ui-datepicker .ui-datepicker-header .ui-datepicker-next.ui-state-disabled,.ui-datepicker .ui-datepicker-header .ui-datepicker-prev.ui-state-disabled{opacity:.4}.ui-datepicker .ui-datepicker-header .ui-datepicker-next-hover:not(.ui-state-disabled),.ui-datepicker .ui-datepicker-header .ui-datepicker-prev-hover:not(.ui-state-disabled){opacity:1;background:none;border:none}.ui-datepicker .ui-datepicker-header .ui-datepicker-next{right:0}.ui-datepicker .ui-datepicker-header .ui-datepicker-next span{margin-left:-5px;width:9px;height:14px;background:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 14' width='9' height='14' fill='%23fff'%3E%3Cpath d='m2 0l-2 2 5 5-5 5 2 2 7-7z'/%3E%3C/svg%3E")}.ui-datepicker .ui-datepicker-header .ui-datepicker-prev{left:0}.ui-datepicker .ui-datepicker-header .ui-datepicker-prev span{margin-left:-4px;width:9px;height:14px;background:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 14' width='9' height='14' fill='%23fff'%3E%3Cpath d='m0 7l7-7 2 2-5 5 5 5-2 2z'/%3E%3C/svg%3E")}.ui-datepicker .ui-datepicker-next span,.ui-datepicker .ui-datepicker-prev span{display:block;position:absolute;left:50%;margin-left:-8px;top:50%;margin-top:-8px}.ui-datepicker .ui-datepicker-title{margin:0;text-align:center}.ui-datepicker .ui-datepicker-title select{font-size:1em}.ui-datepicker select.ui-datepicker-month-year{width:100%}.ui-datepicker select.ui-datepicker-month,.ui-datepicker select.ui-datepicker-year{width:49%}.ui-datepicker table{width:100%;font-size:12px;border-collapse:collapse;margin:0;border:1px solid;border-color:#d6d6d6;border-top:none}html.theme-dark .ui-datepicker table{border-color:#363c4e}.ui-datepicker th{width:40px;height:28px;border-bottom:1px solid red;padding:0;border:none;border-bottom:1px solid;border-bottom-color:#d6d6d6;text-align:center;font-weight:400;color:#8d9196;background-color:#f8f8f8}html.theme-dark .ui-datepicker th{background-color:#2f3241;border-bottom-color:#363c4e}.ui-datepicker td{border:none;padding:0;margin:0}.ui-datepicker td a,.ui-datepicker td a.ui-state-default,.ui-datepicker td span,.ui-datepicker td span.ui-state-default{display:block;position:relative;height:39px;padding:0;margin:0;text-align:center;line-height:40px;background:#fff;border:none;border-width:0 1px 1px 0;border-style:solid;border-color:#d6d6d6;text-decoration:none}html.theme-dark .ui-datepicker td a,html.theme-dark .ui-datepicker td a.ui-state-default,html.theme-dark .ui-datepicker td span,html.theme-dark .ui-datepicker td span.ui-state-default{border-color:#363c4e;background:#1e222d}.ui-datepicker td a.ui-state-default.ui-state-highlight,.ui-datepicker td a.ui-state-highlight,.ui-datepicker td span.ui-state-default.ui-state-highlight,.ui-datepicker td span.ui-state-highlight{color:#00b9db}.ui-datepicker td a.ui-state-default.ui-state-highlight:after,.ui-datepicker td a.ui-state-highlight:after,.ui-datepicker td span.ui-state-default.ui-state-highlight:after,.ui-datepicker td span.ui-state-highlight:after{position:absolute;top:0;right:0;content:" ";width:0;height:0;border-style:solid;border-width:0 11px 11px 0;border-color:transparent #00badb transparent transparent}.ui-datepicker td a.ui-state-default.ui-state-hover,.ui-datepicker td a.ui-state-hover,.ui-datepicker td span.ui-state-default.ui-state-hover,.ui-datepicker td span.ui-state-hover{background:#edf1f2}html.theme-dark .ui-datepicker td a.ui-state-default.ui-state-hover,html.theme-dark .ui-datepicker td a.ui-state-hover,html.theme-dark .ui-datepicker td span.ui-state-default.ui-state-hover,html.theme-dark .ui-datepicker td span.ui-state-hover{background:#262b3e}.ui-datepicker td.ui-datepicker-current-day a,.ui-datepicker td.ui-datepicker-current-day span{border:2px solid;border-color:#f0b90b;color:#f0b90b;height:36px;line-height:37px}html.theme-dark .ui-datepicker td.ui-datepicker-current-day a,html.theme-dark .ui-datepicker td.ui-datepicker-current-day span{color:#f0b90b;border-color:#f0b90b}.ui-datepicker .ui-datepicker-buttonpane{background-image:none;margin:.7em 0 0;padding:0 .2em;border-left:0;border-right:0;border-bottom:0}.ui-datepicker .ui-datepicker-buttonpane button{float:right;margin:.5em .2em .4em;cursor:pointer;padding:.2em .6em .3em;width:auto;overflow:visible}.ui-datepicker .ui-datepicker-buttonpane button.ui-datepicker-current{float:left}.ui-datepicker.ui-datepicker-multi{width:auto}.ui-datepicker-multi .ui-datepicker-group{float:left}.ui-datepicker-multi .ui-datepicker-group table{width:95%;margin:0 auto .4em}.ui-datepicker-multi-2 .ui-datepicker-group{width:50%}.ui-datepicker-multi-3 .ui-datepicker-group{width:33.3%}.ui-datepicker-multi-4 .ui-datepicker-group{width:25%}.ui-datepicker-multi .ui-datepicker-group-last .ui-datepicker-header,.ui-datepicker-multi .ui-datepicker-group-middle .ui-datepicker-header{border-left-width:0}.ui-datepicker-multi .ui-datepicker-buttonpane{clear:left}.ui-datepicker-row-break{clear:both;width:100%}