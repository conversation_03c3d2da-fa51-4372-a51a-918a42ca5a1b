(window.webpackJsonp=window.webpackJsonp||[]).push([["full-tooltips-popup"],{"9dlw":function(e,t,n){"use strict";n.d(t,"a",(function(){return m}));var o=n("mrSG"),r=n("q1tI"),a=n.n(r),i=n("i8i4"),s=n.n(i),l=(n("EsMY"),n("AiMB")),c=n("DTHj"),u=n("X0gx"),d=n("8Rai");function m(e){const{controller:t,children:n,isOpened:i,closeOnClickOutside:m=!0,doNotCloseOn:v,onClickOutside:p,onClose:f}=e,b=Object(o.a)(e,["controller","children","isOpened","closeOnClickOutside","doNotCloseOn","onClickOutside","onClose"]),h=Object(r.useContext)(u.a),w=Object(d.a)({handler:function(e){p&&p(e);if(!m)return;if(v&&e.target instanceof Node){const t=s.a.findDOMNode(v);if(t instanceof Node&&t.contains(e.target))return}f()},mouseDown:!0,touchStart:!0});return i?a.a.createElement(l.a,{top:"0",left:"0",right:"0",bottom:"0",pointerEvents:"none"},a.a.createElement("span",{ref:w,style:{pointerEvents:"auto"}},a.a.createElement(c.b,Object.assign({},b,{isOpened:i,onClose:f,onScroll:function(t){const{onScroll:n}=e;n&&n(t);t.stopPropagation()},customCloseDelegate:h,ref:t}),n))):null}},Sn4D:function(e,t,n){"use strict";n.d(t,"a",(function(){return p}));var o=n("q1tI"),r=n.n(o),a=n("Eyy1"),i=n("TSYQ"),s=n("x0D+"),l=n("Nkvk"),c=n("AiMB"),u=n("mkWe"),d=n("qFKp"),m=n("X0gx"),v=n("sHQ4");function p(e){const{position:t,onClose:n,children:p,className:f,theme:b=v}=e,h=Object(a.ensureNotNull)(Object(o.useContext)(u.a)),[w,g]=Object(o.useState)(0),y=Object(o.useRef)(null),E=Object(o.useContext)(m.a);return Object(o.useEffect)(()=>{var e;return null===(e=y.current)||void 0===e||e.focus({preventScroll:!0}),E.subscribe(h,n),Object(l.setFixedBodyState)(!0),d.CheckMobile.iOS()&&Object(s.disableBodyScroll)(Object(a.ensureNotNull)(y.current)),g(h.addDrawer()),()=>{E.unsubscribe(h,n);const e=h.removeDrawer();d.CheckMobile.iOS()&&Object(s.enableBodyScroll)(Object(a.ensureNotNull)(y.current)),0===e&&Object(l.setFixedBodyState)(!1)}},[]),r.a.createElement(c.a,null,r.a.createElement("div",{className:i(v.wrap,v["position"+t])},w===h.currentDrawer&&r.a.createElement("div",{className:v.backdrop,onClick:n}),r.a.createElement("div",{className:i(v.drawer,b.drawer,v["position"+t],f),ref:function(e){y.current=e},tabIndex:-1,onScroll:function(e){e.stopPropagation()},"data-name":e["data-name"]},p)))}},mkWe:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return i}));var o=n("q1tI"),r=n.n(o);class a extends r.a.PureComponent{constructor(e){super(e),this._addDrawer=()=>{const e=this.state.currentDrawer+1;return this.setState({currentDrawer:e}),e},this._removeDrawer=()=>{const e=this.state.currentDrawer-1;return this.setState({currentDrawer:e}),e},this.state={currentDrawer:0}}render(){return r.a.createElement(i.Provider,{value:{addDrawer:this._addDrawer,removeDrawer:this._removeDrawer,currentDrawer:this.state.currentDrawer}},this.props.children)}}const i=r.a.createContext(null)},my4O:function(e,t,n){e.exports={"css-value-small-size":"18px","css-value-border-radius-small-size":"9px","css-value-large-size":"22px",
"css-value-border-radius-large-size":"11px",popupWidget:"popupWidget-1LnizAbt",desc:"desc-1LnizAbt",icon:"icon-1LnizAbt",small:"small-1LnizAbt",large:"large-1LnizAbt",title:"title-1LnizAbt",text:"text-1LnizAbt",action:"action-1LnizAbt"}},sHQ4:function(e,t,n){e.exports={wrap:"wrap-164vy-kj",positionBottom:"positionBottom-164vy-kj",backdrop:"backdrop-164vy-kj",drawer:"drawer-164vy-kj",positionLeft:"positionLeft-164vy-kj"}},"vR7+":function(e,t,n){"use strict";n.r(t);var o=n("q1tI"),r=n.n(o),a=n("i8i4"),i=(n("YFKU"),n("9dlw")),s=n("/KDZ"),l=n("Sn4D"),c=n("mkWe"),u=n("e3/o"),d=n("TSYQ"),m=n("Iivm"),v=n("vqb8"),p=n("my4O");const f=new WeakMap;function b(e){const t=Object(v.a)({watchedValue:e.info});if(null===t)return null;const n=t.map(t=>{const{title:n,titleColor:o,icon:a,iconClassName:i,html:s,action:l,size:c}=t;f.has(t)||f.set(t,Object(u.randomHash)());return r.a.createElement("div",{key:f.get(t),className:p.popupWidget},r.a.createElement(m.a,{className:d(p.icon,i,p[c]),icon:a||void 0}),r.a.createElement("div",{className:p.desc},r.a.createElement("span",{style:{color:o||void 0},className:d(p.title,p[c])},n),s&&r.a.createElement("p",{className:d(p.text,p[c]),dangerouslySetInnerHTML:{__html:s.join(" ")}}),l&&r.a.createElement("span",{className:d(l.tooltip&&"apply-common-tooltip",p.action,p[c]),onClick:()=>{e.onClose(),null==l||l.onClick()},title:l.tooltip},l.text)))});return r.a.createElement(r.a.Fragment,null,n)}const h=new WeakMap;function w(e){const{statusWidgetInfos:t}=e,n=t.filter(e=>e.visible.value()).map(t=>(h.has(t)||h.set(t,Object(u.randomHash)()),r.a.createElement(b,{key:h.get(t),info:t.model.fullInfo(),onClose:e.onClose})));return r.a.createElement(c.b,null,r.a.createElement(s.a,{rule:"screen and (max-width: 428px)"},t=>t?r.a.createElement(l.a,{onClose:e.onClose,position:"Bottom"},n):r.a.createElement(i.a,{isOpened:!0,onClose:e.onClose,position:e.position,doNotCloseOn:e.rendererButton},n)))}function g(e,t,n,r,i,s){const l={rendererButton:n,position:s,statusWidgetInfos:r,onClose:i};e?a.render(o.createElement(w,Object.assign({},l)),t):a.unmountComponentAtNode(t)}n.d(t,"render",(function(){return g}))},vqb8:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var o=n("q1tI");const r=e=>{const t="watchedValue"in e?e.watchedValue:void 0,n="defaultValue"in e?e.defaultValue:e.watchedValue.value(),[r,a]=Object(o.useState)(t?t.value():n);return Object(o.useEffect)(()=>{if(t){a(t.value());const e=e=>a(e);return t.subscribe(e),()=>t.unsubscribe(e)}return()=>{}},[t]),r}},"x0D+":function(e,t,n){var o,r,a;r=[t],void 0===(a="function"==typeof(o=function(e){"use strict";function t(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}Object.defineProperty(e,"__esModule",{value:!0});var n=!1;if("undefined"!=typeof window){var o={get passive(){n=!0}};window.addEventListener("testPassive",null,o),window.removeEventListener("testPassive",null,o)}
var r="undefined"!=typeof window&&window.navigator&&window.navigator.platform&&/iP(ad|hone|od)/.test(window.navigator.platform),a=[],i=!1,s=-1,l=void 0,c=void 0,u=function(e){return a.some((function(t){return!(!t.options.allowTouchMove||!t.options.allowTouchMove(e))}))},d=function(e){var t=e||window.event;return!!u(t.target)||1<t.touches.length||(t.preventDefault&&t.preventDefault(),!1)},m=function(){setTimeout((function(){void 0!==c&&(document.body.style.paddingRight=c,c=void 0),void 0!==l&&(document.body.style.overflow=l,l=void 0)}))};e.disableBodyScroll=function(e,o){if(r){if(!e)return void console.error("disableBodyScroll unsuccessful - targetElement must be provided when calling disableBodyScroll on IOS devices.");if(e&&!a.some((function(t){return t.targetElement===e}))){var m={targetElement:e,options:o||{}};a=[].concat(t(a),[m]),e.ontouchstart=function(e){1===e.targetTouches.length&&(s=e.targetTouches[0].clientY)},e.ontouchmove=function(t){var n,o,r,a;1===t.targetTouches.length&&(o=e,a=(n=t).targetTouches[0].clientY-s,!u(n.target)&&(o&&0===o.scrollTop&&0<a||(r=o)&&r.scrollHeight-r.scrollTop<=r.clientHeight&&a<0?d(n):n.stopPropagation()))},i||(document.addEventListener("touchmove",d,n?{passive:!1}:void 0),i=!0)}}else{p=o,setTimeout((function(){if(void 0===c){var e=!!p&&!0===p.reserveScrollBarGap,t=window.innerWidth-document.documentElement.clientWidth;e&&0<t&&(c=document.body.style.paddingRight,document.body.style.paddingRight=t+"px")}void 0===l&&(l=document.body.style.overflow,document.body.style.overflow="hidden")}));var v={targetElement:e,options:o||{}};a=[].concat(t(a),[v])}var p},e.clearAllBodyScrollLocks=function(){r?(a.forEach((function(e){e.targetElement.ontouchstart=null,e.targetElement.ontouchmove=null})),i&&(document.removeEventListener("touchmove",d,n?{passive:!1}:void 0),i=!1),a=[],s=-1):(m(),a=[])},e.enableBodyScroll=function(e){if(r){if(!e)return void console.error("enableBodyScroll unsuccessful - targetElement must be provided when calling enableBodyScroll on IOS devices.");e.ontouchstart=null,e.ontouchmove=null,a=a.filter((function(t){return t.targetElement!==e})),i&&0===a.length&&(document.removeEventListener("touchmove",d,n?{passive:!1}:void 0),i=!1)}else 1===a.length&&a[0].targetElement===e?(m(),a=[]):a=a.filter((function(t){return t.targetElement!==e}))}})?o.apply(t,r):o)||(e.exports=a)}}]);