(window.webpackJsonp=window.webpackJsonp||[]).push([["new-edit-object-dialog"],{"+ByK":function(e,t,n){e.exports={itemWrap:"itemWrap-3FEBD9eP",item:"item-3FEBD9eP",icon:"icon-3FEBD9eP",selected:"selected-3FEBD9eP",label:"label-3FEBD9eP"}},"/YRR":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 16.5l5-5a1.414 1.414 0 0 1 2 0m11-1l-5 5a1.414 1.414 0 0 1-2 0"/><path fill="currentColor" d="M14 5h1v2h-1zM14 10h1v2h-1zM14 15h1v2h-1zM14 20h1v2h-1z"/></svg>'},"01Ho":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M14.354 6.646L14 6.293l-.354.353-7 7-.353.354.353.354 7 7 .354.353.354-.353 7-7 .353-.354-.353-.354-7-7z"/></svg>'},"4Njr":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M14 21l7.424-6.114a.5.5 0 0 0-.318-.886H18.5V7h-9v7H6.894a.5.5 0 0 0-.318.886L14 21z"/></svg>'},"4ZyK":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M8.5 22v-5.5m0 0v-8L12 7l4 2.5 3.5-1v8l-3.5 1-4-2.5-3.5 1.5z"/></svg>'},"4pMH":function(e,t,n){},"5VK0":function(e,t,n){e.exports={scrollWrap:"scrollWrap-1KEqJy8_",tabsWrap:"tabsWrap-1KEqJy8_",tabs:"tabs-1KEqJy8_",tab:"tab-1KEqJy8_",withHover:"withHover-1KEqJy8_",headerBottomSeparator:"headerBottomSeparator-1KEqJy8_"}},"5ijr":function(e){e.exports=JSON.parse('{"switcherWrapper":"switcherWrapper-1wFH-_jm","size-small":"size-small-1gT-kZYO","size-large":"size-large-MOSirnj_","intent-select":"intent-select-2kut8F29","switcherThumbWrapper":"switcherThumbWrapper-2u191lDO","input":"input-J7QIcTTo","switcherTrack":"switcherTrack-2XruDVTa","intent-default":"intent-default-3soo5rvS","switcherThumb":"switcherThumb-2yuEucci","focus":"focus-uZMRkCO0"}')},"5o6O":function(e,t,n){e.exports={tabs:"tabs-3I2ohC86",tab:"tab-3I2ohC86",noBorder:"noBorder-3I2ohC86",disabled:"disabled-3I2ohC86",active:"active-3I2ohC86",defaultCursor:"defaultCursor-3I2ohC86",slider:"slider-3I2ohC86",content:"content-3I2ohC86"}},"9FXF":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M6.5 12.5v8h3v-8h-3zM12.5 7.5v13h3v-13h-3zM18.5 15.5v5h3v-5h-3z"/></svg>'},CHgb:function(e,t,n){"use strict";n.d(t,"c",(function(){return d})),n.d(t,"a",(function(){return u})),n.d(t,"b",(function(){return h}));var l=n("mrSG"),a=n("q1tI"),r=n.n(a),s=n("TSYQ"),o=n.n(s),i=n("PECq"),c=n("Iivm"),p=n("+ByK");function d(e){const{menuItemClassName:t}=e,n=Object(l.a)(e,["menuItemClassName"]);return r.a.createElement(i.a,Object.assign({},n,{menuItemClassName:o()(t,p.itemWrap)}))}function u(e){return r.a.createElement("div",{className:o()(p.item,p.selected)},r.a.createElement(c.a,{className:p.icon,icon:e.icon}))}function h(e){return r.a.createElement("div",{className:p.item},r.a.createElement(c.a,{className:o()(p.icon,e.iconClassName),icon:e.icon
}),r.a.createElement("div",{className:p.label},e.label))}},D2im:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M17 8.5h7M20.5 12V5M10 19.5h7M13.5 23v-7M3 12.5h7M6.5 16V9"/></svg>'},Dj0x:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M11 18.5h-.5V8.793l.146-.147 3-3L14 5.293l.354.353 3 3 .146.147V18.5H11z"/></svg>'},HWhk:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path fill="currentColor" fillRule="evenodd" clipRule="evenodd" d="M7.5 13a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM5 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0zm9.5-1.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM12 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0zm9.5-1.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM19 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0z"/></svg>'},J4oI:function(e,t,n){e.exports={lineStyleSelect:"lineStyleSelect-3KjU7hI0"}},"K+KL":function(e,t,n){"use strict";n.d(t,"a",(function(){return m}));var l=n("mrSG"),a=n("q1tI"),r=n.n(a),s=n("ECWH"),o=n("RMU6"),i=n("/3z9"),c=n("AnDN"),p=n("GQPI"),d=n("zS+2"),u=n("UmON");const h={role:"listbox"},m=r.a.forwardRef((e,t)=>{const{id:n,tabIndex:a=0,listboxTabIndex:m=-1,disabled:b,highlight:v,intent:g,children:w,onClick:y,onFocus:C,onBlur:f,listboxAria:E=h}=e,S=Object(l.a)(e,["id","tabIndex","listboxTabIndex","disabled","highlight","intent","children","onClick","onFocus","onBlur","listboxAria"]),{isOpened:_,isFocused:P,highlight:x,intent:O,onOpen:j,close:k,toggle:N,buttonFocusBindings:T,onButtonClick:I,buttonRef:L,listboxRef:B}=Object(d.a)({disabled:b,intent:g,highlight:v,onFocus:C,onBlur:f,onClick:y}),M=void 0!==n?Object(o.a)(n,"listbox"):void 0,D=Object(p.c)(N),R=Object(p.a)(_,k);return r.a.createElement(c.a,Object.assign({},S,T,{id:n,role:"button",tabIndex:b?-1:a,disabled:b,isOpened:_,isFocused:P,ref:Object(s.a)([L,t]),highlight:x,intent:O,onClose:k,onOpen:j,onClick:I,onKeyDown:function(e){const t=Object(i.hashFromEvent)(e);if(D(t)||R(t))return void e.preventDefault()},listboxAria:E,listboxId:M,listboxTabIndex:_?0:m,listboxReference:B,onListboxKeyDown:function(e){_&&27===Object(i.hashFromEvent)(e)&&(e.stopPropagation(),k())}}),w,r.a.createElement("span",{className:u.invisibleFocusHandler,tabIndex:0,"aria-hidden":!0,onFocus:k}))});m.displayName="DisclosureMenu"},K3s3:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return c}));var l=n("q1tI"),a=n("TSYQ"),r=n("Eyy1"),s=n("5o6O");const o=s;function i(e){const t=a(e.className,s.tab,{[s.active]:e.isActive,[s.disabled]:e.isDisabled,[s.defaultCursor]:!!e.shouldUseDefaultCursor,[s.noBorder]:!!e.noBorder});return l.createElement("div",{className:t,onClick:e.onClick,ref:e.reference},e.children)}function c(e){return class extends l.PureComponent{constructor(){super(...arguments),this.activeTab={current:null}}componentDidUpdate(){Object(r.ensureNotNull)(this._slider).style.transition="transform 350ms",
this._componentDidUpdate()}componentDidMount(){this._componentDidUpdate()}render(){const{className:t}=this.props,n=this._generateTabs();return l.createElement("div",{className:a(t,s.tabs),"data-name":this.props["data-name"]},n,l.createElement(e,{reference:e=>{this._slider=e}}))}_generateTabs(){return this.activeTab.current=null,l.Children.map(this.props.children,e=>{const t=e,n=Boolean(t.props.isActive),a={reference:e=>{n&&(this.activeTab.current=e),t.props.reference&&t.props.reference(e)}};return l.cloneElement(t,a)})}_componentDidUpdate(){const e=Object(r.ensureNotNull)(this._slider).style;if(this.activeTab.current){const t=this.activeTab.current.offsetWidth,n=this.activeTab.current.offsetLeft;e.transform=`translateX(${n}px)`,e.width=t+"px",e.opacity="1"}else e.opacity="0"}}}c((function(e){return l.createElement("div",{className:s.slider,ref:e.reference})}))},KKsp:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var l=n("q1tI"),a=n("TSYQ"),r=n.n(a),s=n("NOPy");function o(e){const{size:t="normal",className:n}=e;return l.createElement("div",{className:r()(s.separator,"small"===t&&s.small,"normal"===t&&s.normal,"large"===t&&s.large,n)})}},KacW:function(e,t,n){"use strict";n.d(t,"a",(function(){return m}));n("YFKU");var l=n("q1tI"),a=n.n(l),r=n("TSYQ"),s=n.n(r),o=n("8Uy/"),i=n("CHgb"),c=n("bQEj"),p=n("UXdH"),d=n("ZSM+"),u=n("J4oI");const h=[{type:o.LINESTYLE_SOLID,icon:c,label:window.t("Line")},{type:o.LINESTYLE_DASHED,icon:p,label:window.t("Dashed Line")},{type:o.LINESTYLE_DOTTED,icon:d,label:window.t("Dotted Line")}];class m extends a.a.PureComponent{render(){const{id:e,lineStyle:t,className:n,lineStyleChange:l,disabled:r,additionalItems:o,allowedLineStyles:c}=this.props;let p=function(e){let t=[...h];return void 0!==e&&(t=t.filter(t=>e.includes(t.type))),t.map(e=>({value:e.type,selectedContent:a.a.createElement(i.a,{icon:e.icon}),content:a.a.createElement(i.b,{icon:e.icon,label:e.label})}))}(c);return o&&(p=[{id:"additional",readonly:!0,content:o},...p]),a.a.createElement(i.c,{id:e,disabled:r,className:s()(u.lineStyleSelect,n),hideArrowButton:!0,items:p,value:t,onChange:l,"data-name":"line-style-select"})}}},Ly1u:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M7.5 7.5h13v13h-13z"/></svg>'},MB0Y:function(e,t,n){"use strict";var l=n("q1tI"),a=n.n(l),r=n("TSYQ"),s=n.n(r),o=n("mrSG"),i=n("5ijr");n("4pMH");function c(e){const{className:t="",intent:n="default",size:l="small",disabled:a}=e;return r(t,i.switcherWrapper,i["size-"+l],!a&&i["intent-"+n])}class p extends l.PureComponent{render(){const e=this.props,{reference:t,size:n,intent:a}=e,s=Object(o.a)(e,["reference","size","intent"]),p=r(i.input,-1!==this.props.tabIndex&&i.focus);return l.createElement("div",{className:c(this.props)},l.createElement("input",Object.assign({},s,{type:"checkbox",className:p,ref:t})),l.createElement("div",{className:i.switcherThumbWrapper},l.createElement("div",{className:i.switcherTrack}),l.createElement("div",{className:i.switcherThumb})))}}
var d=n("ijHL"),u=n("OP2o");n.d(t,"a",(function(){return h})),n.d(t,"b",(function(){return m}));const h=u;function m(e){const{className:t,checked:n,id:l,label:r,labelDescription:o,value:i,preventLabelHighlight:c,reference:h,switchReference:m,theme:b=u,disabled:v}=e,g=s()(b.label,n&&!c&&b.labelOn),w=s()(t,b.wrapper,n&&b.wrapperWithOnLabel);return a.a.createElement("label",{className:w,htmlFor:l,ref:h},a.a.createElement("div",{className:b.labelRow},a.a.createElement("div",{className:g},r),o&&a.a.createElement("div",{className:b.labelHint},o)),a.a.createElement(p,Object.assign({disabled:v,className:b.switch,reference:m,checked:n,onChange:function(t){const n=t.target.checked;void 0!==e.onChange&&e.onChange(n)},value:i,tabIndex:-1,id:l},Object(d.b)(e))))}},NOPy:function(e,t,n){e.exports={separator:"separator-eqcGT_ow",small:"small-eqcGT_ow",normal:"normal-eqcGT_ow",large:"large-eqcGT_ow"}},OP2o:function(e,t,n){e.exports={wrapper:"wrapper-1Eudat6L",hovered:"hovered-1Eudat6L",labelRow:"labelRow-1Eudat6L",label:"label-1Eudat6L",labelHint:"labelHint-1Eudat6L",labelOn:"labelOn-1Eudat6L"}},UXdH:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" d="M4 13h5v1H4v-1zM12 13h5v1h-5v-1zM20 13h5v1h-5v-1z"/></svg>'},UXjO:function(e,t,n){"use strict";n.d(t,"a",(function(){return p}));var l=n("q1tI"),a=n.n(l),r=n("TSYQ"),s=n.n(r),o=n("PECq"),i=n("ijHL"),c=n("z1Uu");function p(e){const{id:t,fontSize:n,fontSizes:l=[],className:r,disabled:p,fontSizeChange:d}=e;return a.a.createElement(o.a,Object.assign({id:t,disabled:p,className:s()(r,c.defaultSelect),menuClassName:c.defaultSelect,items:(u=l,u.map(e=>({value:e.value,content:e.title}))),value:n,onChange:d},Object(i.b)(e)));var u}},V1YL:function(e,t,n){e.exports={recalculateCheckbox:"recalculateCheckbox-2z5ytJSA",descriptionCell:"descriptionCell-2z5ytJSA"}},W7Dn:function(e,t,n){e.exports={scrollable:"scrollable-2CTvqFKf"}},Y5hB:function(e,t,n){"use strict";n.r(t);var l=n("YFKU"),a=n("i8i4"),r=n("q1tI"),s=n.n(r),o=n("Eyy1"),i=(n("bSeV"),n("CLNU")),c=n("Vdly"),p=n("Kxc7"),d=n("FQhm"),u=n("JWMC"),h=n("aDg1"),m=n("vHME"),b=n("ycFu"),v=n("tWVy"),g=n("tmL0"),w=n("3ClC"),y=n("W7Dn");class C extends r.PureComponent{constructor(e){super(e),this._renderFooterLeft=e=>{const{source:t,model:n}=this.props;if(Object(w.isStudy)(t))return r.createElement(m.a,{model:n,source:t,mode:e?"compact":"normal"});throw new TypeError("Unsupported source type.")},this._handleSelect=e=>{this.setState({activeTabId:e},()=>{this._requestResize&&this._requestResize()}),this.props.onActiveTabChanged&&this.props.onActiveTabChanged(e)},this._handleScroll=()=>{v.a.fire()},this._handleSubmit=()=>{this.props.onSubmit(),this.props.onClose()};const{pages:t,initialActiveTab:n}=this.props;this.state={activeTabId:t.allIds.includes(n)?n:t.allIds[0]}}render(){const{title:e,onCancel:t,onClose:n}=this.props,{activeTabId:l}=this.state;return r.createElement(b.a,{dataName:"indicator-properties-dialog",title:e,isOpened:!0,onSubmit:this._handleSubmit,onCancel:t,onClickOutside:n,
onClose:n,footerLeftRenderer:this._renderFooterLeft,render:this._renderChildren(l),submitOnEnterKey:!1})}_renderChildren(e){return({requestResize:t})=>{this._requestResize=t;const{pages:n,source:l,model:a}=this.props,s=n.byId[e];"Component"in s||s.page;return r.createElement(r.Fragment,null,r.createElement(h.a,{activeTabId:e,onSelect:this._handleSelect,tabs:n}),r.createElement(g.a,{className:y.scrollable,onScroll:this._handleScroll},"Component"in s&&r.createElement(s.Component,{source:l,model:a})))}}}var f=n("PjdP"),E=n("HfwS"),S=n("HGyE");class _ extends r.PureComponent{render(){const{input:e,value:t,onChange:n,onBlur:l,onKeyDown:a}=this.props,s=e.options.reduce((e,t)=>(e[t]="NONE"===t?window.t("Default"):t,e),{}),o=Object.assign(Object.assign({},e),{optionsTitles:s});return r.createElement(S.b,{input:o,value:t,onChange:n,onBlur:l,onKeyDown:a})}}const P=Object(E.a)(_);var x=n("h5Dg"),O=n("rJEJ"),j=n("XDrA"),k=n("+8gn"),N=n("Q+1u");n("HbRj");const T=r.createContext(null),I=window.t("{currency} per order"),L=window.t("{currency} per contract");class B extends r.PureComponent{render(){const{input:e}=this.props,t=Object(o.ensureNotNull)(this.context),n={percent:"%",cash_per_order:I.format({currency:t}),cash_per_contract:L.format({currency:t})},l=Object.assign(Object.assign({},e),{optionsTitles:n});return r.createElement(S.a,{input:l})}}B.contextType=T;const M=window.t("Contracts"),D=window.t("% of equity");class R extends r.PureComponent{render(){const{input:e}=this.props,t=Object(o.ensureNotNull)(this.context),n={fixed:M,cash_per_order:t,percent_of_equity:D},l=Object.assign(Object.assign({},e),{optionsTitles:n});return r.createElement(S.a,{input:l})}}R.contextType=T;var V=n("+GxX"),z=n("V1YL");class F extends r.PureComponent{render(){const{inputs:e}=this.props;return r.createElement(N.a,null,r.createElement(O.a,{label:window.t("Initial capital")},r.createElement(f.a,{input:e.initial_capital})),r.createElement(O.a,{label:window.t("Base currency")},r.createElement(P,{input:e.currency})),r.createElement(O.a,{label:window.t("Order size"),labelAlign:"adaptive"},r.createElement(j.a,null,r.createElement(f.a,{input:e.default_qty_value}),r.createElement(R,{input:e.default_qty_type}))),r.createElement(O.a,{label:window.t("Pyramiding")},r.createElement("span",null,r.createElement(f.a,{input:e.pyramiding})),r.createElement("span",{className:z.descriptionCell},window.t("orders",{context:"Pyramiding: count orders"}))),r.createElement(N.a.Separator,null),r.createElement(O.a,{label:window.t("Commission"),labelAlign:"adaptive"},r.createElement(j.a,null,r.createElement(f.a,{input:e.commission_value}),r.createElement(B,{input:e.commission_type}))),r.createElement(O.a,{label:window.t("Verify Price for Limit Orders")},r.createElement("span",null,r.createElement(f.a,{input:e.backtest_fill_limits_assumption})),r.createElement("span",{className:z.descriptionCell},window.t("ticks",{context:"slippage ... ticks"}))),r.createElement(O.a,{label:window.t("Slippage")},r.createElement("span",null,r.createElement(f.a,{input:e.slippage
})),r.createElement("span",{className:z.descriptionCell},window.t("ticks",{context:"slippage ... ticks"}))),r.createElement(N.a.Separator,null),Object(V.isFeatureEnabled)("show_strategy_margin_inputs")&&e.margin_long&&e.margin_short&&r.createElement(r.Fragment,null,r.createElement(O.a,{label:window.t("Margin For Long Positions")},r.createElement("span",null,r.createElement(f.a,{input:e.margin_long})),r.createElement("span",{className:z.descriptionCell},"%")),r.createElement(O.a,{label:window.t("Margin For Short Positions")},r.createElement("span",null,r.createElement(f.a,{input:e.margin_short})),r.createElement("span",{className:z.descriptionCell},"%")),r.createElement(N.a.Separator,null)),r.createElement(O.a,{label:window.t("Recalculate"),labelAlign:"top"},r.createElement("div",null,r.createElement("div",{className:z.recalculateCheckbox},r.createElement(x.a,{label:window.t("After Order is Filled"),input:e.calc_on_order_fills})),r.createElement("div",{className:z.recalculateCheckbox},r.createElement(x.a,{label:window.t("On Every Tick"),input:e.calc_on_every_tick})))))}}function H(e){const{property:t,model:n,inputs:l,study:a}=e;return r.createElement(k.a,{property:t.inputs,model:n,study:a},r.createElement(F,{inputs:l}))}F.contextType=k.b;var A=n("z61+"),W=n("txPx");const G=Object(W.getLogger)("Platform.GUI.PropertyDialog.Indicators.StrategyPage");class U extends r.PureComponent{constructor(e){super(e),this._handleWatchedDataChange=()=>{this.setState({currency:this._getCurrency()})};const{source:t}=this.props;if(this._source=t,!Object(w.isStudy)(this._source))throw new TypeError("Strategy page works only for study.");this._properties=t.properties();const n=t.metaInfo(),l=new A.a(n);this._inputs=l.getStrategyProperties(),this.state={currency:this._getCurrency()}}componentDidMount(){this._source.watchedData.subscribe(this._handleWatchedDataChange)}componentWillUnmount(){this._source.watchedData.unsubscribe(this._handleWatchedDataChange)}render(){return r.createElement(T.Provider,{value:this.state.currency},r.createElement(H,{inputs:this._inputs,property:this._properties,model:this.props.model,study:this.props.source}))}_getCurrency(){const e=this._source,t=e.reportData();if(null===t||void 0===t.currency){void 0!==this.state&&null===this.state.currency||G.logWarn("Can't obtain currency from strategy report");const t=e.metaInfo().inputs.find(e=>"currency"===e.internalID),n=null==t?void 0:t.defval;if(n&&"NONE"!==n)return n.toString();const l=this.props.model.mainSeries().symbolInfo();return(null==l?void 0:l.original_currency_code)||(null==l?void 0:l.currency_code)||null}return t.currency}}var K=n("5Ssy");class q extends r.PureComponent{constructor(e){super(e),this._properties=this.props.source.properties(),this._inputs=new A.a(this.props.source.metaInfo()).getUserEditableInputs()}render(){return r.createElement(K.a,{property:this._properties,model:this.props.model,study:this.props.source,inputs:this._inputs})}}var Y=n("RMU6"),Q=n("23IT"),J=n("0YCj"),X=n.n(J),Z=n("Z1Tk"),$=n("S0KV");const ee=window.t("Change Visibility")
;class te extends r.PureComponent{constructor(){super(...arguments),this._onChange=e=>{const{setValue:t}=this.context,{visible:n}=this.props;n&&Object($.b)(n,n=>t(n,e,ee))}}render(){const{id:e,title:t,visible:n,disabled:l}=this.props,a=Object(i.clean)(window.t(t,{context:"input"}),!0);return r.createElement(x.b,{label:a,disabled:l,input:{id:e,type:"bool",defval:!0,name:"visible"},value:!n||Object($.a)(n),onChange:this._onChange})}}te.contextType=Z.b;var ne=n("KKsp"),le=n("MB0Y"),ae=n("CHgb"),re=n("xHjM"),se=n("/YRR"),oe=n("rlj/"),ie=n("ZtdB"),ce=n("D2im"),pe=n("tH7p"),de=n("tQCG"),ue=n("9FXF"),he=n("sPU+");const me={[Q.LineStudyPlotStyle.Line]:{type:Q.LineStudyPlotStyle.Line,order:0,icon:re,label:window.t("Line")},[Q.LineStudyPlotStyle.LineWithBreaks]:{type:Q.LineStudyPlotStyle.LineWithBreaks,order:1,icon:se,label:window.t("Line With Breaks")},[Q.LineStudyPlotStyle.StepLine]:{type:Q.LineStudyPlotStyle.StepLine,order:2,icon:oe,label:window.t("Step Line")},[Q.LineStudyPlotStyle.Histogram]:{type:Q.LineStudyPlotStyle.Histogram,order:3,icon:ie,label:window.t("Histogram")},[Q.LineStudyPlotStyle.Cross]:{type:Q.LineStudyPlotStyle.Cross,order:4,icon:ce,label:window.t("Cross",{context:"chart_type"})},[Q.LineStudyPlotStyle.Area]:{type:Q.LineStudyPlotStyle.Area,order:5,icon:pe,label:window.t("Area")},[Q.LineStudyPlotStyle.AreaWithBreaks]:{type:Q.LineStudyPlotStyle.AreaWithBreaks,order:6,icon:de,label:window.t("Area With Breaks")},[Q.LineStudyPlotStyle.Columns]:{type:Q.LineStudyPlotStyle.Columns,order:7,icon:ue,label:window.t("Columns")},[Q.LineStudyPlotStyle.Circles]:{type:Q.LineStudyPlotStyle.Circles,order:8,icon:he,label:window.t("Circles")}},be=Object.values(me).sort((e,t)=>e.order-t.order).map(e=>({value:e.type,selectedContent:s.a.createElement(ae.a,{icon:e.icon}),content:s.a.createElement(ae.b,{icon:e.icon,label:e.label})})),ve=window.t("Price Line");class ge extends s.a.PureComponent{render(){const{id:e,plotType:t,className:n,priceLine:l,plotTypeChange:a,priceLineChange:r,disabled:o}=this.props,i={readonly:!0,content:s.a.createElement(s.a.Fragment,null,s.a.createElement(le.b,{id:"PlotTypePriceLineSwitch",checked:l,label:ve,preventLabelHighlight:!0,value:"priceLineSwitcher",onChange:r}),s.a.createElement(ne.a,null))};return s.a.createElement(ae.c,{id:e,disabled:o,className:n,hideArrowButton:!0,items:[i,...be],value:t,onChange:a})}}var we=n("lkVX"),ye=n("wwEg");const Ce=window.t("Change Plot Type"),fe=window.t("Change Price Line");class Ee extends r.PureComponent{constructor(){super(...arguments),this._onPlotTypeChange=e=>{const{setValue:t}=this.context,{styleProp:{plottype:n}}=this.props;n&&t(n,e,Ce)},this._onPriceLineChange=e=>{const{setValue:t}=this.context,{styleProp:{trackPrice:n}}=this.props;n&&t(n,e,fe)}}render(){const{id:e,paletteColor:t,paletteColorProps:n,styleProp:l,isLine:a,hasPlotTypeSelect:s,grouped:o}=this.props,i=n.childs();return r.createElement(O.a,{grouped:o,label:r.createElement("div",{className:ye.childRowContainer},window.t(t.name,{context:"input"}))},r.createElement(we.a,{disabled:!l.visible.value(),
color:i.color,transparency:l.transparency,thickness:a?i.width:void 0,isPaletteColor:!0}),a&&s&&l.plottype&&l.trackPrice?r.createElement(ge,{id:Object(Y.a)(e,"plot-type-select"),disabled:!l.visible.value(),className:ye.smallStyleControl,plotType:l.plottype.value(),priceLine:l.trackPrice.value(),plotTypeChange:this._onPlotTypeChange,priceLineChange:this._onPriceLineChange}):null)}}Ee.contextType=Z.b;class Se extends r.PureComponent{render(){const{plot:e,area:t,palette:n,paletteProps:l,hideVisibilitySwitch:a,styleProp:s}=this.props,i=e?e.id:Object(o.ensureDefined)(t).id,c=!i.startsWith("fill")&&e&&Object(Q.isLinePlot)(e);return r.createElement(r.Fragment,null,!a&&r.createElement(N.a.Row,null,r.createElement(N.a.Cell,{placement:"first",colSpan:2,grouped:!0},r.createElement(te,{id:i,title:t?t.title:s.title.value(),visible:s.visible}))),function(e,t,n,l,a){const s=t.colors,i=n.colors;return Object.keys(s).map((t,n)=>r.createElement(Ee,{key:t,id:e,grouped:!0,paletteColor:Object(o.ensureDefined)(s[t]),paletteColorProps:Object(o.ensureDefined)(i[t]),styleProp:l,isLine:a,hasPlotTypeSelect:0===n}))}(i,n,l,s,c),r.createElement(N.a.GroupSeparator,null))}}Se.contextType=Z.b;const _e=window.t("Change Plot Type"),Pe=window.t("Change Price Line");class xe extends r.PureComponent{constructor(){super(...arguments),this._onPlotTypeChange=e=>{const{setValue:t}=this.context,{property:{plottype:n}}=this.props;n&&t(n,e,_e)},this._onPriceLineChange=e=>{const{setValue:t}=this.context,{property:{trackPrice:n}}=this.props;n&&t(n,e,Pe)}}render(){const{id:e,isRGB:t,property:{title:n,color:l,plottype:a,linewidth:s,transparency:o,trackPrice:i,visible:c}}=this.props;return r.createElement(O.a,{label:r.createElement(te,{id:e,title:n.value(),visible:c})},t?null:r.createElement(we.a,{disabled:!c.value(),color:l,transparency:o,thickness:s}),r.createElement(ge,{id:Object(Y.a)(e,"plot-type-select"),disabled:!c.value(),className:ye.smallStyleControl,plotType:a.value(),priceLine:i.value(),plotTypeChange:this._onPlotTypeChange,priceLineChange:this._onPriceLineChange}))}}xe.contextType=Z.b;const Oe=r.createContext(null);class je extends r.PureComponent{render(){const{id:e,property:{visible:t}}=this.props;return r.createElement(Oe.Consumer,null,n=>r.createElement(O.a,{label:r.createElement(te,{id:e,title:ke(Object(o.ensureNotNull)(n),e),visible:t})},this._upColorControl(),this._downColorControl()))}_upColorControl(){const{isRGB:e,property:{colorup:t,transparency:n,visible:l}}=this.props;return e?null:r.createElement(we.a,{disabled:!l.value(),color:t,transparency:n})}_downColorControl(){const{isRGB:e,property:{colordown:t,transparency:n,visible:l}}=this.props;return e?null:r.createElement("span",{className:ye.additionalSelect},r.createElement(we.a,{disabled:!l.value(),color:t,transparency:n}))}}function ke(e,t){const n=Object(o.ensureDefined)(e.metaInfo().styles),{title:l}=Object(o.ensureDefined)(n[t]);return Object(o.ensureDefined)(l)}je.contextType=Z.b;var Ne=n("/SnT"),Te=n.n(Ne),Ie=n("TSYQ"),Le=n.n(Ie),Be=n("wHCJ"),Me=n("PECq"),De=n("972a");const Re={
[De.MarkLocation.AboveBar]:{value:De.MarkLocation.AboveBar,content:window.t("Above Bar"),order:0},[De.MarkLocation.BelowBar]:{value:De.MarkLocation.BelowBar,content:window.t("Below Bar"),order:1},[De.MarkLocation.Top]:{value:De.MarkLocation.Top,content:window.t("Top"),order:2},[De.MarkLocation.Bottom]:{value:De.MarkLocation.Bottom,content:window.t("Bottom"),order:3},[De.MarkLocation.Absolute]:{value:De.MarkLocation.Absolute,content:window.t("Absolute"),order:4}},Ve=Object.values(Re).sort((e,t)=>e.order-t.order);class ze extends r.PureComponent{render(){const{id:e,shapeLocation:t,className:n,menuItemClassName:l,shapeLocationChange:a,disabled:s}=this.props;return r.createElement(Me.a,{id:e,disabled:s,className:n,menuItemClassName:l,items:Ve,value:t,onChange:a})}}const Fe=window.t("Change Char"),He=window.t("Change Location");class Ae extends r.PureComponent{constructor(){super(...arguments),this._onCharChange=e=>{const{setValue:t}=this.context,n=e.currentTarget.value.trim(),l=Te()(n),a=0===l.length?"":l[l.length-1],{property:{char:r}}=this.props;t(r,a,Fe)},this._onLocationChange=e=>{const{setValue:t}=this.context,{property:{location:n}}=this.props;t(n,e,He)}}render(){const{id:e,isRGB:t,property:{title:n,color:l,transparency:a,char:s,location:o,visible:i},hasPalette:c}=this.props;return r.createElement(O.a,{grouped:c,label:r.createElement(te,{id:e,title:n.value(),visible:i})},!c&&!t&&r.createElement(we.a,{disabled:!i.value(),color:l,transparency:a}),r.createElement(Be.a,{disabled:!i.value(),className:ye.smallStyleControl,value:s.value(),onChange:this._onCharChange}),r.createElement(ze,{id:Object(Y.a)(e,"shape-style-select"),disabled:!i.value(),className:Ie(ye.defaultSelect,ye.additionalSelect),menuItemClassName:ye.defaultSelectItem,shapeLocation:o.value(),shapeLocationChange:this._onLocationChange}))}}Ae.contextType=Z.b;var We=n("Nu4p");const Ge={arrow_down:n("4Njr"),arrow_up:n("lOpG"),circle:n("br6c"),cross:n("m+Gx"),diamond:n("01Ho"),flag:n("4ZyK"),label_down:n("kMtk"),label_up:n("Dj0x"),square:n("Ly1u"),triangle_down:n("leq5"),triangle_up:n("flzi"),x_cross:n("iB0j")};function Ue(e){return Ge[e]}const Ke=[];Object.keys(We.a).forEach(e=>{const t=We.a[e];Ke.push({id:t.id,value:t.id,selectedContent:s.a.createElement(ae.a,{icon:Ue(t.icon)}),content:s.a.createElement(ae.b,{icon:Ue(t.icon),label:t.guiName})})});class qe extends s.a.PureComponent{render(){const{id:e,shapeStyleId:t,className:n,shapeStyleChange:l,disabled:a}=this.props;return s.a.createElement(ae.c,{id:e,disabled:a,className:n,hideArrowButton:!0,items:Ke,value:t,onChange:l})}}const Ye=window.t("Change Shape"),Qe=window.t("Change Location");class Je extends r.PureComponent{constructor(){super(...arguments),this._onPlotTypeChange=e=>{const{setValue:t}=this.context,{property:{plottype:n}}=this.props;t(n,e,Ye)},this._onLocationChange=e=>{const{setValue:t}=this.context,{property:{location:n}}=this.props;t(n,e,Qe)}}render(){const{id:e,isRGB:t,hasPalette:n,property:{title:l,color:a,transparency:s,plottype:o,location:i,visible:c}}=this.props
;return r.createElement(O.a,{grouped:n,label:r.createElement(te,{id:e,title:l.value(),visible:c})},!n&&!t&&r.createElement(we.a,{disabled:!c.value(),color:a,transparency:s}),r.createElement(qe,{id:Object(Y.a)(e,"shape-style-select"),disabled:!c.value(),className:ye.smallStyleControl,shapeStyleId:o.value(),shapeStyleChange:this._onPlotTypeChange}),r.createElement(ze,{id:Object(Y.a)(e,"shape-location-select"),disabled:!c.value(),className:Ie(ye.defaultSelect,ye.additionalSelect),menuItemClassName:ye.defaultSelectItem,shapeLocation:i.value(),shapeLocationChange:this._onLocationChange}))}}Je.contextType=Z.b;class Xe extends r.PureComponent{render(){const{id:e,isRGB:t,title:n,visible:l,color:a,transparency:s,thickness:o,children:i,switchable:c=!0}=this.props;return r.createElement(O.a,{label:c?r.createElement(te,{id:e,title:n,visible:l}):n},t?null:r.createElement(we.a,{disabled:l&&!(Array.isArray(l)?l[0].value():l.value()),color:a,transparency:s,thickness:o}),i)}}Xe.contextType=Z.b;var Ze=n("m/cY");const $e=Object(W.getLogger)("Chart.Study.PropertyPage"),et=Object(l.t)("Wick"),tt=Object(l.t)("Border"),nt=Object(l.t)("Arrow Up Color"),lt=Object(l.t)("Arrow Down Color");class at extends r.PureComponent{render(){const{plot:e,palettes:t,study:n}=this.props,l=e.id,a=n.properties().styles[l],s=e.type,i=!!n.metaInfo().isRGB;if("line"===s||"bar_colorer"===s||"bg_colorer"===s){const n=t.main;return n&&n.palette&&n.paletteProps?r.createElement(Se,{plot:e,palette:n.palette,paletteProps:n.paletteProps,styleProp:a}):r.createElement(xe,{id:l,property:a,isRGB:i})}if("arrows"===s){const n=t.up,s=t.down;return n||s?r.createElement(r.Fragment,null,n&&n.palette&&n.paletteProps?r.createElement(Se,{plot:e,palette:n.palette,paletteProps:n.paletteProps,styleProp:Object.assign(Object.assign({},a),{title:Object(Ze.a)(nt)})}):r.createElement(Xe,{id:l,isRGB:i,title:nt,color:a.colorup,visible:a.visible,transparency:a.transparency}),s&&s.palette&&s.paletteProps?r.createElement(Se,{plot:e,palette:s.palette,paletteProps:s.paletteProps,styleProp:Object.assign(Object.assign({},a),{title:Object(Ze.a)(lt)})}):r.createElement(Xe,{id:l,isRGB:i,title:lt,color:a.colordown,visible:a.visible,transparency:a.transparency})):r.createElement(je,{id:l,property:a,isRGB:i,plot:e,palettes:t,styleProp:a})}if("chars"===s||"shapes"===s){const n=t.main;return r.createElement(r.Fragment,null,"chars"===s?r.createElement(Ae,{id:l,property:a,hasPalette:Boolean(n&&n.palette),isRGB:i}):r.createElement(Je,{id:l,property:a,hasPalette:Boolean(n&&n.palette),isRGB:i}),n&&n.palette&&n.paletteProps&&r.createElement(Se,{plot:e,palette:n.palette,paletteProps:n.paletteProps,hideVisibilitySwitch:!0,styleProp:a}))}if(Object(Q.isOhlcPlot)(e)){const l=e.target,a=Object(o.ensureDefined)(n.metaInfo().defaults.ohlcPlots)[l],s=n.properties().ohlcPlots[l],c=t.main;let p,d;return p=c&&c.palette&&c.paletteProps?r.createElement(Se,{plot:e,palette:c.palette,paletteProps:c.paletteProps,styleProp:s}):r.createElement(Xe,{id:l,isRGB:i,title:s.title.value(),color:s.color,visible:s.visible,
transparency:s.transparency}),void 0!==a&&Object(Q.isOhlcPlotStyleCandles)(a)&&(d=r.createElement(r.Fragment,null,t.wick&&t.wick.palette&&t.wick.paletteProps?r.createElement(Se,{plot:e,palette:t.wick.palette,paletteProps:t.wick.paletteProps,styleProp:Object.assign(Object.assign({},s),{title:Object(Ze.a)(et)})}):r.createElement(Xe,{id:l,isRGB:i,title:et,visible:s.drawWick,color:s.wickColor,transparency:s.transparency}),t.border&&t.border.palette&&t.border.paletteProps?r.createElement(Se,{plot:e,palette:t.border.palette,paletteProps:t.border.paletteProps,styleProp:Object.assign(Object.assign({},s),{title:Object(Ze.a)(tt)})}):r.createElement(Xe,{id:l,isRGB:i,title:tt,visible:s.drawBorder,color:s.borderColor,transparency:s.transparency}))),r.createElement(r.Fragment,null,p,d)}return $e.logError("Unknown plot type: "+s),null}}var rt=n("YS4w"),st=n("mrSG"),ot=n("KacW");const it=window.t("Change Line Style");class ct extends s.a.PureComponent{constructor(){super(...arguments),this._onLineStyleChange=e=>{const{setValue:t}=this.context,{lineStyle:n}=this.props;Object($.b)(n,n=>t(n,e,it))}}render(){const e=this.props,{lineStyle:t}=e,n=Object(st.a)(e,["lineStyle"]);return s.a.createElement(ot.a,Object.assign({},n,{lineStyle:Object($.a)(t),lineStyleChange:this._onLineStyleChange}))}}ct.contextType=Z.b;const pt=window.t("Change Value");class dt extends r.PureComponent{constructor(){super(...arguments),this._onValueChange=e=>{const{setValue:t}=this.context,{value:n}=this.props.property;t(n,e,pt)}}render(){const{id:e,property:{name:t,color:n,linestyle:l,linewidth:a,transparency:s,value:o,visible:i}}=this.props;return r.createElement(O.a,{labelAlign:"adaptive",label:r.createElement(te,{id:e,title:t.value(),visible:i})},r.createElement("div",{className:ye.block},r.createElement("div",{className:ye.group},r.createElement(we.a,{disabled:!i.value(),color:n,transparency:s,thickness:a}),r.createElement(ct,{id:Object(Y.a)(e,"line-style-select"),disabled:!i.value(),className:ye.smallStyleControl,lineStyle:l})),r.createElement("div",{className:Ie(ye.wrapGroup,ye.defaultSelect,ye.additionalSelect)},r.createElement(rt.b,{input:{id:"",name:"",type:"float",defval:0},value:o.value(),disabled:!i.value(),onChange:this._onValueChange}))))}}dt.contextType=Z.b;class ut extends r.PureComponent{render(){const{orders:{visible:e,showLabels:t,showQty:n}}=this.props;return r.createElement(r.Fragment,null,r.createElement(N.a.Row,null,r.createElement(N.a.Cell,{placement:"first",colSpan:2},r.createElement(te,{id:"chart-orders-switch",title:window.t("Trades on Chart"),visible:e}))),r.createElement(N.a.Row,null,r.createElement(N.a.Cell,{placement:"first",colSpan:2},r.createElement(te,{id:"chart-orders-labels-switch",title:window.t("Signal Labels"),visible:t}))),r.createElement(N.a.Row,null,r.createElement(N.a.Cell,{placement:"first",colSpan:2},r.createElement(te,{id:"chart-orders-qty-switch",title:window.t("Quantity"),visible:n}))))}}ut.contextType=Z.b;var ht=n("KG+6"),mt=n("kk0y");const bt=[{value:ht.a.LeftToRight,content:window.t("Left")},{
value:ht.a.RightToLeft,content:window.t("Right")}],vt=window.t("Width (% of the Box)"),gt=window.t("Placement"),wt=window.t("Show Values"),yt=window.t("Text Color"),Ct=window.t("Change Percent Width"),ft=window.t("Change Placement"),Et=window.t("Change Show Values");class St extends r.PureComponent{constructor(){super(...arguments),this._onPercentWidthChange=e=>{const{setValue:t}=this.context,{percentWidth:n}=this.props.property.childs();t(n,e,Ct)},this._onPlacementChange=e=>{const{setValue:t}=this.context,{direction:n}=this.props.property.childs();t(n,e,ft)},this._onShowValuesChange=e=>{const{setValue:t}=this.context,{showValues:n}=this.props.property.childs();t(n,e,Et)}}render(){const{title:e,percentWidth:t,direction:n,showValues:l,valuesColor:a,visible:s}=this.props.property.childs();return r.createElement(r.Fragment,null,r.createElement(N.a.Row,null,r.createElement(N.a.Cell,{placement:"first",colSpan:2,grouped:!0},r.createElement(te,{id:e.value(),title:e.value(),visible:s}))),r.createElement(O.a,{label:r.createElement("div",{className:ye.childRowContainer},vt),grouped:!0},r.createElement(mt.b,{input:{id:"",name:"",type:"integer",defval:0},value:t.value(),disabled:!s.value(),onChange:this._onPercentWidthChange})),r.createElement(O.a,{label:r.createElement("div",{className:ye.childRowContainer},gt),grouped:!0},r.createElement(Me.a,{id:"hhist-graphic-placement-select",disabled:!s.value(),className:ye.defaultSelect,menuItemClassName:ye.defaultSelectItem,items:bt,value:n.value(),onChange:this._onPlacementChange})),r.createElement(N.a.Row,null,r.createElement(N.a.Cell,{className:ye.childRowContainer,placement:"first",colSpan:2,grouped:!0},r.createElement(x.b,{label:wt,input:{id:e.value()+"_showValues",type:"bool",defval:!0,name:"visible"},value:!l||l.value(),disabled:!s.value(),onChange:this._onShowValuesChange}))),r.createElement(O.a,{label:r.createElement("div",{className:ye.childRowContainer},yt),grouped:!0},r.createElement(we.a,{disabled:s&&!s.value(),color:a})),this._renderColors(),r.createElement(N.a.GroupSeparator,null))}_renderColors(){const{colors:e,titles:t,transparencies:n,visible:l}=this.props.property.childs();return e.childNames().map(a=>r.createElement(O.a,{key:a,grouped:!0,label:r.createElement("div",{className:ye.childRowContainer},t.childs()[a].value())},r.createElement(we.a,{disabled:!l.value(),color:e.childs()[a],transparency:n.childs()[a]})))}}St.contextType=Z.b;class _t extends r.PureComponent{render(){const{title:e}=this.props,{color:t,transparency:n,width:l,style:a,visible:s}=this.props.property.childs();return r.createElement(O.a,{label:r.createElement(te,{id:e.value(),title:e.value(),visible:s})},r.createElement(we.a,{disabled:!s.value(),color:t,transparency:n,thickness:l}),r.createElement(ct,{id:Object(Y.a)(e.value(),"line-style-select"),disabled:!s.value(),className:ye.smallStyleControl,lineStyle:a}))}}_t.contextType=Z.b;class Pt extends r.PureComponent{render(){const{graphicType:e,study:t}=this.props,n=t.metaInfo().graphics,l=t.properties().graphics,a=Object(o.ensureDefined)(n[e])
;return Object.keys(a).map((t,n)=>{const a=l[e][t];return"horizlines"===e||"vertlines"===e||"lines"===e?r.createElement(_t,{key:t,title:"lines"===e?a.title:a.name,property:a}):"hhists"===e?r.createElement(St,{key:t,property:a}):null})}}const xt=window.t("Change Font"),Ot=["Verdana","Courier New","Times New Roman","Arial"].map(e=>({value:e,content:e}));class jt extends r.PureComponent{constructor(){super(...arguments),this._onFontFamilyChange=e=>{const{setValue:t}=this.context,{fontFamily:n}=this.props;t(n,e,xt)}}render(){const{id:e,fontFamily:t,className:n,disabled:l}=this.props;return r.createElement(Me.a,{id:e,disabled:l,className:Le()(n,ye.defaultSelect),menuItemClassName:ye.defaultSelectItem,items:Ot,value:t.value(),onChange:this._onFontFamilyChange})}}jt.contextType=Z.b;var kt=n("UXjO");const Nt=window.t("Change Font Size"),Tt=[10,11,12,14,16,20,24,28,32,40].map(e=>({value:e,title:e.toString()}));class It extends r.PureComponent{constructor(){super(...arguments),this._onFontSizeChange=e=>{const{setValue:t}=this.context,{fontSize:n}=this.props;t(n,e,Nt)}}render(){const e=this.props,{fontSize:t}=e,n=Object(st.a)(e,["fontSize"]);return r.createElement(kt.a,Object.assign({},n,{fontSizes:Tt,fontSize:t.value(),fontSizeChange:this._onFontSizeChange}))}}It.contextType=Z.b;const Lt=window.t("Change Visibility"),Bt=window.t("Labels Font"),Mt=window.t("Show Labels"),Dt={Traditional:new Set(["S5/R5","S4/R4","S3/R3","S2/R2","S1/R1","P"]),Fibonacci:new Set(["S3/R3","S2/R2","S1/R1","P"]),Woodie:new Set(["S4/R4","S3/R3","S2/R2","S1/R1","P"]),Classic:new Set(["S4/R4","S3/R3","S2/R2","S1/R1","P"]),DM:new Set(["S1/R1","P"]),DeMark:new Set(["S1/R1","P"]),Camarilla:new Set(["S4/R4","S3/R3","S2/R2","S1/R1","P"])};class Rt extends s.a.PureComponent{constructor(){super(...arguments),this._onChange=e=>{const{setValue:t}=this.context,{levelsStyle:n}=this.props.property.childs(),{showLabels:l}=n.childs();t(l,e,Lt)}}render(){const{font:e,fontsize:t,levelsStyle:n}=this.props.property.childs();return s.a.createElement(s.a.Fragment,null,s.a.createElement(O.a,{labelAlign:"adaptive",label:s.a.createElement("span",null,Bt)},s.a.createElement("div",{className:ye.block},s.a.createElement("div",{className:ye.group},s.a.createElement(jt,{id:"pivot-points-standard-font-family-select",fontFamily:e})),s.a.createElement("div",{className:Ie(ye.wrapGroup,ye.additionalSelect)},s.a.createElement(It,{id:"pivot-points-standard-font-size-select",fontSize:t})))),s.a.createElement(N.a.Row,null,s.a.createElement(N.a.Cell,{placement:"first",colSpan:2},s.a.createElement(x.b,{label:Mt,input:{id:"ShowLabels",type:"bool",defval:!0,name:"visible"},value:n.childs().showLabels.value(),onChange:this._onChange}))),this._renderColors())}_renderColors(){const{levelsStyle:e,inputs:t}=this.props.property.childs(),{colors:n,widths:l,visibility:a}=e.childs(),{kind:r}=t.childs(),i=Object(o.ensureDefined)(Dt[r.value()]);return n.childNames().filter(e=>i.has(e)).map(e=>s.a.createElement(Xe,{key:e,id:e,title:e,color:n.childs()[e],visible:a.childs()[e],thickness:l.childs()[e]}))}}
Rt.contextType=Z.b;const Vt=Object(l.t)("Change Visibility"),zt=Object(l.t)("Volume Profile"),Ft=Object(l.t)("Show Values"),Ht=Object(l.t)("Width (% of the Box)"),At=Object(l.t)("Placement"),Wt=Object(l.t)("Developing VA"),Gt=[{value:ht.a.RightToLeft,content:Object(l.t)("Right")},{value:ht.a.LeftToRight,content:Object(l.t)("Left")}];class Ut extends s.a.PureComponent{constructor(){super(...arguments),this._onChange=e=>{this._setHhistsProperty("visible",e)},this._onShowValuesChange=e=>{this._setHhistsProperty("showValues",e)},this._onValueChange=e=>{this._setHhistsProperty("percentWidth",e)},this._onDirectionChange=e=>{this._setHhistsProperty("direction",e)}}render(){var e,t;const{metaInfo:n}=this.props,{graphics:a,styles:r}=this.props.property.childs(),{hhists:i,horizlines:c,polygons:p}=a.childs(),d=Object(o.ensureDefined)(n.graphics.hhists),u=Object.keys(d),h=i.childs()[u[0]],m=h.childs().visible,b=u.map(e=>i.childs()[e].childs().showValues),v=h.childs().percentWidth,g=h.childs().direction,w=u.map(e=>i.childs()[e].childs().valuesColor),y=c.childs().pocLines,C=Object(o.ensureDefined)(null===(e=n.graphics.horizlines)||void 0===e?void 0:e.pocLines),f=r.childs().developingPoc,E=Object(o.ensureDefined)(null===(t=n.styles)||void 0===t?void 0:t.developingPoc),S=r.childs().developingVAHigh,_=r.childs().developingVALow,P=n.graphics.polygons&&n.graphics.polygons.histBoxBg;return s.a.createElement(s.a.Fragment,null,s.a.createElement(N.a.Row,null,s.a.createElement(N.a.Cell,{placement:"first",colSpan:2},s.a.createElement(x.b,{label:zt,input:{id:"VolumeProfile",type:"bool",defval:!0,name:"visible"},value:m.value(),onChange:this._onChange}))),s.a.createElement(N.a.Row,null,s.a.createElement(N.a.Cell,{placement:"first"},s.a.createElement("div",{className:ye.childRowContainer},s.a.createElement(x.b,{disabled:!m.value(),label:Ft,input:{id:"ShowValues",type:"bool",defval:!0,name:"visible"},value:b[0].value(),onChange:this._onShowValuesChange}))),s.a.createElement(N.a.Cell,{placement:"last"},s.a.createElement(we.a,{disabled:!m.value()||!b[0].value(),color:w}))),s.a.createElement(N.a.Row,null,s.a.createElement(N.a.Cell,{placement:"first"},s.a.createElement("div",{className:ye.childRowContainer},Ht)),s.a.createElement(N.a.Cell,{placement:"last"},s.a.createElement(mt.b,{disabled:!m.value(),input:{id:"",name:"",type:"integer",defval:0},value:v.value(),onChange:this._onValueChange}))),s.a.createElement(N.a.Row,null,s.a.createElement(N.a.Cell,{placement:"first"},s.a.createElement("div",{className:ye.childRowContainer},At)),s.a.createElement(N.a.Cell,{placement:"last"},s.a.createElement(Me.a,{id:"hhist-direction-select",disabled:!m.value(),className:ye.defaultSelect,menuItemClassName:ye.defaultSelectItem,items:Gt,value:g.value(),onChange:this._onDirectionChange}))),u.map(e=>s.a.createElement(s.a.Fragment,{key:e},i.childs()[e].childs().colors.childNames().map((t,n)=>{const a=d[e];return s.a.createElement(O.a,{key:n,label:s.a.createElement("div",{className:ye.childRowContainer},a&&Object(l.t)(a.titles[n])||"")},s.a.createElement(we.a,{
disabled:!m.value(),color:i.childs()[e].childs().colors.childs()[n],transparency:i.childs()[e].childs().transparencies.childs()[n]}))}))),s.a.createElement(Xe,{id:"pocLines",title:C.name,color:y.childs().color,visible:y.childs().visible,thickness:y.childs().width},s.a.createElement(ct,{id:"poc-lines-line-style-select",disabled:!y.childs().visible.value(),className:ye.smallStyleControl,lineStyle:y.childs().style})),f&&s.a.createElement(Xe,{id:"developingPoc",title:E.title&&Object(l.t)(E.title)||"",color:f.childs().color,visible:f.childs().visible,thickness:f.childs().linewidth},s.a.createElement(ct,{id:"developing-poc-line-style-select",disabled:!f.childs().visible.value(),className:ye.smallStyleControl,lineStyle:f.childs().linestyle})),S&&_&&s.a.createElement(Xe,{id:"developingPoc",title:Wt,color:[S.childs().color,_.childs().color],visible:[S.childs().visible,_.childs().visible],thickness:[S.childs().linewidth,_.childs().linewidth]},s.a.createElement(ct,{id:"developing-VA-line-style-select",disabled:!S.childs().visible.value()&&!_.childs().visible.value(),className:ye.smallStyleControl,lineStyle:[S.childs().linestyle,_.childs().linestyle]})),p&&s.a.createElement(O.a,{label:s.a.createElement("div",null,P&&Object(l.t)(P.name)||"")},s.a.createElement(we.a,{color:p.childs().histBoxBg.childs().color,transparency:p.childs().histBoxBg.childs().transparency})))}_setHhistsProperty(e,t){const{setValue:n}=this.context,{metaInfo:l,property:a}=this.props,r=a.childs().graphics.childs().hhists,s=Object.keys(Object(o.ensureDefined)(l.graphics.hhists));for(let i=0;i<s.length;i++){const l=r.childs()[s[i]].child(e);n(Object(o.ensureDefined)(l),t,Vt)}}}Ut.contextType=Z.b;var Kt=n("KJt4");const qt={PivotPointsStandard:function(){const e=Object(o.ensureNotNull)(Object(r.useContext)(Oe)).properties();return s.a.createElement(Rt,{property:e})},VbPVisible:function(){const e=Object(o.ensureNotNull)(Object(r.useContext)(Oe)),t=e.metaInfo(),n=e.properties();return s.a.createElement(Ut,{metaInfo:t,property:n})}};class Yt extends r.PureComponent{render(){const e=Object(o.ensureNotNull)(this.context);return r.createElement(Oe.Consumer,null,t=>r.createElement(Z.a,{property:Object(o.ensureNotNull)(t).properties(),model:e},r.createElement(N.a,null,this._renderCustomContent(Object(o.ensureNotNull)(t).metaInfo().shortId))))}_renderCustomContent(e){if(e in qt){const t=qt[e];return r.createElement(t,null)}return null}}Yt.contextType=Kt.a;var Qt=n("Ecpn");const Jt=window.t("Default"),Xt=window.t("Precision"),Zt=window.t("Change Precision"),$t=[{value:"default",content:Jt}];for(let bn=0;bn<=8;bn++)$t.push({value:bn,content:bn.toString()});class en extends r.PureComponent{constructor(){super(...arguments),this._onChange=e=>{const{setValue:t}=this.context,{precision:n}=this.props;t(n,e,Zt)}}render(){const{id:e,precision:t}=this.props;return r.createElement(O.a,{label:Xt},r.createElement(Me.a,{id:e,className:ye.defaultSelect,menuItemClassName:ye.defaultSelectItem,items:$t,value:t.value(),onChange:this._onChange}))}}en.contextType=Z.b
;const tn=window.t("Default"),nn=window.t("Override Min Tick"),ln=window.t("Change Min Tick"),an=[{priceScale:1,minMove:1,frac:!1},{priceScale:10,minMove:1,frac:!1},{priceScale:100,minMove:1,frac:!1},{priceScale:1e3,minMove:1,frac:!1},{priceScale:1e4,minMove:1,frac:!1},{priceScale:1e5,minMove:1,frac:!1},{priceScale:1e6,minMove:1,frac:!1},{priceScale:1e7,minMove:1,frac:!1},{priceScale:1e8,minMove:1,frac:!1},{priceScale:2,minMove:1,frac:!0},{priceScale:4,minMove:1,frac:!0},{priceScale:8,minMove:1,frac:!0},{priceScale:16,minMove:1,frac:!0},{priceScale:32,minMove:1,frac:!0},{priceScale:64,minMove:1,frac:!0},{priceScale:128,minMove:1,frac:!0},{priceScale:320,minMove:1,frac:!0}],rn=[{id:"tick-default",value:"default",content:tn}];for(let bn=0;bn<an.length;bn++){const e=an[bn];rn.push({value:e.priceScale+","+e.minMove+","+e.frac,content:e.minMove+"/"+e.priceScale})}class sn extends r.PureComponent{constructor(){super(...arguments),this._onChange=e=>{const{setValue:t}=this.context,{minTick:n}=this.props;t(n,e,ln)}}render(){const{id:e,minTick:t}=this.props;return r.createElement(O.a,{label:nn},r.createElement(Me.a,{id:e,className:ye.defaultSelect,menuItemClassName:ye.defaultSelectItem,items:rn,value:t.value(),onChange:this._onChange}))}}sn.contextType=Z.b;var on=n("5YG5");class cn extends r.PureComponent{constructor(){super(...arguments),this._findPlotPalettes=e=>{const{study:t}=this.props,n=t.metaInfo(),l=Object(o.ensureDefined)(n.palettes);return Object(Q.isBarColorerPlot)(e)||Object(Q.isBgColorerPlot)(e)?{main:{palette:l[e.palette],paletteProps:t.properties().palettes[e.palette]}}:this._findPalettesByTargetId(e.id)}}render(){const{study:e}=this.props,t=e.metaInfo();if(Object(Qt.a)(t.shortId))return r.createElement(Yt,null);const n=e.properties(),{precision:l,strategy:a,minTick:s}=n,o=t.plots.length>0,i=Object(on.a)(e).canOverrideMinTick();return r.createElement(N.a,null,this._plotsElement(),this._bandsElement(),this._bandsBackgroundsElement(),this._areasBackgroundsElement(),this._filledAreasElement(),this._graphicsElement(),o&&r.createElement(en,{id:Object(Y.a)(t.id,"precision-select"),precision:l}),i&&r.createElement(sn,{id:Object(Y.a)(t.id,"min-tick-select"),minTick:s}),X.a.isScriptStrategy(t)&&r.createElement(ut,{orders:a.orders}))}_plotsElement(){const{study:e}=this.props,t=e.metaInfo();return new A.a(t).getUserEditablePlots().filter(e=>!(Object(Q.isUpColorerPlot)(e)||Object(Q.isDownColorerPlot)(e)||Object(Q.isCandleBorderColorerPlot)(e)||Object(Q.isCandleWickColorerPlot)(e))).map(t=>{const n=Object(Q.isOhlcPlot)(t)?Object.assign(Object.assign({},t),{id:t.target}):t,l=this._findPlotPalettes(n);return r.createElement(at,{key:t.id,plot:t,palettes:l,study:e})})}_bandsElement(){const{study:e}=this.props,t=e.properties(),{bands:n}=t;return n&&n.childNames().map((e,t)=>{const l=n.child(e);if(!l.isHidden||!l.isHidden.value())return r.createElement(dt,{key:t,id:l.name.value(),property:l})})}_bandsBackgroundsElement(){const{study:e}=this.props,t=e.properties(),{bandsBackground:n}=t;return n&&r.createElement(Xe,{
id:"bandsBackground",title:"Background",visible:n.fillBackground,color:n.backgroundColor,transparency:n.transparency})}_areasBackgroundsElement(){const{study:e}=this.props,t=e.metaInfo(),n=e.properties(),{areaBackground:l}=n;return t.isRGB?null:l&&r.createElement(Xe,{id:"areaBackground",title:"Background",visible:l.fillBackground,color:l.backgroundColor,transparency:l.transparency})}_filledAreasElement(){const{study:e}=this.props,t=e.metaInfo(),n=t.filledAreas;return!n||t.isRGB?[]:n.map(t=>{if(t.isHidden)return null;const n=e.properties().filledAreasStyle[t.id],l=t.title||"Background";if(t.palette){const e=this._findPalettesByTargetId(t.id),l=Object(o.ensureDefined)(e.main);return r.createElement(Se,{key:t.id,area:t,palette:Object(o.ensureDefined)(l.palette),paletteProps:Object(o.ensureDefined)(l.paletteProps),styleProp:n})}return r.createElement(Xe,{key:t.id,id:t.id,title:l,color:n.color,visible:n.visible,transparency:n.transparency})})}_graphicsElement(){const{study:e}=this.props,t=e.metaInfo().graphics;return t&&Object.keys(t).map((t,n)=>r.createElement(Pt,{key:t,graphicType:t,study:e}))}_findPalettesByTargetId(e){const{study:t}=this.props,n=t.metaInfo(),l=n.plots,a=Object(o.ensureDefined)(n.palettes),r={};for(const s of l)(Object(Q.isColorerPlot)(s)||Object(Q.isOhlcColorerPlot)(s))&&s.target===e&&(r.main={palette:a[s.palette],paletteProps:t.properties().palettes[s.palette]}),Object(Q.isUpColorerPlot)(s)&&s.target===e&&(r.up={palette:a[s.palette],paletteProps:t.properties().palettes[s.palette]}),Object(Q.isDownColorerPlot)(s)&&s.target===e&&(r.down={palette:a[s.palette],paletteProps:t.properties().palettes[s.palette]}),Object(Q.isCandleWickColorerPlot)(s)&&s.target===e&&(r.wick={palette:a[s.palette],paletteProps:t.properties().palettes[s.palette]}),Object(Q.isCandleBorderColorerPlot)(s)&&s.target===e&&(r.border={palette:a[s.palette],paletteProps:t.properties().palettes[s.palette]});return r}}function pn(e){return Object(Z.c)(cn,Object.assign(Object.assign({},e),{property:e.study.properties()}))}class dn extends r.PureComponent{render(){return r.createElement(Kt.a.Provider,{value:this.props.model},r.createElement(Oe.Provider,{value:this.props.source},r.createElement(pn,{study:this.props.source})))}}var un=n("CW80"),hn=n("sQaR");n.d(t,"EditObjectDialogRenderer",(function(){return mn}));class mn extends hn.a{constructor(e,t,n,l){super(),this._timeout=null,this._handleClose=()=>{a.unmountComponentAtNode(this._container),this._setVisibility(!1),this._subscription.unsubscribe(this,this._handleCollectionChanged)},this._handleCancel=()=>{this._model.undoToCheckpoint(this._checkpoint)},this._handleSubmit=()=>{},this._handleActiveTabChanged=e=>{c.setValue(this._activeTabSettingsName(),e)},this._source=e,this._model=t,this._propertyPages=l,this._checkpoint=this._ensureCheckpoint(n),this._subscription=this._model.model().dataSourceCollectionChanged(),this._subscription.subscribe(this,this._handleCollectionChanged)}hide(e){e?this._handleCancel():this._handleSubmit(),this._handleClose()}isVisible(){return this.visible().value()
}show(e={}){if(!p.enabled("property_pages"))return;const t=this._source.metaInfo();if(Object(un.isLineTool)(this._source)&&Object(u.trackEvent)("GUI","Drawing Properties",this._source.name()),Object(w.isStudy)(this._source)){const e=!this._source.isPine()||this._source.isStandardPine()?t.description:"Custom Pine";Object(u.trackEvent)("GUI","Study Properties",e)}let n={byId:{inputs:{title:window.t("Inputs"),Component:q},style:{title:window.t("Style"),Component:dn},properties:{title:window.t("Properties"),Component:U}},allIds:[]};const l=new A.a(t);l.hasUserEditableInputs()&&n.allIds.push("inputs"),l.hasUserEditableProperties()&&n.allIds.push("properties"),l.hasUserEditableStyles()&&n.allIds.push("style"),n=this._getPagesForStudyLineTool(n);const s=e.initialTab||c.getValue(this._activeTabSettingsName())||"inputs";let o=Object(i.clean)(t.shortDescription,!0);a.render(r.createElement(C,{title:o,model:this._model,source:this._source,initialActiveTab:n.allIds.includes(s)?s:n.allIds[0],pages:n,onSubmit:this._handleSubmit,onCancel:this._handleCancel,onClose:this._handleClose,onActiveTabChanged:this._handleActiveTabChanged}),this._container),this._setVisibility(!0),d.emit("edit_object_dialog",{objectType:"study",scriptTitle:this._source.title()})}_activeTabSettingsName(){return"properties_dialog.active_tab.study"}_ensureCheckpoint(e){return void 0===e&&(e=this._model.createUndoCheckpoint()),e}_getPagesForStudyLineTool(e){if(this._propertyPages){const t=this._propertyPages.filter(e=>"coordinates"===e.id||"visibility"===e.id),n={allIds:t.map(e=>e.id),byId:t.reduce((e,t)=>Object.assign(Object.assign({},e),{[t.id]:{title:t.title,page:t}}),{})};return{allIds:[...e.allIds,...n.allIds],byId:Object.assign(Object.assign({},e.byId),n.byId)}}return e}_handleCollectionChanged(){null===this._timeout&&(this._timeout=setTimeout(()=>{this._closeDialogIfSourceIsDeleted(),this._timeout=null}))}_closeDialogIfSourceIsDeleted(){null===this._model.model().dataSourceForId(this._source.id())&&this._handleClose()}}},"ZSM+":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="currentColor"><circle cx="9" cy="14" r="1"/><circle cx="4" cy="14" r="1"/><circle cx="14" cy="14" r="1"/><circle cx="19" cy="14" r="1"/><circle cx="24" cy="14" r="1"/></svg>'},ZtdB:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M4.5 20v-7m3 7V10m3 10V8m3 12V10m3 10v-8m3 8V10m3 10V8"/></svg>'},aDg1:function(e,t,n){"use strict";n("EsMY");var l=n("q1tI"),a=n("TSYQ"),r=n("K3s3"),s=n("nPPD"),o=n("dMmr");const i=Object(s.a)(r.a,o);var c=n("4Cm8"),p=n("5VK0");n.d(t,"a",(function(){return u}));const d=Object(r.c)((function(e){return l.createElement("div",{className:i.slider,ref:e.reference},l.createElement("div",{className:i.inner}))}));class u extends l.PureComponent{constructor(){super(...arguments),this._createClickHandler=e=>()=>{this.props.onSelect(e)}}render(){const e=this._generateDialogTabs();return l.createElement("div",{className:p.scrollWrap},l.createElement("div",{
className:p.headerBottomSeparator}),l.createElement(c.a,{isVisibleFade:Modernizr.mobiletouch,isVisibleButtons:!Modernizr.mobiletouch,isVisibleScrollbar:!1},l.createElement("div",{className:p.tabsWrap},l.createElement(d,{className:p.tabs},e))))}_generateDialogTabs(){const{activeTabId:e,tabs:t}=this.props;return t.allIds.map(n=>{const s=e===n;return l.createElement(r.b,{key:n,className:a(p.tab,!s&&p.withHover),isActive:s,onClick:this._createClickHandler(n)},t.byId[n].title)})}}},bQEj:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M4 13.5h20"/></svg>'},br6c:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><circle stroke="currentColor" cx="14" cy="14" r="6.5"/></svg>'},dMmr:function(e,t,n){e.exports={slider:"slider-3RfwXbxu",inner:"inner-3RfwXbxu"}},flzi:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M19.424 16.735l.478.765H8.098l.478-.765 5-8L14 8.057l.424.678 5 8z"/></svg>'},iB0j:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M9 9l11 11M9 20L20 9"/></svg>'},kMtk:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M11 8.5h-.5v9.707l.146.147 3 3 .354.353.354-.353 3-3 .146-.147V8.5H11z"/></svg>'},lOpG:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M14 7l7.424 6.114a.5.5 0 0 1-.318.886H18.5v7h-9v-7H6.894a.5.5 0 0 1-.318-.886L14 7z"/></svg>'},leq5:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M19.424 11.265l.478-.765H8.098l.478.765 5 8 .424.678.424-.678 5-8z"/></svg>'},"m+Gx":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M9 14.5h11M14.5 20V9"/></svg>'},nPPD:function(e,t,n){"use strict";function l(e,t,n={}){const l=Object.assign({},t);for(const a of Object.keys(t)){const r=n[a]||a;r in e&&(l[a]=[e[r],t[a]].join(" "))}return l}function a(e,t,n={}){return Object.assign({},e,l(e,t,n))}n.d(t,"b",(function(){return l})),n.d(t,"a",(function(){return a}))},"rlj/":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 17v5.5h4v-18h4v12h4v-9h4V21"/></svg>'},"sPU+":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M10.5 13a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0zM16.5 19a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0zM22.5 8a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0z"/></svg>'},tH7p:function(e,t){
e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 13.52v4.98a1 1 0 0 0 1 1h15a1 1 0 0 0 1-1V8.914c0-.89-1.077-1.337-1.707-.707l-4.66 4.66a1 1 0 0 1-1.332.074l-3.716-2.973a1 1 0 0 0-1.198-.039l-3.96 2.772a1 1 0 0 0-.427.82z"/></svg>'},tQCG:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M13 11.5l-1.915-1.532a1 1 0 0 0-1.198-.039l-3.96 2.772a1 1 0 0 0-.427.82V18.5a1 1 0 0 0 1 1H13m3.5-7l4.293-4.293c.63-.63 1.707-.184 1.707.707V18.5a1 1 0 0 1-1 1H16"/><path fill="currentColor" d="M14 6h1v2h-1zM14 11h1v2h-1zM14 16h1v2h-1zM14 21h1v2h-1z"/></svg>'},vHME:function(e,t,n){"use strict";n.d(t,"a",(function(){return u}));var l=n("q1tI"),a=n("TSYQ"),r=n.n(a),s=(n("YFKU"),n("Iivm")),o=n("K+KL"),i=n("N5tr"),c=n("HWhk"),p=n("wt3x");const d={reset:window.t("Reset Settings"),saveAsDefault:window.t("Save As Default"),defaults:window.t("Defaults")};class u extends l.PureComponent{constructor(){super(...arguments),this._handleResetToDefaults=()=>{this.props.model.restorePropertiesForSource(this.props.source)},this._handleSaveAsDefaults=()=>{this.props.source.properties().saveDefaults()}}render(){const{mode:e}=this.props;return l.createElement(o.a,{id:"study-defaults-manager",className:r()("normal"===e&&p.defaultsButtonText),hideArrowButton:"compact"===e,buttonChildren:this._getPlaceHolderItem("compact"===e)},l.createElement(i.b,{className:p.defaultsButtonItem,isActive:!1,label:d.reset,onClick:this._handleResetToDefaults}),l.createElement(i.b,{className:p.defaultsButtonItem,isActive:!1,label:d.saveAsDefault,onClick:this._handleSaveAsDefaults}))}_getPlaceHolderItem(e){return e?l.createElement(s.a,{className:p.defaultsButtonIcon,icon:c}):d.defaults}}},wt3x:function(e,t,n){e.exports={defaultsButtonText:"defaultsButtonText-3mn75BN0",defaultsButtonItem:"defaultsButtonItem-3mn75BN0",defaultsButtonIcon:"defaultsButtonIcon-3mn75BN0"}},wwEg:function(e,t,n){e.exports={smallStyleControl:"smallStyleControl-11tnC1DU",additionalSelect:"additionalSelect-11tnC1DU",childRowContainer:"childRowContainer-11tnC1DU",defaultSelect:"defaultSelect-11tnC1DU",defaultSelectItem:"defaultSelectItem-11tnC1DU",block:"block-11tnC1DU",group:"group-11tnC1DU",wrapGroup:"wrapGroup-11tnC1DU",textMarkGraphicBlock:"textMarkGraphicBlock-11tnC1DU",textMarkGraphicWrapGroup:"textMarkGraphicWrapGroup-11tnC1DU"}},xHjM:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 16.5l4.586-4.586a2 2 0 0 1 2.828 0l3.172 3.172a2 2 0 0 0 2.828 0L23.5 10.5"/></svg>'},z1Uu:function(e,t,n){e.exports={defaultSelect:"defaultSelect-rvczD149"}}}]);