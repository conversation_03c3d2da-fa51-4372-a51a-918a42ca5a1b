:root {
  --tv-color-platform-background: #22262e;
  --tv-color-pane-background: #22262e;
  --tv-color-toolbar-button-text-active-hover: #E0E03F;
  --tv-color-toolbar-button-text-active: #E0E03F;
}

.theme-dark:root {
  --tv-color-platform-background: #161a1f;
  --tv-color-pane-background: #161a1f;
}

.chart-container {
  border: none !important;
}

.header-chart-panel-content .left .button::before {
  display: none !important;
}

.pane-legend-line.apply-overflow-tooltip.main {
  display: none;
}

.header-chart-panel-content .left .group .button {
  padding: 0 4px;
  line-height: 30px;
}

.header-group-properties,
.header-group-indicators,
.header-group-fullscreen {
  float: left;
}

.header-group-properties svg,
.header-group-indicators svg,
.header-group-fullscreen svg {
  margin-top: 8px;
}

.chart-status-picture {
  display: none !important;
}

.chart-widget .chart-markup-table tr:nth-child(3) .pane-legend .pane-legend-item-value-container span:nth-child(2) {
  display: none;
}

.loading-indicator {
  display: none;
}


.dateRangeWrapper-2yU8ifXU:first-child {
  visibility: visible;
  display: none;
}

.dateRangeWrapper-2yU8ifXU:nth-child(2) {
  display: none;
}

.marketStatusOpen-1Ho_ylkC,
html.theme-dark .marketStatusOpen-1Ho_ylkC {
  display: none;
}
