@import "./tradingview_custom.css";
html.theme-light .chart-page .layout__area--top [class*="isOpened-"],
html.theme-light
  .chart-page
  .layout__area--left
  [class*="isActive-"]
  [class*="bg-"] {
  background: #f5f6fa;
}
html.theme-light,
html.theme-light .chart-page {
  background-color: transparent !important;
}
html.theme-light .chart-page .layout__area--top:hover[class*="isOpened-"],
html.theme-light
  .chart-page
  .layout__area--left
  [class*="isActive-"]:hover[class*="bg-"] {
  color: #f0b90b !important;
  background: #f5f6fa;
}
html.theme-light .inner-1xuW-gY4- {
  background: #fff;
}
html.theme-light .chart-page .layout__area--left [class^="inner-"] {
  background-color: #fff;
}
html.theme-light .container-3_8ayT2Q- .background-Q1Fcmxly- {
  fill: #fff;
  stroke: none;
}
html.theme-light .container-2BvS3Fpg- .inner-3YzQuyJx- {
  background: #fff;
}
html.theme-light .pane-separator {
  height: 0;
  background: #eff0f2 !important;
}
html.theme-light .pane-separator .handle {
  display: none;
  background: #eff0f2 !important;
}
html.theme-light .content-YhoA_L2m- {
  background: #fff;
}
html.theme-light .chart-page .chart-container-border {
  background: transparent !important;
}
