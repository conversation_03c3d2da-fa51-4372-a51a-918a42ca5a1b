import { defineStore } from 'pinia'
import { getPairSortList, getPairAreas, getPairsByArea } from '~/api/public'
import kline from '~/utils/kline'
export const MarketsStore = defineStore('marketsCommon', () => {
  const rankObj = ref({})
  const editableTabs = ref([])
  const editableTabsValue = ref('')
  const secondActive = ref('')
  const tableData = ref([])
  const tableTotal = ref(0)
  const getRankList = async() => {
    const { data } = await getPairSortList({
      sort_by: 'v',
      order_by: 'desc',
      level: 5,
      is_all: 1
    })
    if (data) {
      rankObj.value = data
    }
  }
  const getAreaList = async(lang, markets) => {
    const { data } = await getPairAreas({ lang })
    if (data) {
      editableTabs.value = data
      editableTabsValue.value = data && data[0] && data[0].id || ''
      const secondArr = data.filter((item) => {
        return item.level * 1 === 2 && item.father_area_id === editableTabsValue.value
      })
      secondActive.value = secondArr && secondArr[0] && secondArr[0].id || ''
      getAreaByPage1List(secondArr.length > 0 ? secondActive.value : editableTabsValue.value, markets)
    }
  }
  const setSecondActive = (item) => {
    secondActive.value = item
  }
  const getAreaByPage1List = async(id, markets) => {
    const { data } = await getPairsByArea({
      id,
      page: 1,
      size: 20,
      need_kline: 1
    })
    if (data) {
      tableData.value = data.rows
      tableTotal.value = data.count
      const keys = ['time', 'open', 'high', 'low', 'close', 'dealAmount', 'volume', 'id', 'exchangeAmount']
      tableData.value.forEach((item) => {
        const klineData = item.klineData || []
        let mappedItems = []
        item.kline.forEach((ite) => {
          const finalItem = {}
          ite.forEach((v, i) => {
            finalItem[keys[i]] = Number(v) < 0 ? -Number(v) : Number(v)
          })
          mappedItems.push(finalItem)
        })
        // 将映射后的数据添加到klineData
        klineData.push(...mappedItems)
        // 更新item的klineData
        item.klineData = klineData
        // console.info(klineData, 'klineData')
      })
      setTimeout(() => {
        renderKline(markets)
      }, 10)
    }
  }
  const renderKline = (markets) => {
    tableData.value.forEach((item, index) => {
      const el = document.getElementById(`chart-${item.pair}`)
      const config = {
        width: 96,
        height: 40,
        paddingTop: 0,
        paddingLeft: 0,
        lineWidth: 1,
        data: item.klineData.map((item) => item.close)
      }
      const num = Number((markets[item.pair] || {}).change) || Number(item.change)
      let buy = '#3cc188'
      let sell = '#ff6262'
      let buyFill = 'rgba(60,193,136,0.3)'
      let sellFill = 'rgba(255,98,98,0.3)'
      let buyEndFill = 'rgba(60,193,136,0)'
      let sellEndFill = 'rgba(255,98,98, 0)'
      if (num < 0) {
        config.strokeStyle = sell
        config.fillStyle = [
          sellFill,
          sellEndFill
        ]
      } else {
        config.strokeStyle = buy
        config.fillStyle = [
          buyFill,
          buyEndFill
        ]
      }
      el && kline.draw(el, config)
    })
  }
  const updateTableData = (newData) => {
    tableData.value = newData
  }
  // 添加追加数据的方法
  const appendTableData = (newItems) => {
    tableData.value = [...tableData.value, ...newItems]
  }
  return {
    rankObj,
    editableTabs,
    editableTabsValue,
    secondActive,
    tableData,
    tableTotal,
    setSecondActive,
    renderKline,
    getRankList,
    getAreaList,
    getAreaByPage1List,
    updateTableData,
    appendTableData
  }
}, {
    persist: true // 简单启用，所有状态都会被持久化
})
