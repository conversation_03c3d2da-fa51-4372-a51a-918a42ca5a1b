import { defineStore } from 'pinia'
import { getUserInfo, getSwitchAccountList } from '~/api/user'
import { cookies } from '~/utils'
export const useUserStore = defineStore('user', () => {
  const userInfo = ref({})
  const isLogin = ref(false)
  const isVisibleUserInfo = ref(false)
  const isUserStateLoading = ref(true)
  const isUserInfoLoading = ref(false)
  const mainUserInfo = ref({})
  const sonInfoList = ref([])
  const getUserInfoAction = async() => {
    const { data, error } = await getUserInfo()
    if (data) {
      cookies.set('isLogin', true)
      window.sensors.login(data.user_id)
      userInfo.value = Object.assign({}, data, {
        name: data.user_type * 1 === 100 ? (data.son_type * 1 === 2 ? data.nick_name : data.son_name) : data.email,
        name_visible: data.user_type * 1 === 100 ? data.nick_name : data.email_visible
      })
      cookies.set('session_id', data.sid)
      cookies.set('session_id_origin', data.sid)
      isLogin.value = true
      isVisibleUserInfo.value = data.email_visible !== undefined
      isUserInfoLoading.value = true
    } else {
      cookies.remove('isLogin')
      cookies.remove('session_id')
      cookies.remove('session_id_origin')
      cookies.remove('futureslpcNotOrderConfirmAgain')
      cookies.remove('showMessage')
      userInfo.value = {}
      isLogin.value = false
      isUserStateLoading.value = false
    }
  }
  const getsubAccountList = async() => {
    const { data } = await getSwitchAccountList()
    if (data) {
      mainUserInfo.value = data.main_user_info
      sonInfoList.value = data.son_info_list
    }
  }
  const LoginStaus = (status) => {
    isLogin.value = status
  }
  return {
    userInfo,
    isLogin,
    isVisibleUserInfo,
    isUserStateLoading,
    isUserInfoLoading,
    mainUserInfo,
    sonInfoList,
    LoginStaus,
    getUserInfoAction,
    getsubAccountList
  }
})