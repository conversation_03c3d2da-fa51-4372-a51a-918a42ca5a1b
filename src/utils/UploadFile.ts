import axios from "axios"
import { imgCompress } from '~/utils/index'
import qs from 'qs'
const getFile = function (file) {
  return new Promise((resolve) => {
    resolve(file.file)
  })
}
export class UploadFile {
  constructor(res, fileData, url) {
    this.fileData = fileData
    this.data = res
    this.url = url || ''
    this.getKeyPr = null
    this.zipImgPr = getFile(fileData)
    this.zipImg.bind(this)
    this.upload.bind(this)
  }
  zipImg (w, h) { // 压缩图片
    const fileData = this.fileData
    this.zipImgPr = imgCompress(fileData.file, w, h)
    return this
  }
  async upload (path, isRelativeHref = false) { // 上传
    const zipImgPr = this.zipImgPr
    const configKey = {
      method: 'post',
      url: this.url || baseURL + '/v1/upload/uploadfront',
      data: qs.stringify(this.data),
      withCredentials: true,
      header: {
        'Content-Type': 'application/json'
      }
    }
    const { data } = await axios(configKey)
    const keyData = data.result
    const file = await zipImgPr
    const _path = typeof path === 'function' ? path(keyData, this.fileData.file.name) : path
    return new Promise(async (resolve, reject) => {
      try {
        const data = new FormData()
        data.append('key', _path)
        data.append('OSSAccessKeyId', keyData.OSSAccessKeyId)
        data.append('policy', keyData.policy)
        data.append('saveName', keyData.saveName)
        data.append('signature', keyData.signature)
        data.append('bucket', keyData.bucket)
        data.append('success_action_status', 201)
        data.append('file', file)
        const param = {
          method: 'post',
          url: keyData.host,
          data: data,
          withCredentials: true,
          header: {
            'Content-Type': 'application/json'
          }
        }

        const resXml = await axios(param)
        console.info(resXml, 'FileReader')
        const typeObj = typeof resXml === 'object'
        let xmlDoc
        if (window.DOMParser) {
          const parser = new DOMParser()
          xmlDoc = parser.parseFromString(typeObj ? resXml.data : resXml, 'text/xml')
        } else {
          xmlDoc = new window.ActiveXObject('Microsoft.XMLDOM')
          xmlDoc.async = 'false'
          xmlDoc.loadXML(resXml)
        }
        console.info(xmlDoc, 'FileReader')
        let imgUrl = xmlDoc.getElementsByTagName('Location')[0].innerHTML.split('.com/').pop() || ''
        imgUrl = imgUrl.startsWith('/') ? imgUrl : `/${imgUrl}`
        resolve(isRelativeHref ? imgUrl : keyData.host + imgUrl)
        // return xmlDoc.getElementsByTagName('Location')[0].innerHTML.split('.com/').pop()
      } catch (err) {
        const imgUrl = `${keyData.host}${_path.startsWith('/') ? '' : '/'}${_path}`
        const img = new Image()
        img.src = imgUrl
        img.onload = () => {
          resolve(isRelativeHref ? (_path.startsWith('/') ? _path : `/${_path}`) : imgUrl)
        }
        img.onerror = () => {
          reject(err)
        }
      }
    })
  }
}