export default {
  AR: {
    code: "AR",
    en: "Argentina",
    zh: "阿根廷",
    phoneCode: 54,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  AD: {
    code: "AD",
    en: "Andorra",
    zh: "安道尔",
    phoneCode: 376,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  AE: {
    code: "AE",
    en: "United Arab Emirates",
    zh: "阿联酋",
    phoneCode: 971,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  AF: {
    code: "AF",
    en: "Afghanistan",
    zh: "阿富汗",
    phoneCode: 93,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  AG: {
    code: "AG",
    en: "Antigua & Barbuda",
    zh: "安提瓜和巴布达",
    phoneCode: 1268,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  AI: {
    code: "AI",
    en: "Ang<PERSON><PERSON>",
    zh: "安圭拉",
    phoneCode: 1264,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  AL: {
    code: "AL",
    en: "Albania",
    zh: "阿尔巴尼亚",
    phoneCode: 355,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  AM: {
    code: "AM",
    en: "Armenia",
    zh: "亚美尼亚",
    phoneCode: 374,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  AO: {
    code: "AO",
    en: "Angola",
    zh: "安哥拉",
    phoneCode: 244,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  AQ: {
    code: "AQ",
    en: "Antarctica",
    zh: "南极洲",
    phoneCode: 1000,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  AS: {
    code: "AS",
    en: "American Samoa",
    zh: "美属萨摩亚",
    phoneCode: 684,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  AT: {
    code: "AT",
    en: "Austria",
    zh: "奥地利",
    phoneCode: 43,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  AU: {
    code: "AU",
    en: "Australia",
    zh: "澳大利亚",
    phoneCode: 61,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  AW: {
    code: "AW",
    en: "Aruba",
    zh: "阿鲁巴",
    phoneCode: 297,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  AX: {
    code: "AX",
    en: "Aland Island",
    zh: "奥兰群岛",
    phoneCode: 358,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  AZ: {
    code: "AZ",
    en: "Azerbaijan",
    zh: "阿塞拜疆",
    phoneCode: 994,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  BA: {
    code: "BA",
    en: "Bosnia & Herzegovina",
    zh: "波黑",
    phoneCode: 387,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  BB: {
    code: "BB",
    en: "Barbados",
    zh: "巴巴多斯",
    phoneCode: 1246,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  BD: {
    code: "BD",
    en: "Bangladesh",
    zh: "孟加拉",
    phoneCode: 880,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  BE: {
    code: "BE",
    en: "Belgium",
    zh: "比利时",
    phoneCode: 32,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  BF: {
    code: "BF",
    en: "Burkina",
    zh: "布基纳法索",
    phoneCode: 226,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  BG: {
    code: "BG",
    en: "Bulgaria",
    zh: "保加利亚",
    phoneCode: 359,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  BH: {
    code: "BH",
    en: "Bahrain",
    zh: "巴林",
    phoneCode: 973,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  BI: {
    code: "BI",
    en: "Burundi",
    zh: "布隆迪",
    phoneCode: 257,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  BJ: {
    code: "BJ",
    en: "Benin",
    zh: "贝宁",
    phoneCode: 229,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  BL: {
    code: "BL",
    en: "Saint Barthélemy",
    zh: "圣巴泰勒米岛",
    phoneCode: 590,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  BM: {
    code: "BM",
    en: "Bermuda",
    zh: "百慕大",
    phoneCode: 441,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  BN: {
    code: "BN",
    en: "Brunei",
    zh: "文莱",
    phoneCode: 673,
    isPassport: true,
    isIDCard: false,
    isLicense: true
  },
  BO: {
    code: "BO",
    en: "Bolivia",
    zh: "玻利维亚",
    phoneCode: 591,
    isPassport: true,
    isIDCard: true,
    isLicense: false
  },
  BQ: {
    code: "BQ",
    en: "Caribbean Netherlands",
    zh: "荷兰加勒比区",
    phoneCode: 599,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  BR: {
    code: "BR",
    en: "Brazil",
    zh: "巴西",
    phoneCode: 55,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  BS: {
    code: "BS",
    en: "The Bahamas",
    zh: "巴哈马",
    phoneCode: 1242,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  BT: {
    code: "BT",
    en: "Bhutan",
    zh: "不丹",
    phoneCode: 975,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  BV: {
    code: "BV",
    en: "Bouvet Island",
    zh: "布韦岛",
    phoneCode: 1000,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  BW: {
    code: "BW",
    en: "Botswana",
    zh: "博茨瓦纳",
    phoneCode: 267,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  BY: {
    code: "BY",
    en: "Belarus",
    zh: "白俄罗斯",
    phoneCode: 375,
    isPassport: true,
    isIDCard: false,
    isLicense: true
  },
  BZ: {
    code: "BZ",
    en: "Belize",
    zh: "伯利兹",
    phoneCode: 501,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  CA: {
    code: "CA",
    en: "Canada",
    zh: "加拿大",
    phoneCode: 1,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  CC: {
    code: "CC",
    en: "Cocos (Keeling) Islands",
    zh: "科科斯群岛",
    phoneCode: 891,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  CD: {
    code: "CD",
    en: "Democratic Republic of the Congo",
    zh: "刚果（金）",
    phoneCode: 243,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  CF: {
    code: "CF",
    en: "Central African Republic",
    zh: "中非",
    phoneCode: 236,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  CG: {
    code: "CG",
    en: "Republic of the Congo",
    zh: "刚果（布）",
    phoneCode: 242,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  CH: {
    code: "CH",
    en: "Switzerland",
    zh: "瑞士",
    phoneCode: 41,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  CI: {
    code: "CI",
    en: "Cote d'Ivoire",
    zh: "科特迪瓦",
    phoneCode: 225,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  CK: {
    code: "CK",
    en: "Cook Islands",
    zh: "库克群岛",
    phoneCode: 682,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  CL: {
    code: "CL",
    en: "Chile",
    zh: "智利",
    phoneCode: 56,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  CM: {
    code: "CM",
    en: "Cameroon",
    zh: "喀麦隆",
    phoneCode: 237,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  CN: {
    code: "CN",
    en: "China",
    zh: "中国",
    phoneCode: 86,
    isPassport: true,
    isIDCard: true,
    isLicense: false
  },
  CO: {
    code: "CO",
    en: "Colombia",
    zh: "哥伦比亚",
    phoneCode: 57,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  CR: {
    code: "CR",
    en: "Costa Rica",
    zh: "哥斯达黎加",
    phoneCode: 506,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  CU: {
    code: "CU",
    en: "Cuba",
    zh: "古巴",
    phoneCode: 53,
    isPassport: true,
    isIDCard: true,
    isLicense: false
  },
  CV: {
    code: "CV",
    en: "Cape Verde",
    zh: "佛得角",
    phoneCode: 238,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  CW: {
    code: "CW",
    en: "Curacao",
    zh: "库拉索",
    phoneCode: 5999,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  CX: {
    code: "CX",
    en: "Christmas Island",
    zh: "圣诞岛",
    phoneCode: 6724,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  CY: {
    code: "CY",
    en: "Cyprus",
    zh: "塞浦路斯",
    phoneCode: 357,
    isPassport: true,
    isIDCard: true,
    isLicense: false
  },
  CZ: {
    code: "CZ",
    en: "Czech Republic",
    zh: "捷克",
    phoneCode: 420,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  DE: {
    code: "DE",
    en: "Germany",
    zh: "德国",
    phoneCode: 49,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  DJ: {
    code: "DJ",
    en: "Djibouti",
    zh: "吉布提",
    phoneCode: 253,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  DK: {
    code: "DK",
    en: "Denmark",
    zh: "丹麦",
    phoneCode: 45,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  DM: {
    code: "DM",
    en: "Dominica",
    zh: "多米尼克",
    phoneCode: 1767,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  DO: {
    code: "DO",
    en: "Dominican Republic",
    zh: "多米尼加",
    phoneCode: 1890,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  DZ: {
    code: "DZ",
    en: "Algeria",
    zh: "阿尔及利亚",
    phoneCode: 213,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  EC: {
    code: "EC",
    en: "Ecuador",
    zh: "厄瓜多尔",
    phoneCode: 593,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  EE: {
    code: "EE",
    en: "Estonia",
    zh: "爱沙尼亚",
    phoneCode: 372,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  EG: {
    code: "EG",
    en: "Egypt",
    zh: "埃及",
    phoneCode: 20,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  EH: {
    code: "EH",
    en: "Western Sahara",
    zh: "西撒哈拉",
    phoneCode: 210,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  ER: {
    code: "ER",
    en: "Eritrea",
    zh: "厄立特里亚",
    phoneCode: 291,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  ES: {
    code: "ES",
    en: "Spain",
    zh: "西班牙",
    phoneCode: 34,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  ET: {
    code: "ET",
    en: "Ethiopia",
    zh: "埃塞俄比亚",
    phoneCode: 251,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  FI: {
    code: "FI",
    en: "Finland",
    zh: "芬兰",
    phoneCode: 358,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  FJ: {
    code: "FJ",
    en: "Fiji",
    zh: "斐济群岛",
    phoneCode: 679,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  FK: {
    code: "FK",
    en: "Falkland Islands",
    zh: "马尔维纳斯群岛（福克兰）",
    phoneCode: 500,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  FM: {
    code: "FM",
    en: "Federated States of Micronesia",
    zh: "密克罗尼西亚联邦",
    phoneCode: 691,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  FO: {
    code: "FO",
    en: "Faroe Islands",
    zh: "法罗群岛",
    phoneCode: 298,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  FR: {
    code: "FR",
    en: "France",
    zh: "法国 法国",
    phoneCode: 33,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  GA: {
    code: "GA",
    en: "Gabon",
    zh: "加蓬",
    phoneCode: 241,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  GB: {
    code: "GB",
    en: "Great Britain (United Kingdom; England)",
    zh: "英国",
    phoneCode: 44,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  GD: {
    code: "GD",
    en: "Grenada",
    zh: "格林纳达",
    phoneCode: 1473,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  GE: {
    code: "GE",
    en: "Georgia",
    zh: "格鲁吉亚",
    phoneCode: 995,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  GF: {
    code: "GF",
    en: "French Guiana",
    zh: "法属圭亚那",
    phoneCode: 594,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  GG: {
    code: "GG",
    en: "Guernsey",
    zh: "根西岛",
    phoneCode: 44,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  GH: {
    code: "GH",
    en: "Ghana",
    zh: "加纳",
    phoneCode: 233,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  GI: {
    code: "GI",
    en: "Gibraltar",
    zh: "直布罗陀",
    phoneCode: 350,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  GL: {
    code: "GL",
    en: "Greenland",
    zh: "格陵兰",
    phoneCode: 299,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  GM: {
    code: "GM",
    en: "Gambia",
    zh: "冈比亚",
    phoneCode: 220,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  GN: {
    code: "GN",
    en: "Guinea",
    zh: "几内亚",
    phoneCode: 224,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  GP: {
    code: "GP",
    en: "Guadeloupe",
    zh: "瓜德罗普",
    phoneCode: 590,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  GQ: {
    code: "GQ",
    en: "Equatorial Guinea",
    zh: "赤道几内亚",
    phoneCode: 240,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  GR: {
    code: "GR",
    en: "Greece",
    zh: "希腊",
    phoneCode: 30,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  GS: {
    code: "GS",
    en: "South Georgia and the South Sandwich Islands",
    zh: "南乔治亚岛和南桑威奇群岛",
    phoneCode: 500,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  GT: {
    code: "GT",
    en: "Guatemala",
    zh: "危地马拉",
    phoneCode: 502,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  GU: {
    code: "GU",
    en: "Guam",
    zh: "关岛",
    phoneCode: 1671,
    isPassport: false,
    isIDCard: true,
    isLicense: true
  },
  GW: {
    code: "GW",
    en: "Guinea-Bissau",
    zh: "几内亚比绍",
    phoneCode: 245,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  GY: {
    code: "GY",
    en: "Guyana",
    zh: "圭亚那",
    phoneCode: 592,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  HK: {
    code: "HK",
    en: "Hong Kong",
    zh: "中国香港",
    phoneCode: 852,
    isPassport: true,
    isIDCard: true,
    isLicense: false
  },
  HM: {
    code: "HM",
    en: "Heard Island and McDonald Islands",
    zh: "赫德岛和麦克唐纳群岛",
    phoneCode: 1000,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  HN: {
    code: "HN",
    en: "Honduras",
    zh: "洪都拉斯",
    phoneCode: 504,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  HR: {
    code: "HR",
    en: "Croatia",
    zh: "克罗地亚",
    phoneCode: 385,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  HT: {
    code: "HT",
    en: "Haiti",
    zh: "海地",
    phoneCode: 509,
    isPassport: true,
    isIDCard: false,
    isLicense: true
  },
  HU: {
    code: "HU",
    en: "Hungary",
    zh: "匈牙利",
    phoneCode: 36,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  ID: {
    code: "ID",
    en: "Indonesia",
    zh: "印尼",
    phoneCode: 62,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  IE: {
    code: "IE",
    en: "Ireland",
    zh: "爱尔兰",
    phoneCode: 353,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  IL: {
    code: "IL",
    en: "Israel",
    zh: "以色列",
    phoneCode: 972,
    isPassport: true,
    isIDCard: false,
    isLicense: true
  },
  IM: {
    code: "IM",
    en: "Isle of Man",
    zh: "马恩岛",
    phoneCode: 44,
    isPassport: true,
    isIDCard: false,
    isLicense: true
  },
  IN: {
    code: "IN",
    en: "India",
    zh: "印度",
    phoneCode: 91,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  IO: {
    code: "IO",
    en: "British Indian Ocean Territory",
    zh: "英属印度洋领地",
    phoneCode: 246,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  IQ: {
    code: "IQ",
    en: "Iraq",
    zh: "伊拉克",
    phoneCode: 964,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  IR: {
    code: "IR",
    en: "Iran",
    zh: "伊朗",
    phoneCode: 98,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  IS: {
    code: "IS",
    en: "Iceland",
    zh: "冰岛",
    phoneCode: 354,
    isPassport: true,
    isIDCard: false,
    isLicense: true
  },
  IT: {
    code: "IT",
    en: "Italy",
    zh: "意大利",
    phoneCode: 39,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  JE: {
    code: "JE",
    en: "Jersey",
    zh: "泽西岛",
    phoneCode: 44,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  JM: {
    code: "JM",
    en: "Jamaica",
    zh: "牙买加",
    phoneCode: 1876,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  JO: {
    code: "JO",
    en: "Jordan",
    zh: "约旦",
    phoneCode: 962,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  JP: {
    code: "JP",
    en: "Japan",
    zh: "日本",
    phoneCode: 81,
    isPassport: true,
    isIDCard: false,
    isLicense: true
  },
  KE: {
    code: "KE",
    en: "Kenya",
    zh: "肯尼亚",
    phoneCode: 254,
    isPassport: true,
    isIDCard: true,
    isLicense: false
  },
  KG: {
    code: "KG",
    en: "Kyrgyzstan",
    zh: "吉尔吉斯斯坦",
    phoneCode: 996,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  KH: {
    code: "KH",
    en: "Cambodia",
    zh: "柬埔寨",
    phoneCode: 855,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  KI: {
    code: "KI",
    en: "Kiribati",
    zh: "基里巴斯",
    phoneCode: 686,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  KM: {
    code: "KM",
    en: "The Comoros",
    zh: "科摩罗",
    phoneCode: 269,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  KN: {
    code: "KN",
    en: "St. Kitts & Nevis",
    zh: "圣基茨和尼维斯",
    phoneCode: 1,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  KP: {
    code: "KP",
    en: "North Korea",
    zh: "朝鲜",
    phoneCode: 850,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  KR: {
    code: "KR",
    en: "South Korea",
    zh: "韩国",
    phoneCode: 82,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  KW: {
    code: "KW",
    en: "Kuwait",
    zh: "科威特",
    phoneCode: 965,
    isPassport: true,
    isIDCard: false,
    isLicense: true
  },
  KY: {
    code: "KY",
    en: "Cayman Islands",
    zh: "开曼群岛",
    phoneCode: 1345,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  KZ: {
    code: "KZ",
    en: "Kazakhstan",
    zh: "哈萨克斯坦",
    phoneCode: 7,
    isPassport: true,
    isIDCard: true,
    isLicense: false
  },
  LA: {
    code: "LA",
    en: "Laos",
    zh: "老挝",
    phoneCode: 856,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  LB: {
    code: "LB",
    en: "Lebanon",
    zh: "黎巴嫩",
    phoneCode: 961,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  LC: {
    code: "LC",
    en: "St. Lucia",
    zh: "圣卢西亚",
    phoneCode: 1758,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  LI: {
    code: "LI",
    en: "Liechtenstein",
    zh: "列支敦士登",
    phoneCode: 423,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  LK: {
    code: "LK",
    en: "Sri Lanka",
    zh: "斯里兰卡",
    phoneCode: 94,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  LR: {
    code: "LR",
    en: "Liberia",
    zh: "利比里亚",
    phoneCode: 231,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  LS: {
    code: "LS",
    en: "Lesotho",
    zh: "莱索托",
    phoneCode: 266,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  LT: {
    code: "LT",
    en: "Lithuania",
    zh: "立陶宛",
    phoneCode: 370,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  LU: {
    code: "LU",
    en: "Luxembourg",
    zh: "卢森堡",
    phoneCode: 352,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  LV: {
    code: "LV",
    en: "Latvia",
    zh: "拉脱维亚",
    phoneCode: 371,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  LY: {
    code: "LY",
    en: "Libya",
    zh: "利比亚",
    phoneCode: 218,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  MO: {
    code: "MO",
    en: "Macao",
    zh: "中国澳门",
    phoneCode: 853,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  MA: {
    code: "MA",
    en: "Morocco",
    zh: "摩洛哥",
    phoneCode: 212,
    isPassport: true,
    isIDCard: true,
    isLicense: false
  },
  MC: {
    code: "MC",
    en: "Monaco",
    zh: "摩纳哥",
    phoneCode: 377,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  MD: {
    code: "MD",
    en: "Moldova",
    zh: "摩尔多瓦",
    phoneCode: 373,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  ME: {
    code: "ME",
    en: "Montenegro",
    zh: "黑山",
    phoneCode: 382,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  MF: {
    code: "MF",
    en: "Saint Martin (France)",
    zh: "法属圣马丁",
    phoneCode: 590,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  MG: {
    code: "MG",
    en: "Madagascar",
    zh: "马达加斯加",
    phoneCode: 261,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  MH: {
    code: "MH",
    en: "Marshall islands",
    zh: "马绍尔群岛",
    phoneCode: 692,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  MK: {
    code: "MK",
    en: "Republic of Macedonia (FYROM)",
    zh: "马其顿",
    phoneCode: 389,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  ML: {
    code: "ML",
    en: "Mali",
    zh: "马里",
    phoneCode: 223,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  MM: {
    code: "MM",
    en: "Myanmar (Burma)",
    zh: "缅甸",
    phoneCode: 95,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  MN: {
    code: "MN",
    en: "Mongolia",
    zh: "蒙古国",
    phoneCode: 976,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  MP: {
    code: "MP",
    en: "Northern Mariana Islands",
    zh: "北马里亚纳群岛",
    phoneCode: 1670,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  MQ: {
    code: "MQ",
    en: "Martinique",
    zh: "马提尼克",
    phoneCode: 596,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  MR: {
    code: "MR",
    en: "Mauritania",
    zh: "毛里塔尼亚",
    phoneCode: 222,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  MS: {
    code: "MS",
    en: "Montserrat",
    zh: "蒙塞拉特岛",
    phoneCode: 1664,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  MT: {
    code: "MT",
    en: "Malta",
    zh: "马耳他",
    phoneCode: 356,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  MU: {
    code: "MU",
    en: "Mauritius",
    zh: "毛里求斯",
    phoneCode: 230,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  MV: {
    code: "MV",
    en: "Maldives",
    zh: "马尔代夫",
    phoneCode: 960,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  MW: {
    code: "MW",
    en: "Malawi",
    zh: "马拉维",
    phoneCode: 265,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  MX: {
    code: "MX",
    en: "Mexico",
    zh: "墨西哥",
    phoneCode: 52,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  MY: {
    code: "MY",
    en: "Malaysia",
    zh: "马来西亚",
    phoneCode: 60,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  MZ: {
    code: "MZ",
    en: "Mozambique",
    zh: "莫桑比克",
    phoneCode: 258,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  NA: {
    code: "NA",
    en: "Namibia",
    zh: "纳米比亚",
    phoneCode: 264,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  NC: {
    code: "NC",
    en: "New Caledonia",
    zh: "新喀里多尼亚",
    phoneCode: 687,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  NE: {
    code: "NE",
    en: "Niger",
    zh: "尼日尔",
    phoneCode: 227,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  NF: {
    code: "NF",
    en: "Norfolk Island",
    zh: "诺福克岛",
    phoneCode: 6723,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  NG: {
    code: "NG",
    en: "Nigeria",
    zh: "尼日利亚",
    phoneCode: 234,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  NI: {
    code: "NI",
    en: "Nicaragua",
    zh: "尼加拉瓜",
    phoneCode: 505,
    isPassport: true,
    isIDCard: true,
    isLicense: false
  },
  NL: {
    code: "NL",
    en: "Netherlands",
    zh: "荷兰",
    phoneCode: 31,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  NO: {
    code: "NO",
    en: "Norway",
    zh: "挪威",
    phoneCode: 47,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  NP: {
    code: "NP",
    en: "Nepal",
    zh: "尼泊尔",
    phoneCode: 977,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  NR: {
    code: "NR",
    en: "Nauru",
    zh: "瑙鲁",
    phoneCode: 674,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  NU: {
    code: "NU",
    en: "Niue",
    zh: "纽埃",
    phoneCode: 683,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  NZ: {
    code: "NZ",
    en: "New Zealand",
    zh: "新西兰",
    phoneCode: 64,
    isPassport: true,
    isIDCard: false,
    isLicense: true
  },
  OM: {
    code: "OM",
    en: "Oman",
    zh: "阿曼",
    phoneCode: 968,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  PA: {
    code: "PA",
    en: "Panama",
    zh: "巴拿马",
    phoneCode: 507,
    isPassport: true,
    isIDCard: true,
    isLicense: false
  },
  PE: {
    code: "PE",
    en: "Peru",
    zh: "秘鲁",
    phoneCode: 51,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  PF: {
    code: "PF",
    en: "French polynesia",
    zh: "法属波利尼西亚",
    phoneCode: 689,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  PG: {
    code: "PG",
    en: "Papua New Guinea",
    zh: "巴布亚新几内亚",
    phoneCode: 675,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  PH: {
    code: "PH",
    en: "The Philippines",
    zh: "菲律宾",
    phoneCode: 63,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  PK: {
    code: "PK",
    en: "Pakistan",
    zh: "巴基斯坦",
    phoneCode: 92,
    isPassport: true,
    isIDCard: true,
    isLicense: false
  },
  PL: {
    code: "PL",
    en: "Poland",
    zh: "波兰",
    phoneCode: 48,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  PM: {
    code: "PM",
    en: "Saint-Pierre and Miquelon",
    zh: "圣皮埃尔和密克隆",
    phoneCode: 508,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  PN: {
    code: "PN",
    en: "Pitcairn Islands",
    zh: "皮特凯恩群岛",
    phoneCode: 64,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  PR: {
    code: "PR",
    en: "Puerto Rico",
    zh: "波多黎各",
    phoneCode: 1787,
    isPassport: false,
    isIDCard: true,
    isLicense: true
  },
  PS: {
    code: "PS",
    en: "Palestinian territories",
    zh: "巴勒斯坦",
    phoneCode: 970,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  PT: {
    code: "PT",
    en: "Portugal",
    zh: "葡萄牙",
    phoneCode: 351,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  PW: {
    code: "PW",
    en: "Palau",
    zh: "帕劳",
    phoneCode: 680,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  PY: {
    code: "PY",
    en: "Paraguay",
    zh: "巴拉圭",
    phoneCode: 595,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  QA: {
    code: "QA",
    en: "Qatar",
    zh: "卡塔尔",
    phoneCode: 974,
    isPassport: true,
    isIDCard: false,
    isLicense: true
  },
  RE: {
    code: "RE",
    en: "Réunion",
    zh: "留尼汪",
    phoneCode: 262,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  RO: {
    code: "RO",
    en: "Romania",
    zh: "罗马尼亚",
    phoneCode: 40,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  RS: {
    code: "RS",
    en: "Serbia",
    zh: "塞尔维亚",
    phoneCode: 381,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  RU: {
    code: "RU",
    en: "Russian Federation",
    zh: "俄罗斯",
    phoneCode: 7,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  RW: {
    code: "RW",
    en: "Rwanda",
    zh: "卢旺达",
    phoneCode: 250,
    isPassport: true,
    isIDCard: true,
    isLicense: false
  },
  SA: {
    code: "SA",
    en: "Saudi Arabia",
    zh: "沙特阿拉伯",
    phoneCode: 966,
    isPassport: true,
    isIDCard: false,
    isLicense: true
  },
  SB: {
    code: "SB",
    en: "Solomon Islands",
    zh: "所罗门群岛",
    phoneCode: 677,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  SC: {
    code: "SC",
    en: "Seychelles",
    zh: "塞舌尔",
    phoneCode: 248,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  SD: {
    code: "SD",
    en: "Sudan",
    zh: "苏丹",
    phoneCode: 249,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  SE: {
    code: "SE",
    en: "Sweden",
    zh: "瑞典",
    phoneCode: 46,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  SG: {
    code: "SG",
    en: "Singapore",
    zh: "新加坡",
    phoneCode: 65,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  SH: {
    code: "SH",
    en: "St. Helena & Dependencies",
    zh: "圣赫勒拿",
    phoneCode: 290,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  SI: {
    code: "SI",
    en: "Slovenia",
    zh: "斯洛文尼亚",
    phoneCode: 386,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  SJ: {
    code: "SJ",
    en: "Svalbard and Jan Mayen",
    zh: "斯瓦尔巴群岛和扬马延岛",
    phoneCode: 1000,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  SK: {
    code: "SK",
    en: "Slovakia",
    zh: "斯洛伐克",
    phoneCode: 421,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  SL: {
    code: "SL",
    en: "Sierra Leone",
    zh: "塞拉利昂",
    phoneCode: 232,
    isPassport: true,
    isIDCard: true,
    isLicense: false
  },
  SM: {
    code: "SM",
    en: "San Marino",
    zh: "圣马力诺",
    phoneCode: 378,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  SN: {
    code: "SN",
    en: "Senegal",
    zh: "塞内加尔",
    phoneCode: 221,
    isPassport: true,
    isIDCard: true,
    isLicense: false
  },
  SO: {
    code: "SO",
    en: "Somalia",
    zh: "索马里",
    phoneCode: 252,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  SR: {
    code: "SR",
    en: "Suriname",
    zh: "苏里南",
    phoneCode: 597,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  SS: {
    code: "SS",
    en: "South Sudan",
    zh: "南苏丹",
    phoneCode: 211,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  ST: {
    code: "ST",
    en: "Sao Tome & Principe",
    zh: "圣多美和普林西比",
    phoneCode: 239,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  SV: {
    code: "SV",
    en: "El Salvador",
    zh: "萨尔瓦多",
    phoneCode: 503,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  SX: {
    code: "SX",
    en: "Sint Maarten",
    zh: "荷属圣马丁",
    phoneCode: 1,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  SY: {
    code: "SY",
    en: "Syria",
    zh: "叙利亚",
    phoneCode: 963,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  SZ: {
    code: "SZ",
    en: "Swaziland",
    zh: "斯威士兰",
    phoneCode: 268,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  TW: {
    code: "TW",
    en: "Taiwan",
    zh: "中国台湾",
    phoneCode: 886,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  TC: {
    code: "TC",
    en: "Turks & Caicos Islands",
    zh: "特克斯和凯科斯群岛",
    phoneCode: 1649,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  TD: {
    code: "TD",
    en: "Chad",
    zh: "乍得",
    phoneCode: 235,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  TF: {
    code: "TF",
    en: "French Southern Territories",
    zh: "法属南部领地",
    phoneCode: 1000,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  TG: {
    code: "TG",
    en: "Togo",
    zh: "多哥",
    phoneCode: 228,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  TH: {
    code: "TH",
    en: "Thailand",
    zh: "泰国",
    phoneCode: 66,
    isPassport: true,
    isIDCard: true,
    isLicense: false
  },
  TJ: {
    code: "TJ",
    en: "Tajikistan",
    zh: "塔吉克斯坦",
    phoneCode: 992,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  TK: {
    code: "TK",
    en: "Tokelau",
    zh: "托克劳",
    phoneCode: 690,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  TL: {
    code: "TL",
    en: "Timor-Leste (East Timor)",
    zh: "东帝汶",
    phoneCode: 670,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  TM: {
    code: "TM",
    en: "Turkmenistan",
    zh: "土库曼斯坦",
    phoneCode: 993,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  TN: {
    code: "TN",
    en: "Tunisia",
    zh: "突尼斯",
    phoneCode: 216,
    isPassport: true,
    isIDCard: false,
    isLicense: true
  },
  TO: {
    code: "TO",
    en: "Tonga",
    zh: "汤加",
    phoneCode: 676,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  TR: {
    code: "TR",
    en: "Turkey",
    zh: "土耳其",
    phoneCode: 90,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  TT: {
    code: "TT",
    en: "Trinidad & Tobago",
    zh: "特立尼达和多巴哥",
    phoneCode: 1868,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  TV: {
    code: "TV",
    en: "Tuvalu",
    zh: "图瓦卢",
    phoneCode: 688,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  TZ: {
    code: "TZ",
    en: "Tanzania",
    zh: "坦桑尼亚",
    phoneCode: 255,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  US: {
    code: "US",
    en: "United States of America (USA)",
    zh: "美国",
    phoneCode: 1,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  UA: {
    code: "UA",
    en: "Ukraine",
    zh: "乌克兰",
    phoneCode: 380,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  UG: {
    code: "UG",
    en: "Uganda",
    zh: "乌干达",
    phoneCode: 256,
    isPassport: true,
    isIDCard: false,
    isLicense: true
  },
  UM: {
    code: "UM",
    en: "United States Minor Outlying Islands",
    zh: "美国本土外小岛屿",
    phoneCode: 1000,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  UY: {
    code: "UY",
    en: "Uruguay",
    zh: "乌拉圭",
    phoneCode: 598,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  UZ: {
    code: "UZ",
    en: "Uzbekistan",
    zh: "乌兹别克斯坦",
    phoneCode: 998,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  VA: {
    code: "VA",
    en: "Vatican City (The Holy See)",
    zh: "梵蒂冈",
    phoneCode: 379,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  VC: {
    code: "VC",
    en: "St. Vincent & the Grenadines",
    zh: "圣文森特和格林纳丁斯",
    phoneCode: 1784,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  VE: {
    code: "VE",
    en: "Venezuela",
    zh: "委内瑞拉",
    phoneCode: 58,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  VG: {
    code: "VG",
    en: "British Virgin Islands",
    zh: "英属维尔京群岛",
    phoneCode: 1284,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  VI: {
    code: "VI",
    en: "United States Virgin Islands",
    zh: "美属维尔京群岛",
    phoneCode: 1340,
    isPassport: false,
    isIDCard: false,
    isLicense: true
  },
  VN: {
    code: "VN",
    en: "Vietnam",
    zh: "越南",
    phoneCode: 84,
    isPassport: true,
    isIDCard: true,
    isLicense: false
  },
  VU: {
    code: "VU",
    en: "Vanuatu",
    zh: "瓦努阿图",
    phoneCode: 678,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  WF: {
    code: "WF",
    en: "Wallis and Futuna",
    zh: "瓦利斯和富图纳",
    phoneCode: 1681,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  WS: {
    code: "WS",
    en: "Samoa",
    zh: "萨摩亚",
    phoneCode: 685,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  XK: {
    code: "XK",
    en: "Kosovo",
    zh: "科索沃",
    phoneCode: 383,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  YE: {
    code: "YE",
    en: "Yemen",
    zh: "也门",
    phoneCode: 967,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  YT: {
    code: "YT",
    en: "Mayotte",
    zh: "马约特",
    phoneCode: 262,
    isPassport: false,
    isIDCard: false,
    isLicense: false
  },
  ZA: {
    code: "ZA",
    en: "South Africa",
    zh: "南非",
    phoneCode: 27,
    isPassport: true,
    isIDCard: true,
    isLicense: true
  },
  ZM: {
    code: "ZM",
    en: "Zambia",
    zh: "赞比亚",
    phoneCode: 260,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  },
  ZW: {
    code: "ZW",
    en: "Zimbabwe",
    zh: "津巴布韦",
    phoneCode: 263,
    isPassport: true,
    isIDCard: false,
    isLicense: false
  }
};
