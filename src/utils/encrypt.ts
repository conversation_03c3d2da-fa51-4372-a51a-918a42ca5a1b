import jsrsasign from 'jsrsasign'
import CryptoJS from 'crypto-js'
import { getSessionStorage, setSessionStorage, cookies } from '~/utils'
const AESIV = '0902030405060708'

function getRealPublicKey(_key: string) {
  if (!_key || _key.length < 4) {
    console.error('key error')
    return _key
  }
  const keyArr = _key.split('')
  const splitLength = Number(keyArr[3])
  if (isNaN(splitLength) || _key.length < 5 + splitLength) {
    console.error('key error')
    return _key
  }
  keyArr.splice(keyArr.length - 13, 13)
  keyArr.splice(3 + splitLength, 2)
  keyArr.splice(3, 1)
  return keyArr.join('')
}

export function AESEncrypt(word: string) {
  try {
    let aesPrivateKey = getSessionStorage('akey')
    if (!aesPrivateKey) {
      aesPrivateKey = getPrivateKey()
      setSessionStorage('akey', aesPrivateKey)
    }
    const key = CryptoJS.enc.Utf8.parse(aesPrivateKey)
    const iv = CryptoJS.enc.Utf8.parse(AESIV)

    const decrypt = CryptoJS.AES.encrypt(word, key, { iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 })
    const decryptedStr = decrypt.toString()
    return decryptedStr
  } catch (err) {
    console.error('加密失败')
  }
}
export function AESDecrypt(_data: string) {
  try {
    const aesPrivateKey = getSessionStorage('akey')
    if (!aesPrivateKey) {
      console.error('failed to decrypt,aesKey has been deleted')
      return {}
    }
    const key = CryptoJS.enc.Utf8.parse(aesPrivateKey)
    const iv = CryptoJS.enc.Utf8.parse(AESIV)
    const result = CryptoJS.AES.decrypt(_data, key, { iv }).toString(CryptoJS.enc.Utf8)
    return JSON.parse(result)
  } catch (err) {
    console.error('解密失败', err)
  }
}

export function getPrivateKey() {
  const e = 32
  const t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
  const a = t.length
  let n = ''
  for (let i = 0; i < e; i++) { n += t.charAt(Math.floor(Math.random() * a)) }
  return n
}
export function encryptPrivateKey() {
  const ak: string = getSessionStorage('akey')
  const pkD = cookies.get('pkey')
  const rpk = getRealPublicKey(pkD)
  const pk: string = `-----BEGIN PUBLIC KEY-----
${rpk}
-----END PUBLIC KEY-----`
  // console.info('rpk....', rpk)
  const pub: any = jsrsasign.KEYUTIL.getKey(pk)
  const enc: string = (jsrsasign as any).KJUR.crypto.Cipher.encrypt(ak, pub)
  // 把参数与密文拼接好，返回
  return (jsrsasign as any).hex2b64(enc)
}

/**
 * 前端对一些接口中传递的敏感信息进行加密
 *
 * @param {string} string 需要加密的字符串
 * @return {string}
 */
export function SHA256Encrypt(data: string) {
  return CryptoJS.SHA256(data).toString()
}