const getFileExt = (_type: string) => {
  return _type ? _type.split('/')[1] : ''
}

const createDateFile = (file: Blob) => {
  return new Promise(resolve => {
    const reader = new FileReader()
    reader.onload = (e) => {
      resolve(e.target?.result)
    }
    reader.readAsDataURL(file)
  })
}

function getImageFile(file: string) {
  return new Promise(resolve => {
    // 加载图片获取图片真实宽度和高度
    const image = new Image()
    image.onload = function () {
      resolve(image)
    }
    image.src = file
  })
}

function clipImage(area: { x?: number; y?: number; w: any; h: any; }, data: ImageData) {
  const canvas = document.createElement('canvas')
  canvas.width = area.w
  canvas.height = area.h

  const context = canvas.getContext('2d') as CanvasRenderingContext2D
  context.putImageData(data, 0, 0)
  return canvas.toDataURL('image/png', 1)
}

async function addWaterMark(file: any, text = 'BGE') {
  const image = await getImageFile(file) as any
  // console.info('file ...', image)
  const canvas = document.createElement('canvas')
  const imgWidth = image.width
  const imgHeight = image.height
  const canvasWidth = Math.sqrt(2 * Math.pow(imgWidth / 2, 2)) + Math.sqrt(2 * Math.pow(imgHeight / 2, 2))
  canvas.setAttribute('style', `width:${canvasWidth}px;height:${canvasWidth}px;border:1px solid red;`)
  // document.body.appendChild(canvas)

  canvas.width = canvasWidth
  canvas.height = canvasWidth
  const context = canvas.getContext('2d') as CanvasRenderingContext2D
  // console.info('img...', image.width, image.height)
  // context.draw(image, 0, 0, image.width, image.height)
  context.drawImage(image, (canvasWidth - imgWidth) / 2, (canvasWidth - imgHeight) / 2, image.width, image.height)
  const lineHeight = 85
  const lineWidth = 85

  let line = 0
  context.translate(imgWidth / 2, -imgHeight / 2)
  context.rotate(45 * Math.PI / 180)

  context.font = '24px Roboto'
  while (true) {
    for (let i = 0; i < Math.ceil(canvasWidth / lineWidth); i++) {
      const x = i * lineWidth - line % 2 * 20
      const y = (line - 1) * lineHeight
      context.fillStyle = 'rgba(227,228,230, 0.6)'
      context.fillText(text, x + (lineHeight / 2), y + (lineHeight / 2), lineWidth)
    }
    if ((line) * lineHeight > canvasWidth + 1) {
      break
    }
    line++
  }
  const area = {
    x: 0,
    y: canvasWidth / 2 - imgHeight / 2,
    w: imgWidth,
    h: imgHeight
  }
  const data = context.getImageData((canvasWidth - imgWidth) / 2, (canvasWidth - imgHeight) / 2, image.width, image.height)
  const result = await clipImage(area, data)
  return result
}

export {
  addWaterMark,
  getFileExt,
  createDateFile
}