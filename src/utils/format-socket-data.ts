export const formatData = function (data) {
  let result
  if (data.stream.includes('.order_book.100')) {
    result = formatDepth(data)
  } else if (data.stream.includes('.candles.')) {
    result = formatKline(data)
  } else if (data.stream.includes('.trades')) {
    result = formatDeals(data)
  } else if (data.stream.includes('.info')) {
    result = formatFutureInfo(data)
  } else {
    result = data
  }
  return result
}
export const formatDepth = function (result) {
  const keys = ['price', 'volume'];
  let finalData;
  const dataBak = { ...result.data };

  // 将字符串数值转换为数值类型
  const convertToNumber = (value) => {
    return typeof value === 'string' ? parseFloat(value) : value;
  };

  if (result.t === 0) { // 全量数据
    finalData = {
      asks: {},
      bids: {}
    };

    // 处理 asks
    dataBak.a.forEach((item) => {
      const finalItem = {};
      item.forEach((v, i) => {
        finalItem[keys[i]] = convertToNumber(v); // 转换为数值
      });
      finalData.asks[finalItem.price] = finalItem;
    });

    // 处理 bids
    dataBak.b.forEach((item) => {
      const finalItem = {};
      item.forEach((v, i) => {
        finalItem[keys[i]] = convertToNumber(v); // 转换为数值
      });
      finalData.bids[finalItem.price] = finalItem;
    });

    finalData.pair = result.stream.split('.')[1];

  } else if (result.t === 1) { // 增量数据
    finalData = {
      pair: result.stream.split('.')[1],
      add: {
        asks: [],
        bids: []
      },
      del: {
        asks: [],
        bids: []
      }
    };

    // 处理 bids
    if (dataBak.b) {
      dataBak.b.forEach(bid => {
        const finalBid = {};
        bid.forEach((v, i) => {
          finalBid[keys[i]] = convertToNumber(v); // 转换为数值
        });
        if (bid[1] * 1 === 0) {
          finalData.del.bids.push(finalBid);
        } else {
          finalData.add.bids.push(finalBid);
        }
      });
    }

    // 处理 asks
    if (dataBak.a) {
      dataBak.a.forEach(ask => {
        const finalAsk = {};
        ask.forEach((v, i) => {
          finalAsk[keys[i]] = convertToNumber(v); // 转换为数值
        });
        if (ask[1] * 1 === 0) {
          finalData.del.asks.push(finalAsk);
        } else {
          finalData.add.asks.push(finalAsk);
        }
      });
    }
  }

  return {
    ...result,
    ...{
      d: finalData
    }
  };
}
export const formatKline = function (result) {
  const keys = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
  const dataBak = result.data.e
  const finalData = []
  dataBak.forEach((item) => {
    const finalItem = {
      time: Number(item[0])
    }
    item.forEach((v, i) => {
      finalItem[keys[i]] = Number(v) < 0 ? -Number(v) : Number(v)
    })
    finalData.push(finalItem)
  })
  return {
    ...result,
    ...{
      d: finalData
    }
  }
}
export const formatDeals = function (result) {
  const dataBak = result.data.e
  let finalData = {}
  dataBak.forEach((item) => {
    finalData[item.i] = item
  })
  return {
    ...result,
    ...{
      d: finalData
    }
  }
}
let FutureInfoData = {}; // 将 finalData 移到函数外部
export const formatFutureInfo = function (result) {
  const resKey = result.stream.split('.')[1]; // 获取 stream 中的关键部分作为键
  const dataBak = result.data; // 备份数据
  if (result.t === 0) {
    FutureInfoData[resKey] = dataBak;
  } else if (result.t === 1) {
    FutureInfoData = {
      ...FutureInfoData, // 展开原始对象
      [resKey]: {
        ...(FutureInfoData[resKey] || {}), // 如果 finalData[resKey] 不存在，初始化为空对象
        ...dataBak, // 用新值覆盖
      },
    };
  }
  return {
    ...result, // 保留原始 result 的所有字段
    d: FutureInfoData, // 添加格式化后的数据
  };
};