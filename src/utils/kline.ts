export default {
  draw (el, option) {
    this.el = el
    this.context = this.el.getContext('2d')
    this.setOption(option || {})
  },
  xConvert (n) {
    return this.originX + this.xSpac * n
  },
  yConvert (y) {
    return this.originY - ((y - Math.min.apply(null, this.data)) * this.yRatio)
  },
  setOption (option) {
    const defaultOption = {
      fillStyle: 'transparent',
      strokeStyle: '#f4faed',
      paddingLeft: 0,
      paddingRight: 0,
      paddingTop: 0,
      paddingBottom: 0,
      lineWidth: 1
    }
    const config = Object.assign({}, defaultOption, option)
    const el = this.el
    const context = this.context
    const devicePixelRatio = window.devicePixelRatio || 1
    // 浏览器在渲染canvas之前存储画布信息的像素比
    const backingStoreRatio =
      context.webkitBackingStorePixelRatio ||
      context.mozBackingStorePixelRatio ||
      context.msBackingStorePixelRatio ||
      context.oBackingStorePixelRatio ||
      context.backingStorePixelRatio ||
      1
    this.ratio = devicePixelRatio / backingStoreRatio
    this.width = (config.width || el.width) * this.ratio
    this.height = (config.height || el.height) * this.ratio
    this.fillStyle = config.fillStyle
    this.strokeStyle = config.strokeStyle
    this.lineWidth = config.lineWidth * this.ratio
    this.paddingLeft = config.paddingLeft * this.ratio
    this.paddingRight = config.paddingRight * this.ratio
    this.paddingTop = config.paddingTop * this.ratio
    this.paddingBottom = config.paddingBottom * this.ratio
    this.originX = this.paddingLeft
    this.originY = this.height - this.paddingBottom

    this.setting()
    if (config.data) {
      this.setData(config.data)
    }
  },
  setting () {
    const context = this.context
    const el = this.el
    const paddingLeft = this.paddingLeft
    const paddingBottom = this.paddingBottom
    const originY = this.originY
    const lineWidth = this.lineWidth
    const strokeStyle = this.strokeStyle
    const fillStyle = this.fillStyle

    el.width = this.width
    el.height = this.height
    el.style.width = this.width / this.ratio + 'px'
    el.style.height = this.height / this.ratio + 'px'
    if (Array.isArray(fillStyle)) {
      const lineargradient = context.createLinearGradient(
        paddingLeft,
        paddingBottom,
        paddingLeft,
        originY
      )
      lineargradient.addColorStop(0, fillStyle[0])
      lineargradient.addColorStop(1, fillStyle[1])
      context.fillStyle = lineargradient
    } else {
      context.fillStyle = fillStyle
    }
    context.lineWidth = lineWidth
    context.strokeStyle = strokeStyle
  },
  setData (data) {
    this.data = data
    const length = data.length
    const max = Math.max.apply(null, data)
    const min = Math.min.apply(null, data)
    this.xSpac =
      (this.width - this.paddingLeft - this.paddingRight) / (length - 1)
    this.yRatio =
      (this.height - this.paddingTop - this.paddingBottom) /
      (max - min)
    this.render()
  },
  render () {
    const context = this.context
    const originX = this.originX
    const originY = this.originY
    context.beginPath()
    this.data.forEach((item, index) => {
      context.lineTo(this.xConvert(index), this.yConvert(item))
    })
    context.stroke()
    context.lineTo(this.xConvert(this.data.length - 1), originY)
    context.lineTo(originX, originY)
    context.lineTo(originX, this.data[0])
    context.fill()
    context.closePath()
  }
}
  