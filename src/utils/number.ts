import bignumber from 'bignumber.js'
import { isString } from './types'

export function scientificNotation (value, decimal) {
  console.log(value, 'dhdhdhueud<PERSON><PERSON>')
  value = Number(value)
  if (!value && value !== 0) {
    return ''
  }
  value = value.toString()
  const isNegative = value.startsWith('-')
  const reg = /(?:(\d)+(?:.(\d+))?)[e]{1}-(\d)/.exec(value)
  if (value && reg) {
    if (reg[3] === '7') {
      value = '0.000000' + (reg[2] ? reg[1] + reg[2] : reg[1])
    } else {
      value = '0.0000000' + (reg[2] ? reg[1] + reg[2] : reg[1])
    }
    value = (isNegative ? '-' : '') + value
  }
  if (decimal) {
    const numLength = value.split('.')[1].length || 0
    const diffLength = decimal - numLength
    if (diffLength > 0 && numLength) {
      return `${value}${'0'.repeat(diffLength)}`
    }
  }
  return value
}

export function format(value, decimal, removeZero = false, isAsset = false, emptyFill = '--') {
  const subscriptNumbers = {
    0: '₀', 1: '₁', 2: '₂', 3: '₃', 4: '₄', 5: '₅', 6: '₆', 7: '₇', 8: '₈', 9: '₉',
    10: '₁₀', 11: '₁₁', 12: '₁₂', 13: '₁₃', 14: '₁₄', 15: '₁₅', 16: '₁₆', 17: '₁₇',
    18: '₁₈', 19: '₁₉', 20: '₂₀', 21: '₂₁', 22: '₂₂', 23: '₂₃', 24: '₂₄', 25: '₂₅',
    26: '₂₆', 27: '₂₇', 28: '₂₈', 29: '₂₉', 30: '₃₀', 31: '₃₁', 32: '₃₂', 33: '₃₃'
  };
  // 检查输入是否为有效数字
  if (isNaN(value)) {
    return emptyFill;
  }
  // 转换为数字
  value = Number(value);
  // 处理精度
  let formattedValue = value.toFixed(decimal);
  // 移除尾部多余的零
  if (removeZero) {
    const [integer, decimalPart] = formattedValue.split('.');
    const trimmedDecimalPart = decimalPart ? decimalPart.replace(/0+$/, '') : '';
    formattedValue = integer + (trimmedDecimalPart ? '.' + trimmedDecimalPart : '');
  }
  // 处理超长小数位
  const longDecimalReg = /\.0{8,}[1-9][0-9]*$/;
  if (longDecimalReg.test(formattedValue)) {
    const [integerPart, decimalPart] = formattedValue.split('.');
    const zeroCount = (decimalPart.match(/0*/) || [''])[0].length;
    formattedValue = `${integerPart}.0${subscriptNumbers[zeroCount]}${decimalPart.slice(zeroCount)}`;
  }
  // 千分位格式化
  const [integerPart, decimalPart] = formattedValue.split('.');
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  formattedValue = decimalPart ? `${formattedInteger}.${decimalPart}` : formattedInteger;
  // 单位转换
  if (!isAsset) {
    let unit = '';
    if (value >= 1e12) {
      value /= 1e12;
      unit = 'T'; // 万亿
    } else if (value >= 1e9) {
      value /= 1e9;
      unit = 'B'; // 十亿
    } else if (value >= 1e6) {
      value /= 1e6;
      unit = 'M'; // 百万
    }
    if (unit) {
      formattedValue = `${value.toFixed(2)}${unit}`;
    }
  }

  return formattedValue;
}
export function formatNumberWithCommas(num) {
  // 处理非数字输入
  if (num === null || num === undefined || num === '') return '0';
  // 转为字符串并去除可能存在的千分位符号
  let str = String(num).replace(/,/g, '');
  // 检查是否为有效数字
  if (!/^-?\d*\.?\d+$/.test(str)) return String(num);
  // 分离整数和小数部分
  const parts = str.split('.');
  let integerPart = parts[0];
  const decimalPart = parts.length > 1 ? `.${parts[1]}` : '';
  // 处理负号
  const sign = integerPart.startsWith('-') ? '-' : '';
  if (sign) integerPart = integerPart.slice(1);
  // 添加千分位逗号
  integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return sign + integerPart + decimalPart;
}

export function fixed(value: number | string, decimalPlaces: number | undefined, roundingMode?: bignumber.RoundingMode | undefined) {
  value = isString(value) ? value.replace(/[,，]/g, '') : value
  const result = new bignumber(value).toFixed(decimalPlaces as any, roundingMode)
  return result === 'NaN' ? '' : result
}

// 返回小数点位数
export function decimalPlaces(value: number|string) {
  value = isString(value) ? value.replace(/[,，]/g, '') : value
  return new bignumber(value).decimalPlaces() || 0
}

// 返回一个数字的绝对值
export function absoluteValue(value: number|string) {
  value = isString(value) ? value.replace(/[,，]/g, '') : value
  return new bignumber(value).absoluteValue()
}