// import {
//   unzip
// } from '@/utils'
import {
  formatData
} from '~/utils/format-socket-data'
export default class socket {
  ws: WebSocket|any = ''
  reconnectionDelay: number = 5000 // 关闭后多久后发起重连
  heartCheckDelay: number = 5000 // 每隔多久进行一次心跳
  sendDatas: any = [] // 发送的数据列表
  websocket: any = {}
  heartCheckObj: any = null
  callbacks: any = {}
  reconnecting: any = false
  debug: any = false
  lastPingSendTime: any = 0
  lastPongReceiveTime: any = 0
  pingSlowLimit: any = 5000 // ws数据慢时间判定
  pingDisconnectLimit: any = 10000 // ws已断开时间判定
  receiveCheckTimer: any = null
  lastReceiveMessageTime: any = 0
  sessionId: any = ''
  isClosed: any = false
  reconnectTimer: any = null
  lastShowTime: any = 0
  receivedMessages: Object = new Set()
  domainSuffix: string = ''
  // 标记是否强制销毁（禁止重连）
  private isForceDestroyed = false;
  constructor(ws, domainSuffix = '') {
    this.domainSuffix = domainSuffix
    if (process.client) {
      this.init(ws)
      document.addEventListener('visibilitychange', () => {
        var isHidden = document.hidden
        if (!isHidden && this.lastShowTime && new Date().getTime() - this.lastShowTime >= 1000 * 60 * 10) {
          this.websocket.close()
          const timer = setTimeout(() => {
            if (this.websocket.readyState !== WebSocket.OPEN) {
              this.reconnect()
            }
            clearTimeout(timer)
          }, 1000)
        } else {
          this.lastShowTime = new Date().getTime()
        }
      })
    }
  }

  init(_ws = '') {
    let ws = this.domainSuffix ? (_ws.endsWith(this.domainSuffix) ? _ws : ((_ws || this.ws) + this.domainSuffix)) : (_ws || this.ws)
    if (this.domainSuffix) {
      ws = ws.replace(new RegExp(`(${this.domainSuffix})+$`), this.domainSuffix)
    }
    this.ws = ws
    this.websocket = new WebSocket(ws)
    if (ws.includes('stream-market.tonetou.com')) {
      window.wsss = this.websocket
    }
    // 在 onmessage 中重新设置 result['t']
    this.websocket.onmessage = async (e) => {
      if (this.reconnecting) {
        this.reconnecting = false;
        this.reconnectTimer && clearInterval(this.reconnectTimer);
      }
      if (e.data && e.data.includes('stream')) {
        const result = JSON.parse(e.data);
        if (result.stream && !this.receivedMessages.has(result.stream)) {
          this.receivedMessages.add(result.stream);
          result['t'] = 0; // 第一次收到的数据
        } else if (result.stream) {
          result['t'] = 1; // 后续收到的数据
        }
        this.callbacks[result.stream] && this.callbacks[result.stream].forEach((callback) => {
          callback(formatData(result));
        });
        this.lastReceiveMessageTime = new Date().getTime();
      } else if (e.data.ping || e.data.includes('ping')) {
        let receiveParams = {};
        try {
          receiveParams = JSON.parse(e.data);
        } catch (err) {}
        this.send('pong', receiveParams.ping || Date.now());
      } else if (e.data && e.data.includes('pong')) {
        this.lastPongReceiveTime = new Date().getTime();

        if (this.lastPongReceiveTime && this.lastPingSendTime) {
          if (this.lastPongReceiveTime - this.lastPingSendTime >= this.pingDisconnectLimit) {
            console.warn('ws 心跳数据返回超时');
            this.callbacks['pingDetectDisconnect'] && this.callbacks['pingDetectDisconnect'].forEach(callback => {
              callback && callback(e);
            });
          } else if (this.lastPongReceiveTime - this.lastPingSendTime >= this.pingSlowLimit) {
            console.warn('ws 心跳数据返回缓慢');
            this.callbacks['pingDetectSlow'] && this.callbacks['pingDetectSlow'].forEach(callback => {
              callback && callback(e);
            });
          }
        }
      } else if (e.data && e.data.includes('UNSUBSCRIBE')) {
        const arr = Array.from(this.receivedMessages);
        const result = JSON.parse(e.data);
        const list = arr.filter((item) => {
          return item !== result.events[0];
        });
        this.receivedMessages = new Set(list);
      }
    };
    this.websocket.onclose = (e) => {
      console.info('init ws: close', e)
      this.onclose(e)
      if (this.callbacks['close']) {
        this.callbacks['close'].forEach(callback => {
          callback(e)
        })
      }
    }
    this.websocket.onopen = () => {
      console.info('init ws: open')
      this.onopen()
      if (this.callbacks['open']) {
        this.callbacks['open'].forEach(callback => {
          callback()
        })
      }
    }
    // 连接发生错误的回调方法
    this.websocket.onerror = (err) => {
      console.info('init ws: error', err)
      if (this.debug) {
        console.error('connected error')
      }
      // this.websocket.close()
      // this.reconnect()
      if (this.callbacks['error']) {
        this.callbacks['error'].forEach(callback => {
          callback()
        })
      }
    }
  }

  /**
   *
   * 默认监听 message/open/close/error
   * 由ws默认触发
   */
  on(eventName, callback) {
    if (this.debug) {
      console.info('on=> eventName:' + eventName + ', callback:', callback)
    }
    if (eventName) {
      if (typeof this.callbacks[eventName] !== 'object') {
        this.callbacks[eventName] = []
      }
      this.callbacks[eventName].push(callback)
    }
  }

  off(eventName, callback) {
    if (this.callbacks[eventName]) {
      this.callbacks[eventName] = this.callbacks[eventName].filter(fn => String(fn) !== String(callback))
    }
  }

  updateSendData(isSubOld, eventName, data) {
    // if (this.ws.includes('stream')) {
    //   console.log('z11', isSub, eventName, data)
    // }
    const isSub = !JSON.stringify(eventName).includes('UNSUBSCRIBE') && !JSON.stringify(data).includes('UNSUBSCRIBE')
    // if (this.ws.includes('stream')) {
    //   console.log('z11', isSub, eventName, data)
    // }
    if (isSub) {
      if (eventName.method === 'SUBSCRIBE' || eventName.method === 'LOGIN') {
        if (!this.sendDatas.some(v => JSON.stringify(v) === JSON.stringify(eventName))) {
          this.sendDatas.push(data)
        }
      }
    } else {
      const index = this.sendDatas.findIndex(v => (JSON.stringify(v.params) === JSON.stringify(eventName.params)) || v.method === 'LOGIN')
      if (index > -1) {
        this.sendDatas.splice(index, 1)
      }
    }
    // if (this.ws.includes('stream')) {
    //   console.log('z1111', JSON.stringify(this.sendDatas), this.sendDatas.length)
    // }
  }
  send(eventName, data = {}, isSub = true) {
    if ((isSub && (typeof data === 'boolean' && data))) {
      if (this.sendDatas.some(v => v === eventName.sub || v === eventName || v.sub === eventName.sub || v.sub === eventName)) {
        return;
      }
    }
    if (typeof data === 'boolean') {
      isSub = data;
      data = {};
    }
    if (typeof eventName !== 'object') {
      if (eventName === 'ping') {
        data = {
          ping: Date.now()
        };
      } else if (eventName === 'pong') {
        data = {
          pong: data
        };
      } else {
        data[isSub ? 'sub' : 'unsub'] = eventName;
        this.updateSendData(isSub, eventName, data);
      }
    } else {
      data = eventName;
      this.updateSendData(isSub, eventName, data);
    }
    if (data.session_id) {
      this.sessionId = data.session_id
    }
    if (this.websocket.readyState === this.websocket.OPEN) {
      if (this.debug) {
        console.info('send => eventName:' + eventName + ', data:', data);
      }
      this.websocket.send(JSON.stringify(data));
    } else {
      const timer = setTimeout(() => {
        if (eventName !== 'ping' && eventName !== 'pong' && !this.sendDatas.some(v => v === data || v.sub === data.sub || v.sub === data || v === data.sub)) {
          this.send(data);
        }
        clearTimeout(timer);
      }, 200);
    }
  }
  // 新增销毁方法（彻底关闭且不重连）
  destroy() {
    this.isForceDestroyed = true; // 标记为强制销毁
    this.heartCheckObj && clearInterval(this.heartCheckObj);
    this.reconnectTimer && clearInterval(this.reconnectTimer);
    this.receiveCheckTimer && clearInterval(this.receiveCheckTimer);
    
    if (this.websocket) {
      this.websocket.onclose = null; // 禁用自动重连回调
      this.websocket.close();
    }
    
    this.callbacks = {}; // 清空所有监听
    console.info('WebSocket connection destroyed permanently');
  }
  async onclose(e) {
    if (this.isForceDestroyed) return; // 强制销毁时不处理
    if (this.debug) {
      console.info('connection closed (' + e.code + ')')
    }
    this.reconnect(this.ws + this.domainSuffix)
  }

  reconnect(newWsUrl = '') {
    if (this.debug) {
      console.info('Reconnecting...');
    }
    if (this.reconnecting) {
      return false;
    }
    this.reconnecting = true;
    this.reconnectTimer && clearInterval(this.reconnectTimer);
    this.reconnectTimer = setInterval(() => {
      console.info('Checking WebSocket state:', this.websocket.readyState);
      if (this.websocket.readyState === this.websocket.OPEN) {
        if (this.callbacks['reconnect']) {
          this.callbacks['reconnect'].forEach(callback => {
            callback();
          });
        }
        this.reconnectTimer && clearInterval(this.reconnectTimer);
      } else if (this.websocket.readyState !== this.websocket.CONNECTING) {
        this.init(newWsUrl || this.ws);
      }
    }, this.reconnectionDelay);
  }

  onopen() {
    this.reconnecting = false;
    this.pingDisconnectLimit = this.pingDisconnectLimit + 1000;
    this.lastPongReceiveTime = new Date().getTime();
    this.lastPingSendTime = new Date().getTime();
    this.lastPongReceiveTime = new Date().getTime();
  
    // 重置 receivedMessages 集合
    this.receivedMessages = new Set();
  
    if (this.debug) {
      console.info('connected');
    }
  
    // 增加日志输出，检查 sendDatas
    console.info('Resending data from sendDatas:', this.sendDatas);
  
    this.sendDatas.forEach((data) => {
      const params = {
        ...data,
      };
      if (data.method.includes('LOGIN') && this.sessionId) {
        if (params.auth) {
          params.auth.sid = this.sessionId;
        } else {
          params.auth = {
            sid: this.sessionId,
          };
        }
      }
      this.send(params);
    });
  
    this.send('ping');
    this.heartCheck();
    this.reconnectTimer && clearInterval(this.reconnectTimer);
  }

  heartCheck() {
    this.heartCheckObj && clearInterval(this.heartCheckObj)
    this.heartCheckObj = setInterval(() => {
      if (this.debug) {
        console.info('heartCheck')
      }
      this.send('ping')
      this.lastPingSendTime = new Date().getTime()
    }, this.heartCheckDelay)
  }

  receiveMessageCheck() {
    this.receiveCheckTimer && clearInterval(this.receiveCheckTimer)
    this.receiveCheckTimer = setInterval(() => {
      if (this.sendDatas.length && new Date().getTime() - this.lastReceiveMessageTime > this.pingDisconnectLimit) {
        console.info('webSocket 数据推送缓慢 尝试重连', new Date().getTime(), this.lastReceiveMessageTime, new Date().getTime() - this.lastReceiveMessageTime, this.websocket.readyState)
        if (this.websocket.readyState === this.websocket.OPEN) {
          this.websocket.close()
        } else {
          this.reconnect()
        }
      } else if (this.lastPongReceiveTime && (new Date().getTime() - this.lastPongReceiveTime > this.pingDisconnectLimit)) {
        console.info('ws 心跳检测失败， 断开重连', this.domainSuffix, this.websocket.readyState)
        if (this.websocket.readyState === this.websocket.OPEN) {
          this.websocket.close()
        } else {
          this.reconnect()
        }
      }
    }, 5000)
  }
}
