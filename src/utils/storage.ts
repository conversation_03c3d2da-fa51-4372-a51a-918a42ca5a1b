/**
 * 存储localStorage
 */
export const setStorage = (name: string, content: any) => {
  if (!name) { return }
  content = JSON.stringify(content)
  try {
    window.localStorage.setItem(name, content)
  } catch (error) {}
}

export const setLocalStorage = (name: string, content: any) => {
  if (name !== 'cookieSettingInfo') {
    if (!(getStorage('cookieSettingInfo') && getStorage('cookieSettingInfo').functionalityCookie)) {
      return
    }
  }
  if (!name) { return }
  content = JSON.stringify(content)
  try {
    window.localStorage.setItem(name, content)
  } catch (error) {}
}

export const setSessionStorage = (name: string, content: any) => {
  if (!name) { return }
  content = JSON.stringify(content)
  try {
    window.sessionStorage.setItem(name, content)
  } catch (error) {}
}
/**
 * 获取localStorage
 */
export const getStorage =(name: string) => {
  if (!name) { return }
  try {
    const res = window.localStorage.getItem(name) || ''
    return JSON.parse(res)
  } catch (error) {}
}

export const getLocalStorage =(name: string) => {
  if (name !== 'cookieSettingInfo') {
    if (!(getStorage('cookieSettingInfo') && getStorage('cookieSettingInfo').functionalityCookie)) {
      return
    }
  }

  if (!name) { return }
  try {
    const res = window.localStorage.getItem(name) || ''
    return JSON.parse(res)
  } catch (error) {}
}

export const getSessionStorage =(name: string) => {
  if (!name) { return }
  try {
    const res = window.sessionStorage.getItem(name) || ''
    return JSON.parse(res)
  } catch (error) {}
}

export const removeSessionStorage = (name: string) => {
  if (!name) { return }
  try {
    window.sessionStorage.removeItem(name)
  } catch (error) {}
}
/**
 * 删除localStorage
 */
export const removeLocalStorage =(name: string) => {
  if (!name) { return }
  try {
    window.localStorage.removeItem(name)
  } catch (error) {}
}

export const clearStorate = () => {
  try {
    localStorage.clear()
  } catch (error) {}
}
