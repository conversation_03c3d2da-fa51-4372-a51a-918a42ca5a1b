const CAPTCHA_STATE = {
  default: 0,
  init: 1,
  ready: 2,
  verify: 3,
  err: 4,
  close: 5
}
class VerifyCode {
  constructor(callback,trackType) {
    this.state = CAPTCHA_STATE.default
    this.callback = callback || (() => {})
    this.reconnection = false
    this.trackType = trackType
    /* 人机验证referrer传不过去的问题（浏览器兼容） */
    const referrerMetaDom = document.querySelector('#referrer')
    referrerMetaDom && referrerMetaDom.setAttribute('content', 'no-referrer-when-downgrade')
    this.init()
    this.init = this.init.bind(this)
    this.verify = this.verify.bind(this)
  }

  verify() {
    if (this.state === CAPTCHA_STATE.ready) {
      this.captchaObj.showCaptcha()
    } else if (this.state === CAPTCHA_STATE.err) {
      this.init()
      this.reconnection = true
    } else {
      setTimeout(() => {
        this.verify()
      }, 200)
    }
  }
  init() {
    const initGeetest = window.initGeetest4
    initGeetest({
      captchaId: '9399c587220a7502b8e21439786a26bd',
      product: 'bind',
      language: localStorage.getItem('lang') === 'ja' ? 'jpn' : (localStorage.getItem('lang') === 'zh' ? 'zho' : 'eng')
    }, (captchaObj) => {
      this.state = CAPTCHA_STATE.init
      this.captchaObj = captchaObj
      captchaObj.onReady(() => {
        this.state = CAPTCHA_STATE.init
        if (this.state === CAPTCHA_STATE.verify || this.reconnection) {
          captchaObj.showCaptcha()
        }
        this.state = CAPTCHA_STATE.ready
      })
      captchaObj.onClose(() => {
        this.callback('close', {
          type: 'close',
          state: CAPTCHA_STATE.close
        })
      })
      captchaObj.onError((e) => {
        this.state = CAPTCHA_STATE.err
        this.callback({
          err: e,
          msg: e.user_error
        })
      })
      captchaObj.onSuccess(() => {
        const getValidate = captchaObj.getValidate()
        const param = {
          captcha_id: getValidate.captcha_id,
          captcha_output: getValidate.captcha_output,
          gen_time: getValidate.gen_time,
          pass_token: getValidate.pass_token,
          lot_number: getValidate.lot_number
        }
        this.callback('', {
          type: 'success',
          param
        })
      })
    })
  }
}

export default VerifyCode
